import React from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { Card, Select, FormItem, Button, Alert, Typography } from '@/shared/components/common';
import { RootState } from '@/shared/store';
import { setTimezone } from '../store/settingsSlice';
import { TIMEZONE_OPTIONS } from '../types';

const TimezoneSettings: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const { timezone } = useSelector((state: RootState) => state.settings);

  const handleTimezoneChange = (value: string | string[] | number | number[]) => {
    dispatch(setTimezone(value as string));
  };

  const getCurrentTime = (tz: string) => {
    try {
      return new Intl.DateTimeFormat('vi-VN', {
        timeZone: tz,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }).format(new Date());
    } catch {
      return 'Invalid timezone';
    }
  };

  const resetToSystemTimezone = () => {
    const systemTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    dispatch(setTimezone(systemTimezone));
  };

  return (
    <Card title={t('settings.timezone.title', 'Cài đặt Múi giờ')} className="mb-6">
      <div className="space-y-4">
        <FormItem
          label={t('settings.timezone.selectTimezone', 'Chọn múi giờ')}
        >
          <Select
            value={timezone}
            onChange={handleTimezoneChange}
            options={TIMEZONE_OPTIONS.map(tz => ({
              value: tz.value,
              label: tz.label,
            }))}
            placeholder={t('settings.timezone.selectPlaceholder', 'Chọn múi giờ...')}
            className="w-full"
          />
        </FormItem>

        {/* Current time preview */}
        <Alert
          type="info"
          title={t('settings.timezone.currentTime', 'Thời gian hiện tại')}
          message={
            <div className="flex items-center justify-between">
              <div>
                <Typography variant="body2" color="muted">
                  {t('settings.timezone.selectedTimezone', 'Múi giờ đã chọn')}: {timezone}
                </Typography>
              </div>
              <div className="text-right">
                <Typography
                  variant="subtitle1"
                  weight="semibold"
                  className="font-mono"
                  color="default"
                >
                  {getCurrentTime(timezone)}
                </Typography>
                <Typography variant="caption" color="muted">
                  {TIMEZONE_OPTIONS.find(tz => tz.value === timezone)?.offset || 'N/A'}
                </Typography>
              </div>
            </div>
          }
          showIcon={false}
        />

        {/* Timezone info */}
        <Alert
          type="info"
          title={t('settings.timezone.info', 'Thông tin')}
          message={
            <div className="space-y-1">
              <Typography variant="body2" color="default" className="flex items-start">
                <span className="mr-2">•</span>
                <span>{t('settings.timezone.infoLine1', 'Múi giờ ảnh hưởng đến hiển thị thời gian trong toàn bộ ứng dụng')}</span>
              </Typography>
              <Typography variant="body2" color="default" className="flex items-start">
                <span className="mr-2">•</span>
                <span>{t('settings.timezone.infoLine2', 'Thay đổi sẽ được áp dụng ngay lập tức')}</span>
              </Typography>
              <Typography variant="body2" color="default" className="flex items-start">
                <span className="mr-2">•</span>
                <span>{t('settings.timezone.infoLine3', 'Cài đặt này được lưu trữ cục bộ trên thiết bị của bạn')}</span>
              </Typography>
            </div>
          }
          showIcon={true}
        />

        {/* Reset button */}
        <div className="flex justify-end pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={resetToSystemTimezone}
            className="text-sm"
          >
            {t('settings.timezone.useSystemTimezone', 'Sử dụng múi giờ hệ thống')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default TimezoneSettings;
