import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// Cấu hình Vite cho widget
export default defineConfig({
  // Đặt entry point cho widget
  build: {
    outDir: 'dist/widget',
    emptyOutDir: true,
    lib: {
      entry: path.resolve(__dirname, 'src/widget/index.tsx'),
      name: 'RedAIChat',
      fileName: 'redai-chat-widget',
      formats: ['iife'], // Chỉ build dạng IIFE để có thể nhúng trực tiếp vào HTML
    },
    rollupOptions: {
      // Đảm bảo external không được sử dụng để tất cả dependencies được bundle vào một file
      external: [],
      output: {
        // Ghi đè globals để đảm bảo không có external dependencies
        globals: {},
        // Tùy chỉnh tên file output
        entryFileNames: 'redai-chat-widget.js',
        // Tắt code splitting để tạo một file duy nhất
        manualChunks: undefined,
        // Đảm bảo CSS được extract ra file riêng
        assetFileNames: (assetInfo) => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'redai-chat-widget.css';
          }
          return 'assets/[name]-[hash][extname]';
        },
      },
    },
    // Tắt source map trong production
    sourcemap: process.env.NODE_ENV !== 'production',
    // Tối ưu hóa kích thước bundle
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
      },
    },
    // Đảm bảo CSS được extract ra file riêng
    cssCodeSplit: false,
  },
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve('./src'),
      '@components': path.resolve('./src/components'),
      '@hooks': path.resolve('./src/hooks'),
      '@contexts': path.resolve('./src/contexts'),
      '@layouts': path.resolve('./src/layouts'),
      '@pages': path.resolve('./src/pages'),
      '@services': path.resolve('./src/services'),
      '@store': path.resolve('./src/store'),
      '@styles': path.resolve('./src/styles'),
      '@types': path.resolve('./src/types'),
      '@lib': path.resolve('./src/lib'),
      '@constants': path.resolve('./src/constants'),
      '@assets': path.resolve('./src/assets'),
    },
  },
  // Tùy chỉnh CSS để đảm bảo không xung đột với CSS của trang web
  css: {
    modules: {
      // Đảm bảo CSS modules có prefix để tránh xung đột
      generateScopedName: 'redai-chat__[local]__[hash:base64:5]',
    },
  },
});
