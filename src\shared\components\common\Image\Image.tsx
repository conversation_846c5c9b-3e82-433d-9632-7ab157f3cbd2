/**
 * Component hiển thị hình ảnh
 */
import React, { useState } from 'react';

export interface ImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  /**
   * URL của hình ảnh
   */
  src: string;

  /**
   * Alt text cho hình ảnh
   */
  alt: string;

  /**
   * Chiều rộng của hình ảnh
   */
  width?: number | string;

  /**
   * Chiều cao của hình ảnh
   */
  height?: number | string;

  /**
   * Tỷ lệ khung hình (aspect ratio)
   */
  aspectRatio?: string;

  /**
   * Lazy loading
   */
  lazy?: boolean;

  /**
   * Hiệu ứng blur khi loading
   */
  blur?: boolean;

  /**
   * Hiệu ứng khi hover
   */
  hoverEffect?: 'zoom' | 'brightness' | 'none';

  /**
   * Hiển thị fallback khi lỗi
   */
  fallback?: string;

  /**
   * Hiển thị placeholder khi loading
   */
  placeholder?: string;

  /**
   * Hiển thị border radius
   */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'full';

  /**
   * Hiển thị shadow
   */
  shadow?: 'none' | 'sm' | 'md' | 'lg';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị hình ảnh
 */
const Image: React.FC<ImageProps> = ({
  src,
  alt,
  width,
  height,
  aspectRatio,
  lazy = true,
  blur = false,
  hoverEffect = 'none',
  fallback = '',
  placeholder = '',
  rounded = 'none',
  shadow = 'none',
  className = '',
  ...rest
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Xử lý khi hình ảnh load xong
  const handleLoad = () => {
    setIsLoaded(true);
  };

  // Xử lý khi hình ảnh lỗi
  const handleError = () => {
    setHasError(true);
  };

  // Xác định class dựa trên rounded
  const roundedClasses = {
    none: '',
    sm: 'rounded',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full',
  }[rounded];

  // Xác định class dựa trên shadow
  const shadowClasses = {
    none: '',
    sm: 'shadow-sm',
    md: 'shadow',
    lg: 'shadow-lg',
  }[shadow];

  // Xác định class dựa trên hoverEffect
  const hoverEffectClasses = {
    none: '',
    zoom: 'hover:scale-105 transition-transform duration-300',
    brightness: 'hover:brightness-110 transition-all duration-300',
  }[hoverEffect];

  // Xác định class dựa trên blur
  const blurClass = blur && !isLoaded ? 'blur-sm' : '';

  // Combine all classes
  const imageClasses = [roundedClasses, shadowClasses, hoverEffectClasses, blurClass, className]
    .filter(Boolean)
    .join(' ');

  // Aspect ratio style
  const aspectRatioStyle = aspectRatio ? { aspectRatio, objectFit: 'cover' as const } : {};

  // Size style
  const sizeStyle = {
    width: width !== undefined ? width : '100%',
    height: height !== undefined ? height : 'auto',
  };

  // Combine all styles
  const combinedStyle = {
    ...aspectRatioStyle,
    ...sizeStyle,
    ...rest.style,
  };

  // Nếu có lỗi và có fallback, hiển thị fallback
  if (hasError && fallback) {
    return (
      <img src={fallback} alt={alt} className={imageClasses} style={combinedStyle} {...rest} />
    );
  }

  // Nếu đang loading và có placeholder, hiển thị placeholder
  if (!isLoaded && placeholder && !hasError) {
    return (
      <div className="relative">
        <img
          src={placeholder}
          alt={`${alt} placeholder`}
          className={`${imageClasses} absolute inset-0`}
          style={combinedStyle}
        />
        <img
          src={src}
          alt={alt}
          className={`${imageClasses} opacity-0`}
          style={combinedStyle}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazy ? 'lazy' : undefined}
          {...rest}
        />
      </div>
    );
  }

  // Trường hợp thông thường
  return (
    <img
      src={src}
      alt={alt}
      className={imageClasses}
      style={combinedStyle}
      onLoad={handleLoad}
      onError={handleError}
      loading={lazy ? 'lazy' : undefined}
      {...rest}
    />
  );
};

export default Image;
