import React, { useCallback, useEffect, useMemo } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Button, Icon } from '@/shared/components/common';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { TouchBackend } from 'react-dnd-touch-backend';
import { IconName } from '@/shared/components/common/Icon/Icon';

// Kiểu dữ liệu cho các nút điều khiển
export type FormArrayControls = 'add' | 'remove' | 'move' | 'all';

// Kiểu dữ liệu cho vị trí của các nút điều khiển
export type FormArrayControlsPosition = 'top' | 'bottom' | 'both';

// Kiểu dữ liệu cho kích thước của các nút điều khiển
export type FormArrayButtonSize = 'sm' | 'md' | 'lg';

// Kiểu dữ liệu cho animation khi drag-and-drop
export type FormArrayAnimation = 'none' | 'fade' | 'slide' | 'scale';

// Kiểu dữ liệu cho trục drag-and-drop
export type FormArrayAxis = 'vertical' | 'horizontal';

// Kiểu dữ liệu cho strategy drag-and-drop
export type FormArrayStrategy = 'vertical' | 'horizontal' | 'grid';

// Kiểu dữ liệu cho layout của FormArray
export type FormArrayLayout = 'list' | 'grid' | 'card';

// Kiểu dữ liệu cho item trong mảng
export interface FormArrayItemProps {
  /**
   * Index của item trong mảng
   */
  index: number;

  /**
   * Tổng số item trong mảng
   */
  total: number;

  /**
   * Hàm xóa item
   */
  remove: () => void;

  /**
   * Hàm di chuyển item
   */
  move: (toIndex: number) => void;

  /**
   * Có thể di chuyển item hay không
   */
  canMove: boolean;

  /**
   * Có thể xóa item hay không
   */
  canRemove: boolean;

  /**
   * Kích thước của các nút điều khiển
   */
  buttonSize: FormArrayButtonSize;

  /**
   * Nội dung của item
   */
  children: React.ReactNode;

  /**
   * Animation khi drag-and-drop
   */
  animation: FormArrayAnimation;

  /**
   * Layout của item
   */
  layout: FormArrayLayout;

  /**
   * Class bổ sung cho item
   */
  itemClassName?: string;

  /**
   * Style bổ sung cho item
   */
  itemStyle?: React.CSSProperties;

  /**
   * Class bổ sung cho drag handle
   */
  dragHandleClassName?: string;

  /**
   * Style bổ sung cho drag handle
   */
  dragHandleStyle?: React.CSSProperties;

  /**
   * Có hiển thị drag handle hay không
   */
  showDragHandle?: boolean;

  /**
   * Có sử dụng compact mode hay không
   */
  compact?: boolean;
}

// Component hiển thị một item trong mảng
const FormArrayItem: React.FC<FormArrayItemProps> = ({
  index,
  remove,
  move,
  canMove,
  canRemove,
  buttonSize,
  children,
  animation,
  layout,
  itemClassName = '',
  itemStyle = {},
  dragHandleClassName = '',
  dragHandleStyle = {},
  showDragHandle = true,
  compact = false,
}) => {
  // Drag and drop
  const [{ isDragging }, drag] = useDrag({
    type: 'FORM_ARRAY_ITEM',
    item: { index },
    canDrag: canMove,
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver }, drop] = useDrop({
    accept: 'FORM_ARRAY_ITEM',
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        move(index);
        item.index = index;
      }
    },
    collect: monitor => ({
      isOver: monitor.isOver(),
    }),
  });

  // Kích thước của các nút điều khiển
  const buttonSizeClass = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-10 w-10 text-base',
  }[buttonSize];

  // Animation classes
  const animationClasses = {
    none: '',
    fade: isDragging ? 'opacity-50' : 'opacity-100',
    slide: isDragging ? 'transform scale-95 opacity-50' : 'transform scale-100 opacity-100',
    scale: isDragging ? 'transform scale-110 opacity-75' : 'transform scale-100 opacity-100',
  };

  // Layout classes
  const layoutClasses = {
    list: 'flex flex-col',
    grid: 'grid grid-cols-1',
    card: 'bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4',
  };

  // Base item classes
  const baseItemClasses = [
    'relative',
    compact ? 'p-2' : 'p-4',
    'border rounded-lg',
    layout === 'list' ? 'mb-4' : '',
    layout === 'card' ? layoutClasses.card : 'border-gray-200 dark:border-gray-700',
    animationClasses[animation],
    isDragging ? 'border-dashed' : '',
    isOver ? 'border-primary' : '',
    itemClassName,
  ].filter(Boolean).join(' ');

  return (
    <div
      ref={canMove ? node => drag(drop(node)) : undefined}
      className={baseItemClasses}
      style={itemStyle}
    >
      {/* Drag handle */}
      {canMove && showDragHandle && (
        <div
          className={`absolute top-2 left-2 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 flex items-center justify-center cursor-move ${buttonSizeClass} ${dragHandleClassName}`}
          style={dragHandleStyle}
        >
          <Icon name={'move' as IconName} size="sm" />
        </div>
      )}

      {/* Nút xóa item */}
      {canRemove && (
        <button
          type="button"
          onClick={remove}
          className={`absolute top-2 right-2 rounded-full bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/20 dark:text-red-400 dark:hover:bg-red-800/30 flex items-center justify-center ${buttonSizeClass}`}
          aria-label="Remove item"
        >
          <Icon name={'trash' as IconName} size="sm" />
        </button>
      )}

      {/* Nội dung của item */}
      <div className={`${canMove || canRemove ? (compact ? 'mt-4' : 'mt-6') : ''}`}>
        {children}
      </div>
    </div>
  );
};

export interface FormArrayProps {
  /**
   * Tên của field mảng trong form
   */
  name: string;

  /**
   * Render function để hiển thị mỗi item trong mảng
   * @param index Index của item trong mảng
   * @param field Tên field của item
   * @param remove Hàm xóa item
   * @returns React element
   */
  renderItem: (
    index: number,
    field: Record<string, unknown>,
    remove: () => void
  ) => React.ReactNode;

  /**
   * Giá trị mặc định cho item mới
   * @default {}
   */
  defaultValue?: Record<string, unknown>;

  /**
   * Tiêu đề của mảng
   */
  title?: React.ReactNode;

  /**
   * Mô tả của mảng
   */
  description?: React.ReactNode;

  /**
   * Text cho nút thêm item
   * @default "Add Item"
   */
  addButtonText?: React.ReactNode;

  /**
   * Các nút điều khiển được hiển thị
   * @default "all"
   */
  controls?: FormArrayControls | FormArrayControls[];

  /**
   * Vị trí của các nút điều khiển
   * @default "bottom"
   */
  controlsPosition?: FormArrayControlsPosition;

  /**
   * Kích thước của các nút điều khiển
   * @default "md"
   */
  buttonSize?: FormArrayButtonSize;

  /**
   * Số lượng item tối thiểu
   * @default 0
   */
  minItems?: number;

  /**
   * Số lượng item tối đa
   * @default Infinity
   */
  maxItems?: number;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Class bổ sung cho tiêu đề
   */
  titleClassName?: string;

  /**
   * Class bổ sung cho mô tả
   */
  descriptionClassName?: string;

  /**
   * Class bổ sung cho container của các item
   */
  itemsContainerClassName?: string;

  /**
   * Class bổ sung cho container của các nút điều khiển
   */
  controlsContainerClassName?: string;

  /**
   * Có sử dụng drag-and-drop hay không
   * @default true
   */
  sortable?: boolean;

  /**
   * Có hiển thị drag handle hay không
   * @default true
   */
  sortableHandle?: boolean;

  /**
   * Animation khi drag-and-drop
   * @default 'fade'
   */
  sortableAnimation?: FormArrayAnimation;

  /**
   * Trục drag-and-drop
   * @default 'vertical'
   */
  sortableAxis?: FormArrayAxis;

  /**
   * Strategy drag-and-drop
   * @default 'vertical'
   */
  sortableStrategy?: FormArrayStrategy;

  /**
   * Callback khi sort kết thúc
   */
  onSortEnd?: (oldIndex: number, newIndex: number) => void;

  /**
   * Có sử dụng virtualization cho arrays lớn hay không
   * @default false
   */
  virtualized?: boolean;

  /**
   * Số lượng items hiển thị khi virtualized
   * @default 10
   */
  virtualizedItemCount?: number;

  /**
   * Layout của FormArray
   * @default 'list'
   */
  layout?: FormArrayLayout;

  /**
   * Class bổ sung cho mỗi item
   */
  itemClassName?: string;

  /**
   * Style bổ sung cho mỗi item
   */
  itemStyle?: React.CSSProperties;

  /**
   * Class bổ sung cho drag handle
   */
  dragHandleClassName?: string;

  /**
   * Style bổ sung cho drag handle
   */
  dragHandleStyle?: React.CSSProperties;

  /**
   * Có sử dụng compact mode hay không
   * @default false
   */
  compact?: boolean;

  /**
   * Có hỗ trợ touch devices hay không
   * @default false
   */
  touchSupport?: boolean;
}

/**
 * Component để quản lý mảng các field trong form
 *
 * @example
 * <FormArray
 *   name="addresses"
 *   title="Địa chỉ"
 *   description="Thêm các địa chỉ của bạn"
 *   renderItem={(index, field, remove) => (
 *     <div>
 *       <FormItem name={`addresses.${index}.street`} label="Đường">
 *         <Input />
 *       </FormItem>
 *       <FormItem name={`addresses.${index}.city`} label="Thành phố">
 *         <Input />
 *       </FormItem>
 *     </div>
 *   )}
 *   defaultValue={{ street: '', city: '' }}
 * />
 */
const FormArrayComponent: React.FC<FormArrayProps> = ({
  name,
  renderItem,
  defaultValue = {},
  title,
  description,
  addButtonText = 'Add Item',
  controls = 'all',
  controlsPosition = 'bottom',
  buttonSize = 'md',
  minItems = 0,
  maxItems = Infinity,
  className = '',
  titleClassName = '',
  descriptionClassName = '',
  itemsContainerClassName = '',
  controlsContainerClassName = '',
  sortable = true,
  sortableHandle = true,
  sortableAnimation = 'fade',

  onSortEnd,
  virtualized = false,
  virtualizedItemCount = 10,
  layout = 'list',
  itemClassName = '',
  itemStyle = {},
  dragHandleClassName = '',
  dragHandleStyle = {},
  compact = false,
  touchSupport = false,
}) => {
  // Lấy form context
  const { control } = useFormContext();

  // Sử dụng useFieldArray để quản lý mảng
  const { fields, append, remove, swap } = useFieldArray({
    control,
    name,
  });

  // Chọn backend phù hợp cho drag-and-drop
  const dndBackend = useMemo(() => {
    return touchSupport ? TouchBackend : HTML5Backend;
  }, [touchSupport]);

  // Layout classes
  const layoutClasses = {
    list: 'space-y-4',
    grid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4',
    card: 'grid grid-cols-1 md:grid-cols-2 gap-6',
  };

  // Virtualized items
  const visibleFields = useMemo(() => {
    if (!virtualized) return fields;
    return fields.slice(0, virtualizedItemCount);
  }, [fields, virtualized, virtualizedItemCount]);

  // Kiểm tra các nút điều khiển được hiển thị
  const showControls = Array.isArray(controls) ? controls : [controls];
  const showAddButton = showControls.includes('add') || showControls.includes('all');
  const showRemoveButton = showControls.includes('remove') || showControls.includes('all');
  const showMoveButton = showControls.includes('move') || showControls.includes('all');

  // Thêm item mới
  const handleAddItem = useCallback(() => {
    append(defaultValue);
  }, [append, defaultValue]);

  // Di chuyển item
  const handleMoveItem = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      swap(dragIndex, hoverIndex);
      onSortEnd?.(dragIndex, hoverIndex);
    },
    [swap, onSortEnd]
  );

  // Thêm số lượng item tối thiểu khi mount
  useEffect(() => {
    if (fields.length < minItems) {
      const itemsToAdd = minItems - fields.length;
      for (let i = 0; i < itemsToAdd; i++) {
        append(defaultValue);
      }
    }
  }, [minItems, fields.length, append, defaultValue]);

  // Render các nút điều khiển
  const renderControls = () => (
    <div className={`mt-4 flex justify-end ${controlsContainerClassName}`}>
      {showAddButton && fields.length < maxItems && (
        <Button
          type="button"
          variant="primary"
          onClick={handleAddItem}
          size={buttonSize === 'sm' ? 'sm' : buttonSize === 'lg' ? 'lg' : 'md'}
          className="flex items-center"
        >
          <Icon name="plus" size="sm" className="mr-1" />
          {addButtonText}
        </Button>
      )}
    </div>
  );

  return (
    <div className={`mb-6 ${className}`}>
      {/* Tiêu đề và mô tả */}
      {title && <h3 className={`text-lg font-medium mb-2 ${titleClassName}`}>{title}</h3>}
      {description && (
        <p className={`text-gray-600 dark:text-gray-400 mb-4 ${descriptionClassName}`}>
          {description}
        </p>
      )}

      {/* Nút điều khiển ở trên */}
      {(controlsPosition === 'top' || controlsPosition === 'both') && renderControls()}

      {/* Danh sách các item */}
      <DndProvider backend={dndBackend}>
        <div className={`${layoutClasses[layout]} ${itemsContainerClassName}`}>
          {visibleFields.map((field, index) => (
            <FormArrayItem
              key={field.id}
              index={index}
              total={fields.length}
              remove={() => fields.length > minItems && remove(index)}
              move={toIndex => handleMoveItem(index, toIndex)}
              canMove={sortable && showMoveButton && fields.length > 1}
              canRemove={showRemoveButton && fields.length > minItems}
              buttonSize={buttonSize}
              animation={sortableAnimation}
              layout={layout}
              itemClassName={itemClassName}
              itemStyle={itemStyle}
              dragHandleClassName={dragHandleClassName}
              dragHandleStyle={dragHandleStyle}
              showDragHandle={sortableHandle}
              compact={compact}
            >
              {renderItem(index, field, () => fields.length > minItems && remove(index))}
            </FormArrayItem>
          ))}

          {/* Hiển thị thông báo khi có virtualization */}
          {virtualized && fields.length > virtualizedItemCount && (
            <div className="text-center py-4 text-muted">
              Showing {virtualizedItemCount} of {fields.length} items
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  // Có thể implement load more functionality ở đây
                }}
                className="ml-2"
              >
                Load More
              </Button>
            </div>
          )}
        </div>
      </DndProvider>

      {/* Nút điều khiển ở dưới */}
      {(controlsPosition === 'bottom' || controlsPosition === 'both') && renderControls()}
    </div>
  );
};

export default FormArrayComponent;
