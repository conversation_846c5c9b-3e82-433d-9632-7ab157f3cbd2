import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, FormItem, ResponsiveGrid, Typography } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

const ThemeSettings: React.FC = () => {
  const { t } = useTranslation();
  const { themeMode, setThemeMode, resetToDefaultTheme } = useTheme();

  const handleThemeChange = (mode: 'light' | 'dark' | 'custom') => {
    setThemeMode(mode);
  };

  const themeOptions = [
    {
      value: 'light',
      label: t('common.light', 'Sáng'),
      description: t('settings.theme.lightDescription', '<PERSON>ia<PERSON> diện sáng, phù hợp ban ngày'),
    },
    {
      value: 'dark',
      label: t('common.dark', 'Tối'),
      description: t('settings.theme.darkDescription', '<PERSON><PERSON><PERSON> <PERSON><PERSON> tố<PERSON>, phù hợp ban đêm'),
    },
    {
      value: 'custom',
      label: t('settings.theme.custom', 'Tùy chỉnh'),
      description: t('settings.theme.customDescription', 'Theme tùy chỉnh theo sở thích'),
    },
  ];

  return (
    <Card title={t('settings.theme.title', 'Cài đặt Theme')} className="mb-6">
      <div className="space-y-4">
        <FormItem
          label={t('settings.theme.selectTheme', 'Chọn Theme')}
        >
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
            gap={{ xs: 4, md: 4 }}
          >
            {themeOptions.map((option) => (
              <div
                key={option.value}
                className={`
                  relative p-4 border-2 rounded-lg cursor-pointer transition-all duration-200
                  ${themeMode === option.value
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                  }
                `}
                onClick={() => handleThemeChange(option.value as 'light' | 'dark' | 'custom')}
              >
                <div className="flex items-center justify-between mb-2">
                  <Typography variant="subtitle2" weight="medium" color="default">
                    {option.label}
                  </Typography>
                  {themeMode === option.value && (
                    <div className="w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
                <Typography variant="body2" color="muted">
                  {option.description}
                </Typography>

                {/* Theme preview */}
                <div className="mt-3 flex space-x-1">
                  {option.value === 'light' && (
                    <>
                      <div className="w-4 h-4 bg-white border border-gray-300 rounded"></div>
                      <div className="w-4 h-4 bg-gray-100 border border-gray-300 rounded"></div>
                      <div className="w-4 h-4 bg-gray-200 border border-gray-300 rounded"></div>
                    </>
                  )}
                  {option.value === 'dark' && (
                    <>
                      <div className="w-4 h-4 bg-gray-900 border border-gray-700 rounded"></div>
                      <div className="w-4 h-4 bg-gray-800 border border-gray-700 rounded"></div>
                      <div className="w-4 h-4 bg-gray-700 border border-gray-700 rounded"></div>
                    </>
                  )}
                  {option.value === 'custom' && (
                    <>
                      <div className="w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded"></div>
                      <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-blue-500 rounded"></div>
                      <div className="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded"></div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </ResponsiveGrid>
        </FormItem>

        {/* Reset button */}
        <div className="flex justify-end pt-4 border-t border-border">
          <Button
            variant="outline"
            onClick={resetToDefaultTheme}
            className="text-sm"
          >
            {t('settings.theme.resetToDefault', 'Đặt lại mặc định')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ThemeSettings;
