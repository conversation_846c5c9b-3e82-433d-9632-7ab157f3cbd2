import { useState, useCallback } from 'react';
import { useBulkDelete, UseBulkDeleteOptions } from './useBulkDelete';
import { useSingleDelete, UseSingleDeleteOptions } from './useSingleDelete';

export interface UseTableActionsOptions<T> {
  /**
   * Cấu hình cho single delete
   */
  singleDelete?: UseSingleDeleteOptions<T>;

  /**
   * Cấu hình cho bulk delete
   */
  bulkDelete?: UseBulkDeleteOptions;

  /**
   * Callback khi có item được edit
   */
  onEdit?: (item: T) => void;

  /**
   * Callback khi có item được view
   */
  onView?: (item: T) => void;
}

export interface UseTableActionsResult<T> {
  /**
   * Selected rows state
   */
  selectedRows: string[];
  setSelectedRows: (rows: string[]) => void;

  /**
   * Single delete actions
   */
  singleDelete: {
    itemToDelete: T | null;
    showDeleteConfirm: boolean;
    handleShowDeleteConfirm: (item: T) => void;
    handleCancelDelete: () => void;
    handleConfirmDelete: () => Promise<void>;
    getConfirmMessage: () => string;
  };

  /**
   * Bulk delete actions
   */
  bulkDelete: {
    bulkDeleteCount: number;
    showBulkDeleteConfirm: boolean;
    handleShowBulkDeleteConfirm: (hasData: boolean) => void;
    handleCancelBulkDelete: () => void;
    handleConfirmBulkDelete: () => Promise<void>;
    getConfirmMessage: () => string;
  };

  /**
   * Edit action
   */
  handleEdit: (item: T) => void;

  /**
   * View action
   */
  handleView: (item: T) => void;

  /**
   * Reset all selections
   */
  resetSelection: () => void;
}

/**
 * Hook tổng hợp cho các actions của table
 */
export function useTableActions<T extends { id: string; [key: string]: unknown }>({
  singleDelete: singleDeleteOptions,
  bulkDelete: bulkDeleteOptions,
  onEdit,
  onView,
}: UseTableActionsOptions<T>): UseTableActionsResult<T> {
  const [selectedRows, setSelectedRows] = useState<string[]>([]);

  // Single delete hook
  const singleDelete = useSingleDelete<T>(singleDeleteOptions || {
    deleteMutation: async () => {},
  });

  // Bulk delete hook
  const bulkDelete = useBulkDelete(bulkDeleteOptions || {
    deleteMutation: async () => {},
  });

  // Reset selection
  const resetSelection = useCallback(() => {
    setSelectedRows([]);
  }, []);

  // Enhanced bulk delete handlers
  const enhancedBulkDelete = {
    ...bulkDelete,
    handleShowBulkDeleteConfirm: useCallback((hasData: boolean) => {
      bulkDelete.handleShowBulkDeleteConfirm(selectedRows, hasData);
    }, [bulkDelete, selectedRows]),

    handleConfirmBulkDelete: useCallback(async () => {
      await bulkDelete.handleConfirmBulkDelete(selectedRows, resetSelection);
    }, [bulkDelete, selectedRows, resetSelection]),
  };

  // Edit handler
  const handleEdit = useCallback((item: T) => {
    onEdit?.(item);
  }, [onEdit]);

  // View handler
  const handleView = useCallback((item: T) => {
    onView?.(item);
  }, [onView]);

  return {
    selectedRows,
    setSelectedRows,
    singleDelete,
    bulkDelete: enhancedBulkDelete,
    handleEdit,
    handleView,
    resetSelection,
  };
}
