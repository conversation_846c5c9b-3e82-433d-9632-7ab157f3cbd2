import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Icon, Button } from '@/shared/components/common';
import { DatabaseConnectionConfig } from '../types';

interface DatabaseConnectionCardProps {
  connection: DatabaseConnectionConfig;
  onEdit?: (connection: DatabaseConnectionConfig) => void;
  onView?: (connection: DatabaseConnectionConfig) => void;
  onDelete?: (connection: DatabaseConnectionConfig) => void;
  onTest?: (connection: DatabaseConnectionConfig) => void;
  onStatusChange?: (connection: DatabaseConnectionConfig, status: string) => void;
  className?: string;
}

/**
 * Database Connection Card Component
 * Displays a single database connection in card format
 */
const DatabaseConnectionCard: React.FC<DatabaseConnectionCardProps> = ({
  connection,
  onEdit,
  onView,
  onDelete,
  onTest,
  onStatusChange,
  className = '',
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // Get status color
  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-green-600 bg-green-100',
      inactive: 'text-gray-600 bg-gray-100',
      error: 'text-red-600 bg-red-100',
      testing: 'text-yellow-600 bg-yellow-100',
      pending: 'text-blue-600 bg-blue-100',
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  // Get database type icon
  const getDatabaseIcon = (type: string) => {
    const icons = {
      mysql: 'database',
      postgresql: 'database',
      mongodb: 'database',
      redis: 'database',
      sqlite: 'database',
    };
    return icons[type as keyof typeof icons] || 'database';
  };

  // Format connection info
  const getConnectionInfo = () => {
    const { credentials } = connection;
    
    if (credentials.host) {
      return `${credentials.host}${credentials.port ? `:${credentials.port}` : ''}`;
    }
    
    if (credentials.connectionString) {
      return credentials.connectionString.substring(0, 40) + '...';
    }
    
    if (credentials.filePath) {
      return credentials.filePath;
    }
    
    return t('admin:integration.database.noConnectionInfo');
  };

  return (
    <Card className={`p-6 hover:shadow-md transition-shadow ${className}`}>
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Icon name={getDatabaseIcon(connection.type)} size="md" className="text-blue-600" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <Typography variant="h6" className="font-semibold text-foreground truncate">
                {connection.displayName}
              </Typography>
              {connection.isDefault && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  {t('admin:integration.database.default')}
                </span>
              )}
            </div>
            <Typography variant="body2" className="text-muted-foreground truncate">
              {connection.name}
            </Typography>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(connection.status)}`}>
            {t(`admin:integration.database.status.${connection.status}`)}
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {connection.type.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="space-y-3 mb-4">
        <div>
          <Typography variant="body2" className="text-muted-foreground mb-1">
            {t('admin:integration.database.connectionInfo')}
          </Typography>
          <Typography variant="body2" className="text-foreground font-mono text-sm">
            {getConnectionInfo()}
          </Typography>
        </div>

        {connection.credentials.database && (
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              {t('admin:integration.database.databaseName')}
            </Typography>
            <Typography variant="body2" className="text-foreground">
              {connection.credentials.database}
            </Typography>
          </div>
        )}

        {connection.description && (
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              {t('admin:integration.database.description')}
            </Typography>
            <Typography variant="body2" className="text-foreground">
              {connection.description}
            </Typography>
          </div>
        )}

        {connection.lastTestedAt && (
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              {t('admin:integration.database.lastTested')}
            </Typography>
            <Typography variant="body2" className="text-foreground">
              {new Date(connection.lastTestedAt).toLocaleString()}
            </Typography>
          </div>
        )}
      </div>

      <div className="flex items-center justify-between pt-4 border-t border-border">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name="link" size="sm" />}
            onClick={() => onTest?.(connection)}
          >
            {t('admin:integration.database.actions.test')}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name={connection.status === 'active' ? 'pause' : 'play'} size="sm" />}
            onClick={() => onStatusChange?.(connection, connection.status === 'active' ? 'inactive' : 'active')}
          >
            {connection.status === 'active' 
              ? t('admin:integration.database.actions.deactivate')
              : t('admin:integration.database.actions.activate')
            }
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name="eye" size="sm" />}
            onClick={() => onView?.(connection)}
          >
            {t('common:view')}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name="edit" size="sm" />}
            onClick={() => onEdit?.(connection)}
          >
            {t('admin:integration.database.actions.edit')}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            leftIcon={<Icon name="trash" size="sm" />}
            onClick={() => onDelete?.(connection)}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            {t('admin:integration.database.actions.delete')}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default DatabaseConnectionCard;
