import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ExternalLink } from 'lucide-react';
import {
  Button,
  Input,
  Alert,
  Card,
  FormItem
} from '@/shared/components/common';
import { useConnectZaloAdsAccount } from '../../hooks/zalo-ads/useZaloAdsAccounts';
import type { CreateZaloAdsAccountDto } from '../../types/zalo-ads.types';

// Validation schema
const connectAccountSchema = z.object({
  accountName: z.string().min(1, 'Tên tài khoản là bắt buộc'),
  accessToken: z.string().min(1, 'Access token là bắt buộc'),
  refreshToken: z.string().min(1, 'Refresh token là bắt buộc'),
  businessName: z.string().optional(),
});

type ConnectAccountFormData = z.infer<typeof connectAccountSchema>;

interface ConnectZaloAdsAccountFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form kết nối tài khoản Zalo Ads
 */
export function ConnectZaloAdsAccountForm({ onSuccess, onCancel }: ConnectZaloAdsAccountFormProps) {
  const { t } = useTranslation('marketing');
  const [step, setStep] = useState<'instructions' | 'form'>('instructions');

  const connectAccount = useConnectZaloAdsAccount();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm<ConnectAccountFormData>({
    resolver: zodResolver(connectAccountSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: ConnectAccountFormData) => {
    try {
      const payload: CreateZaloAdsAccountDto = {
        accountName: data.accountName,
        accessToken: data.accessToken,
        refreshToken: data.refreshToken,
        businessName: data.businessName,
      };

      await connectAccount.mutateAsync(payload);
      reset();
      onSuccess?.();
    } catch (error) {
      console.error('Error connecting Zalo Ads account:', error);
    }
  };

  const handleGetTokens = () => {
    // Redirect to Zalo Ads OAuth
    const clientId = process.env.REACT_APP_ZALO_ADS_CLIENT_ID;
    const redirectUri = encodeURIComponent(`${window.location.origin}/marketing/zalo-ads/oauth/callback`);
    const scope = encodeURIComponent('ads_management,ads_read');

    const authUrl = `https://oauth.zaloapp.com/v4/permission?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}`;

    window.open(authUrl, '_blank', 'width=600,height=700');
  };

  if (step === 'instructions') {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold mb-2">
            {t('marketing:zaloAds.connect.instructions.title', 'Hướng dẫn kết nối Zalo Ads')}
          </h3>
          <p className="text-sm text-muted-foreground">
            {t('marketing:zaloAds.connect.instructions.description', 'Làm theo các bước sau để kết nối tài khoản Zalo Ads')}
          </p>
        </div>

        <div className="space-y-4">
          <Card className="p-4">
            <div className="flex items-start space-x-3">
              <div className="h-6 w-6 rounded-full bg-blue-600 text-white text-sm font-semibold flex items-center justify-center flex-shrink-0">
                1
              </div>
              <div>
                <h4 className="font-medium mb-1">
                  {t('marketing:zaloAds.connect.instructions.step1.title', 'Truy cập Zalo Ads Manager')}
                </h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {t('marketing:zaloAds.connect.instructions.step1.description', 'Đăng nhập vào tài khoản Zalo Ads của bạn')}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={() => window.open('https://ads.zalo.me', '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                  {t('marketing:zaloAds.connect.instructions.step1.action', 'Mở Zalo Ads')}
                </Button>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-start space-x-3">
              <div className="h-6 w-6 rounded-full bg-blue-600 text-white text-sm font-semibold flex items-center justify-center flex-shrink-0">
                2
              </div>
              <div>
                <h4 className="font-medium mb-1">
                  {t('marketing:zaloAds.connect.instructions.step2.title', 'Lấy Access Token')}
                </h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {t('marketing:zaloAds.connect.instructions.step2.description', 'Nhấp vào nút bên dưới để lấy access token')}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={handleGetTokens}
                >
                  <ExternalLink className="h-4 w-4" />
                  {t('marketing:zaloAds.connect.instructions.step2.action', 'Lấy Token')}
                </Button>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-start space-x-3">
              <div className="h-6 w-6 rounded-full bg-blue-600 text-white text-sm font-semibold flex items-center justify-center flex-shrink-0">
                3
              </div>
              <div>
                <h4 className="font-medium mb-1">
                  {t('marketing:zaloAds.connect.instructions.step3.title', 'Nhập thông tin')}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {t('marketing:zaloAds.connect.instructions.step3.description', 'Sao chép access token và refresh token vào form bên dưới')}
                </p>
              </div>
            </div>
          </Card>
        </div>

        <Alert
          type="info"
          message={t('marketing:zaloAds.connect.instructions.note.title', 'Lưu ý quan trọng')}
          description={t('marketing:zaloAds.connect.instructions.note.description', 'Access token có thời hạn sử dụng. Hệ thống sẽ tự động gia hạn token khi cần thiết.')}
        />

        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onCancel}>
            {t('common.cancel', 'Hủy')}
          </Button>
          <Button onClick={() => setStep('form')}>
            {t('marketing:zaloAds.connect.instructions.continue', 'Tiếp tục')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {t('marketing:zaloAds.connect.form.title', 'Thông tin tài khoản')}
        </h3>
        <p className="text-sm text-muted-foreground">
          {t('marketing:zaloAds.connect.form.description', 'Nhập thông tin để kết nối tài khoản Zalo Ads')}
        </p>
      </div>

      {connectAccount.isError && (
        <Alert
          type="error"
          message={t('marketing:zaloAds.connect.form.error.title', 'Lỗi kết nối')}
          description={connectAccount.error?.message || t('marketing:zaloAds.connect.form.error.message', 'Không thể kết nối tài khoản. Vui lòng kiểm tra lại thông tin.')}
        />
      )}

      <div className="space-y-4">
        <FormItem
          name="accountName"
          label={t('marketing:zaloAds.connect.form.accountName.label', 'Tên tài khoản')}
          required
        >
          <Input
            id="accountName"
            {...register('accountName')}
            placeholder={t('marketing:zaloAds.connect.form.accountName.placeholder', 'Nhập tên tài khoản')}
            error={errors.accountName?.message}
          />
        </FormItem>

        <FormItem
          name="businessName"
          label={t('marketing:zaloAds.connect.form.businessName.label', 'Tên doanh nghiệp')}
        >
          <Input
            id="businessName"
            {...register('businessName')}
            placeholder={t('marketing:zaloAds.connect.form.businessName.placeholder', 'Nhập tên doanh nghiệp (tùy chọn)')}
          />
        </FormItem>

        <FormItem
          name="accessToken"
          label={t('marketing:zaloAds.connect.form.accessToken.label', 'Access Token')}
          required
        >
          <Input
            id="accessToken"
            type="password"
            {...register('accessToken')}
            placeholder={t('marketing:zaloAds.connect.form.accessToken.placeholder', 'Nhập access token từ Zalo Ads')}
            error={errors.accessToken?.message}
          />
        </FormItem>

        <FormItem
          name="refreshToken"
          label={t('marketing:zaloAds.connect.form.refreshToken.label', 'Refresh Token')}
          required
        >
          <Input
            id="refreshToken"
            type="password"
            {...register('refreshToken')}
            placeholder={t('marketing:zaloAds.connect.form.refreshToken.placeholder', 'Nhập refresh token từ Zalo Ads')}
            error={errors.refreshToken?.message}
          />
        </FormItem>
      </div>

      {connectAccount.isSuccess && (
        <Alert
          type="success"
          message={t('marketing:zaloAds.connect.form.success.title', 'Kết nối thành công')}
          description={t('marketing:zaloAds.connect.form.success.message', 'Tài khoản Zalo Ads đã được kết nối thành công.')}
        />
      )}

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={() => setStep('instructions')}
          disabled={connectAccount.isPending}
        >
          {t('common.back', 'Quay lại')}
        </Button>
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={connectAccount.isPending}
        >
          {t('common.cancel', 'Hủy')}
        </Button>
        <Button
          type="submit"
          disabled={!isValid || connectAccount.isPending}
          isLoading={connectAccount.isPending}
        >
          {connectAccount.isPending
            ? t('marketing:zaloAds.connect.form.connecting', 'Đang kết nối...')
            : t('marketing:zaloAds.connect.form.connect', 'Kết nối tài khoản')
          }
        </Button>
      </div>
    </form>
  );
}

export default ConnectZaloAdsAccountForm;
