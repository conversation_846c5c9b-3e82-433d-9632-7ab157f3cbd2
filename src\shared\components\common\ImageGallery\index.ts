import ImageGallery from './ImageGallery';
import ImageGalleryGrid from './ImageGalleryGrid';
import ImageGalleryMasonry from './ImageGalleryMasonry';
import ImageGalleryCarousel from './ImageGalleryCarousel';
import ImageGalleryLightbox from './ImageGalleryLightbox';
import ImageGalleryThumbnails from './ImageGalleryThumbnails';
import { ImageGalleryProvider } from './ImageGalleryContext';
import { useImageGallery } from './useImageGallery';

// Export types
export type {
  GalleryImage,
  LightboxConfig,
  ThumbnailsConfig,
  ZoomConfig,
  GalleryLayout,
  ColumnsConfig,
  ImageGalleryProps,
} from './types';

// Export hooks
export { useImageGallery };

// Export components
export {
  ImageGalleryGrid,
  ImageGalleryMasonry,
  ImageGalleryCarousel,
  ImageGalleryLightbox,
  ImageGalleryThumbnails,
  ImageGalleryProvider,
};

// Export default component
export default ImageGallery;
