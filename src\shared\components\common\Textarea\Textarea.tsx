import React, { useRef, useState, useEffect } from 'react';
import { TextareaProps } from './types';
import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';

/**
 * Textarea component với các tính năng nâng cao như auto-resize, đếm ký tự, và clear button.
 *
 * @example
 * ```tsx
 * // Textarea cơ bản
 * <Textarea placeholder="Enter your message" />
 *
 * // Textarea với auto-resize
 * <Textarea autoSize placeholder="This will grow as you type" />
 *
 * // Textarea với đếm ký tự
 * <Textarea showCount maxLength={100} placeholder="Limited to 100 characters" />
 *
 * // Textarea với trạng thái lỗi
 * <Textarea status="error" placeholder="This has an error" />
 * ```
 */
const Textarea: React.FC<TextareaProps> = ({
  size = 'md',
  status = 'default',
  autoSize = false,
  showCount = false,
  maxLength,
  placeholder,
  disabled = false,
  readOnly = false,
  className = '',
  bordered = true,
  onChange,
  onFocus,
  onBlur,
  onKeyDown,
  onPressEnter,
  onResize,
  allowClear = false,
  countFormatter,
  value,
  defaultValue,
  fullWidth = false,
  ...rest
}) => {
  useTheme();

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [textareaValue, setTextareaValue] = useState<string>(
    (value !== undefined
      ? String(value)
      : defaultValue !== undefined
        ? String(defaultValue)
        : '') || ''
  );
  const [focused, setFocused] = useState(false);

  // Update internal value when controlled value changes
  useEffect(() => {
    if (value !== undefined) {
      setTextareaValue(String(value));
    }
  }, [value]);

  // Auto resize functionality
  useEffect(() => {
    if (!autoSize || !textareaRef.current) return;

    const textarea = textareaRef.current;

    // Save the current height
    const previousHeight = textarea.style.height;

    // Reset the height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Calculate new height
    let newHeight = textarea.scrollHeight;

    // Apply min/max constraints if provided
    if (typeof autoSize === 'object') {
      const lineHeight = parseInt(getComputedStyle(textarea).lineHeight) || 20;
      const paddingTop = parseInt(getComputedStyle(textarea).paddingTop) || 0;
      const paddingBottom = parseInt(getComputedStyle(textarea).paddingBottom) || 0;
      const borderTop = parseInt(getComputedStyle(textarea).borderTopWidth) || 0;
      const borderBottom = parseInt(getComputedStyle(textarea).borderBottomWidth) || 0;

      const minHeight = autoSize.minRows
        ? autoSize.minRows * lineHeight + paddingTop + paddingBottom + borderTop + borderBottom
        : 0;

      const maxHeight = autoSize.maxRows
        ? autoSize.maxRows * lineHeight + paddingTop + paddingBottom + borderTop + borderBottom
        : Infinity;

      newHeight = Math.max(minHeight, Math.min(newHeight, maxHeight));
    }

    // Set the new height
    textarea.style.height = `${newHeight}px`;

    // Call onResize callback if provided and height changed
    if (onResize && previousHeight !== textarea.style.height) {
      onResize({
        width: textarea.offsetWidth,
        height: textarea.offsetHeight,
      });
    }
  }, [autoSize, textareaValue, onResize]);

  // Handle change
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;

    // Apply maxLength constraint if needed
    if (maxLength !== undefined && newValue.length > maxLength) {
      e.target.value = newValue.slice(0, maxLength);
    }

    // Update internal state for uncontrolled component
    if (value === undefined) {
      setTextareaValue(e.target.value);
    }

    // Call original onChange
    onChange?.(e);
  };

  // Handle focus
  const handleFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setFocused(true);
    onFocus?.(e);
  };

  // Handle blur
  const handleBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    setFocused(false);
    onBlur?.(e);
  };

  // Handle key down
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Call onPressEnter when Enter key is pressed
    if (e.key === 'Enter' && onPressEnter) {
      onPressEnter(e);
    }

    // Call original onKeyDown
    onKeyDown?.(e);
  };

  // Handle clear
  const handleClear = () => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;

      // Create a synthetic event
      const event = new Event('change', {
        bubbles: true,
      }) as unknown as React.ChangeEvent<HTMLTextAreaElement>;
      Object.defineProperty(event, 'target', { value: textarea });

      // Clear the value
      textarea.value = '';

      // Update internal state for uncontrolled component
      if (value === undefined) {
        setTextareaValue('');
      }

      // Call original onChange
      onChange?.(event);

      // Focus the textarea
      textarea.focus();
    }
  };

  // Size classes
  const sizeClasses = {
    sm: 'py-1 px-2 text-sm',
    md: 'py-2 px-3 text-base',
    lg: 'py-3 px-4 text-lg',
  }[size];

  // Status classes - bỏ border trong light mode
  const statusClasses = {
    default: focused ? 'dark:border-primary' : 'border-0 dark:border dark:border-border',
    error: 'dark:border dark:border-error',
    success: 'dark:border dark:border-success',
    warning: 'dark:border dark:border-warning',
  }[status];

  // Disabled classes
  const disabledClasses = disabled
    ? 'bg-gray-100 text-gray-400 cursor-not-allowed dark:bg-gray-800 dark:text-gray-500'
    : 'bg-card-muted text-foreground';

  // Border classes
  const borderClasses = bordered ? statusClasses : 'border-transparent';

  // Format count display
  const formatCount = () => {
    if (countFormatter) {
      return countFormatter(textareaValue.length, maxLength);
    }

    return maxLength !== undefined
      ? `${textareaValue.length}/${maxLength}`
      : `${textareaValue.length}`;
  };

  return (
    <div className={`relative ${className} ${fullWidth ? 'w-full' : ''}`}>
      <textarea
        ref={textareaRef}
        className={`
          w-full rounded-md outline-none transition-colors duration-200
          ${sizeClasses}
          ${borderClasses}
          ${disabledClasses}
          ${allowClear ? 'pr-8' : ''}
          dark:focus:border-primary
          resize-${typeof autoSize === 'boolean' && autoSize ? 'none' : 'vertical'}
        `}
        placeholder={placeholder}
        disabled={disabled}
        readOnly={readOnly}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        value={value !== undefined ? value : undefined}
        defaultValue={value !== undefined ? undefined : defaultValue}
        maxLength={showCount ? undefined : maxLength}
        {...rest}
      />

      {/* Clear button */}
      {allowClear && textareaValue && !disabled && !readOnly && (
        <button
          type="button"
          className="absolute right-2 top-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none"
          onClick={handleClear}
          tabIndex={-1}
          aria-label="Clear"
        >
          <Icon name="close" size="sm" />
        </button>
      )}

      {/* Character count */}
      {showCount && (
        <div className="text-right text-xs text-gray-500 dark:text-gray-400 mt-1">
          {formatCount()}
        </div>
      )}
    </div>
  );
};

export default Textarea;
