/**
 * Component hiển thị trạng thái kết nối SSE
 */
import React from 'react';
import Badge from '@/shared/components/common/Badge';
import Icon from '@/shared/components/common/Icon';
import Tooltip from '@/shared/components/common/Tooltip';
import {
  SSEConnectionState,
  SSEConnectionInfo,
  SSEMetrics
} from '@/shared/types/sse.types';
import { cn } from '@/shared/utils';

/**
 * Props cho SSEStatus component
 */
export interface SSEStatusProps {
  /**
   * Thông tin kết nối SSE
   */
  connectionInfo: SSEConnectionInfo;

  /**
   * Metrics (optional)
   */
  metrics?: SSEMetrics | null;

  /**
   * C<PERSON> hiển thị chi tiết không
   */
  showDetails?: boolean;

  /**
   * Có hiển thị metrics không
   */
  showMetrics?: boolean;

  /**
   * Size của component
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant hiển thị
   */
  variant?: 'badge' | 'card' | 'inline';

  /**
   * Custom className
   */
  className?: string;

  /**
   * Callback khi click vào status
   */
  onClick?: () => void;
}

/**
 * Lấy thông tin hiển thị cho từng trạng thái
 */
const getStatusInfo = (state: SSEConnectionState) => {
  switch (state) {
    case SSEConnectionState.CONNECTED:
      return {
        label: 'Đã kết nối',
        color: 'success' as const,
        icon: 'wifi',
        bgColor: 'bg-green-100',
        textColor: 'text-green-800',
        iconColor: 'text-green-600',
      };
    case SSEConnectionState.CONNECTING:
      return {
        label: 'Đang kết nối',
        color: 'warning' as const,
        icon: 'loader',
        bgColor: 'bg-yellow-100',
        textColor: 'text-yellow-800',
        iconColor: 'text-yellow-600',
      };
    case SSEConnectionState.RECONNECTING:
      return {
        label: 'Đang kết nối lại',
        color: 'warning' as const,
        icon: 'refresh-cw',
        bgColor: 'bg-orange-100',
        textColor: 'text-orange-800',
        iconColor: 'text-orange-600',
      };
    case SSEConnectionState.ERROR:
      return {
        label: 'Lỗi kết nối',
        color: 'danger' as const,
        icon: 'wifi-off',
        bgColor: 'bg-red-100',
        textColor: 'text-red-800',
        iconColor: 'text-red-600',
      };
    case SSEConnectionState.DISCONNECTED:
    default:
      return {
        label: 'Ngắt kết nối',
        color: 'info' as const,
        icon: 'wifi-off',
        bgColor: 'bg-gray-100',
        textColor: 'text-gray-800',
        iconColor: 'text-gray-600',
      };
  }
};

/**
 * Format thời gian
 */
const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
};

/**
 * SSEStatus component
 */
export const SSEStatus: React.FC<SSEStatusProps> = ({
  connectionInfo,
  metrics,
  showDetails = false,
  showMetrics = false,
  size = 'md',
  variant = 'badge',
  className,
  onClick,
}) => {
  const statusInfo = getStatusInfo(connectionInfo.state);

  // Tính uptime
  const uptime = connectionInfo.connectedAt
    ? Date.now() - connectionInfo.connectedAt.getTime()
    : 0;

  // Tooltip content
  const tooltipContent = (
    <div className="space-y-2">
      <div className="font-medium">{statusInfo.label}</div>
      <div className="text-sm space-y-1">
        <div>URL: {connectionInfo.url}</div>
        {connectionInfo.connectedAt && (
          <div>Uptime: {formatDuration(uptime)}</div>
        )}
        <div>Events: {connectionInfo.eventsReceived}</div>
        {connectionInfo.reconnectAttempts > 0 && (
          <div>Reconnects: {connectionInfo.reconnectAttempts}</div>
        )}
        {connectionInfo.lastError && (
          <div className="text-red-400">
            Error: {connectionInfo.lastError.message}
          </div>
        )}
      </div>
    </div>
  );

  // Badge variant
  if (variant === 'badge') {
    const badgeContent = (
      <Badge
        variant={statusInfo.color}
        size={size}
        className={cn(onClick && 'cursor-pointer', className)}
      >
        <Icon
          name={statusInfo.icon}
          size={size === 'sm' ? 'xs' : size === 'lg' ? 'md' : 'sm'}
          className={cn(
            'mr-1',
            connectionInfo.state === SSEConnectionState.CONNECTING && 'animate-spin'
          )}
        />
        {statusInfo.label}
      </Badge>
    );

    return (
      <Tooltip content={tooltipContent}>
        {onClick ? (
          <div onClick={onClick} className="inline-block">
            {badgeContent}
          </div>
        ) : (
          badgeContent
        )}
      </Tooltip>
    );
  }

  // Inline variant
  if (variant === 'inline') {
    return (
      <div
        className={cn(
          'inline-flex items-center gap-2 text-sm',
          statusInfo.textColor,
          onClick && 'cursor-pointer',
          className
        )}
        onClick={onClick}
      >
        <Icon
          name={statusInfo.icon}
          size="xs"
          className={cn(
            statusInfo.iconColor,
            connectionInfo.state === SSEConnectionState.CONNECTING && 'animate-spin'
          )}
        />
        <span>{statusInfo.label}</span>
        {showDetails && connectionInfo.eventsReceived > 0 && (
          <span className="text-gray-500">
            ({connectionInfo.eventsReceived} events)
          </span>
        )}
      </div>
    );
  }

  // Card variant
  return (
    <div
      className={cn(
        'p-4 rounded-lg border',
        statusInfo.bgColor,
        onClick && 'cursor-pointer hover:opacity-80',
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-3">
        <Icon
          name={statusInfo.icon}
          size="lg"
          className={cn(
            statusInfo.iconColor,
            connectionInfo.state === SSEConnectionState.CONNECTING && 'animate-spin'
          )}
        />
        <div className="flex-1">
          <div className={cn('font-medium', statusInfo.textColor)}>
            {statusInfo.label}
          </div>
          {showDetails && (
            <div className="text-sm text-gray-600 mt-1">
              <div>URL: {connectionInfo.url}</div>
              {connectionInfo.connectedAt && (
                <div>Uptime: {formatDuration(uptime)}</div>
              )}
              <div>Events received: {connectionInfo.eventsReceived}</div>
            </div>
          )}
        </div>
      </div>

      {showMetrics && metrics && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="text-gray-500">Total Events</div>
              <div className="font-medium">{metrics.totalEvents}</div>
            </div>
            <div>
              <div className="text-gray-500">Reconnects</div>
              <div className="font-medium">{metrics.reconnectCount}</div>
            </div>
            <div>
              <div className="text-gray-500">Errors</div>
              <div className="font-medium">{metrics.errorCount}</div>
            </div>
            <div>
              <div className="text-gray-500">Data Received</div>
              <div className="font-medium">
                {(metrics.bytesReceived / 1024).toFixed(1)} KB
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SSEStatus;
