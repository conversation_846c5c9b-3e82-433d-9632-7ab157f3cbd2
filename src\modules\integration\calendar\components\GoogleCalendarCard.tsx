import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  Chip,
  ActionMenu,
  Modal,
} from '@/shared/components/common';
import {
  useDeleteGoogleCalendarConfiguration,
  useTestGoogleCalendarConfiguration,
  useSyncGoogleCalendarEvents,
  useGoogleCalendarSyncStatus,
} from '../hooks';
import GoogleCalendarForm from './GoogleCalendarForm';
import type { GoogleCalendarConfiguration } from '../types';

interface GoogleCalendarCardProps {
  /**
   * D<PERSON> liệu cấu hình Google Calendar
   */
  configuration: GoogleCalendarConfiguration;

  /**
   * Callback khi cập nhật thành công
   */
  onUpdate?: () => void;
}

/**
 * Card hiển thị thông tin cấu hình Google Calendar
 */
const GoogleCalendarCard: React.FC<GoogleCalendarCardProps> = ({
  configuration,
  onUpdate,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Mutations
  const deleteMutation = useDeleteGoogleCalendarConfiguration();
  const testMutation = useTestGoogleCalendarConfiguration();
  const syncMutation = useSyncGoogleCalendarEvents();

  // Queries
  const { data: syncStatus } = useGoogleCalendarSyncStatus(configuration.id);

  // Handle actions
  const handleEdit = () => setShowEditModal(true);
  const handleDelete = () => setShowDeleteModal(true);
  const handleTest = () => {
    testMutation.mutate({ id: configuration.id });
  };
  const handleSync = () => {
    syncMutation.mutate(configuration.id);
  };

  const handleConfirmDelete = async () => {
    await deleteMutation.mutateAsync(configuration.id);
    setShowDeleteModal(false);
    onUpdate?.();
  };

  const handleEditSuccess = () => {
    setShowEditModal(false);
    onUpdate?.();
  };

  // Format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const actionMenuItems = [
    {
      id: 'edit',
      label: t('common:edit'),
      icon: 'edit' as const,
      onClick: handleEdit,
    },
    {
      id: 'test',
      label: t('integration:calendar.actions.test'),
      icon: 'zap' as const,
      onClick: handleTest,
      disabled: testMutation.isPending,
    },
    {
      id: 'sync',
      label: t('integration:calendar.actions.sync'),
      icon: 'refresh-cw' as const,
      onClick: handleSync,
      disabled: syncMutation.isPending || !configuration.isActive || !configuration.syncEnabled,
    },
    {
      id: 'divider1',
      divider: true,
    } as const,
    {
      id: 'delete',
      label: t('common:delete'),
      icon: 'trash-2' as const,
      onClick: handleDelete,
    },
  ];

  return (
    <>
      <Card className="w-full hover:shadow-lg transition-shadow duration-200">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Icon name="calendar" size="lg" className="text-primary" />
              </div>
              <div>
                <Typography variant="h4" className="font-semibold">
                  {configuration.accountName}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {configuration.calendarId || t('integration:calendar.defaultCalendar')}
                </Typography>
              </div>
            </div>

            <ActionMenu items={actionMenuItems} />
          </div>

          {/* Status Tags */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Chip
              variant={configuration.isActive ? 'success' : 'default'}
              size="sm"
            >
              {configuration.isActive
                ? t('integration:calendar.status.active')
                : t('integration:calendar.status.inactive')}
            </Chip>

            <Chip
              variant={configuration.syncEnabled ? 'info' : 'default'}
              size="sm"
            >
              {configuration.syncEnabled
                ? t('integration:calendar.status.syncEnabled')
                : t('integration:calendar.status.syncDisabled')}
            </Chip>

            {syncStatus?.syncInProgress && (
              <Chip variant="warning" size="sm">
                <Icon name="loader" size="xs" className="animate-spin mr-1" />
                {t('integration:calendar.status.syncing')}
              </Chip>
            )}
          </div>

          {/* Configuration Details */}
          <div className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {t('integration:calendar.details.clientId')}
                </Typography>
                <Typography variant="body2" className="font-mono">
                  {configuration.clientId.substring(0, 20)}...
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {t('integration:calendar.details.lastSync')}
                </Typography>
                <Typography variant="body2">
                  {configuration.lastSyncAt
                    ? formatDate(configuration.lastSyncAt)
                    : t('integration:calendar.details.neverSynced')}
                </Typography>
              </div>
            </div>

            {/* Sync Status */}
            {syncStatus && (
              <div className="pt-3 border-t border-border">
                <div className="flex items-center justify-between">
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('integration:calendar.syncStatus.eventsCount')}
                  </Typography>
                  <Typography variant="body2" className="font-semibold">
                    {syncStatus.eventsCount}
                  </Typography>
                </div>

                {syncStatus.errorMessage && (
                  <div className="mt-2 p-2 bg-error/10 rounded border border-error/20">
                    <Typography variant="body2" className="text-error">
                      {syncStatus.errorMessage}
                    </Typography>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 mt-4 border-t border-border text-xs text-muted-foreground">
            <span>
              {t('integration:calendar.details.created')}: {formatDate(configuration.createdAt)}
            </span>
            <span>
              {t('integration:calendar.details.updated')}: {formatDate(configuration.updatedAt)}
            </span>
          </div>
        </div>
      </Card>

      {/* Edit Modal */}
      <Modal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        title={t('integration:calendar.modal.editTitle')}
        size="lg"
      >
        <GoogleCalendarForm
          initialData={configuration}
          onSuccess={handleEditSuccess}
          onCancel={() => setShowEditModal(false)}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('integration:calendar.modal.deleteTitle')}
        size="sm"
      >
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Icon name="alert-triangle" size="lg" className="text-warning" />
            <Typography variant="h4">
              {t('integration:calendar.modal.deleteConfirm')}
            </Typography>
          </div>

          <Typography variant="body1" className="mb-6 text-muted-foreground">
            {t('integration:calendar.modal.deleteDescription', {
              name: configuration.accountName,
            })}
          </Typography>

          <div className="flex gap-3">
            <Button
              variant="ghost"
              onClick={() => setShowDeleteModal(false)}
              className="flex-1"
            >
              {t('common:cancel')}
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmDelete}
              isLoading={deleteMutation.isPending}
              className="flex-1"
            >
              {t('common:delete')}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default GoogleCalendarCard;
