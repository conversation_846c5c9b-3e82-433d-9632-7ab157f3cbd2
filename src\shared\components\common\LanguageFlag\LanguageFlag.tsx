import React from 'react';

export type LanguageCode = 'vi' | 'en' | 'zh';

export interface LanguageFlagProps {
  /**
   * Mã ngôn ngữ
   */
  code: LanguageCode;

  /**
   * Trạng thái được chọn
   */
  isSelected?: boolean;

  /**
   * Hàm xử lý khi click vào cờ
   */
  onClick?: () => void;

  /**
   * <PERSON><PERSON>ch thước của cờ
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Hiển thị tên ngôn ngữ
   */
  showLabel?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị cờ quốc gia cho các ngôn ngữ
 */
const LanguageFlag: React.FC<LanguageFlagProps> = ({
  code,
  isSelected = false,
  onClick,
  size = 'md',
  showLabel = false,
  className = '',
}) => {
  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'w-5 h-3',
    md: 'w-6 h-4',
    lg: 'w-8 h-5',
  }[size];

  // Xác định class chung cho tất cả các cờ
  const flagClasses = `${sizeClasses} overflow-hidden ${isSelected ? 'shadow-sm' : 'opacity-80 hover:opacity-100'} ${isSelected ? 'bg-opacity-100' : 'bg-opacity-90'} ${className}`;

  // Xác định title cho mỗi cờ
  const titles = {
    vi: 'Tiếng Việt',
    en: 'English',
    zh: '中文',
  };

  // Render cờ dựa trên mã ngôn ngữ
  const renderFlag = () => {
    switch (code) {
      case 'vi':
        return (
          <div className={flagClasses} onClick={onClick} title={titles.vi}>
            <div className="bg-red-600 w-full h-full flex items-center justify-center">
              <div className="text-yellow-400">
                <svg className="w-2.5 h-2.5" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                </svg>
              </div>
            </div>
          </div>
        );
      case 'en':
        return (
          <div className={flagClasses} onClick={onClick} title={titles.en}>
            <div className="bg-blue-900 w-full h-full relative">
              <div className="absolute inset-0">
                {/* White background with red cross */}
                <div className="absolute inset-0 bg-white"></div>
                {/* Red vertical line */}
                <div className="absolute left-1/2 top-0 bottom-0 w-1 bg-red-600 transform -translate-x-1/2"></div>
                {/* Red horizontal line */}
                <div className="absolute top-1/2 left-0 right-0 h-1 bg-red-600 transform -translate-y-1/2"></div>
                {/* Diagonal blue lines for Scotland */}
                <div className="absolute inset-0">
                  <div className="absolute h-full w-full">
                    <div className="absolute top-0 left-0 right-0 h-1/3 bg-blue-900 clip-triangle-top-left"></div>
                    <div className="absolute bottom-0 left-0 right-0 h-1/3 bg-blue-900 clip-triangle-bottom-right"></div>
                    <div className="absolute top-0 right-0 bottom-0 w-1/3 bg-blue-900 clip-triangle-top-right"></div>
                    <div className="absolute bottom-0 left-0 top-0 w-1/3 bg-blue-900 clip-triangle-bottom-left"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'zh':
        return (
          <div className={flagClasses} onClick={onClick} title={titles.zh}>
            <div className="bg-red-600 w-full h-full relative">
              {/* Large star */}
              <div className="absolute top-0.5 left-0.5">
                <div className="text-yellow-400">
                  <svg className="w-2 h-2" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                </div>
              </div>
              {/* Small stars */}
              <div className="absolute top-0.5 right-1">
                <div className="text-yellow-400">
                  <svg className="w-0.5 h-0.5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                </div>
              </div>
              <div className="absolute bottom-0.5 right-1.5">
                <div className="text-yellow-400">
                  <svg className="w-0.5 h-0.5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                </div>
              </div>
              <div className="absolute bottom-1.5 right-0.5">
                <div className="text-yellow-400">
                  <svg className="w-0.5 h-0.5" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex items-center">
      {renderFlag()}
      {showLabel && (
        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">{titles[code]}</span>
      )}
    </div>
  );
};

export default LanguageFlag;
