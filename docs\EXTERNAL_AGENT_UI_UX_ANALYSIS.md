# 🎨 **External Agent Integration - UI/UX Analysis & Design**

## 📋 **Overview**

Phân tích chi tiết về giao diện và trải nghiệm người dùng cho tính năng External Agent Integration, dựa trên các pattern UI/UX hiện có trong hệ thống RedAI.

---

## 🎯 **1. UI/UX Principles & Patterns**

### **A. Design System Consistency**
- ✅ **Component Reuse**: Sử dụng các component có sẵn từ `@/shared/components/common`
- ✅ **Theme Integration**: Tuân thủ theme system với CSS Variables và dark mode
- ✅ **Responsive Design**: Tự động điều chỉnh theo kích thước màn hình và chat panel
- ✅ **Accessibility**: Hỗ trợ keyboard navigation, screen readers, và ARIA labels

### **B. Established Patterns**
- 🔄 **SlideInForm Pattern**: Form trượt từ phải sang trái cho create/edit
- 🔍 **MenuIconBar Pattern**: <PERSON>h công cụ với search, filter, add, column visibility
- 🏷️ **ActiveFilters Pattern**: Hiển thị và quản lý các filter đang active
- 📱 **ResponsiveGrid Pattern**: Grid tự động điều chỉnh số cột theo breakpoint
- 🃏 **Card Pattern**: Container chính cho hiển thị thông tin

---

## 🖼️ **2. Main Interface Layouts**

### **A. External Agents Management Page**

#### **Layout Structure**
```
┌─────────────────────────────────────────────────────────────┐
│ Header Section                                              │
│ ┌─────────────────────┐ ┌─────────────────────────────────┐ │
│ │ Title & Description │ │ MenuIconBar (Add, Search, etc.) │ │
│ └─────────────────────┘ └─────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Filters Section                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Search | Status Filter | Protocol Filter | Refresh     │ │
│ │ ActiveFilters: [Search: "test"] [Status: active] [×]    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Content Section                                             │
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│ │ Agent Card  │ │ Agent Card  │ │ Agent Card  │           │
│ │             │ │             │ │             │           │
│ └─────────────┘ └─────────────┘ └─────────────┘           │
│ ┌─────────────┐ ┌─────────────┐                           │
│ │ Agent Card  │ │ Agent Card  │                           │
│ │             │ │             │                           │
│ └─────────────┘ └─────────────┘                           │
└─────────────────────────────────────────────────────────────┘
```

#### **Responsive Behavior**
- **Desktop (≥1280px)**: 3 cột
- **Tablet (768px-1279px)**: 2 cột  
- **Mobile (<768px)**: 1 cột
- **With Chat Panel**: Giảm 1 cột ở mỗi breakpoint

### **B. Agent Card Design**

#### **Card Structure**
```
┌─────────────────────────────────────────────────────────┐
│ ┌─────────┐ Agent Name              [Status Badge]     │
│ │ Avatar  │ Protocol • Base URL                        │
│ │   Icon  │ Description text...                        │
│ └─────────┘                                            │
├─────────────────────────────────────────────────────────┤
│ Capabilities:                                          │
│ [Chat] [Streaming] [Function Calling] [Multimodal]    │
├─────────────────────────────────────────────────────────┤
│ Last Test: ✓ Success (125ms)                          │
│ Response time: 125ms                                   │
├─────────────────────────────────────────────────────────┤
│ [Test Connection] [Send Message] [Edit] [Delete]      │
└─────────────────────────────────────────────────────────┘
```

#### **Visual States**
- **Active**: Green border, success indicators
- **Inactive**: Gray border, muted colors
- **Testing**: Yellow border, loading spinner
- **Error**: Red border, error indicators
- **Default Agent**: Star badge, primary ring

---

## 🎨 **3. Component Specifications**

### **A. ExternalAgentCard Component**

#### **Visual Elements**
```typescript
interface CardVisualElements {
  // Header Section
  avatar: {
    type: 'icon' | 'image';
    fallback: string; // First letter of name
    size: 'md'; // 48px
  };
  
  // Status Indicators
  statusBadge: {
    variants: {
      active: 'success';
      inactive: 'secondary';
      testing: 'warning';
      error: 'destructive';
    };
    position: 'top-right';
  };
  
  // Protocol Icon
  protocolIcon: {
    mcp: 'layers';
    'google-agent': 'users';
    'openai-assistant': 'robot';
    'anthropic-computer-use': 'monitor';
    webhook: 'webhook';
    'custom-rest': 'settings';
  };
  
  // Capability Badges
  capabilityBadges: {
    variant: 'outline';
    size: 'sm';
    layout: 'flex-wrap';
  };
}
```

#### **Interactive Elements**
```typescript
interface CardInteractions {
  // Primary Actions
  testConnection: {
    icon: 'activity';
    variant: 'outline';
    loading: boolean;
    disabled: boolean;
  };
  
  sendMessage: {
    icon: 'message-circle';
    variant: 'outline';
    disabled: 'when status !== active';
  };
  
  // Secondary Actions
  edit: {
    icon: 'edit';
    variant: 'outline';
  };
  
  delete: {
    icon: 'trash-2';
    variant: 'outline';
    className: 'text-red-600 hover:text-red-700';
  };
}
```

### **B. ExternalAgentForm Component**

#### **Form Sections**
```typescript
interface FormSections {
  basicInfo: {
    title: 'Basic Information';
    fields: ['name', 'displayName', 'description'];
    layout: 'grid-cols-2';
  };
  
  protocolConfig: {
    title: 'Protocol Configuration';
    fields: ['protocolStandard', 'baseUrl', 'protocolVersion'];
    features: ['protocolDetection'];
  };
  
  authentication: {
    title: 'Authentication';
    fields: ['authType', 'credentials'];
    dynamic: true; // Fields change based on authType
  };
  
  capabilities: {
    title: 'Capabilities';
    fields: ['chat', 'streaming', 'functionCalling', 'multimodal', 'fileHandling'];
    layout: 'grid-cols-3';
    component: 'Checkbox';
  };
}
```

#### **Dynamic Field Rendering**
```typescript
interface DynamicFields {
  authType: {
    'api-key': ['apiKey'];
    'bearer': ['bearerToken'];
    'basic': ['username', 'password'];
    'oauth2': ['clientId', 'clientSecret', 'redirectUri'];
    'custom': ['customFields'];
  };
  
  protocolStandard: {
    mcp: ['mcpSpecificFields'];
    'google-agent': ['grpcEndpoint', 'serviceAccountKey'];
    'openai-assistant': ['assistantId'];
  };
}
```

---

## 🔧 **4. Advanced UI Features**

### **A. Protocol Detection**

#### **Visual Flow**
```
┌─────────────────────────────────────────────────────────┐
│ Base URL: [https://api.example.com        ] [Detect]   │
├─────────────────────────────────────────────────────────┤
│ Detecting protocols... 🔄                              │
├─────────────────────────────────────────────────────────┤
│ ✓ Detected: MCP, OpenAI Assistant                      │
│ ⚠️ Recommended: MCP (Latest standard)                   │
└─────────────────────────────────────────────────────────┘
```

#### **Implementation**
```typescript
const ProtocolDetection: React.FC = () => {
  const [detecting, setDetecting] = useState(false);
  const [detectedProtocols, setDetectedProtocols] = useState<string[]>([]);
  
  return (
    <div className="space-y-3">
      <div className="flex gap-2">
        <Input 
          placeholder="https://api.external-agent.com"
          className="flex-1"
        />
        <Button 
          variant="outline" 
          onClick={handleDetect}
          isLoading={detecting}
        >
          Detect Protocol
        </Button>
      </div>
      
      {detecting && (
        <div className="flex items-center gap-2 text-blue-600">
          <Icon name="loader" className="animate-spin" size="sm" />
          <Typography variant="body2">Detecting protocols...</Typography>
        </div>
      )}
      
      {detectedProtocols.length > 0 && (
        <div className="p-3 bg-green-50 rounded-md">
          <Typography variant="body2" className="font-medium text-green-800">
            ✓ Detected: {detectedProtocols.join(', ')}
          </Typography>
        </div>
      )}
    </div>
  );
};
```

### **B. Connection Testing**

#### **Test Result Display**
```typescript
interface TestResultDisplay {
  success: {
    icon: 'check-circle';
    color: 'text-green-500';
    background: 'bg-green-50';
    border: 'border-green-200';
  };
  
  failure: {
    icon: 'x-circle';
    color: 'text-red-500';
    background: 'bg-red-50';
    border: 'border-red-200';
  };
  
  testing: {
    icon: 'loader';
    animation: 'animate-spin';
    color: 'text-blue-500';
    background: 'bg-blue-50';
  };
}
```

#### **Test Progress Indicator**
```
┌─────────────────────────────────────────────────────────┐
│ Testing Connection... 🔄                               │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ ████████████████████████████████████████████████    │ │
│ │ 85% - Validating credentials...                     │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### **C. Capability Matrix**

#### **Visual Representation**
```
┌─────────────────────────────────────────────────────────┐
│ Capabilities Matrix                                     │
├─────────────────────────────────────────────────────────┤
│ ✓ Chat                    ✓ Streaming                   │
│ ✓ Function Calling        ✗ Multimodal                  │
│ ✓ File Handling          ✓ MCP Tools                    │
│ ✗ Google Collaboration   ✓ Real-time                    │
└─────────────────────────────────────────────────────────┘
```

---

## 📱 **5. Responsive Design Patterns**

### **A. Breakpoint Strategy**
```typescript
const ResponsiveBreakpoints = {
  mobile: {
    range: '< 640px';
    agentCards: 1;
    formLayout: 'single-column';
    menuIconBar: 'collapsed';
  };
  
  tablet: {
    range: '640px - 1023px';
    agentCards: 2;
    formLayout: 'mixed';
    menuIconBar: 'expanded';
  };
  
  desktop: {
    range: '≥ 1024px';
    agentCards: 3;
    formLayout: 'multi-column';
    menuIconBar: 'full';
  };
  
  withChatPanel: {
    reduction: -1; // Giảm 1 cột khi chat panel mở
  };
};
```

### **B. Mobile Optimizations**
```typescript
const MobileOptimizations = {
  agentCard: {
    stackedLayout: true;
    reducedPadding: true;
    collapsibleSections: ['capabilities', 'testResults'];
  };
  
  form: {
    singleColumn: true;
    largerTouchTargets: true;
    bottomSheetModal: true;
  };
  
  filters: {
    collapsibleFilters: true;
    bottomDrawer: true;
    swipeGestures: true;
  };
};
```

---

## 🎭 **6. Animation & Transitions**

### **A. Micro-interactions**
```typescript
const Animations = {
  cardHover: {
    transform: 'translateY(-2px)';
    boxShadow: 'elevated';
    duration: '200ms';
    easing: 'ease-out';
  };
  
  statusChange: {
    badgeTransition: 'color 300ms ease';
    iconRotation: '360deg';
    pulseEffect: 'scale(1.1)';
  };
  
  testConnection: {
    buttonLoading: 'spinner';
    progressBar: 'width transition';
    resultFadeIn: 'opacity 300ms';
  };
  
  formSlideIn: {
    direction: 'right-to-left';
    duration: '300ms';
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)';
  };
};
```

### **B. Loading States**
```typescript
const LoadingStates = {
  agentCard: {
    skeleton: true;
    shimmerEffect: true;
    preserveLayout: true;
  };
  
  testConnection: {
    buttonSpinner: true;
    progressIndicator: true;
    disableInteraction: true;
  };
  
  protocolDetection: {
    inlineSpinner: true;
    statusMessage: true;
    timeoutHandling: true;
  };
};
```

---

## 🔍 **7. Search & Filter UX**

### **A. Search Experience**
```typescript
const SearchUX = {
  instantSearch: {
    debounceDelay: 300;
    minCharacters: 2;
    searchFields: ['name', 'displayName', 'description', 'baseUrl'];
  };
  
  searchSuggestions: {
    recentSearches: true;
    popularFilters: true;
    autoComplete: true;
  };
  
  searchResults: {
    highlighting: true;
    resultCount: true;
    noResultsState: true;
  };
};
```

### **B. Filter System**
```typescript
const FilterSystem = {
  quickFilters: {
    status: ['active', 'inactive', 'testing', 'error'];
    protocol: ['mcp', 'google-agent', 'openai-assistant'];
    capabilities: ['chat', 'streaming', 'function-calling'];
  };
  
  advancedFilters: {
    dateRange: 'lastTested';
    responseTime: 'range';
    customTags: 'multiSelect';
  };
  
  filterPersistence: {
    localStorage: true;
    urlParams: true;
    userPreferences: true;
  };
};
```

---

## 🎨 **8. Visual Design Tokens**

### **A. Color Palette**
```typescript
const AgentColors = {
  status: {
    active: 'green-500';
    inactive: 'gray-400';
    testing: 'yellow-500';
    error: 'red-500';
  };
  
  protocol: {
    mcp: 'blue-500';
    googleAgent: 'purple-500';
    openaiAssistant: 'emerald-500';
    anthropic: 'orange-500';
    webhook: 'pink-500';
    custom: 'gray-500';
  };
  
  capability: {
    available: 'green-100';
    unavailable: 'gray-100';
    border: 'green-300';
  };
};
```

### **B. Typography Scale**
```typescript
const Typography = {
  agentName: 'text-lg font-semibold';
  protocolInfo: 'text-sm text-muted-foreground';
  description: 'text-sm text-gray-600';
  capability: 'text-xs font-medium';
  testResult: 'text-sm';
  formLabel: 'text-sm font-medium';
  helpText: 'text-xs text-gray-500';
};
```

### **C. Spacing System**
```typescript
const Spacing = {
  cardPadding: 'p-6';
  cardGap: 'gap-6';
  sectionSpacing: 'space-y-6';
  fieldSpacing: 'space-y-4';
  buttonSpacing: 'gap-2';
  badgeSpacing: 'gap-1';
};
```

---

## 📊 **9. Performance Considerations**

### **A. Optimization Strategies**
```typescript
const Performance = {
  virtualScrolling: {
    enabled: 'when > 100 agents';
    itemHeight: 'fixed';
    bufferSize: 10;
  };
  
  lazyLoading: {
    images: true;
    capabilities: 'on-demand';
    testResults: 'cached';
  };
  
  memoization: {
    agentCards: 'React.memo';
    filterResults: 'useMemo';
    searchResults: 'debounced';
  };
};
```

### **B. Caching Strategy**
```typescript
const Caching = {
  agentList: {
    staleTime: '5 minutes';
    cacheTime: '30 minutes';
    refetchOnWindowFocus: false;
  };
  
  testResults: {
    staleTime: '1 minute';
    cacheTime: '10 minutes';
    invalidateOnUpdate: true;
  };
  
  capabilities: {
    staleTime: '10 minutes';
    cacheTime: '1 hour';
    backgroundRefetch: true;
  };
};
```

---

## 🔒 **10. Security & Privacy UX**

### **A. Credential Handling**
```typescript
const SecurityUX = {
  credentialInput: {
    type: 'password';
    autoComplete: 'new-password';
    showToggle: true;
    strengthIndicator: true;
  };
  
  credentialDisplay: {
    masking: '••••••••';
    revealOnClick: true;
    copyToClipboard: true;
    autoHide: '5 seconds';
  };
  
  securityWarnings: {
    weakCredentials: 'warning';
    insecureConnection: 'error';
    expiredTokens: 'info';
  };
};
```

### **B. Data Privacy**
```typescript
const PrivacyFeatures = {
  dataRetention: {
    testResults: '30 days';
    errorLogs: '7 days';
    credentials: 'encrypted';
  };
  
  userConsent: {
    dataCollection: 'explicit';
    thirdPartySharing: 'opt-in';
    analytics: 'configurable';
  };
  
  auditTrail: {
    agentCreation: 'logged';
    credentialChanges: 'logged';
    testAttempts: 'logged';
  };
};
```

---

## 📈 **11. Analytics & Monitoring UX**

### **A. Usage Analytics**
```typescript
const AnalyticsUX = {
  agentMetrics: {
    responseTime: 'real-time';
    successRate: 'percentage';
    usageFrequency: 'chart';
  };
  
  protocolPopularity: {
    distribution: 'pie-chart';
    trends: 'line-chart';
    adoption: 'timeline';
  };
  
  errorTracking: {
    errorTypes: 'categorized';
    errorFrequency: 'heatmap';
    resolution: 'tracked';
  };
};
```

### **B. Health Dashboard**
```typescript
const HealthDashboard = {
  systemStatus: {
    overallHealth: 'traffic-light';
    agentStatus: 'grid-view';
    alertSummary: 'notification-center';
  };
  
  performanceMetrics: {
    averageResponseTime: 'gauge';
    throughput: 'line-chart';
    errorRate: 'percentage';
  };
  
  recommendations: {
    optimization: 'suggestions';
    maintenance: 'scheduled';
    upgrades: 'notifications';
  };
};
```

---

## 🎯 **Conclusion**

Thiết kế UI/UX cho External Agent Integration tập trung vào:

1. **Consistency**: Tuân thủ design system hiện có
2. **Usability**: Dễ sử dụng cho cả người dùng mới và chuyên gia
3. **Performance**: Tối ưu cho trải nghiệm mượt mà
4. **Accessibility**: Hỗ trợ đầy đủ các tiêu chuẩn accessibility
5. **Scalability**: Có thể mở rộng khi số lượng agent tăng

Thiết kế này đảm bảo trải nghiệm người dùng nhất quán và chuyên nghiệp trong việc quản lý các external agents.

---

## ⚠️ **12. Error Handling & User Feedback**

### **A. Error States & Messages**
```typescript
const ErrorHandling = {
  connectionErrors: {
    timeout: {
      icon: 'clock';
      title: 'Connection Timeout';
      message: 'Unable to connect to agent. Please check the URL and try again.';
      actions: ['retry', 'editConfig'];
    };

    unauthorized: {
      icon: 'lock';
      title: 'Authentication Failed';
      message: 'Invalid credentials. Please check your API key or token.';
      actions: ['updateCredentials', 'testAgain'];
    };

    networkError: {
      icon: 'wifi-off';
      title: 'Network Error';
      message: 'Unable to reach the agent endpoint. Check your internet connection.';
      actions: ['retry', 'checkNetwork'];
    };

    protocolMismatch: {
      icon: 'alert-triangle';
      title: 'Protocol Mismatch';
      message: 'The selected protocol is not supported by this agent.';
      actions: ['detectProtocol', 'changeProtocol'];
    };
  };

  validationErrors: {
    inline: true;
    realTime: true;
    fieldLevel: true;
    summary: true;
  };

  globalErrors: {
    toast: 'critical errors';
    modal: 'blocking errors';
    banner: 'system-wide issues';
  };
};
```

### **B. Error Recovery Flows**
```
Connection Failed Flow:
┌─────────────────────────────────────────────────────────┐
│ ❌ Connection Failed                                    │
│ Unable to connect to https://api.example.com           │
│                                                         │
│ Possible causes:                                        │
│ • Invalid URL or endpoint                               │
│ • Network connectivity issues                           │
│ • Authentication problems                               │
│                                                         │
│ [Try Again] [Edit Configuration] [Contact Support]     │
└─────────────────────────────────────────────────────────┘
```

### **C. Progressive Error Disclosure**
```typescript
const ErrorDisclosure = {
  level1: {
    display: 'Simple error message';
    details: 'hidden';
    actions: ['primary', 'secondary'];
  };

  level2: {
    display: 'Detailed error information';
    technicalDetails: 'collapsible';
    diagnostics: 'available';
  };

  level3: {
    display: 'Full error context';
    logs: 'downloadable';
    support: 'contact options';
  };
};
```

---

## ♿ **13. Accessibility Features**

### **A. Keyboard Navigation**
```typescript
const KeyboardNavigation = {
  agentCards: {
    tabIndex: 0;
    arrowKeys: 'grid navigation';
    enter: 'activate card';
    space: 'select card';
  };

  forms: {
    tabOrder: 'logical';
    skipLinks: 'available';
    fieldsets: 'grouped';
    labels: 'associated';
  };

  modals: {
    focusTrap: true;
    escapeKey: 'close';
    returnFocus: 'previous element';
  };

  shortcuts: {
    'Ctrl+N': 'new agent';
    'Ctrl+F': 'search';
    'Ctrl+R': 'refresh';
    '/': 'focus search';
  };
};
```

### **B. Screen Reader Support**
```typescript
const ScreenReaderSupport = {
  semanticHTML: {
    headings: 'hierarchical';
    landmarks: 'navigation, main, aside';
    lists: 'structured';
    tables: 'headers associated';
  };

  ariaLabels: {
    agentCard: 'Agent {name}, status {status}, protocol {protocol}';
    testButton: 'Test connection for {agentName}';
    statusBadge: 'Status: {status}';
    capabilityBadge: 'Capability: {capability}, {available}';
  };

  liveRegions: {
    testResults: 'polite';
    errorMessages: 'assertive';
    statusChanges: 'polite';
  };

  descriptions: {
    protocolInfo: 'aria-describedby';
    formHelp: 'aria-describedby';
    errorDetails: 'aria-describedby';
  };
};
```

### **C. Visual Accessibility**
```typescript
const VisualAccessibility = {
  colorContrast: {
    minimum: 'WCAG AA (4.5:1)';
    enhanced: 'WCAG AAA (7:1)';
    testing: 'automated checks';
  };

  colorIndependence: {
    statusIndicators: 'icons + color';
    errorStates: 'text + color';
    successStates: 'symbols + color';
  };

  textScaling: {
    support: 'up to 200%';
    layout: 'responsive';
    readability: 'maintained';
  };

  focusIndicators: {
    visible: 'always';
    highContrast: 'enhanced';
    customizable: 'user preference';
  };
};
```

---

## 🎯 **14. User Experience Flows**

### **A. First-Time User Flow**
```
New User Journey:
┌─────────────────────────────────────────────────────────┐
│ 1. Landing Page                                         │
│    "Welcome to External Agent Integration"              │
│    [Get Started] [Learn More]                          │
├─────────────────────────────────────────────────────────┤
│ 2. Quick Setup Wizard                                  │
│    "Let's connect your first agent"                    │
│    Protocol Selection → URL Input → Test Connection    │
├─────────────────────────────────────────────────────────┤
│ 3. Success State                                       │
│    "🎉 Agent connected successfully!"                   │
│    [View Agent] [Add Another] [Explore Features]       │
└─────────────────────────────────────────────────────────┘
```

### **B. Expert User Flow**
```
Power User Journey:
┌─────────────────────────────────────────────────────────┐
│ 1. Bulk Import                                          │
│    [Import from CSV] [API Configuration] [Templates]   │
├─────────────────────────────────────────────────────────┤
│ 2. Advanced Configuration                              │
│    Custom protocols, batch operations, automation      │
├─────────────────────────────────────────────────────────┤
│ 3. Monitoring Dashboard                                │
│    Real-time metrics, alerts, performance analytics    │
└─────────────────────────────────────────────────────────┘
```

### **C. Error Recovery Flow**
```
Error Recovery Journey:
┌─────────────────────────────────────────────────────────┐
│ 1. Error Detection                                     │
│    Automatic monitoring → Issue identified             │
├─────────────────────────────────────────────────────────┤
│ 2. User Notification                                   │
│    Toast notification → Detailed error view            │
├─────────────────────────────────────────────────────────┤
│ 3. Guided Resolution                                   │
│    Step-by-step troubleshooting → Auto-fix options     │
├─────────────────────────────────────────────────────────┤
│ 4. Verification                                        │
│    Test connection → Confirm resolution                │
└─────────────────────────────────────────────────────────┘
```

---

## 🔧 **15. Advanced Interaction Patterns**

### **A. Drag & Drop Interface**
```typescript
const DragDropPatterns = {
  agentReordering: {
    enabled: true;
    visualFeedback: 'ghost image';
    dropZones: 'highlighted';
    constraints: 'grid boundaries';
  };

  bulkOperations: {
    multiSelect: 'checkbox + drag';
    groupActions: 'context menu';
    batchEdit: 'slide-in panel';
  };

  configImport: {
    fileUpload: 'drag & drop zone';
    validation: 'real-time';
    preview: 'before import';
  };
};
```

### **B. Context Menus**
```typescript
const ContextMenus = {
  agentCard: {
    trigger: 'right-click';
    items: [
      'Test Connection',
      'Send Message',
      'Edit Configuration',
      'Duplicate Agent',
      'Export Config',
      'Delete Agent'
    ];
    keyboard: 'menu key';
  };

  bulkSelection: {
    trigger: 'multi-select';
    items: [
      'Test All',
      'Bulk Edit',
      'Export Selected',
      'Delete Selected'
    ];
  };
};
```

### **C. Quick Actions**
```typescript
const QuickActions = {
  keyboardShortcuts: {
    'Ctrl+T': 'test selected agent';
    'Ctrl+E': 'edit selected agent';
    'Ctrl+D': 'duplicate agent';
    'Delete': 'delete selected';
  };

  hoverActions: {
    agentCard: ['test', 'edit', 'delete'];
    delay: '500ms';
    position: 'top-right';
  };

  swipeGestures: {
    mobile: true;
    leftSwipe: 'delete';
    rightSwipe: 'edit';
    feedback: 'haptic';
  };
};
```

---

## 📊 **16. Data Visualization**

### **A. Agent Status Overview**
```typescript
const StatusVisualization = {
  dashboardCards: {
    totalAgents: 'number + trend';
    activeAgents: 'percentage + chart';
    averageResponseTime: 'gauge + history';
    errorRate: 'percentage + alerts';
  };

  protocolDistribution: {
    type: 'donut chart';
    colors: 'protocol-specific';
    interactive: 'click to filter';
  };

  performanceMetrics: {
    responseTime: 'line chart';
    successRate: 'area chart';
    usage: 'bar chart';
    timeRange: 'selectable';
  };
};
```

### **B. Real-time Monitoring**
```typescript
const RealTimeMonitoring = {
  liveUpdates: {
    interval: '5 seconds';
    indicators: 'pulse animation';
    autoRefresh: 'configurable';
  };

  alertSystem: {
    visual: 'color changes';
    audio: 'optional sounds';
    notifications: 'browser/system';
  };

  healthChecks: {
    automatic: 'background';
    manual: 'on-demand';
    scheduling: 'configurable';
  };
};
```

---

## 🎨 **17. Theming & Customization**

### **A. Theme Support**
```typescript
const ThemeSupport = {
  lightMode: {
    background: 'white';
    cards: 'gray-50';
    text: 'gray-900';
    borders: 'gray-200';
  };

  darkMode: {
    background: 'gray-900';
    cards: 'gray-800';
    text: 'gray-100';
    borders: 'gray-700';
  };

  customThemes: {
    userDefined: true;
    companyBranding: true;
    accessibility: 'high contrast';
  };
};
```

### **B. Layout Customization**
```typescript
const LayoutCustomization = {
  gridDensity: {
    compact: '2x2 cards';
    comfortable: '3x3 cards';
    spacious: '4x4 cards';
  };

  cardSize: {
    small: 'minimal info';
    medium: 'standard';
    large: 'detailed view';
  };

  columnLayout: {
    auto: 'responsive';
    fixed: 'user defined';
    adaptive: 'content based';
  };
};
```

---

## 🔍 **18. Search & Discovery**

### **A. Advanced Search**
```typescript
const AdvancedSearch = {
  searchOperators: {
    exact: '"exact phrase"';
    exclude: '-excluded';
    wildcard: 'agent*';
    field: 'protocol:mcp';
  };

  savedSearches: {
    personal: 'user specific';
    shared: 'team wide';
    smart: 'auto-updating';
  };

  searchHistory: {
    recent: 'last 10 searches';
    popular: 'most used';
    suggestions: 'auto-complete';
  };
};
```

### **B. Filtering & Sorting**
```typescript
const FilteringAndSorting = {
  multipleFilters: {
    combination: 'AND/OR logic';
    persistence: 'session/permanent';
    sharing: 'URL parameters';
  };

  sortingOptions: {
    name: 'alphabetical';
    status: 'priority order';
    lastTested: 'chronological';
    responseTime: 'performance';
    protocol: 'grouped';
  };

  viewModes: {
    grid: 'card layout';
    list: 'table layout';
    timeline: 'chronological';
  };
};
```

---

## 🎯 **19. Performance Optimization**

### **A. Rendering Optimization**
```typescript
const RenderingOptimization = {
  virtualization: {
    threshold: '> 50 agents';
    itemHeight: 'dynamic';
    overscan: '5 items';
  };

  lazyLoading: {
    images: 'intersection observer';
    components: 'code splitting';
    data: 'on-demand';
  };

  memoization: {
    components: 'React.memo';
    calculations: 'useMemo';
    callbacks: 'useCallback';
  };
};
```

### **B. Network Optimization**
```typescript
const NetworkOptimization = {
  caching: {
    agentList: 'stale-while-revalidate';
    testResults: 'cache-first';
    configurations: 'network-first';
  };

  prefetching: {
    nextPage: 'predictive';
    relatedData: 'background';
    images: 'link prefetch';
  };

  compression: {
    responses: 'gzip';
    images: 'webp/avif';
    bundles: 'brotli';
  };
};
```

---

## 🎯 **Conclusion & Best Practices**

### **Key Design Principles:**

1. **🎨 Consistency**: Tuân thủ design system hiện có của RedAI
2. **👥 Usability**: Thiết kế cho cả người dùng mới và chuyên gia
3. **⚡ Performance**: Tối ưu cho trải nghiệm mượt mà
4. **♿ Accessibility**: Hỗ trợ đầy đủ các tiêu chuẩn WCAG
5. **📱 Responsive**: Hoạt động tốt trên mọi thiết bị
6. **🔒 Security**: Bảo vệ thông tin nhạy cảm
7. **🔧 Extensibility**: Dễ dàng mở rộng và tùy chỉnh

### **Implementation Priorities:**

1. **Phase 1**: Core components (AgentCard, AgentForm, AgentList)
2. **Phase 2**: Advanced features (Protocol detection, Testing)
3. **Phase 3**: Analytics & Monitoring
4. **Phase 4**: Advanced interactions & Customization

### **Success Metrics:**

- **User Adoption**: % users successfully connecting first agent
- **Task Completion**: Time to complete common tasks
- **Error Recovery**: % users successfully resolving errors
- **Accessibility**: WCAG compliance score
- **Performance**: Page load times, interaction responsiveness

Thiết kế này tạo ra một trải nghiệm người dùng toàn diện, chuyên nghiệp và dễ sử dụng cho việc quản lý external agents trong hệ thống RedAI.
