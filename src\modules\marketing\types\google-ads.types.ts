/**
 * Types cho tích hợp Google Ads
 */

/**
 * Trạng thái tài khoản Google Ads
 */
export enum GoogleAdsAccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  ERROR = 'ERROR',
}

/**
 * Trạng thái chiến dịch Google Ads
 */
export enum GoogleAdsCampaignStatus {
  ENABLED = 'ENABLED',
  PAUSED = 'PAUSED',
  REMOVED = 'REMOVED',
  DRAFT = 'DRAFT',
}

/**
 * Loại chiến dịch Google Ads
 */
export enum GoogleAdsCampaignType {
  SEARCH = 'SEARCH',
  DISPLAY = 'DISPLAY',
  VIDEO = 'VIDEO',
  SHOPPING = 'SHOPPING',
  APP = 'APP',
  PERFORMANCE_MAX = 'PERFORMANCE_MAX',
}

/**
 * <PERSON><PERSON><PERSON> đ<PERSON><PERSON> s<PERSON>h từ khóa
 */
export enum GoogleAdsKeywordMatchType {
  EXACT = 'EXACT',
  PHRASE = 'PHRASE',
  BROAD = 'BROAD',
}

/**
 * DTO tài khoản Google Ads
 */
export interface GoogleAdsAccountDto {
  id: number;
  userId: number;
  customerId: string;
  name: string;
  email?: string;
  currency?: string;
  status: GoogleAdsAccountStatus;
  lastSyncAt?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO chiến dịch Google Ads
 */
export interface GoogleAdsCampaignDto {
  id: number;
  userId: number;
  accountId: number;
  campaignId: string;
  name: string;
  status: GoogleAdsCampaignStatus;
  type: GoogleAdsCampaignType;
  budget: number;
  budgetType?: 'DAILY' | 'MONTHLY';
  cost?: number;
  impressions?: number;
  clicks?: number;
  ctr?: number;
  conversions?: number;
  conversionRate?: number;
  startDate: string;
  endDate?: string;
  userCampaignId?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO nhóm quảng cáo Google Ads
 */
export interface GoogleAdsAdGroupDto {
  id: number;
  userId: number;
  campaignId: number;
  adGroupId: string;
  name: string;
  status: string;
  cpcBidMicros?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO từ khóa Google Ads
 */
export interface GoogleAdsKeywordDto {
  id: number;
  userId: number;
  adGroupId: number;
  keywordId: string;
  text: string;
  matchType: GoogleAdsKeywordMatchType;
  status: string;
  cpcBidMicros?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO hiệu suất Google Ads
 */
export interface GoogleAdsPerformanceDto {
  id: number;
  userId: number;
  campaignId: number;
  date: string;
  impressions: number;
  clicks: number;
  cost: number;
  ctr: number;
  averageCpc: number;
  conversions: number;
  conversionValue: number;
  createdAt: number;
}

/**
 * DTO tham số truy vấn tài khoản Google Ads
 */
export interface GoogleAdsAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: GoogleAdsAccountStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn chiến dịch Google Ads
 */
export interface GoogleAdsCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  accountId?: number;
  status?: GoogleAdsCampaignStatus;
  type?: GoogleAdsCampaignType;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tạo tài khoản Google Ads
 */
export interface CreateGoogleAdsAccountDto {
  customerId: string;
  name: string;
}

/**
 * DTO cập nhật tài khoản Google Ads
 */
export interface UpdateGoogleAdsAccountDto {
  name?: string;
  status?: GoogleAdsAccountStatus;
}

/**
 * DTO tạo chiến dịch Google Ads
 */
export interface CreateGoogleAdsCampaignDto {
  accountId: number;
  name: string;
  type: GoogleAdsCampaignType;
  budget: number;
  startDate: string;
  endDate?: string;
  userCampaignId?: number;
}

/**
 * DTO cập nhật chiến dịch Google Ads
 */
export interface UpdateGoogleAdsCampaignDto {
  name?: string;
  status?: GoogleAdsCampaignStatus;
  budget?: number;
  startDate?: string;
  endDate?: string;
}

/**
 * DTO phản hồi paging
 */
export interface PagingResponseDto<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}