import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON>ton,
  Card,
  Icon,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useProducts } from '@/modules/business/hooks/useProductQuery';
import { ProductQueryParams, HasPriceDto } from '@/modules/business/types/product.types';
import { Product } from '@/modules/ai-agents/types/response';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component ProductSlideInForm
 */
interface ProductSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các sản phẩm
   */
  onSelect: (selectedProducts: Product[]) => void;

  /**
   * Danh sách ID của các sản phẩm đã chọn
   */
  selectedProductIds?: string[];
}

/**
 * Component form trượt để chọn các sản phẩm
 */
const ProductSlideInForm: React.FC<ProductSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedProductIds = [],
}) => {
  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedProductIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: 'name',
    sortDirection: 'ASC',
  });

  // API hook để lấy danh sách products
  const { data: productResponse, isLoading } = useProducts(queryParams);

  // Lấy dữ liệu từ API response và chuyển đổi sang format Product
  const products: Product[] = (productResponse?.items || []).map(item => ({
    id: String(item.id),
    name: item.name,
    sku: `PROD-${item.id}`,
    price: (item.price as HasPriceDto)?.listPrice || 0,
    salePrice: (item.price as HasPriceDto)?.salePrice,
    imageUrl: item.images?.[0]?.url,
    category: item.tags?.[0] || '',
    stock: 0, // API không có thông tin stock
    status: 'active' as const, // Mặc định là active
    createdAt: new Date(item.createdAt * 1000).toISOString().split('T')[0],
  }));

  const totalItems = productResponse?.meta?.totalItems || 0;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Product>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'product',
      title: 'Sản phẩm',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.imageUrl ? (
              <img
                src={record.imageUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon name="box" size="md" className="text-gray-500" />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              SKU: {record.sku}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'price',
      title: 'Giá',
      dataIndex: 'price',
      width: '15%',
      render: (_, record) => (
        <div>
          {record.salePrice ? (
            <>
              <Typography variant="body2" className="text-red-500 font-medium">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.salePrice)}
              </Typography>
              <Typography variant="caption" className="text-gray-500 line-through">
                {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
              </Typography>
            </>
          ) : (
            <Typography variant="body2" className="font-medium">
              {new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(record.price)}
            </Typography>
          )}
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '15%',
    },
    {
      key: 'stock',
      title: 'Tồn kho',
      dataIndex: 'stock',
      width: '15%',
      render: (_, record) => (
        <Typography
          variant="body2"
          className={
            record.stock > 10
              ? 'text-green-500'
              : record.stock > 0
              ? 'text-yellow-500'
              : 'text-red-500'
          }
        >
          {record.stock}
        </Typography>
      ),
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      width: '15%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.status === 'active' ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đang bán
            </span>
          ) : record.status === 'inactive' ? (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="x-circle" size="sm" className="mr-1" />
              Ngừng bán
            </span>
          ) : (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              Hết hàng
            </span>
          )}
        </div>
      ),
    },
  ];



  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedProductIds.length ||
      selectedIds.some(id => !selectedProductIds.includes(id)) ||
      selectedProductIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedProductIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: column,
      sortDirection: direction,
    }));
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Lấy thông tin đầy đủ của các sản phẩm đã chọn
      const selectedProducts = products.filter(product =>
        selectedIds.includes(product.id)
      );

      onSelect(selectedProducts);
      onClose();
    } catch (error) {
      console.error('Error saving selected products:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setQueryParams(prev => ({ ...prev, search: '' }));
    onClose();
  }, [hasChanges, onClose]);



  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-price',
      label: 'Giá',
      onClick: () => handleSortChange('price', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-stock',
      label: 'Tồn kho',
      onClick: () => handleSortChange('stock', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', queryParams.sortDirection === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider-1',
      divider: true,
    },
    {
      id: 'filter-category',
      label: 'Danh mục',
      icon: 'folder',
      onClick: () => { },
    },
    {
      id: 'category-all',
      label: 'Tất cả',
      onClick: () => setQueryParams(prev => ({ ...prev, search: '' })),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn sản phẩm</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<Product>
            columns={columns}
            data={products}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== (queryParams.limit || 10)) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default ProductSlideInForm;
