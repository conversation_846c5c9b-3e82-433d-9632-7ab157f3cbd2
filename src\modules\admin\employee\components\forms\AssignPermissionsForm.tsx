/**
 * Form gán quyền cho vai trò
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Card, Checkbox } from '@/shared/components/common';
import { RoleDto } from '../../types/role.types';
import { PermissionDto } from '../../types/employee.types';
import { useAllPermissions } from '../../hooks/usePermissionQuery';
import { useRolePermissions, useAssignPermissions } from '../../hooks/useRoleQuery';

interface AssignPermissionsFormProps {
  role: RoleDto;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Component form gán quyền cho vai trò
 */
const AssignPermissionsForm: React.FC<AssignPermissionsFormProps> = ({
  role,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['employee', 'common']);

  // State
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);

  // Queries
  const allPermissionsQuery = useAllPermissions();
  const rolePermissionsQuery = useRolePermissions(role.id);
  const assignPermissionsMutation = useAssignPermissions(role.id);

  // Effect để set permissions hiện tại của role
  useEffect(() => {
    if (rolePermissionsQuery.data) {
      const currentPermissionIds = rolePermissionsQuery.data.map(p => p.id);
      setSelectedPermissions(currentPermissionIds);
    }
  }, [rolePermissionsQuery.data]);

  // Xử lý thay đổi checkbox
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    if (checked) {
      setSelectedPermissions(prev => [...prev, permissionId]);
    } else {
      setSelectedPermissions(prev => prev.filter(id => id !== permissionId));
    }
  };

  // Xử lý submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await assignPermissionsMutation.mutateAsync({
        permissionIds: selectedPermissions,
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error assigning permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Nhóm permissions theo module
  const groupedPermissions = React.useMemo(() => {
    if (!allPermissionsQuery.data) return {};

    return allPermissionsQuery.data.reduce((groups, permission) => {
      const module = permission.module;
      if (!groups[module]) {
        groups[module] = [];
      }
      groups[module].push(permission);
      return groups;
    }, {} as Record<string, PermissionDto[]>);
  }, [allPermissionsQuery.data]);

  if (allPermissionsQuery.isLoading || rolePermissionsQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">{t('common:loading')}</div>
      </div>
    );
  }

  return (
    <Card className="m-6">
      <div className="p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('employee:role.assignPermissions')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('employee:role.assignPermissionsDescription', { roleName: role.name })}
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-6 max-h-96 overflow-y-auto">
            {Object.entries(groupedPermissions).map(([module, permissions]) => (
              <Card key={module} className="p-4 border border-gray-200 dark:border-gray-700">
                <h4 className="font-medium text-gray-900 dark:text-white mb-3 capitalize">
                  {module}
                </h4>
                <div className="space-y-2">
                  {permissions.map((permission) => (
                    <div key={permission.id} className="flex items-start space-x-3">
                      <Checkbox
                        id={`permission-${permission.id}`}
                        checked={selectedPermissions.includes(permission.id)}
                        onChange={(checked) => handlePermissionChange(permission.id, checked)}
                        variant="filled"
                      />
                      <div className="flex-1">
                        <label
                          htmlFor={`permission-${permission.id}`}
                          className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
                        >
                          {permission.action}
                        </label>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          {permission.description}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            ))}
          </div>

          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="secondary"
              onClick={onCancel}
              disabled={loading}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={loading}
              disabled={assignPermissionsMutation.isPending}
            >
              {t('employee:role.assignPermissions')}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default AssignPermissionsForm;
