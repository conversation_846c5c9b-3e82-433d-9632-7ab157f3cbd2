import { ImgHTMLAttributes, useState } from 'react';

type AvatarSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';

interface AvatarProps extends ImgHTMLAttributes<HTMLImageElement> {
  src?: string;
  alt: string;
  size?: AvatarSize;
  status?: 'online' | 'offline' | 'away' | 'busy';
}

const Avatar = ({ src, alt, size = 'md', status, className = '', ...rest }: AvatarProps) => {
  const [hasError, setHasError] = useState(false);

  // Đường dẫn đến avatar mặc định
  const defaultAvatarSrc = '/assets/images/default-avatar.svg';

  // Size classes
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16',
    '2xl': 'w-20 h-20',
    '3xl': 'w-24 h-24',
  };

  // Status color classes
  const statusColorClasses = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
  };

  // Status size classes
  const statusSizeClasses = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
    xl: 'w-4 h-4',
    '2xl': 'w-5 h-5',
    '3xl': 'w-6 h-6',
  };

  // Combine all classes
  const avatarClasses = ['rounded-full object-cover', sizeClasses[size], className].join(' ');

  // Xử lý khi hình ảnh lỗi
  const handleError = () => {
    setHasError(true);
  };

  // Xác định nguồn hình ảnh
  const imageSrc = !src || hasError ? defaultAvatarSrc : src;

  return (
    <div className="relative inline-block">
      <img src={imageSrc} alt={alt} className={avatarClasses} onError={handleError} {...rest} />

      {status && (
        <span
          className={`absolute bottom-0 right-0 block rounded-full ring-2 ring-white ${statusColorClasses[status]} ${statusSizeClasses[size]}`}
        />
      )}
    </div>
  );
};

export default Avatar;
