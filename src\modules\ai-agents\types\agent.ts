import { ConvertData } from './convert';
import { IntegrationsData } from './integration';
import { ModelConfigData } from './model';
import { ProfileData } from './profile';
import { ResponseData } from './response';
import { StrategyData } from './strategy';

/**
 * Interface cho item trong multi-agent
 */
export interface MultiAgentItem {
    id: string;
    agentTypeId: number;
    name: string;
    description: string;
    avatar?: string;
    customDescription?: string;
}

/**
 * Interface cho dữ liệu cấu hình multi-agent
 */
export interface MultiAgentConfigData {
    agents: MultiAgentItem[];
}

/**
 * Interface cho dữ liệu cấu hình agent
 */
export interface AgentConfigData {
    id?: string;
    name: string;
    avatar?: string;
    profile: ProfileData;
    modelConfig: ModelConfigData;
    integrations: IntegrationsData;
    strategy: StrategyData;
    response: ResponseData;
    convert: ConvertData;
    multiAgent: MultiAgentConfigData;
}
