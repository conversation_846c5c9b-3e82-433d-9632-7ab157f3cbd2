import { createRoot } from 'react-dom/client';
import React from 'react';
import LiveChatWidget from './LiveChatWidget';
import './LiveChatWidget.css';

// Import theme CSS
import '@/shared/styles/theme.css';

/**
 * Khởi tạo RedAI Live Chat Widget
 *
 * @param config - Cấu hình cho widget
 */
interface RedAIWidgetConfig {
  apiKey?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  primaryColor?: string;
  title?: string;
  greeting?: string;
  darkMode?: boolean;
}

class RedAIChat {
  private config: RedAIWidgetConfig;
  private containerElement: HTMLElement | null = null;
  private styleElement: HTMLStyleElement | null = null;

  constructor(config: RedAIWidgetConfig = {}) {
    this.config = config;
  }

  /**
   * Khởi tạo và hiển thị widget chat
   */
  public init(): void {
    // Tạo container element nếu chưa tồn tại
    if (!this.containerElement) {
      this.containerElement = document.createElement('div');
      this.containerElement.id = 'redai-chat-widget-container';

      // Thêm class dark nếu darkMode = true
      if (this.config.darkMode) {
        this.containerElement.classList.add('dark');
      }

      document.body.appendChild(this.containerElement);
    }

    // Tạo style element để inject CSS variables
    if (!this.styleElement) {
      this.styleElement = document.createElement('style');
      this.styleElement.id = 'redai-chat-widget-styles';
      document.head.appendChild(this.styleElement);

      // Inject CSS variables
      this.updateThemeVariables();
    }

    // Render widget vào container
    const root = createRoot(this.containerElement);
    root.render(React.createElement(LiveChatWidget, this.config));
  }

  /**
   * Cập nhật theme variables
   */
  private updateThemeVariables(): void {
    if (!this.styleElement) return;

    const { primaryColor } = this.config;

    const css = `
      #redai-chat-widget-container {
        --color-primary: ${primaryColor || '#ff3333'};
        --color-primary-foreground: #ffffff;
      }
    `;

    this.styleElement.textContent = css;
  }

  /**
   * Cập nhật cấu hình widget
   */
  public updateConfig(newConfig: Partial<RedAIWidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Cập nhật darkMode
    if (this.containerElement) {
      if (this.config.darkMode) {
        this.containerElement.classList.add('dark');
      } else {
        this.containerElement.classList.remove('dark');
      }
    }

    // Cập nhật CSS variables
    this.updateThemeVariables();

    // Re-render widget
    if (this.containerElement) {
      const root = createRoot(this.containerElement);
      root.render(React.createElement(LiveChatWidget, this.config));
    }
  }

  /**
   * Hủy widget chat
   */
  public destroy(): void {
    if (this.containerElement) {
      document.body.removeChild(this.containerElement);
      this.containerElement = null;
    }

    if (this.styleElement) {
      document.head.removeChild(this.styleElement);
      this.styleElement = null;
    }
  }
}

// Tạo instance mặc định và gán vào window
declare global {
  interface Window {
    RedAIChat: typeof RedAIChat;
    redaiChat: RedAIChat;
  }
}

// Gán constructor vào window để có thể tạo instance mới
window.RedAIChat = RedAIChat;

// Tạo instance mặc định
window.redaiChat = new RedAIChat();

// Tự động khởi tạo nếu có data-auto-init attribute
document.addEventListener('DOMContentLoaded', () => {
  const scriptTag = document.querySelector('script[data-redai-chat]');
  if (scriptTag && scriptTag.getAttribute('data-auto-init') === 'true') {
    // Lấy cấu hình từ data attributes
    const config: RedAIWidgetConfig = {};

    const apiKey = scriptTag.getAttribute('data-api-key');
    if (apiKey) config.apiKey = apiKey;

    const position = scriptTag.getAttribute('data-position') as RedAIWidgetConfig['position'];
    if (position) config.position = position;

    const primaryColor = scriptTag.getAttribute('data-primary-color');
    if (primaryColor) config.primaryColor = primaryColor;

    const title = scriptTag.getAttribute('data-title');
    if (title) config.title = title;

    const greeting = scriptTag.getAttribute('data-greeting');
    if (greeting) config.greeting = greeting;

    const darkMode = scriptTag.getAttribute('data-dark-mode');
    if (darkMode) config.darkMode = darkMode === 'true';

    // Khởi tạo với cấu hình từ data attributes
    window.redaiChat = new RedAIChat(config);
    window.redaiChat.init();
  }
});

export default RedAIChat;
