import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useMenu } from './MenuContext';
import Icon, { IconName } from '@/shared/components/common/Icon';
import { ScrollArea, Typography } from '@/shared/components/common';
import { useKeyboardNavigation } from '@/shared/hooks/common';

export interface SubMenuProps {
  /**
   * Key duy nhất của submenu
   */
  itemKey: string;

  /**
   * Label hiển thị
   */
  label: React.ReactNode;

  /**
   * Icon hiển thị (tùy chọn)
   */
  icon?: IconName | React.ReactNode;

  /**
   * Children của submenu
   */
  children: React.ReactNode;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Popup mode (chỉ hiển thị popup khi hover)
   */
  popup?: boolean;

  /**
   * Chiều cao tối đa của submenu content
   */
  maxHeight?: number;
}

const SubMenu: React.FC<SubMenuProps> = ({
  itemKey,
  label,
  icon,
  children,
  disabled = false,
  className = '',
  popup = false,
  maxHeight = 300,
}) => {
  const { mode, theme, isExpanded, toggleExpand, collapsed } = useMenu();
  const [isHovered, setIsHovered] = useState(false);
  const submenuRef = useRef<HTMLDivElement>(null);
  const isKeyboardUser = useKeyboardNavigation();

  // Kiểm tra xem submenu có đang được mở rộng không
  const expanded = isExpanded(itemKey);

  // Xử lý khi click vào submenu title
  const handleTitleClick = (e: React.MouseEvent) => {
    if (disabled) {
      e.preventDefault();
      return;
    }

    // Nếu là popup mode và đang ở horizontal mode, không toggle
    if (popup && mode === 'horizontal') {
      return;
    }

    toggleExpand(itemKey);
  };

  // Xử lý khi hover
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // Xử lý click bên ngoài để đóng submenu
  useEffect(() => {
    if (!popup) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (submenuRef.current && !submenuRef.current.contains(event.target as Node)) {
        setIsHovered(false);
      }
    };

    if (isHovered) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isHovered, popup]);

  // Xác định các class dựa trên props và state
  const getTitleClasses = () => {
    const baseClasses =
      'flex items-center justify-between transition-all duration-200 cursor-pointer select-none';
    const modeClasses = {
      horizontal: 'px-4 py-2',
      vertical: 'px-4 py-2',
      inline: 'px-4 py-2',
    }[mode];

    const themeClasses = {
      default: `text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-lighter ${
        expanded ? 'bg-gray-100 dark:bg-dark-lighter' : ''
      }`,
      dark: `text-gray-200 hover:bg-dark-lighter ${expanded ? 'bg-dark-lighter' : ''}`,
      light: `text-gray-700 hover:bg-gray-100 ${expanded ? 'bg-gray-100' : ''}`,
    }[theme];

    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : '';

    return `${baseClasses} ${modeClasses} ${themeClasses} ${disabledClasses} ${className}`;
  };

  // Xác định class cho submenu content
  const getContentClasses = () => {
    if (mode === 'horizontal') {
      return 'absolute left-0 top-full mt-1 bg-white dark:bg-dark-light shadow-lg rounded-md overflow-hidden z-10 min-w-[200px]';
    }

    if (mode === 'vertical' && collapsed) {
      return 'absolute left-full top-0 ml-1 bg-white dark:bg-dark-light shadow-lg rounded-md overflow-hidden z-10 min-w-[200px]';
    }

    return 'overflow-hidden transition-all duration-300';
  };

  // Render icon
  const renderedIcon = useMemo(() => {
    if (!icon) return null;

    if (typeof icon === 'string') {
      return <Icon name={icon as IconName} size="md" className="mr-2 flex-shrink-0" />;
    }

    return <div className="mr-2 flex-shrink-0">{icon}</div>;
  }, [icon]);

  // Xác định xem có nên hiển thị submenu content không
  const shouldShowContent = () => {
    if (mode === 'horizontal') {
      return popup ? isHovered || isKeyboardUser : expanded;
    }

    if (mode === 'vertical' && collapsed) {
      return isHovered || isKeyboardUser;
    }

    return expanded;
  };

  return (
    <div
      className="relative"
      ref={submenuRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Submenu title */}
      <div
        className={getTitleClasses()}
        onClick={handleTitleClick}
        role="menuitem"
        aria-haspopup="true"
        aria-expanded={expanded}
        tabIndex={disabled ? -1 : 0}
        aria-disabled={disabled}
      >
        <div className="flex items-center">
          {renderedIcon}
          {(!collapsed || mode === 'horizontal') && (
            <Typography variant="body1" className="truncate">
              {label}
            </Typography>
          )}
        </div>
        {(!collapsed || mode === 'horizontal') && (
          <Icon
            name={mode === 'horizontal' ? 'chevron-down' : 'chevron-right'}
            size="sm"
            className={`transition-transform duration-300 ${expanded ? 'rotate-180' : ''}`}
          />
        )}
      </div>

      {/* Submenu content */}
      {shouldShowContent() && (
        <div className={getContentClasses()}>
          {mode === 'horizontal' || (mode === 'vertical' && collapsed) ? (
            <ScrollArea height="auto" maxHeight={`${maxHeight}px`}>
              {children}
            </ScrollArea>
          ) : (
            children
          )}
        </div>
      )}
    </div>
  );
};

export default React.memo(SubMenu);
