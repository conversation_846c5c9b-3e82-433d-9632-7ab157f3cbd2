import React, { useMemo } from 'react';
import { useImageGallery } from './useImageGallery';
import { GalleryImage } from './types';
import { Loading } from '@/shared/components/common';

interface ImageGalleryGridProps {
  className?: string;
}

/**
 * Component hiển thị layout dạng lưới cho Image Gallery
 */
const ImageGalleryGrid: React.FC<ImageGalleryGridProps> = ({ className = '' }) => {
  const { images, columns, gap, lightboxEnabled, openLightbox, onImageClick, lazyLoadEnabled } =
    useImageGallery();

  // Tạo CSS grid template columns cho responsive
  const getGridTemplateColumns = useMemo(() => {
    if (typeof columns === 'number') {
      return `repeat(${columns}, 1fr)`;
    }

    return `repeat(${columns.xs || 1}, 1fr)`;
  }, [columns]);

  // Xử lý click vào hình ảnh
  const handleImageClick = (image: GalleryImage, index: number) => {
    if (lightboxEnabled) {
      openLightbox(index);
    }

    if (onImageClick) {
      onImageClick(image, index);
    }
  };

  return (
    <div
      className={`grid gap-${typeof gap === 'number' ? gap : 4} ${className}`}
      style={{
        gridTemplateColumns: getGridTemplateColumns,
        gap: typeof gap === 'string' ? gap : undefined,
      }}
      data-testid="image-gallery-grid"
    >
      {images.map((image: GalleryImage, index: number) => (
        <GridItem
          key={`gallery-image-${index}`}
          image={image}
          index={index}
          onClick={handleImageClick}
          useLazyLoad={lazyLoadEnabled}
        />
      ))}
    </div>
  );
};

interface GridItemProps {
  image: GalleryImage;
  index: number;
  onClick: (image: GalleryImage, index: number) => void;
  useLazyLoad: boolean;
}

const GridItem: React.FC<GridItemProps> = ({ image, index, onClick, useLazyLoad }) => {
  // Sử dụng lazy load nếu được bật
  const { ref, inView, isLoaded, imageSrc, onLoad } = useLazyLoad
    ? {
        ref: React.createRef<HTMLElement>(),
        inView: true,
        isLoaded: false,
        imageSrc: image.src,
        onLoad: () => {},
      }
    : {
        ref: React.createRef<HTMLElement>(),
        inView: true,
        isLoaded: true,
        imageSrc: image.src,
        onLoad: () => {},
      };

  // Xử lý click vào hình ảnh
  const handleClick = () => {
    onClick(image, index);
  };

  return (
    <figure
      ref={useLazyLoad ? (ref as React.RefObject<HTMLElement>) : undefined}
      className="relative overflow-hidden rounded-lg transition-transform duration-300 hover:scale-[1.02] cursor-pointer shadow-sm dark:shadow-gray-800"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={image.alt || `Image ${index + 1}`}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <div className="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800">
        <img
          src={useLazyLoad ? imageSrc : image.src}
          alt={image.alt || `Image ${index + 1}`}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            useLazyLoad && !isLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          onLoad={useLazyLoad ? onLoad : undefined}
        />

        {/* Loading indicator */}
        {useLazyLoad && inView && !isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loading />
          </div>
        )}
      </div>

      {/* Caption */}
      {image.caption && (
        <figcaption className="p-2 text-sm text-gray-600 dark:text-gray-300 truncate">
          {image.caption}
        </figcaption>
      )}
    </figure>
  );
};

export default ImageGalleryGrid;
