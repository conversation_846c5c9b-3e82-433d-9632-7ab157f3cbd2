import React, { ReactNode, useState } from 'react';
import { ProfileCardId } from '../constants/profile-cards';
import { ProfileAccordionContext } from './ProfileAccordionContext';

interface ProfileAccordionContextType {
  openCard: ProfileCardId | null;
  setOpenCard: (cardId: ProfileCardId | null) => void;
  toggleCard: (cardId: ProfileCardId) => void;
  isCardOpen: (cardId: ProfileCardId) => boolean;
}

interface ProfileAccordionProviderProps {
  children: ReactNode;
  defaultOpenCard?: ProfileCardId | null;
}

export const ProfileAccordionProvider: React.FC<ProfileAccordionProviderProps> = ({
  children,
  defaultOpenCard = null,
}) => {
  const [openCard, setOpenCard] = useState<ProfileCardId | null>(defaultOpenCard);

  const toggleCard = (cardId: ProfileCardId) => {
    setOpenCard(current => current === cardId ? null : cardId);
  };

  const isCardOpen = (cardId: ProfileCardId) => {
    return openCard === cardId;
  };

  const value: ProfileAccordionContextType = {
    openCard,
    setOpenCard,
    toggleCard,
    isCardOpen,
  };

  return (
    <ProfileAccordionContext.Provider value={value}>
      {children}
    </ProfileAccordionContext.Provider>
  );
};
