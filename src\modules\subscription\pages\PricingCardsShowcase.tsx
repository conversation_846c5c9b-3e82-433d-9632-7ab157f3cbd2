/**
 * Pricing Cards Showcase - Demo page showing different card designs
 */
import React, { useState } from 'react';

import { Typo<PERSON>, <PERSON><PERSON>, Card } from '@/shared/components/common';
import { SubscriptionDuration, ServicePackage, ServiceType } from '../types';

// Import all card components
import ModernPricingCard from '../components/cards/ModernPricingCard';
import GlassmorphismCard from '../components/cards/GlassmorphismCard';
import NeumorphismCard from '../components/cards/NeumorphismCard';
import MinimalCard from '../components/cards/MinimalCard';
import GradientCard from '../components/cards/GradientCard';

const PricingCardsShowcase: React.FC = () => {

  const [selectedDuration, setSelectedDuration] = useState<SubscriptionDuration>(SubscriptionDuration.MONTHLY);
  const [selectedCardType, setSelectedCardType] = useState<string>('modern');

  // Mock data for demonstration
  const mockPackages: ServicePackage[] = [
    {
      id: 'basic',
      name: 'Basic',
      type: ServiceType.MAIN,
      description: 'Perfect for individuals and small projects',
      icon: 'user',
      prices: {
        [SubscriptionDuration.MONTHLY]: 10000,
        [SubscriptionDuration.SEMI_ANNUAL]: 27000,
        [SubscriptionDuration.ANNUAL]: 96000,
      },
      features: [
        { name: 'API Calls', value: '1,000/month' },
        { name: 'Storage', value: '5GB' },
        { name: 'Support', value: 'Email' },
        { name: 'Custom Domain', value: false },
        { name: 'Analytics', value: true },
      ],
      isPopular: false,
      isRecommended: false,
    },
    {
      id: 'pro',
      name: 'Pro',
      type: ServiceType.MAIN,
      description: 'Best for growing businesses and teams',
      icon: 'users',
      prices: {
        [SubscriptionDuration.MONTHLY]: 25000,
        [SubscriptionDuration.SEMI_ANNUAL]: 67500,
        [SubscriptionDuration.ANNUAL]: 240000,
      },
      features: [
        { name: 'API Calls', value: '10,000/month' },
        { name: 'Storage', value: '50GB' },
        { name: 'Support', value: 'Priority' },
        { name: 'Custom Domain', value: true },
        { name: 'Analytics', value: true },
        { name: 'Team Collaboration', value: true },
      ],
      isPopular: true,
      isRecommended: true,
    },
    {
      id: 'enterprise',
      name: 'Enterprise',
      type: ServiceType.MAIN,
      description: 'Advanced features for large organizations',
      icon: 'building',
      prices: {
        [SubscriptionDuration.MONTHLY]: 50000,
        [SubscriptionDuration.SEMI_ANNUAL]: 135000,
        [SubscriptionDuration.ANNUAL]: 480000,
      },
      features: [
        { name: 'API Calls', value: 'Unlimited' },
        { name: 'Storage', value: '500GB' },
        { name: 'Support', value: '24/7 Phone' },
        { name: 'Custom Domain', value: true },
        { name: 'Analytics', value: true },
        { name: 'Team Collaboration', value: true },
        { name: 'SSO Integration', value: true },
      ],
      isPopular: false,
      isRecommended: false,
    },
  ];

  const handleSelectPackage = (pkg: ServicePackage) => {
    console.log('Selected package:', pkg);
    // Handle package selection
  };

  const cardTypes = [
    { id: 'modern', name: 'Modern', component: ModernPricingCard },
    { id: 'glassmorphism', name: 'Glassmorphism', component: GlassmorphismCard },
    { id: 'neumorphism', name: 'Neumorphism', component: NeumorphismCard },
    { id: 'minimal', name: 'Minimal', component: MinimalCard },
    { id: 'gradient', name: 'Gradient', component: GradientCard },
  ];

  const selectedCardComponent = cardTypes.find(type => type.id === selectedCardType)?.component || ModernPricingCard;
  const CardComponent = selectedCardComponent;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <Typography variant="h1" className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Pricing Cards Showcase
          </Typography>
          <Typography variant="body1" className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Explore different pricing card designs inspired by modern web applications.
            Choose your favorite style and see how it looks with different pricing plans.
          </Typography>
        </div>

        {/* Controls */}
        <div className="mb-12">
          <Card className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Card Type Selector */}
              <div>
                <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                  Card Design
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {cardTypes.map((type) => (
                    <Button
                      key={type.id}
                      variant={selectedCardType === type.id ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedCardType(type.id)}
                    >
                      {type.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Duration Selector */}
              <div>
                <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                  Billing Period
                </Typography>
                <div className="flex flex-wrap gap-2">
                  {Object.values(SubscriptionDuration).map((duration) => (
                    <Button
                      key={duration}
                      variant={selectedDuration === duration ? 'primary' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedDuration(duration)}
                    >
                      {duration === 'semi_annual' ? 'Semi Annual' : duration.charAt(0).toUpperCase() + duration.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Cards Grid */}
        <div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12"
          style={{
            display: 'grid',
            alignItems: 'stretch',
            gridTemplateRows: 'repeat(auto-fit, minmax(600px, 1fr))'
          }}
        >
          {mockPackages.map((pkg) => (
            <div
              key={pkg.id}
              className="flex"
              style={{ minHeight: '600px' }}
            >
              <CardComponent
                package={pkg}
                duration={selectedDuration}
                onSelect={handleSelectPackage}
                className="w-full"
              />
            </div>
          ))}
        </div>

        {/* Installation & Imports */}
        <Card className="p-8 mb-8">
          <Typography variant="h4" className="font-bold mb-6 text-gray-900 dark:text-white">
            Installation & Imports
          </Typography>

          <div className="space-y-6">
            {/* Available Components */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Available Components
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`// Individual card components
import { ModernPricingCard } from '@/modules/subscription/components/cards/ModernPricingCard';
import { GlassmorphismCard } from '@/modules/subscription/components/cards/GlassmorphismCard';
import { NeumorphismCard } from '@/modules/subscription/components/cards/NeumorphismCard';
import { MinimalCard } from '@/modules/subscription/components/cards/MinimalCard';
import { GradientCard } from '@/modules/subscription/components/cards/GradientCard';

// Or import all at once
import {
  ModernPricingCard,
  GlassmorphismCard,
  NeumorphismCard,
  MinimalCard,
  GradientCard,
} from '@/modules/subscription/components/cards';

// Main wrapper component
import { ServicePackageCard } from '@/modules/subscription/components/ServicePackageCard';

// Types
import { ServicePackage, SubscriptionDuration } from '@/modules/subscription/types/subscription.types';`}
                </pre>
              </div>
            </div>

            {/* Props Interface */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Props Interface
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`interface PricingCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

interface ServicePackageCardProps extends PricingCardProps {
  cardType?: 'modern' | 'glassmorphism' | 'neumorphism' | 'minimal' | 'gradient';
}`}
                </pre>
              </div>
            </div>
          </div>
        </Card>

        {/* Usage Examples */}
        <Card className="p-8 mb-8">
          <Typography variant="h4" className="font-bold mb-6 text-gray-900 dark:text-white">
            Usage Examples
          </Typography>

          <div className="space-y-8">
            {/* Basic Usage */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Basic Usage
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`import { ModernPricingCard } from '@/modules/subscription/components/cards';
import { SubscriptionDuration } from '@/modules/subscription/types/subscription.types';

const MyComponent = () => {
  const handleSelectPackage = (pkg) => {
    console.log('Selected package:', pkg);
  };

  return (
    <ModernPricingCard
      package={packageData}
      duration={SubscriptionDuration.MONTHLY}
      onSelect={handleSelectPackage}
    />
  );
};`}
                </pre>
              </div>
            </div>

            {/* ServicePackageCard with cardType */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Using ServicePackageCard with Different Styles
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`import { ServicePackageCard } from '@/modules/subscription/components';

const PricingSection = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {packages.map((pkg) => (
        <ServicePackageCard
          key={pkg.id}
          package={pkg}
          duration={duration}
          onSelect={handleSelect}
          cardType="glassmorphism" // modern | glassmorphism | neumorphism | minimal | gradient
        />
      ))}
    </div>
  );
};`}
                </pre>
              </div>
            </div>

            {/* Package Data Structure */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Package Data Structure
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`const packageData = {
  id: 'pro',
  name: 'Pro',
  description: 'Best for growing businesses and teams',
  icon: 'users',
  prices: {
    monthly: 25000,
    quarterly: 67500,
    yearly: 240000,
  },
  features: [
    { name: 'API Calls', value: '10,000/month' },
    { name: 'Storage', value: '50GB' },
    { name: 'Support', value: 'Priority' },
    { name: 'Custom Domain', value: true },
    { name: 'Analytics', value: true },
    { name: 'Team Collaboration', value: true },
  ],
  isPopular: true,
  isRecommended: true,
};`}
                </pre>
              </div>
            </div>

            {/* Grid Layout with Fixed Height */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Grid Layout with Button Alignment
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`const PricingGrid = () => {
  return (
    <div
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      style={{
        display: 'grid',
        alignItems: 'stretch',
        gridTemplateRows: 'repeat(auto-fit, minmax(600px, 1fr))'
      }}
    >
      {packages.map((pkg) => (
        <div
          key={pkg.id}
          className="flex"
          style={{ minHeight: '600px' }}
        >
          <ModernPricingCard
            package={pkg}
            duration={duration}
            onSelect={handleSelect}
            className="w-full"
          />
        </div>
      ))}
    </div>
  );
};`}
                </pre>
              </div>
            </div>
          </div>
        </Card>

        {/* Customization & Best Practices */}
        <Card className="p-8 mb-8">
          <Typography variant="h4" className="font-bold mb-6 text-gray-900 dark:text-white">
            Customization & Best Practices
          </Typography>

          <div className="space-y-8">
            {/* Custom Styling */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Custom Styling
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`// Add custom classes
<ModernPricingCard
  package={pkg}
  duration={duration}
  onSelect={handleSelect}
  className="custom-pricing-card shadow-2xl"
/>

// Custom CSS
.custom-pricing-card {
  border: 2px solid #3b82f6;
  transform: scale(1.05);
}

.custom-pricing-card:hover {
  transform: scale(1.1);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}`}
                </pre>
              </div>
            </div>

            {/* Responsive Design */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Responsive Design Best Practices
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`// Responsive grid with proper spacing
const ResponsivePricingGrid = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      <div
        className="grid gap-6 sm:gap-8 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
        style={{
          alignItems: 'stretch',
          gridTemplateRows: 'repeat(auto-fit, minmax(500px, 1fr))'
        }}
      >
        {packages.map((pkg) => (
          <div key={pkg.id} className="flex" style={{ minHeight: '500px' }}>
            <ServicePackageCard
              package={pkg}
              duration={duration}
              onSelect={handleSelect}
              cardType="modern"
              className="w-full"
            />
          </div>
        ))}
      </div>
    </div>
  );
};`}
                </pre>
              </div>
            </div>

            {/* Performance Tips */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Performance Tips
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`// Memoize expensive calculations
const PricingSection = React.memo(({ packages, duration }) => {
  const memoizedPackages = useMemo(() =>
    packages.map(pkg => ({
      ...pkg,
      displayPrice: formatPrice(pkg.prices[duration])
    })), [packages, duration]
  );

  const handleSelect = useCallback((pkg) => {
    // Handle selection
    console.log('Selected:', pkg);
  }, []);

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
      {memoizedPackages.map((pkg) => (
        <ModernPricingCard
          key={pkg.id}
          package={pkg}
          duration={duration}
          onSelect={handleSelect}
        />
      ))}
    </div>
  );
});`}
                </pre>
              </div>
            </div>

            {/* Accessibility */}
            <div>
              <Typography variant="h6" className="font-semibold mb-3 text-gray-900 dark:text-white">
                Accessibility Features
              </Typography>
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 overflow-x-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200">
{`// All cards include:
// ✅ ARIA labels and roles
// ✅ Keyboard navigation support
// ✅ Screen reader friendly
// ✅ High contrast support
// ✅ Focus indicators

// Example with custom accessibility
<ModernPricingCard
  package={pkg}
  duration={duration}
  onSelect={handleSelect}
  aria-label={\`\${pkg.name} pricing plan\`}
  role="button"
  tabIndex={0}
/>`}
                </pre>
              </div>
            </div>
          </div>
        </Card>

        {/* Design Information */}
        <Card className="p-8">
          <Typography variant="h4" className="font-bold mb-6 text-gray-900 dark:text-white">
            Design Inspirations
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-blue-600">
                🎯 Modern
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Inspired by Stripe, Vercel, and modern SaaS platforms. Clean design with subtle shadows and hover effects.
              </Typography>
            </div>

            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-purple-600">
                ✨ Glassmorphism
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Trendy glass effect with backdrop blur and transparency. Popular in modern UI design.
              </Typography>
            </div>

            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-orange-600">
                🎨 Neumorphism
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Soft UI design with subtle shadows creating a pressed/embossed effect.
              </Typography>
            </div>

            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-gray-600">
                📐 Minimal
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Clean and simple design inspired by Apple and Linear. Focus on typography and whitespace.
              </Typography>
            </div>

            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-pink-600">
                🌈 Gradient
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                Vibrant gradient design with dynamic colors and modern visual effects.
              </Typography>
            </div>

            <div className="space-y-3">
              <Typography variant="h6" className="font-semibold text-green-600">
                🚀 Features
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                All cards support dark mode, responsive design, accessibility features, and smooth animations.
              </Typography>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default PricingCardsShowcase;
