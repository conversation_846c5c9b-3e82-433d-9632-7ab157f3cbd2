import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography } from '@/shared/components/common';
import { AgentPerformanceMetrics } from '../../types';

interface PerformanceChartProps {
  data?: AgentPerformanceMetrics;
  timeRange?: string;
  className?: string;
}

const PerformanceChart: React.FC<PerformanceChartProps> = ({
  className,
}) => {
  // TODO: Implement chart functionality when needed
  // const data = props.data;
  // const timeRange = props.timeRange || '24h';
  const { t } = useTranslation(['external-agents']);

  return (
    <Card className={className}>
      <div className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('external-agents:performance.chart')}
        </Typography>
        <div className="text-center py-8">
          <Typography variant="body2" className="text-muted-foreground">
            Performance Chart Component
          </Typography>
          <Typography variant="caption" className="text-muted-foreground">
            This component will be implemented in a future task.
          </Typography>
        </div>
      </div>
    </Card>
  );
};

export default PerformanceChart;
