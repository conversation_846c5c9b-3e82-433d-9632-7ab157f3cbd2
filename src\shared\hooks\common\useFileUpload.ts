import { useState } from 'react';
import axios, { AxiosRequestConfig, AxiosError } from 'axios';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';

/**
 * Tham số cho hàm uploadToPresignedUrl
 */
export interface UploadToPresignedUrlParams {
  /**
   * File cần upload
   */
  file: File;

  /**
   * URL tạm thời để upload file
   */
  presignedUrl: string;

  /**
   * Headers bổ sung cho request upload
   */
  headers?: Record<string, string>;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Hàm upload file lên presigned URL
 */
export const uploadToPresignedUrl = async ({
  file,
  presignedUrl,
  headers = {},
  onProgress,
}: UploadToPresignedUrlParams): Promise<string> => {
  try {
    // Cấu hình cho request upload
    const config: AxiosRequestConfig = {
      headers: {
        'Content-Type': file.type,
        ...headers,
      },
      onUploadProgress: progressEvent => {
        if (progressEvent.total && onProgress) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(percentCompleted);
        }
      },
    };

    // Upload file lên cloud thông qua presigned URL
    // Sử dụng cấu hình mặc định của axios, không sử dụng httpsAgent trong môi trường browser
    // Giải pháp tốt hơn: Sửa chứng chỉ SSL trên máy chủ hoặc sử dụng proxy CORS
    try {
      await axios.put(presignedUrl, file, config);
    } catch (error) {
      // Kiểm tra nếu là lỗi CORS hoặc Network Error nhưng status code là 200 (thành công)
      // Đây là trường hợp đặc biệt khi S3 trả về lỗi nhưng file đã được upload thành công
      const axiosError = error as AxiosError;

      // Kiểm tra nếu là lỗi CORS hoặc Network Error
      const isCorsError = axiosError.message?.includes('CORS');
      const isNetworkError = axiosError.message === 'Network Error';

      // Kiểm tra nếu có response với status 200
      const hasSuccessStatus = axiosError.response?.status === 200;

      if ((isCorsError || isNetworkError) && hasSuccessStatus) {
        console.warn(`${isCorsError ? 'CORS' : 'Network'} error detected but file was uploaded successfully (status 200)`);
        // Tiếp tục xử lý như thành công
      } else {
        // Nếu là lỗi khác, ném lỗi để xử lý ở catch bên ngoài
        throw error;
      }
    }

    // Trả về URL gốc của file (không có query params)
    return presignedUrl.split('?')[0] || '';
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
};

/**
 * Tham số cho hook useFileUpload
 */
export interface UseFileUploadOptions {
  /**
   * Headers bổ sung cho request upload
   */
  uploadHeaders?: Record<string, string>;

  /**
   * Tùy chọn cho mutation
   */
  mutationOptions?: Omit<
    UseMutationOptions<
      string,
      unknown,
      { file: File; presignedUrl: string; onUploadProgress?: (progress: number) => void }
    >,
    'mutationFn'
  >;
}

export function useFileUpload({
  uploadHeaders = {},
  mutationOptions = {},
}: UseFileUploadOptions = {}) {
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  // Mutation để upload file
  const mutation = useMutation({
    mutationFn: async ({
      file,
      presignedUrl,
      onUploadProgress,
    }: {
      file: File;
      presignedUrl: string;
      onUploadProgress?: (progress: number) => void;
    }) => {
      try {
        const progressCallback = (progress: number) => {
          setUploadProgress(progress);
          if (onUploadProgress) {
            onUploadProgress(progress);
          }
        };

        const result = await uploadToPresignedUrl({
          file,
          presignedUrl,
          headers: uploadHeaders,
          onProgress: progressCallback,
        });

        // Reset progress sau khi hoàn thành
        setUploadProgress(0);
        return result;
      } catch (error) {
        console.error('Error uploading file:', error);
        setUploadProgress(0);
        throw error;
      }
    },
    ...mutationOptions,
  });

  return {
    /**
     * Hàm upload file lên presigned URL
     */
    uploadToUrl: mutation.mutateAsync,

    /**
     * Đang upload hay không
     */
    isUploading: mutation.isPending,

    /**
     * Tiến trình upload (0-100)
     */
    uploadProgress,

    /**
     * Lỗi upload (nếu có)
     */
    error: mutation.error,

    /**
     * Reset trạng thái
     */
    reset: mutation.reset,
  };
}

export default useFileUpload;
