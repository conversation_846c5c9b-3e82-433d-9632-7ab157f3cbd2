import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/shared/store';
import { getMenuItemsByAuthType } from '@/shared/components/layout/chat-panel/menu-items';
import { AuthType } from '@/shared/hooks/useAuthCommon';
import { ModernMenuItem } from '@/shared/components/layout/chat-panel/ModernMenu';
import { ChatKeyword } from '../types';

/**
 * Hook để lấy danh sách chat keywords kết hợp từ menu items mặc định và custom keywords từ settings
 */
export const useChatKeywords = (authType: AuthType): ModernMenuItem[] => {
  const { chatKeywords, customKeywords } = useSelector((state: RootState) => state.settings);

  return useMemo(() => {
    // Lấy menu items mặc định dựa trên auth type
    const defaultMenuItems = getMenuItemsByAuthType(authType);
    
    // Tạo map để dễ dàng override keywords
    const menuItemsMap = new Map<string, ModernMenuItem>();
    
    // Thêm tất cả default menu items vào map
    defaultMenuItems.forEach(item => {
      menuItemsMap.set(item.id, item);
    });

    // Override với chat keywords từ settings (nếu enabled)
    chatKeywords.forEach((keyword: ChatKeyword) => {
      if (keyword.enabled) {
        const existingItem = menuItemsMap.get(keyword.id);
        if (existingItem) {
          // Cập nhật keywords cho item đã tồn tại
          menuItemsMap.set(keyword.id, {
            ...existingItem,
            keywords: [
              ...(existingItem.keywords || []),
              keyword.keyword,
              ...(keyword.description ? [keyword.description] : [])
            ]
          });
        } else {
          // Tạo menu item mới từ keyword
          menuItemsMap.set(keyword.id, {
            id: keyword.id,
            label: keyword.keyword,
            path: keyword.path,
            icon: 'link', // Default icon cho custom keywords
            keywords: [keyword.keyword, ...(keyword.description ? [keyword.description] : [])]
          });
        }
      } else {
        // Nếu keyword bị disable, xóa khỏi map
        menuItemsMap.delete(keyword.id);
      }
    });

    // Thêm custom keywords (nếu enabled)
    customKeywords.forEach((keyword: ChatKeyword) => {
      if (keyword.enabled) {
        menuItemsMap.set(keyword.id, {
          id: keyword.id,
          label: keyword.keyword,
          path: keyword.path,
          icon: 'link', // Default icon cho custom keywords
          keywords: [keyword.keyword, ...(keyword.description ? [keyword.description] : [])]
        });
      }
    });

    // Chuyển map thành array và sắp xếp
    return Array.from(menuItemsMap.values()).sort((a, b) => {
      // Ưu tiên các item mặc định trước, sau đó là custom
      const aIsDefault = defaultMenuItems.some(item => item.id === a.id);
      const bIsDefault = defaultMenuItems.some(item => item.id === b.id);
      
      if (aIsDefault && !bIsDefault) return -1;
      if (!aIsDefault && bIsDefault) return 1;
      
      // Nếu cùng loại, sắp xếp theo label
      return a.label.localeCompare(b.label);
    });
  }, [authType, chatKeywords, customKeywords]);
};

export default useChatKeywords;
