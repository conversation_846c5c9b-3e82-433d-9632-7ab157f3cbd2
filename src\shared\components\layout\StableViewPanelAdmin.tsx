import { ReactNode, memo } from 'react';
import ViewPanelAdmin from './view-panel/ViewPanelAdmin';

interface StableViewPanelAdminProps {
  title: string;
  children: ReactNode;
  actions?: ReactNode;
}

/**
 * Wrapper component để tối ưu hóa ViewPanelAdmin
 * Sử dụng memo với shallow comparison để tránh re-render không cần thiết
 */
const StableViewPanelAdmin = memo<StableViewPanelAdminProps>(({ title, children, actions }) => {
  console.log('[StableViewPanelAdmin] Rendering with title:', title);
  
  return (
    <ViewPanelAdmin title={title} actions={actions}>
      {children}
    </ViewPanelAdmin>
  );
});

StableViewPanelAdmin.displayName = 'StableViewPanelAdmin';

export default StableViewPanelAdmin;
