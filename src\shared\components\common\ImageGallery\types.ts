/**
 * Types for ImageGallery component
 */

export interface GalleryImage {
  /**
   * URL hình ảnh gốc
   */
  src: string;

  /**
   * URL thumbnail (nếu không có sẽ sử dụng src)
   */
  thumbnail?: string;

  /**
   * Alt text cho hình ảnh
   */
  alt?: string;

  /**
   * Caption hiển thị trong lightbox
   */
  caption?: string;

  /**
   * Width của hình ảnh
   */
  width?: number;

  /**
   * Height của hình ảnh
   */
  height?: number;

  /**
   * Tags hoặc metadata khác
   */
  tags?: string[];

  /**
   * Dữ liệu tùy chỉnh
   */
  [key: string]: unknown;
}

export interface LightboxConfig {
  /**
   * Hiển thị caption
   */
  showCaption?: boolean;

  /**
   * Hiển thị counter (e.g., "1/10")
   */
  showCounter?: boolean;

  /**
   * Hiển thị nút close
   */
  showCloseButton?: boolean;

  /**
   * Hiển thị nút next/prev
   */
  showNavigation?: boolean;

  /**
   * Đóng lightbox khi click vào backdrop
   */
  closeOnBackdropClick?: boolean;

  /**
   * Đóng lightbox khi nhấn ESC
   */
  closeOnEsc?: boolean;

  /**
   * Animation duration (ms)
   */
  animationDuration?: number;

  /**
   * Backdrop opacity (0-1)
   */
  backdropOpacity?: number;
}

export interface ThumbnailsConfig {
  /**
   * Vị trí thumbnails
   */
  position?: 'bottom' | 'top' | 'left' | 'right';

  /**
   * Kích thước thumbnails
   */
  size?: number;

  /**
   * Khoảng cách giữa các thumbnails
   */
  gap?: number;

  /**
   * Số lượng thumbnails hiển thị
   */
  visibleItems?: number;

  /**
   * Tự động scroll đến thumbnail active
   */
  autoScroll?: boolean;
}

export interface ZoomConfig {
  /**
   * Mức zoom tối đa
   */
  maxZoom?: number;

  /**
   * Mức zoom tối thiểu
   */
  minZoom?: number;

  /**
   * Bước zoom mỗi lần
   */
  zoomStep?: number;

  /**
   * Hiển thị controls zoom
   */
  showControls?: boolean;
}

export type GalleryLayout = 'grid' | 'masonry' | 'carousel';

export type ColumnsConfig =
  | number
  | {
      xs?: number;
      sm?: number;
      md?: number;
      lg?: number;
      xl?: number;
    };

export interface ImageGalleryProps {
  /**
   * Danh sách hình ảnh
   */
  images: GalleryImage[];

  /**
   * Cấu hình layout
   */
  layout?: GalleryLayout;

  /**
   * Số cột hiển thị (có thể là object cho responsive)
   */
  columns?: ColumnsConfig;

  /**
   * Khoảng cách giữa các hình ảnh
   */
  gap?: number | string;

  /**
   * Bật/tắt lightbox
   */
  lightbox?: boolean;

  /**
   * Cấu hình lightbox
   */
  lightboxConfig?: LightboxConfig;

  /**
   * Bật/tắt thumbnails
   */
  thumbnails?: boolean;

  /**
   * Cấu hình thumbnails
   */
  thumbnailsConfig?: ThumbnailsConfig;

  /**
   * Bật/tắt zoom
   */
  zoom?: boolean;

  /**
   * Cấu hình zoom
   */
  zoomConfig?: ZoomConfig;

  /**
   * Bật/tắt lazy loading
   */
  lazyLoad?: boolean;

  /**
   * Callback khi click vào hình ảnh
   */
  onImageClick?: (image: GalleryImage, index: number) => void;

  /**
   * Callback khi thay đổi hình ảnh trong lightbox
   */
  onImageChange?: (currentImage: GalleryImage, index: number) => void;

  /**
   * Class bổ sung
   */
  className?: string;
}
