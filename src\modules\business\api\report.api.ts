import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import {
  ReportOverviewQueryDto,
  ReportOverviewResponseDto,
  SalesChartQueryDto,
  SalesChartResponseDto,
  OrdersChartQueryDto,
  OrdersChartResponseDto,
  CustomersChartQueryDto,
  CustomersChartResponseDto,
  ProductsChartQueryDto,
  ProductsChartResponseDto,
  TopSellingProductsQueryDto,
  TopSellingProductsResponseDto,
  PotentialCustomersQueryDto,
  PotentialCustomersResponseDto,
} from '../types/report.types';

/**
 * Business Report API Layer
 * Raw API calls without business logic
 */

const BASE_URL = '/user/business/reports';

/**
 * Lấy dữ liệu tổng quan báo cáo
 */
export const getReportOverview = async (
  params?: ReportOverviewQueryDto
): Promise<ApiResponseDto<ReportOverviewResponseDto>> => {
  return apiClient.get(`${BASE_URL}/overview`, { params });
};

/**
 * L<PERSON>y dữ liệu biểu đồ doanh thu
 */
export const getSalesChart = async (
  params?: SalesChartQueryDto
): Promise<ApiResponseDto<SalesChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/sales-chart`, { params });
};

/**
 * Lấy dữ liệu biểu đồ đơn hàng
 */
export const getOrdersChart = async (
  params?: OrdersChartQueryDto
): Promise<ApiResponseDto<OrdersChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/orders-chart`, { params });
};

/**
 * Lấy dữ liệu biểu đồ khách hàng
 */
export const getCustomersChart = async (
  params?: CustomersChartQueryDto
): Promise<ApiResponseDto<CustomersChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/customers-chart`, { params });
};

/**
 * Lấy dữ liệu biểu đồ sản phẩm
 */
export const getProductsChart = async (
  params?: ProductsChartQueryDto
): Promise<ApiResponseDto<ProductsChartResponseDto>> => {
  return apiClient.get(`${BASE_URL}/products-chart`, { params });
};

/**
 * Lấy danh sách sản phẩm bán chạy
 */
export const getTopSellingProducts = async (
  params?: TopSellingProductsQueryDto
): Promise<ApiResponseDto<TopSellingProductsResponseDto>> => {
  return apiClient.get(`${BASE_URL}/top-selling-products`, { params });
};

/**
 * Lấy danh sách khách hàng tiềm năng
 */
export const getPotentialCustomers = async (
  params?: PotentialCustomersQueryDto
): Promise<ApiResponseDto<PotentialCustomersResponseDto>> => {
  return apiClient.get(`${BASE_URL}/potential-customers`, { params });
};
