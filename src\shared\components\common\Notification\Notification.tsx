import React, { useState, useEffect, useCallback } from 'react';
import { Icon } from '@/shared/components/common';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationProps {
  type: NotificationType;
  message: string;
  title?: string;
  duration?: number;
  onClose?: () => void;
  isVisible?: boolean;
  inChatFlow?: boolean;
  icon?: React.ReactNode;
}

const getTypeStyles = (type: NotificationType, inChatFlow: boolean = false) => {
  if (inChatFlow) {
    // Trong luồng chat, sử dụng style nhẹ nhàng hơn
    switch (type) {
      case 'success':
        return 'text-green-800 dark:text-green-300';
      case 'error':
        return 'text-red-800 dark:text-red-300';
      case 'warning':
        return 'text-yellow-800 dark:text-yellow-300';
      case 'info':
        return 'text-blue-800 dark:text-blue-300';
      default:
        return 'text-gray-800 dark:text-gray-300';
    }
  } else {
    // Style gốc cho notification độ<PERSON> lậ<PERSON>
    switch (type) {
      case 'success':
        return 'bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-300 border-l-4 border-green-500';
      case 'error':
        return 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-300 border-l-4 border-red-500';
      case 'warning':
        return 'bg-yellow-50 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 border-l-4 border-yellow-500';
      case 'info':
        return 'bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border-l-4 border-blue-500';
      default:
        return 'bg-gray-50 dark:bg-gray-800 text-gray-800 dark:text-gray-300 border-l-4 border-gray-500';
    }
  }
};

const getIconName = (type: NotificationType) => {
  switch (type) {
    case 'success':
      return 'check';
    case 'error':
      return 'close';
    case 'warning':
      return 'warning';
    case 'info':
      return 'info';
    default:
      return 'info';
  }
};

const Notification: React.FC<NotificationProps> = ({
  type,
  message,
  title,
  duration = 5000,
  onClose,
  isVisible = true,
  inChatFlow = false,
  icon,
}) => {
  const [visible, setVisible] = useState(isVisible);
  const [exiting, setExiting] = useState(false);

  useEffect(() => {
    setVisible(isVisible);
    if (isVisible) {
      setExiting(false);
    }
  }, [isVisible]);

  const handleClose = useCallback(() => {
    setExiting(true);
    setTimeout(() => {
      setVisible(false);
      if (onClose) {
        onClose();
      }
    }, 300); // Match the animation duration
  }, [onClose]);

  useEffect(() => {
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => {
        clearTimeout(timer);
      };
    }
    return undefined;
  }, [visible, duration, handleClose]);

  if (!visible) return null;

  return (
    <div
      className={`flex items-start p-3 max-w-full transition-all duration-300 ${
        exiting ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      } ${getTypeStyles(type, inChatFlow)}`}
      style={{ maxWidth: '100%', width: '100%' }}
    >
      <div className="flex-shrink-0 mr-2">
        {icon || (
          <Icon
            name={getIconName(type)}
            size="sm"
            className={
              inChatFlow
                ? type === 'success'
                  ? 'text-green-500'
                  : type === 'error'
                    ? 'text-red-500'
                    : type === 'warning'
                      ? 'text-yellow-500'
                      : 'text-blue-500'
                : ''
            }
          />
        )}
      </div>
      <div className="flex-grow text-sm">
        {title && <div className="font-medium mb-1">{title}</div>}
        <div>{message}</div>
      </div>
      {!inChatFlow && (
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
        >
          <Icon name="close" size="xs" />
        </button>
      )}
    </div>
  );
};

export default Notification;
