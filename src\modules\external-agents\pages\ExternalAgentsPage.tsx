import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  ResponsiveGrid,
  SlideInForm,
  Button,
  Typography,
  Input,
  Select
} from '@/shared/components/common';
import ExternalAgentCard from '../components/cards/ExternalAgentCard';
import ExternalAgentForm from '../components/forms/ExternalAgentForm';
import {
  useExternalAgents,
  useCreateExternalAgent,
  useUpdateExternalAgent,
  useDeleteExternalAgent
} from '../hooks';
import { ExternalAgentCreateDto, ExternalAgentUpdateDto } from '../types';
import { ExternalAgent, ExternalAgentQueryDto, ProtocolType, ExternalAgentStatus } from '../types';
import { DEFAULT_QUERY_PARAMS } from '../constants';

const ExternalAgentsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'external-agents']);
  
  // State management
  const [queryParams, setQueryParams] = useState<ExternalAgentQueryDto>(DEFAULT_QUERY_PARAMS);
  const [selectedAgent, setSelectedAgent] = useState<ExternalAgent | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');

  // API hooks
  const { data: agentsData, isLoading, error, refetch } = useExternalAgents(queryParams);
  const createMutation = useCreateExternalAgent();
  const updateMutation = useUpdateExternalAgent();
  const deleteMutation = useDeleteExternalAgent();
  // Connection test will be handled per agent

  // Filter options
  const statusOptions = Object.values(ExternalAgentStatus).map(status => ({
    value: status,
    label: t(`external-agents:status.${status}`),
  }));

  const protocolOptions = Object.values(ProtocolType).map(protocol => ({
    value: protocol,
    label: t(`external-agents:protocol.${protocol}`),
  }));

  // Active filters
  // const activeFilters = useMemo(() => {
  //   const filters: Array<{ key: string; label: string; value: string }> = [];
  //
  //   if (queryParams.search) {
  //     filters.push({
  //       key: 'search',
  //       label: t('external-agents:filters.search'),
  //       value: queryParams.search,
  //     });
  //   }
  //
  //   if (queryParams.status) {
  //     filters.push({
  //       key: 'status',
  //       label: t('external-agents:filters.status'),
  //       value: t(`external-agents:status.${queryParams.status}`),
  //     });
  //   }
  //
  //   if (queryParams.protocol) {
  //     filters.push({
  //       key: 'protocol',
  //       label: t('external-agents:filters.protocol'),
  //       value: t(`external-agents:protocol.${queryParams.protocol}`),
  //     });
  //   }

  //   return filters;
  // }, [queryParams, t]);

  // Event handlers
  const handleSearch = (search: string) => {
    setQueryParams(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (key: string, value: string) => {
    setQueryParams(prev => ({ 
      ...prev, 
      [key]: value || undefined, 
      page: 1 
    }));
  };

  // const handleRemoveFilter = (key: string) => {
  //   setQueryParams(prev => ({
  //     ...prev,
  //     [key]: undefined,
  //     page: 1
  //   }));
  // };

  // const handleClearFilters = () => {
  //   setQueryParams(DEFAULT_QUERY_PARAMS);
  // };

  const handleCreateAgent = () => {
    setSelectedAgent(null);
    setFormMode('create');
    setIsFormOpen(true);
  };

  const handleEditAgent = (agent: ExternalAgent) => {
    setSelectedAgent(agent);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  const handleDeleteAgent = async (agent: ExternalAgent) => {
    if (window.confirm(t('external-agents:messages.confirmDelete'))) {
      try {
        await deleteMutation.mutateAsync(agent.id);
        // Success message will be handled by the mutation
      } catch (error) {
        console.error('Delete failed:', error);
      }
    }
  };

  const handleTestConnection = async (agent: ExternalAgent) => {
    // Connection test will be handled by individual agent cards
    console.log('Testing connection for agent:', agent.id);
  };

  const handleViewAgent = (agent: ExternalAgent) => {
    // Navigate to agent detail page
    window.location.href = `/external-agents/${agent.id}`;
  };

  const handleFormSubmit = async (data: ExternalAgentCreateDto | ExternalAgentUpdateDto) => {
    try {
      if (formMode === 'create') {
        await createMutation.mutateAsync(data as ExternalAgentCreateDto);
      } else if (selectedAgent) {
        await updateMutation.mutateAsync({ id: selectedAgent.id, data });
      }
      setIsFormOpen(false);
      setSelectedAgent(null);
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedAgent(null);
  };

  // Menu items for MenuIconBar
  // const menuItems = [
  //   {
  //     icon: 'plus',
  //     label: t('external-agents:actions.create'),
  //     onClick: handleCreateAgent,
  //   },
  //   {
  //     icon: 'refresh-cw',
  //     label: t('external-agents:actions.refresh'),
  //     onClick: () => refetch(),
  //   },
  //   {
  //     icon: 'download',
  //     label: t('external-agents:actions.export'),
  //     onClick: () => {
  //       // TODO: Implement export functionality
  //       console.log('Export agents');
  //     },
  //   },
  // ];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header with Actions */}
      <div className="flex items-center justify-between mb-6">
        <Typography variant="h1">
          {t('external-agents:title')}
        </Typography>
        <Button variant="primary" onClick={handleCreateAgent}>
          {t('external-agents:actions.create')}
        </Button>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <div className="p-4">
          <div className="flex items-center gap-4 flex-wrap">
            {/* Search */}
            <div className="flex-1 min-w-64">
              <Input
                placeholder={t('external-agents:form.placeholder.search')}
                value={queryParams.search || ''}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>

            {/* Status Filter */}
            <Select
              value={queryParams.status || ''}
              onChange={(value) => handleFilterChange('status', value as string)}
              options={[
                { value: '', label: t('external-agents:filters.all') },
                ...statusOptions
              ]}
              className="min-w-40"
            />

            {/* Protocol Filter */}
            <Select
              value={queryParams.protocol || ''}
              onChange={(value) => handleFilterChange('protocol', value as string)}
              options={[
                { value: '', label: t('external-agents:filters.all') },
                ...protocolOptions
              ]}
              className="min-w-40"
            />
          </div>
        </div>
      </Card>

      {/* Content */}
      <Card>
        {isLoading ? (
          <div className="p-8 text-center">
            <Typography variant="body1">
              {t('external-agents:loading.agents')}
            </Typography>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <Typography variant="body1" className="text-destructive">
              {t('external-agents:messages.error.loadFailed')}
            </Typography>
            <Button 
              variant="outline" 
              onClick={() => refetch()}
              className="mt-4"
            >
              {t('external-agents:actions.retry')}
            </Button>
          </div>
        ) : !agentsData?.items?.length ? (
          <div className="p-8 text-center">
            <Typography variant="h3" className="mb-2">
              {t('external-agents:empty.noAgents')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-4">
              {t('external-agents:empty.createFirst')}
            </Typography>
            <Button variant="primary" onClick={handleCreateAgent}>
              {t('external-agents:actions.create')}
            </Button>
          </div>
        ) : (
          <div className="p-6">
            <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}>
              {agentsData.items.map((agent) => (
                <ExternalAgentCard
                  key={agent.id}
                  agent={agent}
                  onEdit={handleEditAgent}
                  onDelete={handleDeleteAgent}
                  onTest={handleTestConnection}
                  onView={handleViewAgent}
                />
              ))}
            </ResponsiveGrid>

            {/* Pagination would go here */}
            {agentsData.totalPages > 1 && (
              <div className="mt-6 flex justify-center">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('external-agents:pagination.showing')} {agentsData.items.length} {t('external-agents:pagination.of')} {agentsData.total} {t('external-agents:pagination.items')}
                </Typography>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* SlideInForm for Create/Edit */}
      <SlideInForm
        isVisible={isFormOpen}
      >
        <ExternalAgentForm
          agent={selectedAgent || undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={createMutation.isPending || updateMutation.isPending}
          mode={formMode}
        />
      </SlideInForm>
    </div>
  );
};

export default ExternalAgentsPage;
