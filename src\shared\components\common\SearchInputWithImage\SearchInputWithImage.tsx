import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import { Icon } from '@/shared/components/common';
import {
  SearchInputWithImageProps,
  SearchItem,
} from '@/shared/types/search-input-with-image.types';

/**
 * Component SearchInputWithImage - Input tìm kiếm với kết quả hiển thị hình ảnh và tên
 */
const SearchInputWithImage = forwardRef<HTMLInputElement, SearchInputWithImageProps>(
  (
    {
      value,
      onChange,
      items = [],
      placeholder = '',
      label,
      disabled = false,
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      maxResults = 5,
      onBlur,
      onFocus,
      onInputChange,
      imageSize = 'md',
      showSearchIcon = true,
      className = '',
      showClearButton = true,
      noResultsText,
    },
    forwardedRef
  ) => {
    const { t } = useTranslation();
    useTheme(); // Sử dụng hook theme
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [filteredItems, setFilteredItems] = useState<SearchItem[]>([]);
    const [highlightedIndex, setHighlightedIndex] = useState(-1);

    const inputRef = useRef<HTMLInputElement>(null);
    const dropdownRef = useRef<HTMLDivElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    // Kết hợp ref được chuyển tiếp với inputRef
    useEffect(() => {
      if (forwardedRef && inputRef.current) {
        if (typeof forwardedRef === 'function') {
          forwardedRef(inputRef.current);
        } else {
          forwardedRef.current = inputRef.current;
        }
      }
    }, [forwardedRef]);

    // Xác định kích thước dựa trên prop size
    const sizeClasses = {
      sm: 'py-1 px-2 text-sm',
      md: 'py-2 px-3 text-base',
      lg: 'py-3 px-4 text-lg',
    }[size];

    // Xác định kích thước hình ảnh
    const imageSizeClasses = {
      sm: 'w-6 h-6',
      md: 'w-8 h-8',
      lg: 'w-10 h-10',
    }[imageSize];

    // Tìm item đã chọn từ value
    useEffect(() => {
      if (value !== undefined) {
        const item = items.find(item => item.id === value);
        if (item) {
          setInputValue(item.name);
        } else {
          setInputValue('');
        }
      } else {
        setInputValue('');
      }
    }, [value, items]);

    // Lọc items dựa trên inputValue
    useEffect(() => {
      if (!inputValue.trim() && !isOpen) {
        setFilteredItems([]);
        return;
      }

      const filtered = items
        .filter(
          item => !item.disabled && item.name.toLowerCase().includes(inputValue.toLowerCase())
        )
        .slice(0, maxResults);

      setFilteredItems(filtered);
      setHighlightedIndex(filtered.length > 0 ? 0 : -1);
    }, [inputValue, items, maxResults, isOpen]);

    // Xử lý click bên ngoài để đóng dropdown
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Xử lý thay đổi input
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);
      onInputChange?.(value);

      if (value) {
        setIsOpen(true);
      } else {
        onChange?.('' as string, {} as SearchItem);
      }
    };

    // Xử lý chọn item
    const handleSelectItem = (item: SearchItem) => {
      setInputValue(item.name);
      setIsOpen(false);
      onChange?.(item.id, item);
    };

    // Xử lý phím
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!isOpen) {
        if (e.key === 'ArrowDown') {
          setIsOpen(true);
          e.preventDefault();
        }
        return;
      }

      switch (e.key) {
        case 'ArrowDown':
          setHighlightedIndex(prev => (prev < filteredItems.length - 1 ? prev + 1 : prev));
          e.preventDefault();
          break;
        case 'ArrowUp':
          setHighlightedIndex(prev => (prev > 0 ? prev - 1 : prev));
          e.preventDefault();
          break;
        case 'Enter':
          if (highlightedIndex >= 0 && filteredItems[highlightedIndex]) {
            handleSelectItem(filteredItems[highlightedIndex]);
            e.preventDefault();
          }
          break;
        case 'Escape':
          setIsOpen(false);
          e.preventDefault();
          break;
        default:
          break;
      }
    };

    // Xử lý xóa giá trị
    const handleClear = () => {
      setInputValue('');
      onChange?.('' as string, {} as SearchItem);
      inputRef.current?.focus();
    };

    // Xử lý focus
    const handleFocus = () => {
      onFocus?.();
      setIsOpen(true);
    };

    // Xử lý blur
    const handleBlur = () => {
      // Delay để cho phép click vào item trong dropdown
      setTimeout(() => {
        if (!dropdownRef.current?.contains(document.activeElement)) {
          setIsOpen(false);
          onBlur?.();
        }
      }, 100);
    };

    return (
      <div ref={containerRef} className={`relative ${fullWidth ? 'w-full' : ''} ${className}`}>
        {label && <label className="block text-sm font-medium text-foreground mb-1">{label}</label>}

        <div
          className={`
            flex items-center
            border rounded-md bg-white dark:bg-dark-light
            ${sizeClasses}
            ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
            ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
            ${isOpen ? 'ring-2 ring-primary/30' : ''}
            ${fullWidth ? 'w-full' : ''}
          `}
        >
          {showSearchIcon && (
            <div className="flex-shrink-0 mr-2 text-gray-400">
              <Icon name="search" size="sm" />
            </div>
          )}

          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className="w-full bg-transparent border-none focus:outline-none dark:text-white"
          />

          {showClearButton && inputValue && (
            <button
              type="button"
              onClick={handleClear}
              className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <Icon name="x" size="sm" />
            </button>
          )}
        </div>

        {error && <p className="mt-1 text-sm text-error">{error}</p>}

        {helperText && !error && <p className="mt-1 text-sm text-muted">{helperText}</p>}

        {/* Dropdown */}
        {isOpen && (
          <div
            ref={dropdownRef}
            className="absolute z-10 w-full mt-1 bg-white dark:bg-dark-light rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in"
          >
            {filteredItems.length > 0 ? (
              <div role="listbox">
                {filteredItems.map((item, index) => (
                  <div
                    key={`item-${item.id}`}
                    role="option"
                    aria-selected={index === highlightedIndex}
                    className={`
                      flex items-center px-4 py-2 cursor-pointer
                      ${index === highlightedIndex ? 'bg-primary-light/20 dark:bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-dark-lighter'}
                    `}
                    onClick={() => handleSelectItem(item)}
                    onMouseEnter={() => setHighlightedIndex(index)}
                  >
                    {/* Hình ảnh hoặc icon */}
                    <div
                      className={`flex-shrink-0 ${imageSizeClasses} mr-3 rounded-full overflow-hidden bg-gray-100 dark:bg-dark-lighter flex items-center justify-center`}
                    >
                      {item.imageUrl ? (
                        <img
                          src={item.imageUrl}
                          alt={item.name}
                          className="w-full h-full object-cover"
                          onError={e => {
                            // Hiển thị icon khi lỗi hình ảnh
                            e.currentTarget.style.display = 'none';
                            const parent = e.currentTarget.parentElement;
                            if (parent) {
                              parent.innerHTML =
                                '<div class="flex items-center justify-center w-full h-full"><svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg></div>';
                            }
                          }}
                        />
                      ) : item.icon ? (
                        item.icon
                      ) : (
                        <Icon name="user" size="sm" className="text-gray-400" />
                      )}
                    </div>

                    {/* Tên và mô tả */}
                    <div className="flex-grow min-w-0">
                      <div className="font-medium text-foreground truncate">{item.name}</div>
                      {item.description && (
                        <div className="text-xs text-muted truncate">{item.description}</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-3 text-sm text-gray-500 dark:text-gray-400 text-center">
                {noResultsText || t('common.noResults', 'Không tìm thấy kết quả')}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

SearchInputWithImage.displayName = 'SearchInputWithImage';

export default SearchInputWithImage;
