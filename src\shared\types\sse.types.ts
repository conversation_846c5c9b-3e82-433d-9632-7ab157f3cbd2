/**
 * Types và interfaces cho Server-Sent Events (SSE)
 */

/**
 * Trạng thái kết nối SSE
 */
export enum SSEConnectionState {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error',
}

/**
 * Loại SSE event
 */
export enum SSEEventType {
  OPEN = 'open',
  MESSAGE = 'message',
  ERROR = 'error',
  CLOSE = 'close',
  CUSTOM = 'custom',
}

/**
 * Cấu hình cho SSE client
 */
export interface SSEClientConfig {
  /**
   * URL endpoint cho SSE
   */
  url: string;

  /**
   * Headers để gửi kèm request
   */
  headers?: Record<string, string>;

  /**
   * Có tự động kết nối lại khi bị ngắt không
   */
  autoReconnect?: boolean;

  /**
   * Thời gian chờ trước khi kết nối lại (ms)
   */
  reconnectDelay?: number;

  /**
   * Số lần thử kết nối lại tối đa
   */
  maxReconnectAttempts?: number;

  /**
   * Timeout cho kết nối (ms)
   */
  timeout?: number;

  /**
   * Có gửi credentials không
   */
  withCredentials?: boolean;

  /**
   * Callback khi kết nối thành công
   */
  onOpen?: (event: Event) => void;

  /**
   * Callback khi có lỗi
   */
  onError?: (error: Event | Error) => void;

  /**
   * Callback khi kết nối bị đóng
   */
  onClose?: (event: Event) => void;

  /**
   * Callback khi nhận được message
   */
  onMessage?: (event: MessageEvent) => void;
}

/**
 * SSE Event data
 */
export interface SSEEvent<T = unknown> {
  /**
   * ID của event
   */
  id?: string;

  /**
   * Loại event
   */
  type: string;

  /**
   * Dữ liệu của event
   */
  data: T;

  /**
   * Timestamp khi nhận event
   */
  timestamp: number;

  /**
   * Retry time (nếu có)
   */
  retry?: number;
}

/**
 * SSE Connection info
 */
export interface SSEConnectionInfo {
  /**
   * Trạng thái kết nối
   */
  state: SSEConnectionState;

  /**
   * URL đang kết nối
   */
  url: string;

  /**
   * Thời gian kết nối
   */
  connectedAt?: Date;

  /**
   * Thời gian ngắt kết nối cuối
   */
  lastDisconnectedAt?: Date;

  /**
   * Số lần thử kết nối lại
   */
  reconnectAttempts: number;

  /**
   * Lỗi cuối cùng (nếu có)
   */
  lastError?: Error;

  /**
   * Số lượng event đã nhận
   */
  eventsReceived: number;
}

/**
 * SSE Subscription
 */
export interface SSESubscription {
  /**
   * ID của subscription
   */
  id: string;

  /**
   * Loại event subscribe
   */
  eventType: string;

  /**
   * Callback xử lý event
   */
  handler: (event: SSEEvent) => void;

  /**
   * Có active không
   */
  active: boolean;

  /**
   * Thời gian tạo subscription
   */
  createdAt: Date;
}

/**
 * Options cho useSSE hook
 */
export interface UseSSEOptions extends Omit<SSEClientConfig, 'url'> {
  /**
   * Có tự động kết nối khi mount không
   */
  autoConnect?: boolean;

  /**
   * Có log debug không
   */
  debug?: boolean;
}

/**
 * Return type của useSSE hook
 */
export interface UseSSEReturn {
  /**
   * Thông tin kết nối
   */
  connectionInfo: SSEConnectionInfo;

  /**
   * Danh sách events đã nhận
   */
  events: SSEEvent[];

  /**
   * Event cuối cùng
   */
  lastEvent: SSEEvent | null;

  /**
   * Kết nối SSE
   */
  connect: () => void;

  /**
   * Ngắt kết nối SSE
   */
  disconnect: () => void;

  /**
   * Subscribe vào một loại event
   */
  subscribe: (eventType: string, handler: (event: SSEEvent) => void) => string;

  /**
   * Unsubscribe khỏi một subscription
   */
  unsubscribe: (subscriptionId: string) => void;

  /**
   * Clear tất cả events
   */
  clearEvents: () => void;

  /**
   * Gửi message (nếu server hỗ trợ)
   */
  send?: (data?: unknown) => void;
}

/**
 * SSE Context value
 */
export interface SSEContextValue {
  /**
   * Danh sách các kết nối SSE
   */
  connections: Map<string, UseSSEReturn>;

  /**
   * Tạo kết nối SSE mới
   */
  createConnection: (id: string, url: string, options?: UseSSEOptions) => UseSSEReturn;

  /**
   * Lấy kết nối SSE theo ID
   */
  getConnection: (id: string) => UseSSEReturn | undefined;

  /**
   * Xóa kết nối SSE
   */
  removeConnection: (id: string) => void;

  /**
   * Ngắt tất cả kết nối
   */
  disconnectAll: () => void;
}

/**
 * SSE Task cho TaskQueue
 */
export interface SSETask {
  /**
   * ID của task
   */
  id: string;

  /**
   * Loại task
   */
  type: 'sse_event';

  /**
   * Event data
   */
  event: SSEEvent;

  /**
   * Handler để xử lý event
   */
  handler: (event: SSEEvent) => Promise<void> | void;

  /**
   * Trạng thái task
   */
  status: 'pending' | 'processing' | 'completed' | 'failed';

  /**
   * Thời gian tạo
   */
  createdAt: Date;

  /**
   * Thời gian hoàn thành
   */
  completedAt?: Date;

  /**
   * Lỗi (nếu có)
   */
  error?: Error;
}

/**
 * SSE Event patterns cho filtering
 */
export interface SSEEventPattern {
  /**
   * Pattern cho event type
   */
  type?: string | RegExp;

  /**
   * Pattern cho data
   */
  data?: unknown;

  /**
   * Custom filter function
   */
  filter?: (event: SSEEvent) => boolean;
}

/**
 * SSE Metrics
 */
export interface SSEMetrics {
  /**
   * Tổng số events đã nhận
   */
  totalEvents: number;

  /**
   * Số events theo loại
   */
  eventsByType: Record<string, number>;

  /**
   * Thời gian kết nối trung bình
   */
  averageConnectionTime: number;

  /**
   * Số lần reconnect
   */
  reconnectCount: number;

  /**
   * Số lỗi
   */
  errorCount: number;

  /**
   * Bandwidth sử dụng (bytes)
   */
  bytesReceived: number;
}
