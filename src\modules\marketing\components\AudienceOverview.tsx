import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, CollapsibleCard, Icon, Chip } from '@/shared/components/common';
import ListOverviewCard from '@/shared/components/widgets/ListOverviewCard/ListOverviewCard';
import { Users, Tag, Activity } from 'lucide-react';
import { Audience } from '../types/audience.types';

interface AudienceOverviewProps {
  audience: Audience;
}

/**
 * Component hiển thị tổng quan về audience
 */
const AudienceOverview: React.FC<AudienceOverviewProps> = ({ audience }) => {
  const { t } = useTranslation(['marketing', 'common']);

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('vi-VN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'danger';
      case 'draft':
        return 'warning';
      default:
        return 'default';
    }
  };

  // Get type display name
  const getTypeDisplayName = (type: string) => {
    switch (type) {
      case 'customer':
        return t('marketing:audience.type.customer');
      case 'lead':
        return t('marketing:audience.type.lead');
      case 'subscriber':
        return t('marketing:audience.type.subscriber');
      case 'custom':
        return t('marketing:audience.type.custom');
      default:
        return type;
    }
  };

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('marketing:audience.detail.overview')}
        </Typography>
      }
      defaultOpen={true}
    >
      <div className="space-y-8">
        {/* Thông tin cơ bản */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('marketing:audience.overview.basicInfo')}
          </Typography>
          <ListOverviewCard
            items={[
              {
                title: t('marketing:audience.detail.totalContacts'),
                value: audience.totalContacts || 0,
                icon: Users,
                color: 'blue',
              },
              {
                title: t('marketing:audience.detail.type'),
                value: getTypeDisplayName(audience.type),
                icon: Tag,
                color: 'purple',
              },
              {
                title: t('marketing:audience.detail.status'),
                value: t(`common:${audience.status}`),
                customValue: (
                  <Chip
                    variant={getStatusColor(audience.status) as 'success' | 'danger' | 'warning' | 'default'}
                    size="sm"
                  >
                    {t(`common:${audience.status}`)}
                  </Chip>
                ),
                icon: Activity,
                color: 'green',
              },
            ]}
            maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}
          />
        </div>

        {/* Thông tin liên hệ */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('marketing:audience.overview.contactInfo')}
          </Typography>
          <div className="space-y-3">
            {audience.email && (
              <div className="flex items-center space-x-3 p-3 bg-muted/20 rounded-lg">
                <div className="p-2 rounded-full bg-primary/10">
                  <Icon name="mail" size="sm" className="text-primary" />
                </div>
                <div>
                  <Typography variant="body2" className="text-muted">
                    {t('common:email')}
                  </Typography>
                  <Typography variant="body1" className="text-foreground font-medium">
                    {audience.email}
                  </Typography>
                </div>
              </div>
            )}

            {audience.phone && (
              <div className="flex items-center space-x-3 p-3 bg-muted/20 rounded-lg">
                <div className="p-2 rounded-full bg-info/10">
                  <Icon name="phone" size="sm" className="text-info" />
                </div>
                <div>
                  <Typography variant="body2" className="text-muted">
                    {t('common:phone')}
                  </Typography>
                  <Typography variant="body1" className="text-foreground font-medium">
                    {audience.countryCode ? `${audience.countryCode} ${audience.phone}` : audience.phone}
                  </Typography>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tags */}
        {audience.tagIds && audience.tagIds.length > 0 && (
          <div>
            <Typography variant="subtitle1" className="text-foreground mb-4">
              {t('marketing:audience.overview.tags')}
            </Typography>
            <div className="flex flex-wrap gap-2">
              {audience.tagIds.map((tagId, index) => (
                <Chip key={index} variant="secondary" size="sm">
                  Tag {tagId}
                </Chip>
              ))}
            </div>
          </div>
        )}

        {/* Thông tin thời gian */}
        <div>
          <Typography variant="subtitle1" className="text-foreground mb-4">
            {t('marketing:audience.overview.timeInfo')}
          </Typography>
          <div className="space-y-3">
            <div className="flex items-center space-x-3 p-3 bg-muted/20 rounded-lg">
              <div className="p-2 rounded-full bg-success/10">
                <Icon name="calendar" size="sm" className="text-success" />
              </div>
              <div>
                <Typography variant="body2" className="text-muted">
                  {t('common:createdAt')}
                </Typography>
                <Typography variant="body1" className="text-foreground font-medium">
                  {formatDate(audience.createdAt)}
                </Typography>
              </div>
            </div>

            <div className="flex items-center space-x-3 p-3 bg-muted/20 rounded-lg">
              <div className="p-2 rounded-full bg-warning/10">
                <Icon name="clock" size="sm" className="text-warning" />
              </div>
              <div>
                <Typography variant="body2" className="text-muted">
                  {t('common:updatedAt')}
                </Typography>
                <Typography variant="body1" className="text-foreground font-medium">
                  {formatDate(audience.updatedAt)}
                </Typography>
              </div>
            </div>
          </div>
        </div>


      </div>
    </CollapsibleCard>
  );
};

export default AudienceOverview;
