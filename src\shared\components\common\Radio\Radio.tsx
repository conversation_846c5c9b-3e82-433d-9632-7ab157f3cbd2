import React, { forwardRef, useMemo } from 'react';
import { useTheme } from '@/shared/contexts/theme';
import { useFormControl } from '@/shared/components/common/Form/hooks/useFormControl';
import {
  FormControlColor,
  FormControlSize,
  RadioVariant,
  getRadioVariantClasses,
} from '@/shared/components/common/Form/utils/formControlUtils';

export type { RadioVariant } from '@/shared/components/common/Form/utils/formControlUtils';
export type { FormControlColor as RadioColor } from '@/shared/components/common/Form/utils/formControlUtils';

export interface RadioProps {
  /**
   * Nhãn hiển thị bên cạnh radio button
   */
  label?: string | React.ReactNode;

  /**
   * Trạng thái checked của radio button
   */
  checked?: boolean;

  /**
   * Callback khi trạng thái radio button thay đổi
   */
  onChange?: (checked: boolean) => void;

  /**
   * Giá trị của radio button, thường dùng trong RadioGroup
   */
  value?: string | number | readonly string[];

  /**
   * Vô hiệu hóa radio button
   */
  disabled?: boolean;

  /**
   * CSS classes tùy chỉnh
   */
  className?: string;

  /**
   * ID của radio button
   */
  id?: string;

  /**
   * Tên của radio button, dùng khi submit form
   */
  name?: string | undefined;

  /**
   * Kích thước của radio button
   */
  size?: FormControlSize;

  /**
   * Biến thể của radio button
   */
  variant?: RadioVariant;

  /**
   * Màu sắc của radio button
   */
  color?: FormControlColor;

  /**
   * Callback khi blur, thường dùng với React Hook Form
   */
  onBlur?: () => void;
}

/**
 * Component Radio cho phép người dùng chọn một tùy chọn từ một danh sách
 */
const Radio = forwardRef<HTMLInputElement, RadioProps>(
  (
    {
      label,
      checked = false,
      onChange,
      value,
      disabled = false,
      className = '',
      id,
      name,
      size = 'md',
      variant = 'default',
      color = 'primary',
      onBlur,
      ...rest
    },
    ref
  ) => {
    useTheme(); // Sử dụng hook theme mới

    // Sử dụng custom hook để xử lý logic chung
    const {
      handleChange,
      sizeClasses,
      dotSizeClasses,
      labelSizeClasses,
      colorClasses,
      ariaAttributes,
    } = useFormControl({
      checked,
      ...(onChange && { onChange }),
      disabled,
      size,
      color,
      ...(onBlur && { onBlur }),
    });

    // Memoize variant classes
    const variantClasses = useMemo(() => getRadioVariantClasses(variant), [variant]);

    return (
      <label
        className={`inline-flex items-center ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'} ${className}`}
      >
        <div className="relative flex items-center">
          <input
            type="radio"
            ref={ref}
            checked={checked}
            onChange={handleChange}
            disabled={disabled}
            className={`
            appearance-none
            ${sizeClasses}
            border ${variant === 'outlined' ? 'border-2' : 'border'} ${checked ? colorClasses.border : 'border-border'}
            rounded-full
            ${variant === 'filled' && checked ? colorClasses.filledBg : 'bg-card-muted'}
            ${variant === 'filled' && checked ? colorClasses.filledText : ''}
            focus:outline-none
            ${variantClasses}
            transition-colors
          `}
            id={id}
            name={name}
            value={value}
            {...ariaAttributes}
            {...rest}
          />

          {/* Custom radio dot */}
          {checked && (
            <div
              className={`
            absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2
            ${dotSizeClasses} ${variant === 'filled' ? 'bg-primary-foreground' : colorClasses.dot} rounded-full
          `}
            ></div>
          )}
        </div>

        {label && (
          <span
            className={`ml-2 ${labelSizeClasses} ${disabled ? 'text-muted' : 'text-foreground'}`}
          >
            {label}
          </span>
        )}
      </label>
    );
  }
);

Radio.displayName = 'Radio';

export default Radio;
