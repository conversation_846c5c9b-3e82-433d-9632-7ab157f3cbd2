import { useState } from 'react';

/**
 * Hook quản lý trạng thái hiển thị/ẩn form với animation
 * @returns Các hàm và state để quản lý form
 */
export const useSlideForm = <T = unknown>(initialState = false) => {
  // State quản lý việc hiển thị form
  const [isVisible, setIsVisible] = useState(initialState);

  // State quản lý dữ liệu form
  const [formData, setFormData] = useState<T | null>(null);

  // Hàm hiển thị form
  const showForm = () => setIsVisible(true);

  // Hàm ẩn form
  const hideForm = () => setIsVisible(false);

  // Hàm toggle form
  const toggleForm = () => setIsVisible(prev => !prev);

  return {
    isVisible,
    formData,
    setFormData,
    showForm,
    hideForm,
    toggleForm,
  };
};

export default useSlideForm;
