# 📋 **External Agent Integration - Implementation Documentation**

## 🎯 **Overview**

Tài liệu này mô tả chi tiết việc triển khai tính năng tích hợp Agent hệ thống khác vào RedAI, hỗ trợ các chuẩn hiện đại như MCP (Model Context Protocol) và Google Agent-to-Agent Communication.

---

## 🏗️ **BACKEND IMPLEMENTATION**

### **1. Database Schema**

#### **1.1 External Agents Table**
```sql
-- Bảng quản lý các Agent bên ngoài
CREATE TABLE external_agents (
    -- Kh<PERSON>a chính và định danh
    id UUID PRIMARY KEY DEFAULT gen_random_uuid()
        COMMENT 'ID duy nhất của agent bên ngoài',
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE
        COMMENT 'ID người dùng sở hữu agent nà<PERSON>',
    name VARCHAR(255) NOT NULL
        COMMENT 'Tên định danh agent (dùng trong code, không dấu, viết thường)',
    display_name VARCHAR(255) NOT NULL
        COMMENT 'Tên hiển thị của agent (có thể có dấu, viết hoa)',
    description TEXT
        COMMENT 'Mô tả chi tiết về chức năng và mục đích của agent',
    avatar_url VARCHAR(500)
        COMMENT 'URL ảnh đại diện của agent',

    -- Cấu hình giao thức kết nối
    protocol_standard VARCHAR(50) NOT NULL CHECK (protocol_standard IN ('mcp', 'google-agent', 'openai-assistant', 'anthropic-computer-use', 'custom-rest', 'webhook'))
        COMMENT 'Chuẩn giao thức sử dụng (MCP, Google Agent, OpenAI Assistant, v.v.)',
    protocol_version VARCHAR(20) NOT NULL DEFAULT '1.0'
        COMMENT 'Phiên bản của giao thức',
    base_url VARCHAR(500) NOT NULL
        COMMENT 'URL gốc để kết nối với agent bên ngoài',

    -- Cấu hình xác thực
    auth_type VARCHAR(50) NOT NULL CHECK (auth_type IN ('api-key', 'oauth2', 'bearer', 'basic', 'custom'))
        COMMENT 'Loại xác thực (API Key, OAuth2, Bearer Token, Basic Auth, Custom)',
    auth_credentials JSONB NOT NULL DEFAULT '{}'
        COMMENT 'Thông tin xác thực (API key, token, username/password, v.v.) - được mã hóa',

    -- Khả năng của agent
    capabilities JSONB NOT NULL DEFAULT '{}'
        COMMENT 'Danh sách khả năng của agent (chat, streaming, function calling, v.v.)',

    -- Cài đặt bổ sung
    settings JSONB NOT NULL DEFAULT '{}'
        COMMENT 'Các cài đặt khác (timeout, retry, rate limit, webhook config, v.v.)',

    -- Trạng thái và metrics
    status VARCHAR(20) NOT NULL DEFAULT 'inactive' CHECK (status IN ('active', 'inactive', 'testing', 'error'))
        COMMENT 'Trạng thái hiện tại của agent (hoạt động, không hoạt động, đang test, lỗi)',
    last_tested_at TIMESTAMP
        COMMENT 'Thời điểm test kết nối gần nhất',
    test_result JSONB
        COMMENT 'Kết quả test kết nối gần nhất (success, error message, response time)',

    -- Thông tin metadata
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm tạo agent',
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm cập nhật thông tin agent gần nhất',
    last_active_at TIMESTAMP
        COMMENT 'Thời điểm agent hoạt động gần nhất (gửi/nhận message)',

    -- Ràng buộc duy nhất
    UNIQUE(user_id, name)
        COMMENT 'Mỗi user chỉ có thể có một agent với tên duy nhất'
);

-- Tạo các index để tối ưu truy vấn
CREATE INDEX idx_external_agents_user_id ON external_agents(user_id)
    COMMENT 'Index cho truy vấn theo user_id';
CREATE INDEX idx_external_agents_status ON external_agents(status)
    COMMENT 'Index cho truy vấn theo trạng thái';
CREATE INDEX idx_external_agents_protocol ON external_agents(protocol_standard)
    COMMENT 'Index cho truy vấn theo loại giao thức';
```

#### **1.2 Agent Bridges Table**
```sql
-- Bảng quản lý cầu nối giữa các Agent
CREATE TABLE agent_bridges (
    -- Khóa chính và định danh
    id UUID PRIMARY KEY DEFAULT gen_random_uuid()
        COMMENT 'ID duy nhất của cầu nối agent',
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE
        COMMENT 'ID người dùng sở hữu cầu nối này',
    name VARCHAR(255) NOT NULL
        COMMENT 'Tên định danh của cầu nối',

    -- Cấu hình cầu nối
    source_agent_id UUID REFERENCES agents(id) ON DELETE CASCADE
        COMMENT 'ID agent nguồn (agent nội bộ RedAI)',
    target_agent_id UUID REFERENCES external_agents(id) ON DELETE CASCADE
        COMMENT 'ID agent đích (agent bên ngoài)',
    bridge_type VARCHAR(20) NOT NULL CHECK (bridge_type IN ('proxy', 'relay', 'transform'))
        COMMENT 'Loại cầu nối: proxy (chuyển tiếp trực tiếp), relay (chuyển tiếp có xử lý), transform (biến đổi dữ liệu)',

    -- Quy tắc và cấu hình
    transform_rules JSONB DEFAULT '[]'
        COMMENT 'Danh sách quy tắc biến đổi dữ liệu khi chuyển tiếp message',
    routing_rules JSONB DEFAULT '[]'
        COMMENT 'Quy tắc định tuyến message (điều kiện, ưu tiên, filter)',
    fallback_config JSONB DEFAULT '{}'
        COMMENT 'Cấu hình dự phòng khi agent đích không khả dụng',

    -- Trạng thái
    is_active BOOLEAN NOT NULL DEFAULT true
        COMMENT 'Trạng thái hoạt động của cầu nối (true: đang hoạt động, false: tạm dừng)',

    -- Thông tin metadata
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm tạo cầu nối',
    updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm cập nhật cấu hình cầu nối gần nhất',

    -- Ràng buộc duy nhất
    UNIQUE(user_id, name)
        COMMENT 'Mỗi user chỉ có thể có một cầu nối với tên duy nhất'
);

-- Tạo các index để tối ưu truy vấn
CREATE INDEX idx_agent_bridges_user_id ON agent_bridges(user_id)
    COMMENT 'Index cho truy vấn theo user_id';
CREATE INDEX idx_agent_bridges_source_agent ON agent_bridges(source_agent_id)
    COMMENT 'Index cho truy vấn theo agent nguồn';
CREATE INDEX idx_agent_bridges_target_agent ON agent_bridges(target_agent_id)
    COMMENT 'Index cho truy vấn theo agent đích';
```

#### **1.3 Agent Messages Table**
```sql
-- Bảng lưu trữ lịch sử tin nhắn giữa các Agent
CREATE TABLE agent_messages (
    -- Khóa chính và định danh
    id UUID PRIMARY KEY DEFAULT gen_random_uuid()
        COMMENT 'ID duy nhất của tin nhắn',
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE
        COMMENT 'ID người dùng sở hữu tin nhắn này',

    -- Định tuyến tin nhắn
    source_agent_id UUID
        COMMENT 'ID agent gửi tin nhắn (có thể là agent nội bộ hoặc bên ngoài)',
    target_agent_id UUID REFERENCES external_agents(id) ON DELETE CASCADE
        COMMENT 'ID agent nhận tin nhắn (agent bên ngoài)',
    bridge_id UUID REFERENCES agent_bridges(id) ON DELETE SET NULL
        COMMENT 'ID cầu nối được sử dụng (nếu có)',

    -- Nội dung tin nhắn
    message_type VARCHAR(50) NOT NULL
        COMMENT 'Loại tin nhắn (text, image, file, function_call, system, v.v.)',
    content JSONB NOT NULL
        COMMENT 'Nội dung tin nhắn (text, data, parameters, v.v.)',
    metadata JSONB DEFAULT '{}'
        COMMENT 'Metadata bổ sung (conversation_id, thread_id, priority, v.v.)',

    -- Phản hồi
    response JSONB
        COMMENT 'Phản hồi từ agent đích (nội dung, kết quả, lỗi)',
    response_time_ms INTEGER
        COMMENT 'Thời gian phản hồi tính bằng milliseconds',

    -- Trạng thái
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'timeout'))
        COMMENT 'Trạng thái tin nhắn: pending (chờ gửi), sent (đã gửi), delivered (đã nhận), failed (thất bại), timeout (hết thời gian)',
    error_message TEXT
        COMMENT 'Thông báo lỗi chi tiết (nếu có)',

    -- Thời gian
    created_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm tạo tin nhắn',
    sent_at TIMESTAMP
        COMMENT 'Thời điểm gửi tin nhắn',
    delivered_at TIMESTAMP
        COMMENT 'Thời điểm nhận được phản hồi'
);

-- Tạo các index để tối ưu truy vấn
CREATE INDEX idx_agent_messages_user_id ON agent_messages(user_id)
    COMMENT 'Index cho truy vấn theo user_id';
CREATE INDEX idx_agent_messages_target_agent ON agent_messages(target_agent_id)
    COMMENT 'Index cho truy vấn theo agent đích';
CREATE INDEX idx_agent_messages_status ON agent_messages(status)
    COMMENT 'Index cho truy vấn theo trạng thái';
CREATE INDEX idx_agent_messages_created_at ON agent_messages(created_at)
    COMMENT 'Index cho truy vấn theo thời gian tạo';
```

#### **1.4 Protocol Capabilities Table**
```sql
-- Bảng lưu trữ khả năng chi tiết của các Agent theo từng giao thức
CREATE TABLE protocol_capabilities (
    -- Khóa chính và liên kết
    id UUID PRIMARY KEY DEFAULT gen_random_uuid()
        COMMENT 'ID duy nhất của bản ghi khả năng',
    external_agent_id UUID NOT NULL REFERENCES external_agents(id) ON DELETE CASCADE
        COMMENT 'ID agent bên ngoài mà khả năng này thuộc về',

    -- Khả năng MCP (Model Context Protocol)
    mcp_tools BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ MCP Tools - khả năng sử dụng các công cụ thông qua MCP',
    mcp_resources BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ MCP Resources - khả năng truy cập tài nguyên thông qua MCP',
    mcp_prompts BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ MCP Prompts - khả năng sử dụng prompt templates',
    mcp_sampling BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ MCP Sampling - khả năng lấy mẫu dữ liệu',
    mcp_logging BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ MCP Logging - khả năng ghi log thông qua MCP',

    -- Khả năng Google Agent
    google_reasoning BOOLEAN DEFAULT false
        COMMENT 'Khả năng suy luận logic của Google Agent',
    google_planning BOOLEAN DEFAULT false
        COMMENT 'Khả năng lập kế hoạch và phân tích task',
    google_tool_use BOOLEAN DEFAULT false
        COMMENT 'Khả năng sử dụng công cụ bên ngoài',
    google_memory BOOLEAN DEFAULT false
        COMMENT 'Khả năng lưu trữ và truy xuất bộ nhớ',
    google_collaboration BOOLEAN DEFAULT false
        COMMENT 'Khả năng cộng tác với các agent khác',

    -- Khả năng chung
    chat BOOLEAN DEFAULT false
        COMMENT 'Khả năng chat/trò chuyện cơ bản',
    streaming BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ streaming response (phản hồi theo thời gian thực)',
    multimodal BOOLEAN DEFAULT false
        COMMENT 'Hỗ trợ đa phương tiện (text, image, audio, video)',
    file_handling BOOLEAN DEFAULT false
        COMMENT 'Khả năng xử lý file (upload, download, parse)',
    function_calling BOOLEAN DEFAULT false
        COMMENT 'Khả năng gọi function/API bên ngoài',

    -- Thông tin metadata
    discovered_at TIMESTAMP NOT NULL DEFAULT NOW()
        COMMENT 'Thời điểm phát hiện các khả năng này',
    last_verified_at TIMESTAMP
        COMMENT 'Thời điểm xác minh khả năng gần nhất',

    -- Ràng buộc duy nhất
    UNIQUE(external_agent_id)
        COMMENT 'Mỗi agent chỉ có một bản ghi khả năng'
);

-- Tạo index để tối ưu truy vấn
CREATE INDEX idx_protocol_capabilities_agent_id ON protocol_capabilities(external_agent_id)
    COMMENT 'Index cho truy vấn theo external_agent_id';
CREATE INDEX idx_protocol_capabilities_mcp ON protocol_capabilities(mcp_tools, mcp_resources, mcp_prompts)
    COMMENT 'Index cho truy vấn khả năng MCP';
CREATE INDEX idx_protocol_capabilities_google ON protocol_capabilities(google_reasoning, google_planning, google_collaboration)
    COMMENT 'Index cho truy vấn khả năng Google Agent';
CREATE INDEX idx_protocol_capabilities_common ON protocol_capabilities(chat, streaming, multimodal, function_calling)
    COMMENT 'Index cho truy vấn khả năng chung';
```

### **2. Backend API Structure**

#### **2.1 Controller Layer**
```typescript
// src/modules/external-agents/controllers/external-agent.controller.ts
import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@/auth/decorators/current-user.decorator';
import { ExternalAgentService } from '../services/external-agent.service';
import {
  CreateExternalAgentDto,
  UpdateExternalAgentDto,
  ExternalAgentQueryDto,
  TestExternalAgentDto,
  SendMessageDto
} from '../dto';

@ApiTags('External Agents')
@Controller('user/external-agents')
@UseGuards(JwtAuthGuard)
export class ExternalAgentController {
  constructor(private readonly externalAgentService: ExternalAgentService) {}

  @Get()
  @ApiOperation({ summary: 'Get external agents list' })
  async getExternalAgents(
    @CurrentUser('id') userId: number,
    @Query() query: ExternalAgentQueryDto
  ) {
    return this.externalAgentService.getExternalAgents(userId, query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get external agent detail' })
  async getExternalAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string
  ) {
    return this.externalAgentService.getExternalAgent(userId, id);
  }

  @Post()
  @ApiOperation({ summary: 'Create external agent' })
  async createExternalAgent(
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateExternalAgentDto
  ) {
    return this.externalAgentService.createExternalAgent(userId, createDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update external agent' })
  async updateExternalAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
    @Body() updateDto: UpdateExternalAgentDto
  ) {
    return this.externalAgentService.updateExternalAgent(userId, id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete external agent' })
  async deleteExternalAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string
  ) {
    return this.externalAgentService.deleteExternalAgent(userId, id);
  }

  @Post(':id/test')
  @ApiOperation({ summary: 'Test external agent connection' })
  async testExternalAgent(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
    @Body() testDto: TestExternalAgentDto
  ) {
    return this.externalAgentService.testExternalAgent(userId, id, testDto);
  }

  @Post(':id/message')
  @ApiOperation({ summary: 'Send message to external agent' })
  async sendMessage(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
    @Body() messageDto: SendMessageDto
  ) {
    return this.externalAgentService.sendMessage(userId, id, messageDto);
  }

  @Get(':id/capabilities')
  @ApiOperation({ summary: 'Get agent capabilities' })
  async getCapabilities(
    @CurrentUser('id') userId: number,
    @Param('id') id: string
  ) {
    return this.externalAgentService.getCapabilities(userId, id);
  }

  @Post(':id/webhook')
  @ApiOperation({ summary: 'Setup webhook for external agent' })
  async setupWebhook(
    @CurrentUser('id') userId: number,
    @Param('id') id: string,
    @Body() webhookConfig: any
  ) {
    return this.externalAgentService.setupWebhook(userId, id, webhookConfig);
  }
}
```

### **3. Protocol Implementation**

#### **3.1 MCP Protocol Client**
```typescript
// src/modules/external-agents/protocols/mcp/mcp-client.ts
export class MCPClient {
  private transport: MCPTransport;
  private capabilities: MCPCapabilities;

  constructor(config: MCPClientConfig) {
    this.transport = this.createTransport(config.transport);
    this.capabilities = config.capabilities;
  }

  async initialize(): Promise<MCPInitializeResult> {
    const initRequest: MCPInitializeRequest = {
      jsonrpc: "2.0",
      id: this.generateId(),
      method: "initialize",
      params: {
        protocolVersion: "2024-11-05",
        capabilities: this.capabilities,
        clientInfo: {
          name: "RedAI",
          version: "1.0.0"
        }
      }
    };

    const response = await this.transport.send(initRequest);
    return response.result;
  }

  async listTools(): Promise<MCPTool[]> {
    const request: MCPRequest = {
      jsonrpc: "2.0",
      id: this.generateId(),
      method: "tools/list"
    };

    const response = await this.transport.send(request);
    return response.result.tools;
  }

  async callTool(name: string, arguments_: Record<string, unknown>): Promise<MCPToolResult> {
    const request: MCPToolCallRequest = {
      jsonrpc: "2.0",
      id: this.generateId(),
      method: "tools/call",
      params: {
        name,
        arguments: arguments_
      }
    };

    const response = await this.transport.send(request);
    return response.result;
  }
}
```

#### **3.2 Google Agent Protocol Client**
```typescript
// src/modules/external-agents/protocols/google-agent/google-agent-client.ts
export class GoogleAgentClient {
  private grpcClient: GrpcClient;
  private capabilities: GoogleAgentCapabilities;

  constructor(config: GoogleAgentConfig) {
    this.grpcClient = new GrpcClient(config.endpoint, config.credentials);
    this.capabilities = config.capabilities;
  }

  async establishConnection(): Promise<AgentConnectionResult> {
    const handshake: AgentHandshake = {
      agent_id: this.agentId,
      capabilities: this.capabilities,
      protocol_version: "1.0",
      security_context: this.securityContext
    };

    return await this.grpcClient.call('EstablishConnection', handshake);
  }

  async sendMessage(message: AgentMessage): Promise<AgentResponse> {
    const request: AgentMessageRequest = {
      target_agent_id: message.targetAgentId,
      message_type: message.type,
      payload: message.payload,
      context: {
        conversation_id: message.conversationId,
        task_id: message.taskId,
        priority: message.priority
      },
      coordination: {
        requires_consensus: message.requiresConsensus,
        timeout_ms: message.timeoutMs
      }
    };

    return await this.grpcClient.call('SendMessage', request);
  }
}
```

---

## 🎨 **FRONTEND IMPLEMENTATION**

### **1. Module Structure**
```
src/modules/external-agents/
├── types/
│   ├── external-agent.types.ts
│   ├── protocol.types.ts
│   └── bridge.types.ts
├── services/
│   ├── external-agent.service.ts
│   ├── protocol-adapter.service.ts
│   └── agent-bridge.service.ts
├── hooks/
│   ├── useExternalAgents.ts
│   ├── useAgentBridge.ts
│   └── useProtocolDetection.ts
├── components/
│   ├── ExternalAgentCard.tsx
│   ├── ExternalAgentForm.tsx
│   ├── ProtocolSelector.tsx
│   ├── AgentBridgeForm.tsx
│   └── AgentDashboard.tsx
├── pages/
│   ├── ExternalAgentsPage.tsx
│   └── AgentBridgesPage.tsx
└── index.ts
```

### **2. Type Definitions**
```typescript
// src/modules/external-agents/types/external-agent.types.ts
export enum ProtocolStandard {
  MCP = 'mcp',
  GOOGLE_AGENT = 'google-agent',
  OPENAI_ASSISTANT = 'openai-assistant',
  ANTHROPIC_COMPUTER_USE = 'anthropic-computer-use',
  CUSTOM_REST = 'custom-rest',
  WEBHOOK = 'webhook'
}

export interface ExternalAgent {
  id: string;
  name: string;
  displayName: string;
  description: string;
  avatarUrl?: string;
  protocolStandard: ProtocolStandard;
  protocolVersion: string;
  baseUrl: string;
  authType: AuthType;
  authCredentials: Record<string, string>;
  capabilities: AgentCapabilities;
  settings: AgentSettings;
  status: AgentStatus;
  lastTestedAt?: string;
  testResult?: TestResult;
  createdAt: string;
  updatedAt: string;
  lastActiveAt?: string;
}

export interface AgentCapabilities {
  // MCP Capabilities
  mcp?: {
    tools?: boolean;
    resources?: boolean;
    prompts?: boolean;
    sampling?: boolean;
    logging?: boolean;
  };
  
  // Google Agent Capabilities
  googleAgent?: {
    reasoning: boolean;
    planning: boolean;
    toolUse: boolean;
    memory: boolean;
    collaboration: boolean;
  };
  
  // Common Capabilities
  common: {
    chat: boolean;
    streaming: boolean;
    multimodal: boolean;
    fileHandling: boolean;
    functionCalling: boolean;
  };
}

export interface CreateExternalAgentDto {
  name: string;
  displayName: string;
  description?: string;
  protocolStandard: ProtocolStandard;
  protocolVersion?: string;
  baseUrl: string;
  authType: AuthType;
  authCredentials: Record<string, string>;
  capabilities?: AgentCapabilities;
  settings?: AgentSettings;
}

export interface UpdateExternalAgentDto extends Partial<CreateExternalAgentDto> {}

export interface ExternalAgentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: AgentStatus;
  protocolStandard?: ProtocolStandard;
}

export interface TestExternalAgentDto {
  testMessage?: string;
  testCapabilities?: boolean;
}

export interface SendMessageDto {
  content: string;
  type?: string;
  metadata?: Record<string, unknown>;
}

export type AgentStatus = 'active' | 'inactive' | 'testing' | 'error';
export type AuthType = 'api-key' | 'oauth2' | 'bearer' | 'basic' | 'custom';

export interface AgentSettings {
  timeout?: number;
  retryAttempts?: number;
  rateLimits?: RateLimitConfig;
  webhook?: WebhookConfig;
}

export interface TestResult {
  success: boolean;
  message?: string;
  error?: string;
  responseTime?: number;
  timestamp: string;
  capabilities?: AgentCapabilities;
}
```

### **3. Service Layer**
```typescript
// src/modules/external-agents/services/external-agent.service.ts
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import type {
  ExternalAgent,
  CreateExternalAgentDto,
  UpdateExternalAgentDto,
  ExternalAgentQueryParams,
  TestExternalAgentDto,
  SendMessageDto,
  TestResult,
  AgentCapabilities
} from '../types';

export class ExternalAgentService {
  private static readonly BASE_URL = '/user/external-agents';

  static async getExternalAgents(
    params?: ExternalAgentQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<ExternalAgent>>> {
    return apiClient.get(this.BASE_URL, { params });
  }

  static async getExternalAgent(id: string): Promise<ApiResponseDto<ExternalAgent>> {
    return apiClient.get(`${this.BASE_URL}/${id}`);
  }

  static async createExternalAgent(
    data: CreateExternalAgentDto
  ): Promise<ApiResponseDto<ExternalAgent>> {
    return apiClient.post(this.BASE_URL, data);
  }

  static async updateExternalAgent(
    id: string,
    data: UpdateExternalAgentDto
  ): Promise<ApiResponseDto<ExternalAgent>> {
    return apiClient.put(`${this.BASE_URL}/${id}`, data);
  }

  static async deleteExternalAgent(id: string): Promise<ApiResponseDto<{ success: boolean }>> {
    return apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  static async testExternalAgent(
    id: string,
    testData?: TestExternalAgentDto
  ): Promise<ApiResponseDto<TestResult>> {
    return apiClient.post(`${this.BASE_URL}/${id}/test`, testData);
  }

  static async sendMessage(
    id: string,
    message: SendMessageDto
  ): Promise<ApiResponseDto<any>> {
    return apiClient.post(`${this.BASE_URL}/${id}/message`, message);
  }

  static async getCapabilities(id: string): Promise<ApiResponseDto<AgentCapabilities>> {
    return apiClient.get(`${this.BASE_URL}/${id}/capabilities`);
  }

  static async setupWebhook(
    id: string,
    webhookConfig: any
  ): Promise<ApiResponseDto<{ webhookUrl: string }>> {
    return apiClient.post(`${this.BASE_URL}/${id}/webhook`, webhookConfig);
  }

  static async detectProtocol(endpoint: string): Promise<ApiResponseDto<string[]>> {
    return apiClient.post('/user/external-agents/detect-protocol', { endpoint });
  }
}
```

### **4. Hooks Layer**
```typescript
// src/modules/external-agents/hooks/useExternalAgents.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ExternalAgentService } from '../services/external-agent.service';
import { NotificationUtil } from '@/shared/utils/notification.util';
import type {
  ExternalAgentQueryParams,
  CreateExternalAgentDto,
  UpdateExternalAgentDto,
  TestExternalAgentDto,
  SendMessageDto
} from '../types';

export const EXTERNAL_AGENT_QUERY_KEYS = {
  ALL: ['external-agents'] as const,
  LIST: (params: ExternalAgentQueryParams) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'detail', id] as const,
  CAPABILITIES: (id: string) => [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'capabilities', id] as const,
};

export function useExternalAgents(params?: ExternalAgentQueryParams) {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.LIST(params || {}),
    queryFn: () => ExternalAgentService.getExternalAgents(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useExternalAgent(id: string) {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.DETAIL(id),
    queryFn: () => ExternalAgentService.getExternalAgent(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCreateExternalAgent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateExternalAgentDto) =>
      ExternalAgentService.createExternalAgent(data),
    onSuccess: (response) => {
      NotificationUtil.success({
        message: 'External agent created successfully!',
        title: response.result.displayName,
      });
      queryClient.invalidateQueries({ queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to create external agent',
        title: error.message,
      });
    },
  });
}

export function useUpdateExternalAgent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateExternalAgentDto }) =>
      ExternalAgentService.updateExternalAgent(id, data),
    onSuccess: (response) => {
      NotificationUtil.success({
        message: 'External agent updated successfully!',
        title: response.result.displayName,
      });
      queryClient.invalidateQueries({ queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to update external agent',
        title: error.message,
      });
    },
  });
}

export function useDeleteExternalAgent() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ExternalAgentService.deleteExternalAgent(id),
    onSuccess: () => {
      NotificationUtil.success({
        message: 'External agent deleted successfully!',
      });
      queryClient.invalidateQueries({ queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to delete external agent',
        title: error.message,
      });
    },
  });
}

export function useTestExternalAgent() {
  return useMutation({
    mutationFn: ({ id, testData }: { id: string; testData?: TestExternalAgentDto }) =>
      ExternalAgentService.testExternalAgent(id, testData),
    onSuccess: (response) => {
      const result = response.result;
      if (result.success) {
        NotificationUtil.success({
          message: 'Agent connection test successful!',
          title: result.message || 'Connection established',
        });
      } else {
        NotificationUtil.warning({
          message: 'Agent connection test failed',
          title: result.error || 'Connection failed',
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to test agent connection',
        title: error.message,
      });
    },
  });
}

export function useSendMessageToAgent() {
  return useMutation({
    mutationFn: ({ id, message }: { id: string; message: SendMessageDto }) =>
      ExternalAgentService.sendMessage(id, message),
    onSuccess: (response) => {
      NotificationUtil.success({
        message: 'Message sent successfully!',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to send message',
        title: error.message,
      });
    },
  });
}

export function useAgentCapabilities(id: string) {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.CAPABILITIES(id),
    queryFn: () => ExternalAgentService.getCapabilities(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useProtocolDetection() {
  return useMutation({
    mutationFn: (endpoint: string) => ExternalAgentService.detectProtocol(endpoint),
    onSuccess: (response) => {
      const protocols = response.result;
      if (protocols.length > 0) {
        NotificationUtil.success({
          message: `Detected protocols: ${protocols.join(', ')}`,
          title: 'Protocol detection successful',
        });
      } else {
        NotificationUtil.warning({
          message: 'No supported protocols detected',
          title: 'Protocol detection',
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Failed to detect protocols',
        title: error.message,
      });
    },
  });
}
```

### **5. UI Components**

#### **5.1 External Agent Card**
```typescript
// src/modules/external-agents/components/ExternalAgentCard.tsx
import React from 'react';
import { Card, Typography, Badge, Button, Avatar, Icon } from '@/shared/components/common';
import { useTestExternalAgent, useSendMessageToAgent } from '../hooks/useExternalAgents';
import type { ExternalAgent } from '../types';

interface ExternalAgentCardProps {
  agent: ExternalAgent;
  onEdit?: (agent: ExternalAgent) => void;
  onDelete?: (agent: ExternalAgent) => void;
}

const ExternalAgentCard: React.FC<ExternalAgentCardProps> = ({
  agent,
  onEdit,
  onDelete
}) => {
  const testAgent = useTestExternalAgent();
  const sendMessage = useSendMessageToAgent();

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'inactive': return 'secondary';
      case 'testing': return 'warning';
      case 'error': return 'destructive';
      default: return 'secondary';
    }
  };

  const getProtocolIcon = (protocol: string) => {
    switch (protocol) {
      case 'mcp': return 'layers';
      case 'google-agent': return 'users';
      case 'openai-assistant': return 'robot';
      case 'anthropic-computer-use': return 'monitor';
      case 'webhook': return 'webhook';
      default: return 'settings';
    }
  };

  const handleTestConnection = () => {
    testAgent.mutate({
      id: agent.id,
      testData: { testMessage: 'Hello from RedAI!' }
    });
  };

  const handleSendMessage = () => {
    sendMessage.mutate({
      id: agent.id,
      message: {
        content: 'Hello from RedAI! This is a test message.',
        type: 'text'
      }
    });
  };

  return (
    <Card className="relative p-6">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <Avatar
            src={agent.avatarUrl}
            fallback={agent.displayName[0]}
            size="md"
          />
          <div>
            <Typography variant="h6" className="mb-1">
              {agent.displayName}
            </Typography>
            <div className="flex items-center gap-2">
              <Icon name={getProtocolIcon(agent.protocolStandard)} size="sm" />
              <Typography variant="body2" color="muted">
                {agent.protocolStandard.toUpperCase()} • {agent.baseUrl}
              </Typography>
            </div>
          </div>
        </div>
        <Badge variant={getStatusVariant(agent.status)}>
          {agent.status}
        </Badge>
      </div>

      {agent.description && (
        <Typography variant="body2" color="muted" className="mb-4">
          {agent.description}
        </Typography>
      )}

      {/* Capabilities */}
      <div className="mb-4">
        <Typography variant="body2" className="font-medium mb-2">
          Capabilities:
        </Typography>
        <div className="flex flex-wrap gap-1">
          {agent.capabilities.common.chat && (
            <Badge variant="outline" size="sm">Chat</Badge>
          )}
          {agent.capabilities.common.streaming && (
            <Badge variant="outline" size="sm">Streaming</Badge>
          )}
          {agent.capabilities.common.functionCalling && (
            <Badge variant="outline" size="sm">Function Calling</Badge>
          )}
          {agent.capabilities.common.multimodal && (
            <Badge variant="outline" size="sm">Multimodal</Badge>
          )}
          {agent.capabilities.mcp?.tools && (
            <Badge variant="outline" size="sm">MCP Tools</Badge>
          )}
          {agent.capabilities.googleAgent?.collaboration && (
            <Badge variant="outline" size="sm">Collaboration</Badge>
          )}
        </div>
      </div>

      {/* Test Results */}
      {agent.testResult && (
        <div className="mb-4 p-3 bg-muted rounded-md">
          <div className="flex items-center gap-2 mb-1">
            <Icon
              name={agent.testResult.success ? 'check-circle' : 'x-circle'}
              size="sm"
              className={agent.testResult.success ? 'text-green-500' : 'text-red-500'}
            />
            <Typography variant="body2" className="font-medium">
              Last Test: {agent.testResult.success ? 'Success' : 'Failed'}
            </Typography>
          </div>
          {agent.testResult.message && (
            <Typography variant="body2" color="muted">
              {agent.testResult.message}
            </Typography>
          )}
          {agent.testResult.responseTime && (
            <Typography variant="body2" color="muted">
              Response time: {agent.testResult.responseTime}ms
            </Typography>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleTestConnection}
          isLoading={testAgent.isPending}
          disabled={testAgent.isPending}
        >
          <Icon name="activity" size="sm" />
          Test Connection
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleSendMessage}
          isLoading={sendMessage.isPending}
          disabled={sendMessage.isPending || agent.status !== 'active'}
        >
          <Icon name="message-circle" size="sm" />
          Send Message
        </Button>

        {onEdit && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onEdit(agent)}
          >
            <Icon name="edit" size="sm" />
            Edit
          </Button>
        )}

        {onDelete && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(agent)}
            className="text-red-600 hover:text-red-700"
          >
            <Icon name="trash-2" size="sm" />
            Delete
          </Button>
        )}
      </div>
    </Card>
  );
};

export default ExternalAgentCard;
```

#### **5.2 External Agent Form**
```typescript
// src/modules/external-agents/components/ExternalAgentForm.tsx
import React, { useState, useEffect } from 'react';
import {
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  Button,
  Card,
  Typography,
  Checkbox
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { useProtocolDetection } from '../hooks/useExternalAgents';
import type {
  CreateExternalAgentDto,
  UpdateExternalAgentDto,
  ProtocolStandard,
  AuthType,
  ExternalAgent
} from '../types';

interface ExternalAgentFormProps {
  agent?: ExternalAgent;
  onSubmit: (data: CreateExternalAgentDto | UpdateExternalAgentDto) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ExternalAgentForm: React.FC<ExternalAgentFormProps> = ({
  agent,
  onSubmit,
  onCancel,
  isLoading = false
}) => {
  const { formRef, setFormErrors } = useFormErrors<CreateExternalAgentDto>();
  const protocolDetection = useProtocolDetection();

  const [formData, setFormData] = useState<CreateExternalAgentDto>({
    name: agent?.name || '',
    displayName: agent?.displayName || '',
    description: agent?.description || '',
    protocolStandard: agent?.protocolStandard || ProtocolStandard.MCP,
    protocolVersion: agent?.protocolVersion || '1.0',
    baseUrl: agent?.baseUrl || '',
    authType: agent?.authType || 'api-key',
    authCredentials: agent?.authCredentials || {},
    capabilities: agent?.capabilities || {
      common: {
        chat: true,
        streaming: false,
        multimodal: false,
        fileHandling: false,
        functionCalling: false
      }
    },
    settings: agent?.settings || {}
  });

  const protocolOptions = [
    {
      value: ProtocolStandard.MCP,
      label: 'Model Context Protocol (MCP)',
      description: 'Modern standard for AI model context sharing'
    },
    {
      value: ProtocolStandard.GOOGLE_AGENT,
      label: 'Google Agent-to-Agent',
      description: 'Google\'s agent collaboration protocol'
    },
    {
      value: ProtocolStandard.OPENAI_ASSISTANT,
      label: 'OpenAI Assistant API',
      description: 'OpenAI Assistant API compatibility'
    },
    {
      value: ProtocolStandard.ANTHROPIC_COMPUTER_USE,
      label: 'Anthropic Computer Use',
      description: 'Anthropic\'s computer interaction protocol'
    },
    {
      value: ProtocolStandard.CUSTOM_REST,
      label: 'Custom REST API',
      description: 'Custom REST API integration'
    },
    {
      value: ProtocolStandard.WEBHOOK,
      label: 'Webhook',
      description: 'Webhook-based integration'
    }
  ];

  const authTypeOptions = [
    { value: 'api-key', label: 'API Key' },
    { value: 'bearer', label: 'Bearer Token' },
    { value: 'oauth2', label: 'OAuth 2.0' },
    { value: 'basic', label: 'Basic Auth' },
    { value: 'custom', label: 'Custom' }
  ];

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    const errors: Partial<CreateExternalAgentDto> = {};

    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }

    if (!formData.displayName.trim()) {
      errors.displayName = 'Display name is required';
    }

    if (!formData.baseUrl.trim()) {
      errors.baseUrl = 'Base URL is required';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    onSubmit(formData);
  };

  const handleDetectProtocol = async () => {
    if (!formData.baseUrl.trim()) {
      setFormErrors({ baseUrl: 'Please enter a base URL first' });
      return;
    }

    protocolDetection.mutate(formData.baseUrl);
  };

  const updateFormData = (updates: Partial<CreateExternalAgentDto>) => {
    setFormData(prev => ({ ...prev, ...updates }));
  };

  const updateCapabilities = (capability: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      capabilities: {
        ...prev.capabilities,
        common: {
          ...prev.capabilities?.common,
          [capability]: value
        }
      }
    }));
  };

  const updateAuthCredentials = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      authCredentials: {
        ...prev.authCredentials,
        [key]: value
      }
    }));
  };

  return (
    <Form ref={formRef} onSubmit={handleSubmit} submitOnEnter={false}>
      <div className="space-y-6">
        {/* Basic Information */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Basic Information
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label="Name" name="name" required>
              <Input
                value={formData.name}
                onChange={(e) => updateFormData({ name: e.target.value })}
                placeholder="my-external-agent"
              />
            </FormItem>

            <FormItem label="Display Name" name="displayName" required>
              <Input
                value={formData.displayName}
                onChange={(e) => updateFormData({ displayName: e.target.value })}
                placeholder="My External Agent"
              />
            </FormItem>
          </div>

          <FormItem label="Description" name="description">
            <Textarea
              value={formData.description}
              onChange={(e) => updateFormData({ description: e.target.value })}
              placeholder="Description of the external agent..."
              rows={3}
            />
          </FormItem>
        </Card>

        {/* Protocol Configuration */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Protocol Configuration
          </Typography>

          <FormItem label="Protocol Standard" name="protocolStandard" required>
            <Select
              value={formData.protocolStandard}
              onChange={(value) => updateFormData({ protocolStandard: value as ProtocolStandard })}
              options={protocolOptions}
            />
          </FormItem>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <FormItem label="Base URL" name="baseUrl" required>
                <Input
                  value={formData.baseUrl}
                  onChange={(e) => updateFormData({ baseUrl: e.target.value })}
                  placeholder="https://api.external-agent.com"
                />
              </FormItem>
            </div>

            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                onClick={handleDetectProtocol}
                isLoading={protocolDetection.isPending}
                className="w-full"
              >
                Detect Protocol
              </Button>
            </div>
          </div>

          <FormItem label="Protocol Version" name="protocolVersion">
            <Input
              value={formData.protocolVersion}
              onChange={(e) => updateFormData({ protocolVersion: e.target.value })}
              placeholder="1.0"
            />
          </FormItem>
        </Card>

        {/* Authentication */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Authentication
          </Typography>

          <FormItem label="Authentication Type" name="authType" required>
            <Select
              value={formData.authType}
              onChange={(value) => updateFormData({ authType: value as AuthType })}
              options={authTypeOptions}
            />
          </FormItem>

          {/* Dynamic auth fields based on auth type */}
          {formData.authType === 'api-key' && (
            <FormItem label="API Key" name="apiKey" required>
              <Input
                type="password"
                value={formData.authCredentials.apiKey || ''}
                onChange={(e) => updateAuthCredentials('apiKey', e.target.value)}
                placeholder="Enter API key"
              />
            </FormItem>
          )}

          {formData.authType === 'bearer' && (
            <FormItem label="Bearer Token" name="bearerToken" required>
              <Input
                type="password"
                value={formData.authCredentials.bearerToken || ''}
                onChange={(e) => updateAuthCredentials('bearerToken', e.target.value)}
                placeholder="Enter bearer token"
              />
            </FormItem>
          )}

          {formData.authType === 'basic' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem label="Username" name="username" required>
                <Input
                  value={formData.authCredentials.username || ''}
                  onChange={(e) => updateAuthCredentials('username', e.target.value)}
                  placeholder="Username"
                />
              </FormItem>
              <FormItem label="Password" name="password" required>
                <Input
                  type="password"
                  value={formData.authCredentials.password || ''}
                  onChange={(e) => updateAuthCredentials('password', e.target.value)}
                  placeholder="Password"
                />
              </FormItem>
            </div>
          )}
        </Card>

        {/* Capabilities */}
        <Card className="p-6">
          <Typography variant="h6" className="mb-4">
            Capabilities
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <FormItem label="Chat" name="chat">
              <Checkbox
                checked={formData.capabilities?.common?.chat || false}
                onChange={(checked) => updateCapabilities('chat', checked)}
                variant="filled"
              />
            </FormItem>

            <FormItem label="Streaming" name="streaming">
              <Checkbox
                checked={formData.capabilities?.common?.streaming || false}
                onChange={(checked) => updateCapabilities('streaming', checked)}
                variant="filled"
              />
            </FormItem>

            <FormItem label="Function Calling" name="functionCalling">
              <Checkbox
                checked={formData.capabilities?.common?.functionCalling || false}
                onChange={(checked) => updateCapabilities('functionCalling', checked)}
                variant="filled"
              />
            </FormItem>

            <FormItem label="Multimodal" name="multimodal">
              <Checkbox
                checked={formData.capabilities?.common?.multimodal || false}
                onChange={(checked) => updateCapabilities('multimodal', checked)}
                variant="filled"
              />
            </FormItem>

            <FormItem label="File Handling" name="fileHandling">
              <Checkbox
                checked={formData.capabilities?.common?.fileHandling || false}
                onChange={(checked) => updateCapabilities('fileHandling', checked)}
                variant="filled"
              />
            </FormItem>
          </div>
        </Card>

        {/* Form Actions */}
        <div className="flex justify-end gap-3">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isLoading}
            disabled={isLoading}
          >
            {agent ? 'Update Agent' : 'Create Agent'}
          </Button>
        </div>
      </div>
    </Form>
  );
};

export default ExternalAgentForm;
```

#### **5.3 External Agents Page**
```typescript
// src/modules/external-agents/pages/ExternalAgentsPage.tsx
import React, { useState } from 'react';
import {
  Card,
  Typography,
  Button,
  Input,
  Select,
  ResponsiveGrid,
  SlideInForm,
  ActiveFilters,
  MenuIconBar
} from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  useExternalAgents,
  useCreateExternalAgent,
  useUpdateExternalAgent,
  useDeleteExternalAgent
} from '../hooks/useExternalAgents';
import ExternalAgentCard from '../components/ExternalAgentCard';
import ExternalAgentForm from '../components/ExternalAgentForm';
import type { ExternalAgent, ExternalAgentQueryParams, ProtocolStandard } from '../types';

const ExternalAgentsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'external-agents']);

  const [queryParams, setQueryParams] = useState<ExternalAgentQueryParams>({
    page: 1,
    limit: 12,
    search: '',
    status: undefined,
    protocolStandard: undefined
  });

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAgent, setEditingAgent] = useState<ExternalAgent | undefined>();

  const { data, isLoading, refetch } = useExternalAgents(queryParams);
  const createAgent = useCreateExternalAgent();
  const updateAgent = useUpdateExternalAgent();
  const deleteAgent = useDeleteExternalAgent();

  const handleSearch = (search: string) => {
    setQueryParams(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (key: keyof ExternalAgentQueryParams, value: any) => {
    setQueryParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleCreateAgent = () => {
    setEditingAgent(undefined);
    setIsFormOpen(true);
  };

  const handleEditAgent = (agent: ExternalAgent) => {
    setEditingAgent(agent);
    setIsFormOpen(true);
  };

  const handleDeleteAgent = async (agent: ExternalAgent) => {
    if (window.confirm(`Are you sure you want to delete "${agent.displayName}"?`)) {
      await deleteAgent.mutateAsync(agent.id);
    }
  };

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingAgent) {
        await updateAgent.mutateAsync({ id: editingAgent.id, data });
      } else {
        await createAgent.mutateAsync(data);
      }
      setIsFormOpen(false);
      setEditingAgent(undefined);
    } catch (error) {
      // Error handling is done in hooks
    }
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setEditingAgent(undefined);
  };

  const statusOptions = [
    { value: '', label: 'All Statuses' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'testing', label: 'Testing' },
    { value: 'error', label: 'Error' }
  ];

  const protocolOptions = [
    { value: '', label: 'All Protocols' },
    { value: 'mcp', label: 'MCP' },
    { value: 'google-agent', label: 'Google Agent' },
    { value: 'openai-assistant', label: 'OpenAI Assistant' },
    { value: 'anthropic-computer-use', label: 'Anthropic Computer Use' },
    { value: 'custom-rest', label: 'Custom REST' },
    { value: 'webhook', label: 'Webhook' }
  ];

  const activeFilters = [
    queryParams.search && { key: 'search', label: `Search: ${queryParams.search}` },
    queryParams.status && { key: 'status', label: `Status: ${queryParams.status}` },
    queryParams.protocolStandard && {
      key: 'protocolStandard',
      label: `Protocol: ${queryParams.protocolStandard}`
    }
  ].filter(Boolean);

  const clearFilter = (key: string) => {
    setQueryParams(prev => ({ ...prev, [key]: undefined, page: 1 }));
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <Typography variant="h4" className="mb-2">
            {t('external-agents:title')}
          </Typography>
          <Typography variant="body1" color="muted">
            {t('external-agents:description')}
          </Typography>
        </div>

        <MenuIconBar>
          <Button variant="primary" onClick={handleCreateAgent}>
            {t('external-agents:create_agent')}
          </Button>
        </MenuIconBar>
      </div>

      {/* Filters */}
      <Card className="p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Input
            placeholder={t('external-agents:search_placeholder')}
            value={queryParams.search || ''}
            onChange={(e) => handleSearch(e.target.value)}
          />

          <Select
            value={queryParams.status || ''}
            onChange={(value) => handleFilterChange('status', value || undefined)}
            options={statusOptions}
            placeholder={t('external-agents:filter_status')}
          />

          <Select
            value={queryParams.protocolStandard || ''}
            onChange={(value) => handleFilterChange('protocolStandard', value as ProtocolStandard || undefined)}
            options={protocolOptions}
            placeholder={t('external-agents:filter_protocol')}
          />

          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            {t('common:refresh')}
          </Button>
        </div>

        {/* Active Filters */}
        {activeFilters.length > 0 && (
          <div className="mt-4">
            <ActiveFilters
              filters={activeFilters}
              onClearFilter={clearFilter}
              onClearAll={() => setQueryParams({ page: 1, limit: 12 })}
            />
          </div>
        )}
      </Card>

      {/* Content */}
      {isLoading ? (
        <div className="flex justify-center py-12">
          <Typography variant="body1" color="muted">
            {t('common:loading')}...
          </Typography>
        </div>
      ) : data?.result?.items?.length === 0 ? (
        <Card className="p-12 text-center">
          <Typography variant="h6" className="mb-2">
            {t('external-agents:no_agents')}
          </Typography>
          <Typography variant="body1" color="muted" className="mb-4">
            {t('external-agents:no_agents_description')}
          </Typography>
          <Button variant="primary" onClick={handleCreateAgent}>
            {t('external-agents:create_first_agent')}
          </Button>
        </Card>
      ) : (
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3 }}>
          {data?.result?.items?.map((agent) => (
            <ExternalAgentCard
              key={agent.id}
              agent={agent}
              onEdit={handleEditAgent}
              onDelete={handleDeleteAgent}
            />
          ))}
        </ResponsiveGrid>
      )}

      {/* Form Slide-in */}
      <SlideInForm
        isOpen={isFormOpen}
        onClose={handleFormCancel}
        title={editingAgent ? t('external-agents:edit_agent') : t('external-agents:create_agent')}
        size="lg"
      >
        <ExternalAgentForm
          agent={editingAgent}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={createAgent.isPending || updateAgent.isPending}
        />
      </SlideInForm>
    </div>
  );
};

export default ExternalAgentsPage;
```

---

## 🚀 **IMPLEMENTATION GUIDE**

### **1. Backend Implementation Steps**

#### **Step 1: Database Setup**
```bash
# Create migration files
npm run migration:create -- --name=create-external-agents-tables

# Run migrations
npm run migration:run

# Verify tables
npm run migration:show
```

#### **Step 2: Install Dependencies**
```bash
# Backend dependencies
npm install @grpc/grpc-js @grpc/proto-loader
npm install ws socket.io
npm install jsonrpc-lite
npm install @nestjs/event-emitter

# Protocol-specific dependencies
npm install @anthropic-ai/sdk
npm install @google-cloud/aiplatform
```

#### **Step 3: Environment Configuration**
```env
# .env
# External Agent Configuration
EXTERNAL_AGENTS_ENABLED=true
WEBHOOK_BASE_URL=https://your-domain.com
MCP_SERVER_PORT=3001
GRPC_SERVER_PORT=50051

# Protocol-specific settings
GOOGLE_AGENT_SERVICE_ACCOUNT_KEY=path/to/service-account.json
ANTHROPIC_API_KEY=your-anthropic-key
```

#### **Step 4: Module Registration**
```typescript
// src/app.module.ts
import { ExternalAgentsModule } from './modules/external-agents/external-agents.module';

@Module({
  imports: [
    // ... other modules
    ExternalAgentsModule,
  ],
})
export class AppModule {}
```

### **2. Frontend Implementation Steps**

#### **Step 1: Install Dependencies**
```bash
# Frontend dependencies
npm install @grpc/grpc-js
npm install ws
npm install jsonrpc-lite
```

#### **Step 2: Module Registration**
```typescript
// src/modules/external-agents/index.ts
export * from './types';
export * from './services';
export * from './hooks';
export { default as ExternalAgentCard } from './components/ExternalAgentCard';
export { default as ExternalAgentForm } from './components/ExternalAgentForm';
export { default as ExternalAgentsPage } from './pages/ExternalAgentsPage';
```

#### **Step 3: Route Configuration**
```typescript
// src/routes/integration.routes.tsx
import { ExternalAgentsPage } from '@/modules/external-agents';

export const integrationRoutes = [
  // ... other routes
  {
    path: '/integration/external-agents',
    element: <ExternalAgentsPage />,
  },
];
```

#### **Step 4: Navigation Menu**
```typescript
// Add to navigation menu
{
  label: 'External Agents',
  path: '/integration/external-agents',
  icon: 'users',
  children: []
}
```

### **3. Testing Strategy**

#### **3.1 Backend Testing**
```typescript
// tests/external-agents/external-agent.service.spec.ts
describe('ExternalAgentService', () => {
  let service: ExternalAgentService;
  let repository: Repository<ExternalAgent>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ExternalAgentService,
        {
          provide: getRepositoryToken(ExternalAgent),
          useClass: Repository,
        },
      ],
    }).compile();

    service = module.get<ExternalAgentService>(ExternalAgentService);
    repository = module.get<Repository<ExternalAgent>>(getRepositoryToken(ExternalAgent));
  });

  describe('createExternalAgent', () => {
    it('should create an external agent successfully', async () => {
      const createDto: CreateExternalAgentDto = {
        name: 'test-agent',
        displayName: 'Test Agent',
        protocolStandard: ProtocolStandard.MCP,
        baseUrl: 'https://test.com',
        authType: 'api-key',
        authCredentials: { apiKey: 'test-key' },
      };

      const result = await service.createExternalAgent(1, createDto);

      expect(result).toBeDefined();
      expect(result.name).toBe(createDto.name);
    });
  });

  describe('testExternalAgent', () => {
    it('should test agent connection successfully', async () => {
      const agent = createMockAgent();
      jest.spyOn(repository, 'findOne').mockResolvedValue(agent);

      const result = await service.testExternalAgent(1, agent.id, {});

      expect(result.success).toBe(true);
    });
  });
});
```

#### **3.2 Frontend Testing**
```typescript
// tests/external-agents/ExternalAgentCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ExternalAgentCard from '../components/ExternalAgentCard';

const mockAgent: ExternalAgent = {
  id: '1',
  name: 'test-agent',
  displayName: 'Test Agent',
  protocolStandard: ProtocolStandard.MCP,
  status: 'active',
  // ... other properties
};

describe('ExternalAgentCard', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: { queries: { retry: false } },
    });
  });

  it('renders agent information correctly', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <ExternalAgentCard agent={mockAgent} />
      </QueryClientProvider>
    );

    expect(screen.getByText('Test Agent')).toBeInTheDocument();
    expect(screen.getByText('active')).toBeInTheDocument();
  });

  it('handles test connection click', () => {
    render(
      <QueryClientProvider client={queryClient}>
        <ExternalAgentCard agent={mockAgent} />
      </QueryClientProvider>
    );

    const testButton = screen.getByText('Test Connection');
    fireEvent.click(testButton);

    // Assert that the test mutation was called
  });
});
```

### **4. Deployment Checklist**

#### **4.1 Backend Deployment**
- [ ] Database migrations applied
- [ ] Environment variables configured
- [ ] Protocol-specific credentials set up
- [ ] Webhook endpoints configured
- [ ] SSL certificates for gRPC (if using Google Agent protocol)
- [ ] Rate limiting configured
- [ ] Monitoring and logging set up

#### **4.2 Frontend Deployment**
- [ ] Build process includes new modules
- [ ] Environment variables for API endpoints
- [ ] Route configuration updated
- [ ] Navigation menu updated
- [ ] Internationalization files updated
- [ ] Error boundaries configured

#### **4.3 Security Considerations**
- [ ] API authentication and authorization
- [ ] Webhook signature verification
- [ ] Credential encryption at rest
- [ ] Rate limiting for external agent calls
- [ ] Input validation and sanitization
- [ ] CORS configuration for external agents

### **5. Monitoring and Maintenance**

#### **5.1 Metrics to Track**
- External agent response times
- Success/failure rates
- Protocol-specific metrics
- Webhook delivery rates
- Resource usage

#### **5.2 Alerting**
- Agent connection failures
- High error rates
- Webhook delivery failures
- Performance degradation

#### **5.3 Maintenance Tasks**
- Regular credential rotation
- Protocol version updates
- Performance optimization
- Security patches

---

## 📚 **CONCLUSION**

This implementation provides a comprehensive solution for integrating external agents into RedAI using modern standards like MCP and Google Agent-to-Agent protocols. The architecture is designed to be:

- **Extensible**: Easy to add new protocols
- **Scalable**: Handles multiple agents efficiently
- **Secure**: Proper authentication and validation
- **Maintainable**: Clear separation of concerns
- **User-friendly**: Intuitive UI for management

The implementation follows RedAI's established patterns and maintains consistency with the existing codebase while providing powerful new capabilities for agent integration.
