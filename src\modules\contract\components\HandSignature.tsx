/**
 * Component chữ ký tay
 */
import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Icon } from '@/shared/components/common';
import { ContractStepProps } from '../types';

// Hook để detect dark mode
const useIsDarkMode = () => {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const checkDarkMode = () => {
      const isDarkMode = document.documentElement.classList.contains('dark') ||
                        window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDark(isDarkMode);
    };

    checkDarkMode();

    // Listen for theme changes
    const observer = new MutationObserver(checkDarkMode);
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    });

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', checkDarkMode);

    return () => {
      observer.disconnect();
      mediaQuery.removeEventListener('change', checkDarkMode);
    };
  }, []);

  return isDark;
};

const HandSignature: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [canvasData, setCanvasData] = useState<string | null>(null);
  const isDarkMode = useIsDarkMode();

  // Get stroke color based on theme
  const getStrokeColor = useCallback(() => {
    return isDarkMode ? '#ffffff' : '#000000';
  }, [isDarkMode]);

  // Separate effect for canvas initialization (only run once)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size with high DPI support
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';

    ctx.scale(dpr, dpr);

    // Set initial drawing styles
    ctx.strokeStyle = getStrokeColor();
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Load existing signature if available
    if (data.handSignature) {
      const img = new Image();
      img.onload = () => {
        ctx.drawImage(img, 0, 0);
        setHasSignature(true);
      };
      img.src = data.handSignature;
    }
  }, [data.handSignature, getStrokeColor]);

  // Separate effect for theme changes (restore canvas if needed)
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Update stroke style for future drawings
    ctx.strokeStyle = getStrokeColor();

    // Restore canvas data if it exists (in case canvas was cleared)
    if (canvasData && hasSignature) {
      const img = new Image();
      img.onload = () => {
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.drawImage(img, 0, 0);
      };
      img.src = canvasData;
    }
  }, [isDarkMode, getStrokeColor, canvasData, hasSignature]);

  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    e.preventDefault(); // Prevent scrolling on touch devices

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Ensure stroke color is updated for current theme
    ctx.strokeStyle = getStrokeColor();
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    setIsDrawing(true);

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0]?.clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0]?.clientY : e.clientY;

    const x = (clientX || 0) - rect.left;
    const y = (clientY || 0) - rect.top;

    ctx.beginPath();
    ctx.moveTo(x, y);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;
    e.preventDefault(); // Prevent scrolling on touch devices

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const clientX = 'touches' in e ? e.touches[0]?.clientX : e.clientX;
    const clientY = 'touches' in e ? e.touches[0]?.clientY : e.clientY;

    const x = (clientX || 0) - rect.left;
    const y = (clientY || 0) - rect.top;

    ctx.lineTo(x, y);
    ctx.stroke();
    setHasSignature(true);
  };

  const stopDrawing = () => {
    if (isDrawing) {
      setIsDrawing(false);
      // Ensure signature is marked as present when drawing stops
      setHasSignature(true);

      // Backup canvas data
      const canvas = canvasRef.current;
      if (canvas) {
        const dataURL = canvas.toDataURL('image/png');
        setCanvasData(dataURL);
      }
    }
  };

  const clearSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    setHasSignature(false);
    setCanvasData(null);
  };

  const saveSignature = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Use saved canvas data if available, otherwise get current canvas data
    const signatureData = canvasData || canvas.toDataURL('image/png');
    onNext({ handSignature: signatureData });
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        {/* Signature Canvas */}
        <div className="border-2 border-dashed border-border rounded-lg p-4 mb-4 bg-background">
            <canvas
              ref={canvasRef}
              className="w-full h-64 cursor-crosshair touch-none bg-background rounded-md"
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={stopDrawing}
              onMouseLeave={stopDrawing}
              onTouchStart={startDrawing}
              onTouchMove={draw}
              onTouchEnd={stopDrawing}
              style={{
                touchAction: 'none',
                backgroundColor: 'var(--background)',
              }}
            />
          </div>

        {/* Action buttons */}
        <div className="flex justify-center space-x-4">
          <Button
            variant="outline"
            leftIcon={<Icon name="trash-2" />}
            onClick={clearSignature}
            disabled={!hasSignature}
          >
            {t('contract:handSignature.clear')}
          </Button>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract:actions.previous')}
        </Button>

        <Button
          variant="primary"
          onClick={saveSignature}
          disabled={!hasSignature}
          isLoading={isLoading || false}
        >
          {t('contract:handSignature.save')}
        </Button>
      </div>
    </div>
  );
};

export default HandSignature;
