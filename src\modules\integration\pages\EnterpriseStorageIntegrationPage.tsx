import React, { useState } from 'react';
import { SlideInForm } from '@/shared/components/common';
import EnterpriseStorageProviderList from '../enterprise-storage/components/EnterpriseStorageProviderList';
import EnterpriseStorageProviderForm from '../enterprise-storage/components/EnterpriseStorageProviderForm';

/**
 * Trang quản lý tích hợp enterprise storage
 */
const EnterpriseStorageIntegrationPage: React.FC = () => {
  const [showCreateForm, setShowCreateForm] = useState(false);

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleCreateCancel = () => {
    setShowCreateForm(false);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <EnterpriseStorageProviderList onCreateNew={handleCreateNew} />

      {/* Create Form Slide-in */}
      <SlideInForm
        isVisible={showCreateForm}
      >
        <EnterpriseStorageProviderForm
          onSuccess={handleCreateSuccess}
          onCancel={handleCreateCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default EnterpriseStorageIntegrationPage;
