import { AxiosError } from 'axios';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Interface cho lỗi API từ backend
 */
export interface ApiError {
  code: number;
  message: string;
  result?: {
    code?: string;
    [key: string]: unknown;
  };
}

/**
 * Marketplace specific error codes (từ backend documentation)
 */
export const MARKETPLACE_ERROR_CODES = {
  // Product errors
  PRODUCT_NOT_FOUND: 'MARKETPLACE_PRODUCT_NOT_FOUND',
  PRODUCT_NOT_APPROVED: 'MARKETPLACE_PRODUCT_NOT_APPROVED',
  PRODUCT_DELETED: 'MARKETPLACE_PRODUCT_DELETED',
  CANNOT_BUY_OWN_PRODUCT: 'MARKETPLACE_CANNOT_BUY_OWN_PRODUCT',
  PRODUCT_ALREADY_PURCHASED: 'MARKETPLACE_PRODUCT_ALREADY_PURCHASED',

  // Cart errors
  CART_ITEM_NOT_FOUND: 'MARKETPLACE_CART_ITEM_NOT_FOUND',
  INVALID_QUANTITY: 'MARKETPLACE_INVALID_QUANTITY',
  CART_EMPTY: 'MARKETPLACE_CART_EMPTY',

  // Payment errors
  INSUFFICIENT_BALANCE: 'MARKETPLACE_INSUFFICIENT_BALANCE',
  PAYMENT_FAILED: 'MARKETPLACE_PAYMENT_FAILED',
  INVALID_PAYMENT_DATA: 'MARKETPLACE_INVALID_PAYMENT_DATA',

  // User errors
  USER_NOT_FOUND: 'MARKETPLACE_USER_NOT_FOUND',
  UNAUTHORIZED: 'MARKETPLACE_UNAUTHORIZED',

  // Validation errors
  VALIDATION_FAILED: 'MARKETPLACE_VALIDATION_FAILED',
  INVALID_INPUT: 'MARKETPLACE_INVALID_INPUT',
} as const;

/**
 * Mapping error codes to user-friendly messages
 */
const ERROR_MESSAGES: Record<string, string> = {
  [MARKETPLACE_ERROR_CODES.PRODUCT_NOT_FOUND]: 'Sản phẩm không tồn tại hoặc đã bị xóa',
  [MARKETPLACE_ERROR_CODES.PRODUCT_NOT_APPROVED]: 'Sản phẩm chưa được phê duyệt',
  [MARKETPLACE_ERROR_CODES.PRODUCT_DELETED]: 'Sản phẩm đã bị xóa',
  [MARKETPLACE_ERROR_CODES.CANNOT_BUY_OWN_PRODUCT]: 'Không thể mua sản phẩm của chính mình',
  [MARKETPLACE_ERROR_CODES.PRODUCT_ALREADY_PURCHASED]: 'Bạn đã mua sản phẩm này rồi',

  [MARKETPLACE_ERROR_CODES.CART_ITEM_NOT_FOUND]: 'Sản phẩm không có trong giỏ hàng',
  [MARKETPLACE_ERROR_CODES.INVALID_QUANTITY]: 'Số lượng không hợp lệ',
  [MARKETPLACE_ERROR_CODES.CART_EMPTY]: 'Giỏ hàng trống',

  [MARKETPLACE_ERROR_CODES.INSUFFICIENT_BALANCE]: 'Số dư R-Point không đủ để thanh toán',
  [MARKETPLACE_ERROR_CODES.PAYMENT_FAILED]: 'Thanh toán thất bại',
  [MARKETPLACE_ERROR_CODES.INVALID_PAYMENT_DATA]: 'Dữ liệu thanh toán không hợp lệ',

  [MARKETPLACE_ERROR_CODES.USER_NOT_FOUND]: 'Người dùng không tồn tại',
  [MARKETPLACE_ERROR_CODES.UNAUTHORIZED]: 'Bạn không có quyền thực hiện hành động này',

  [MARKETPLACE_ERROR_CODES.VALIDATION_FAILED]: 'Dữ liệu không hợp lệ',
  [MARKETPLACE_ERROR_CODES.INVALID_INPUT]: 'Thông tin nhập vào không đúng',
};

/**
 * Utility class để xử lý lỗi marketplace
 */
export class MarketplaceErrorHandler {
  /**
   * Xử lý lỗi từ API response
   */
  static handleApiError(error: unknown): string {
    if (error instanceof AxiosError) {
      const apiError = error.response?.data as ApiError;

      if (apiError?.code) {
        // Tìm message từ error code
        const errorCode = this.extractErrorCode(apiError);
        const userMessage = ERROR_MESSAGES[errorCode];

        if (userMessage) {
          return userMessage;
        }

        // Fallback to API message
        if (apiError.message) {
          return apiError.message;
        }
      }

      // HTTP status code errors
      switch (error.response?.status) {
        case 400:
          return 'Yêu cầu không hợp lệ';
        case 401:
          return 'Bạn cần đăng nhập để thực hiện hành động này';
        case 403:
          return 'Bạn không có quyền thực hiện hành động này';
        case 404:
          return 'Không tìm thấy tài nguyên yêu cầu';
        case 409:
          return 'Xung đột dữ liệu';
        case 422:
          return 'Dữ liệu không hợp lệ';
        case 429:
          return 'Quá nhiều yêu cầu, vui lòng thử lại sau';
        case 500:
          return 'Lỗi máy chủ, vui lòng thử lại sau';
        case 502:
        case 503:
        case 504:
          return 'Dịch vụ tạm thời không khả dụng, vui lòng thử lại sau';
        default:
          return 'Có lỗi xảy ra, vui lòng thử lại';
      }
    }

    // Network errors
    if (error instanceof Error) {
      if (error.message.includes('Network Error')) {
        return 'Lỗi kết nối mạng, vui lòng kiểm tra kết nối internet';
      }

      if (error.message.includes('timeout')) {
        return 'Yêu cầu quá thời gian chờ, vui lòng thử lại';
      }

      return error.message;
    }

    return 'Có lỗi không xác định xảy ra';
  }

  /**
   * Trích xuất error code từ API response
   */
  private static extractErrorCode(apiError: ApiError): string {
    // Nếu có result.code (từ AppException)
    if (apiError.result?.code) {
      return apiError.result.code;
    }

    // Nếu message chứa error code pattern
    const codeMatch = apiError.message.match(/\[([A-Z_]+)\]/);
    if (codeMatch) {
      return codeMatch[1];
    }

    // Fallback
    return 'UNKNOWN_ERROR';
  }

  /**
   * Hiển thị lỗi với notification
   */
  static showError(error: unknown, customMessage?: string): void {
    const errorMessage = customMessage || this.handleApiError(error);
    NotificationUtil.error({ message: errorMessage });
  }

  /**
   * Kiểm tra xem có phải lỗi cụ thể không
   */
  static isSpecificError(error: unknown, errorCode: string): boolean {
    if (error instanceof AxiosError) {
      const apiError = error.response?.data as ApiError;
      const extractedCode = this.extractErrorCode(apiError);
      return extractedCode === errorCode;
    }
    return false;
  }

  /**
   * Kiểm tra xem có phải lỗi authentication không
   */
  static isAuthError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return error.response?.status === 401;
    }
    return false;
  }

  /**
   * Kiểm tra xem có phải lỗi permission không
   */
  static isPermissionError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return error.response?.status === 403;
    }
    return false;
  }

  /**
   * Kiểm tra xem có phải lỗi validation không
   */
  static isValidationError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return error.response?.status === 422 ||
             this.isSpecificError(error, MARKETPLACE_ERROR_CODES.VALIDATION_FAILED);
    }
    return false;
  }

  /**
   * Kiểm tra xem có phải lỗi network không
   */
  static isNetworkError(error: unknown): boolean {
    if (error instanceof AxiosError) {
      return !error.response && error.request;
    }
    return false;
  }
}

/**
 * Hook để xử lý lỗi trong React components
 */
export const useMarketplaceErrorHandler = () => {
  const handleError = (error: unknown, customMessage?: string) => {
    MarketplaceErrorHandler.showError(error, customMessage);
  };

  const getErrorMessage = (error: unknown): string => {
    return MarketplaceErrorHandler.handleApiError(error);
  };

  const isSpecificError = (error: unknown, errorCode: string): boolean => {
    return MarketplaceErrorHandler.isSpecificError(error, errorCode);
  };

  return {
    handleError,
    getErrorMessage,
    isSpecificError,
    isAuthError: (error: unknown) => MarketplaceErrorHandler.isAuthError(error),
    isPermissionError: (error: unknown) => MarketplaceErrorHandler.isPermissionError(error),
    isValidationError: (error: unknown) => MarketplaceErrorHandler.isValidationError(error),
    isNetworkError: (error: unknown) => MarketplaceErrorHandler.isNetworkError(error),
  };
};

export default MarketplaceErrorHandler;
