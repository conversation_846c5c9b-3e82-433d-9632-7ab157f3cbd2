/**
 * Types cho tích hợp Zalo Ads
 */

/**
 * <PERSON>ục tiêu quảng cáo Zalo Ads
 */
export enum ZaloAdsObjective {
  REACH = 'REACH',
  TRAFFIC = 'TRAFFIC',
  ENGAGEMENT = 'ENGAGEMENT',
  LEAD_GENERATION = 'LEAD_GENERATION',
  CONVERSIONS = 'CONVERSIONS',
  BRAND_AWARENESS = 'BRAND_AWARENESS',
  APP_INSTALLS = 'APP_INSTALLS',
  VIDEO_VIEWS = 'VIDEO_VIEWS'
}

/**
 * Trạng thái chiến dịch Zalo Ads
 */
export enum ZaloAdsCampaignStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  PENDING_REVIEW = 'PENDING_REVIEW',
  REJECTED = 'REJECTED',
  DRAFT = 'DRAFT'
}

/**
 * Trạng thái tài khoản Zalo Ads
 */
export enum ZaloAdsAccountStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION'
}

/**
 * Vị trí hiển thị quảng cáo
 */
export enum ZaloAdsPlacement {
  ZALO_NEWSFEED = 'ZALO_NEWSFEED',
  ZALO_MEDIA_BOX = 'ZALO_MEDIA_BOX',
  ZALO_VIDEO = 'ZALO_VIDEO',
  ZALO_STORY = 'ZALO_STORY',
  ZING_MP3 = 'ZING_MP3',
  ZING_NEWS = 'ZING_NEWS',
  BAO_MOI = 'BAO_MOI'
}

/**
 * Hình thức quảng cáo
 */
export enum ZaloAdsFormat {
  OFFICIAL_ACCOUNT = 'OFFICIAL_ACCOUNT',
  WEBSITE = 'WEBSITE',
  VIDEO = 'VIDEO',
  ARTICLE = 'ARTICLE',
  FORM = 'FORM',
  MESSAGE = 'MESSAGE',
  DISPLAY = 'DISPLAY',
  COMMERCE = 'COMMERCE'
}

/**
 * Chiến lược đấu giá
 */
export enum ZaloAdsBidStrategy {
  LOWEST_COST = 'LOWEST_COST',
  TARGET_COST = 'TARGET_COST',
  COST_CAP = 'COST_CAP',
  BID_CAP = 'BID_CAP'
}

/**
 * Hình thức tính phí
 */
export enum ZaloAdsBillingEvent {
  CPC = 'CPC', // Cost per Click
  CPM = 'CPM', // Cost per Mille (1000 impressions)
  CPV = 'CPV', // Cost per View
  CPA = 'CPA'  // Cost per Action
}

/**
 * DTO tài khoản Zalo Ads
 */
export interface ZaloAdsAccountDto {
  id: number;
  userId: number;
  accountId: string;
  accountName: string;
  accessToken: string;
  refreshToken: string;
  status: ZaloAdsAccountStatus;
  balance: number;
  currency: string;
  timeZone: string;
  businessId?: string;
  businessName?: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO targeting cho quảng cáo
 */
export interface ZaloAdsTargetingDto {
  locations: string[];
  ageMin: number;
  ageMax: number;
  genders: ('MALE' | 'FEMALE')[];
  interests: string[];
  behaviors: string[];
  customAudiences?: string[];
  lookalikeSources?: string[];
  deviceTypes?: string[];
  operatingSystems?: string[];
  connectionTypes?: string[];
}

/**
 * DTO lịch trình quảng cáo
 */
export interface ZaloAdsScheduleDto {
  startTime: number;
  endTime?: number;
  dayParting?: {
    days: number[]; // 0-6 (Sunday-Saturday)
    hours: number[]; // 0-23
  };
}

/**
 * DTO chiến dịch Zalo Ads
 */
export interface ZaloAdsCampaignDto {
  id: number;
  userId: number;
  accountId: string;
  campaignId: string;
  name: string;
  objective: ZaloAdsObjective;
  status: ZaloAdsCampaignStatus;
  dailyBudget?: number;
  lifetimeBudget?: number;
  bidStrategy: ZaloAdsBidStrategy;
  bidAmount?: number;
  targeting: ZaloAdsTargetingDto;
  schedule: ZaloAdsScheduleDto;
  placements: ZaloAdsPlacement[];
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO creative quảng cáo
 */
export interface ZaloAdsCreativeDto {
  id: number;
  userId: number;
  accountId: string;
  creativeId: string;
  campaignId: string;
  name: string;
  format: ZaloAdsFormat;
  title: string;
  description: string;
  imageUrl?: string;
  videoUrl?: string;
  callToAction: string;
  destinationUrl?: string;
  status: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO báo cáo hiệu suất
 */
export interface ZaloAdsReportDto {
  campaignId: string;
  creativeId?: string;
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  spend: number;
  cpc: number;
  cpm: number;
  ctr: number;
  conversionRate: number;
  roas?: number;
  costPerConversion: number;
}

/**
 * DTO tham số truy vấn tài khoản
 */
export interface ZaloAdsAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: ZaloAdsAccountStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn chiến dịch
 */
export interface ZaloAdsCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  accountId?: string;
  status?: ZaloAdsCampaignStatus;
  objective?: ZaloAdsObjective;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tạo tài khoản Zalo Ads
 */
export interface CreateZaloAdsAccountDto {
  accountName: string;
  accessToken: string;
  refreshToken: string;
  businessId?: string;
  businessName?: string;
}

/**
 * DTO tạo chiến dịch
 */
export interface CreateZaloAdsCampaignDto {
  accountId: string;
  name: string;
  objective: ZaloAdsObjective;
  dailyBudget?: number;
  lifetimeBudget?: number;
  bidStrategy: ZaloAdsBidStrategy;
  bidAmount?: number;
  targeting: ZaloAdsTargetingDto;
  schedule: ZaloAdsScheduleDto;
  placements: ZaloAdsPlacement[];
}

/**
 * DTO cập nhật chiến dịch
 */
export interface UpdateZaloAdsCampaignDto {
  name?: string;
  status?: ZaloAdsCampaignStatus;
  dailyBudget?: number;
  lifetimeBudget?: number;
  bidAmount?: number;
  targeting?: Partial<ZaloAdsTargetingDto>;
  schedule?: Partial<ZaloAdsScheduleDto>;
}

/**
 * DTO tạo creative
 */
export interface CreateZaloAdsCreativeDto {
  accountId: string;
  campaignId: string;
  name: string;
  format: ZaloAdsFormat;
  title: string;
  description: string;
  imageUrl?: string;
  videoUrl?: string;
  callToAction: string;
  destinationUrl?: string;
}

/**
 * Response từ Zalo Ads API
 */
export interface ZaloAdsApiResponse<T> {
  error: number;
  message: string;
  data: T;
}

/**
 * Token response từ OAuth
 */
export interface ZaloAdsTokenResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  token_type: string;
}

/**
 * Metrics tổng hợp
 */
export interface ZaloAdsMetrics {
  totalCampaigns: number;
  activeCampaigns: number;
  totalSpend: number;
  totalImpressions: number;
  totalClicks: number;
  totalConversions: number;
  averageCpc: number;
  averageCtr: number;
  averageRoas: number;
}

/**
 * Top performing campaign data
 */
export interface ZaloAdsTopCampaign {
  campaignId: string;
  name: string;
  objective: ZaloAdsObjective;
  status: ZaloAdsCampaignStatus;
  spend: number;
  impressions: number;
  clicks: number;
  conversions: number;
  ctr: number;
  cpc: number;
  roas: number;
  createdAt: number;
}

/**
 * Performance trend data point
 */
export interface ZaloAdsPerformanceTrend {
  date: string;
  impressions: number;
  clicks: number;
  conversions: number;
  spend: number;
  ctr: number;
  cpc: number;
  roas: number;
}

/**
 * Date range cho báo cáo
 */
export interface ZaloAdsDateRange {
  startDate: string; // YYYY-MM-DD
  endDate: string;   // YYYY-MM-DD
}
