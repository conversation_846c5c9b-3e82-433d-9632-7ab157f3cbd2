import { EmailElement, ViewportSize, ExportFormat, ThemeSettings } from './types';

// Tạo chuỗi CSS từ đối tượng style
export const generateStyleString = (style?: Record<string, unknown>): string => {
  if (!style) return '';

  return Object.entries(style)
    .filter(([, value]) => value !== undefined && value !== null)
    .map(([key, value]) => {
      // Chuyển đổi camelCase sang kebab-case
      const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();

      // Xử lý các trường hợp đặc biệt
      if (typeof value === 'number' && !['zIndex', 'opacity', 'fontWeight', 'lineHeight', 'flex'].includes(key)) {
        return `${cssKey}: ${value}px;`;
      }

      return `${cssKey}: ${value};`;
    })
    .join(' ');
};

// Tạo HTML cho từng phần tử
export const generateElementHTML = (element: EmailElement): string => {
  let elementHtml = '';

  switch (element.type) {
    case 'text':
      elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || 'Văn bản'}</div>`;
      break;

    case 'heading': {
      const headingTag = element.headingType || 'h2';
      elementHtml = `<${headingTag} style="${generateStyleString(element.style)}">${element.content || 'Tiêu đề'}</${headingTag}>`;
      break;
    }

    case 'image':
      elementHtml = `<div style="${generateStyleString(element.style)}">
  <img src="${element.src || 'https://via.placeholder.com/600x200?text=Hình+ảnh'}" alt="${element.alt || 'Hình ảnh'}" style="max-width: 100%; display: block;">
</div>`;
      break;

    case 'button': {
      const buttonAlign = element.style?.textAlign || 'center';
      elementHtml = `<div style="text-align: ${buttonAlign};">
  <a href="${element.url || '#'}" style="background-color: ${element.style?.backgroundColor || '#0070f3'}; color: ${element.style?.color || '#ffffff'}; padding: ${element.style?.padding || '10px 20px'}; border-radius: ${element.style?.borderRadius || 4}px; border: none; display: inline-block; text-align: center; text-decoration: none; font-weight: ${element.style?.fontWeight || 'bold'}; ${element.style?.width ? `width: ${element.style.width};` : ''}">${element.text || 'Nút nhấn'}</a>
</div>`;
      break;
    }

    case 'divider':
      elementHtml = `<hr style="${generateStyleString(element.style)}">`;
      break;

    case 'spacer':
      elementHtml = `<div style="${generateStyleString(element.style)}">&nbsp;</div>`;
      break;

    case 'link':
      elementHtml = `<a href="${element.url || '#'}" style="${generateStyleString(element.style)}">${element.text || 'Liên kết'}</a>`;
      break;

    case 'list':
      elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<ul><li>Mục danh sách</li></ul>'}</div>`;
      break;

    case 'social':
      elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<div style="text-align: center;"><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Facebook"></a><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Twitter"></a><a href="#" style="margin: 0 10px;"><img src="https://via.placeholder.com/30" alt="Instagram"></a></div>'}</div>`;
      break;

    case 'html':
      elementHtml = `<div style="${generateStyleString(element.style)}">${element.content || '<div>Mã HTML</div>'}</div>`;
      break;

    case 'header':
      elementHtml = `<div style="${generateStyleString(element.style)}" class="header">${element.content || '<div style="text-align: center; padding: 20px;"><img src="https://via.placeholder.com/200x50" alt="Logo"></div>'}</div>`;
      break;

    case 'footer':
      elementHtml = `<div style="${generateStyleString(element.style)}" class="footer">${element.content || '<div style="text-align: center; padding: 20px; font-size: 12px; color: #666;">© 2023 Công ty của bạn. Tất cả các quyền được bảo lưu.</div>'}</div>`;
      break;

    case '1column':
      elementHtml = `<div style="${generateStyleString(element.style)}" class="column">`;
      if (element.children && element.children.length > 0) {
        element.children.forEach(child => {
          elementHtml += generateElementHTML(child);
        });
      } else {
        elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột trống</div>';
      }
      elementHtml += `</div>`;
      break;

    case '2columns': {
      elementHtml = `<div style="${generateStyleString(element.style)}" class="two-columns">`;

      // Tìm cột trái và phải
      const leftColumn = element.children?.find(child => child.columnPosition === 'left');
      const rightColumn = element.children?.find(child => child.columnPosition === 'right');

      // Tạo HTML cho cột trái
      elementHtml += `<div class="column" style="${generateStyleString(leftColumn?.style)}">`;
      if (leftColumn && leftColumn.children && leftColumn.children.length > 0) {
        leftColumn.children.forEach(child => {
          elementHtml += generateElementHTML(child);
        });
      } else {
        elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột trái trống</div>';
      }
      elementHtml += `</div>`;

      // Tạo HTML cho cột phải
      elementHtml += `<div class="column" style="${generateStyleString(rightColumn?.style)}">`;
      if (rightColumn && rightColumn.children && rightColumn.children.length > 0) {
        rightColumn.children.forEach(child => {
          elementHtml += generateElementHTML(child);
        });
      } else {
        elementHtml += '<div style="text-align: center; padding: 20px; color: #999;">Cột phải trống</div>';
      }
      elementHtml += `</div>`;

      elementHtml += `</div>`;
      break;
    }

    default:
      elementHtml = `<div>${element.type}</div>`;
  }

  return elementHtml;
};

// Tạo HTML từ emailElements
export const generateHTML = (emailElements: EmailElement[], emailData: Record<string, unknown>): string => {
  // Tạo phần đầu của HTML
  let html = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${emailData['subject'] || 'Email Template'}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      line-height: 1.5;
      color: #333333;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      background-color: #ffffff;
    }
    img {
      max-width: 100%;
      height: auto;
    }
    a {
      color: #0070f3;
      text-decoration: underline;
    }
    .button {
      display: inline-block;
      padding: 10px 20px;
      background-color: #0070f3;
      color: #ffffff;
      text-decoration: none;
      border-radius: 4px;
      font-weight: bold;
    }
    .column {
      padding: 10px;
    }
    .two-columns {
      display: table;
      width: 100%;
    }
    .two-columns .column {
      display: table-cell;
      width: 50%;
      vertical-align: top;
    }
    @media only screen and (max-width: 480px) {
      .two-columns .column {
        display: block;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
`;

  // Tạo phần thân của HTML từ emailElements
  emailElements.forEach(element => {
    html += generateElementHTML(element);
  });

  // Tạo phần cuối của HTML
  html += `  </div>
</body>
</html>`;

  return html;
};

// Trích xuất CSS từ inline styles trong HTML
export const extractCssFromHtml = (html: string): string => {
  // Tạo một đối tượng để lưu trữ các selector và style tương ứng
  const cssRules: Record<string, Record<string, string>> = {};

  // Tạo một DOM parser để phân tích HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // Lấy tất cả các phần tử có thuộc tính style
  const elementsWithStyle = doc.querySelectorAll('[style]');

  // Duyệt qua từng phần tử và trích xuất style
  elementsWithStyle.forEach((element, index) => {
    const style = element.getAttribute('style');
    if (style) {
      // Tạo một selector duy nhất cho phần tử này
      const tagName = element.tagName.toLowerCase();
      const className = element.className ? `.${element.className.replace(/\s+/g, '.')}` : '';
      const id = element.id ? `#${element.id}` : '';
      const selector = `${tagName}${id}${className}.element-${index}`;

      // Phân tích style thành các thuộc tính riêng lẻ
      const styleProps: Record<string, string> = {};
      style.split(';').forEach(prop => {
        const [name, value] = prop.split(':').map(s => s.trim());
        if (name && value) {
          styleProps[name] = value;
        }
      });

      // Lưu trữ selector và style
      cssRules[selector] = styleProps;
    }
  });

  // Chuyển đổi cssRules thành chuỗi CSS
  let cssString = '';
  Object.entries(cssRules).forEach(([selector, props]) => {
    cssString += `${selector} {\n`;
    Object.entries(props).forEach(([prop, value]) => {
      cssString += `  ${prop}: ${value};\n`;
    });
    cssString += '}\n\n';
  });

  // Thêm các style từ thẻ <style> trong HTML
  const styleElements = doc.querySelectorAll('style');
  styleElements.forEach(styleElement => {
    cssString += styleElement.textContent + '\n\n';
  });

  return cssString;
};

// Hàm tạo CSS responsive cho các kích thước màn hình khác nhau
export const generateResponsiveCSS = (viewportSize: ViewportSize): string => {
  switch (viewportSize) {
    case 'desktop':
      return `
        .email-container {
          max-width: 600px;
        }
      `;
    case 'tablet':
      return `
        .email-container {
          max-width: 480px;
        }
        .two-columns .column {
          width: 100% !important;
          display: block !important;
          margin-bottom: 20px;
        }
      `;
    case 'mobile':
      return `
        .email-container {
          max-width: 320px;
        }
        .two-columns .column {
          width: 100% !important;
          display: block !important;
          margin-bottom: 20px;
        }
        h1 {
          font-size: 24px !important;
        }
        h2 {
          font-size: 20px !important;
        }
        p {
          font-size: 14px !important;
        }
      `;
    default:
      return '';
  }
};

// Hàm áp dụng theme cho email
export const applyTheme = (elements: EmailElement[], theme: ThemeSettings): EmailElement[] => {
  return elements.map(element => {
    const newElement = { ...element };

    // Áp dụng theme cho các phần tử
    if (newElement.style) {
      if (newElement.type === 'button') {
        newElement.style = {
          ...newElement.style,
          backgroundColor: theme.primaryColor,
          color: theme.textColor,
          borderRadius: theme.borderRadius
        };
      } else if (newElement.type === 'heading') {
        newElement.style = {
          ...newElement.style,
          color: theme.primaryColor,
          fontFamily: theme.fontFamily
        };
      } else if (newElement.type === 'text') {
        newElement.style = {
          ...newElement.style,
          color: theme.textColor,
          fontFamily: theme.fontFamily
        };
      }
    }

    // Áp dụng theme cho các phần tử con
    if (newElement.children && newElement.children.length > 0) {
      newElement.children = applyTheme(newElement.children, theme);
    }

    return newElement;
  });
};

// Hàm xuất email theo định dạng
export const exportEmail = (format: ExportFormat, html: string, emailData: Record<string, unknown>): string | Blob => {
  switch (format) {
    case 'html':
      return html;
    case 'json':
      return JSON.stringify({ html, data: emailData }, null, 2);
    case 'image':
      // Tạo blob cho hình ảnh (giả lập)
      return new Blob([html], { type: 'text/html' });
    case 'pdf':
      // Tạo blob cho PDF (giả lập)
      return new Blob([html], { type: 'application/pdf' });
    default:
      return html;
  }
};
