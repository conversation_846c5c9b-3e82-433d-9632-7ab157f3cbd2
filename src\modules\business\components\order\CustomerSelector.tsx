import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Input,
  Button,
  Icon,
  FormItem,
} from '@/shared/components/common';
import { useCustomerQuery } from '../../hooks/useCustomerQuery';
import { OrderCustomerDto, AddressDto } from '../../types/order.types';
import { UserConvertCustomerListItemDto } from '../../types/customer.types';
import { debounce } from 'lodash';

interface CustomerSelectorProps {
  selectedCustomer?: OrderCustomerDto;
  onCustomerSelect: (customer: OrderCustomerDto) => void;
  onCustomerChange: (customer: Partial<OrderCustomerDto>) => void;
}

/**
 * Component chọn/tạo khách hàng cho đơn hàng
 */
const CustomerSelector: React.FC<CustomerSelectorProps> = ({
  selectedCustomer,
  onCustomerSelect,
  onCustomerChange,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const [searchTerm, setSearchTerm] = useState('');


  // Hook lấy danh sách khách hàng
  const { data: customersData, isLoading } = useCustomerQuery({
    search: searchTerm,
    limit: 10,
  });

  // Debounced search
  const debouncedSearch = useMemo(
    () => debounce((value: string) => {
      setSearchTerm(value);
    }, 300),
    []
  );

  // Xử lý tìm kiếm khách hàng
  const handleSearchChange = useCallback((value: string) => {
    debouncedSearch(value);
  }, [debouncedSearch]);

  // Helper function to safely extract email from customer data
  const getCustomerEmail = (email: { primary?: string } | string | null): string => {
    if (!email) return '';
    if (typeof email === 'string') return email;
    return email.primary || '';
  };

  // Xử lý chọn khách hàng từ danh sách
  const handleSelectExistingCustomer = useCallback((customer: UserConvertCustomerListItemDto) => {
    const orderCustomer: OrderCustomerDto = {
      id: customer.id,
      name: customer.name || '',
      email: getCustomerEmail(customer.email),
      phone: customer.phone || '',
      address: {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
    };
    onCustomerSelect(orderCustomer);
  }, [onCustomerSelect]);

  // Xử lý thay đổi thông tin khách hàng
  const handleCustomerInfoChange = useCallback((field: string, value: string) => {
    if (field.startsWith('address.')) {
      const addressField = field.replace('address.', '');
      onCustomerChange({
        address: {
          ...selectedCustomer?.address,
          [addressField]: value,
        } as AddressDto,
      });
    } else {
      onCustomerChange({ [field]: value });
    }
  }, [selectedCustomer, onCustomerChange]);



  // Render thông tin khách hàng đã chọn
  const renderSelectedCustomer = () => {
    if (!selectedCustomer) return null;

    return (
      <Card className="mt-4">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6">
              {t('business:order.customerInfo')}
            </Typography>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onCustomerSelect({} as OrderCustomerDto)}
            >
              <Icon name="x" size="sm" />
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('business:customer.form.name')}>
              <Input
                value={selectedCustomer.name}
                onChange={(e) => handleCustomerInfoChange('name', e.target.value)}
                placeholder={t('business:customer.form.namePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.phone')}>
              <Input
                value={selectedCustomer.phone}
                onChange={(e) => handleCustomerInfoChange('phone', e.target.value)}
                placeholder={t('business:customer.form.phonePlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.email')}>
              <Input
                type="email"
                value={selectedCustomer.email}
                onChange={(e) => handleCustomerInfoChange('email', e.target.value)}
                placeholder={t('business:customer.form.emailPlaceholder')}
                fullWidth
              />
            </FormItem>

            <FormItem label={t('business:customer.form.address')}>
              <Input
                value={selectedCustomer.address?.address || ''}
                onChange={(e) => handleCustomerInfoChange('address.address', e.target.value)}
                placeholder={t('business:customer.form.addressPlaceholder')}
                fullWidth
              />
            </FormItem>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div>
      <Typography variant="h6" className="mb-4">
        {t('business:order.selectCustomer')}
      </Typography>

      {!selectedCustomer?.name && (
        <>
          <div className="mb-4">
            <Input
              placeholder={t('business:order.searchCustomerPlaceholder')}
              onChange={(e) => handleSearchChange(e.target.value)}
              leftIcon={<Icon name="search" size="sm" />}
              fullWidth
            />
          </div>

          {/* Danh sách khách hàng tìm được */}
          {searchTerm && (
            <Card className="mb-4">
              <div className="p-4">
                <Typography variant="subtitle2" className="mb-2">
                  {t('business:order.searchResults')}
                </Typography>
                {isLoading ? (
                  <div className="text-center py-4">
                    <Typography variant="body2">
                      {t('common:loading')}
                    </Typography>
                  </div>
                ) : customersData?.items?.length ? (
                  <div className="space-y-2">
                    {customersData.items.map((customer: UserConvertCustomerListItemDto) => (
                      <div
                        key={customer.id}
                        className="flex items-center justify-between p-2 border rounded hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleSelectExistingCustomer(customer)}
                      >
                        <div>
                          <Typography variant="subtitle2">{customer.name || 'N/A'}</Typography>
                          <Typography variant="caption" className="text-gray-500">
                            {customer.phone || 'N/A'} • {getCustomerEmail(customer.email) || 'N/A'}
                          </Typography>
                        </div>
                        <Icon name="chevron-right" size="sm" />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <Typography variant="body2" className="text-gray-500">
                      {t('business:order.noCustomersFound')}
                    </Typography>
                  </div>
                )}
              </div>
            </Card>
          )}


        </>
      )}

      {selectedCustomer?.name && renderSelectedCustomer()}
    </div>
  );
};

export default CustomerSelector;
