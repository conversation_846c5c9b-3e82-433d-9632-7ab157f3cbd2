import {
  ValidationRule,
  ValidationResult,
  ValidationContext,
  AsyncValidator,
  SyncValidator,
  ValidationError,
  ValidationWarning,
  ValidationInfo
} from './types';
import { ValidationRules, CompositeRules } from './rules';
// import { defaultMessageProvider } from './messages';

/**
 * Async validators for server-side validation
 */
export const AsyncValidators = {
  /**
   * Check if email is unique
   */
  uniqueEmail: (
    checkFunction: (email: string) => Promise<boolean>,
    message = 'Email is already taken'
  ): AsyncValidator<string> => {
    return async (value: string, context?: ValidationContext): Promise<ValidationResult> => {
      if (!value || typeof value !== 'string') {
        return { isValid: true, errors: [], warnings: [], infos: [] };
      }

      try {
        const isUnique = await checkFunction(value);

        if (!isUnique) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message,
              code: 'EMAIL_NOT_UNIQUE',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return { isValid: true, errors: [], warnings: [], infos: [] };
      } catch {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: 'Unable to verify email uniqueness',
            code: 'EMAIL_CHECK_FAILED',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },

  /**
   * Check if username is unique
   */
  uniqueUsername: (
    checkFunction: (username: string) => Promise<boolean>,
    message = 'Username is already taken'
  ): AsyncValidator<string> => {
    return async (value: string, context?: ValidationContext): Promise<ValidationResult> => {
      if (!value || typeof value !== 'string') {
        return { isValid: true, errors: [], warnings: [], infos: [] };
      }

      try {
        const isUnique = await checkFunction(value);

        if (!isUnique) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message,
              code: 'USERNAME_NOT_UNIQUE',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return { isValid: true, errors: [], warnings: [], infos: [] };
      } catch {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: 'Unable to verify username uniqueness',
            code: 'USERNAME_CHECK_FAILED',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },

  /**
   * Validate domain availability
   */
  domainAvailable: (
    checkFunction: (domain: string) => Promise<boolean>,
    message = 'Domain is not available'
  ): AsyncValidator<string> => {
    return async (value: string, context?: ValidationContext): Promise<ValidationResult> => {
      if (!value || typeof value !== 'string') {
        return { isValid: true, errors: [], warnings: [], infos: [] };
      }

      try {
        const isAvailable = await checkFunction(value);

        if (!isAvailable) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message,
              code: 'DOMAIN_NOT_AVAILABLE',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return { isValid: true, errors: [], warnings: [], infos: [] };
      } catch {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: 'Unable to check domain availability',
            code: 'DOMAIN_CHECK_FAILED',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },

  /**
   * Validate file upload
   */
  fileUpload: (
    uploadFunction: (file: File) => Promise<{ success: boolean; url?: string; error?: string }>,
    message = 'File upload failed'
  ): AsyncValidator<File> => {
    return async (value: File, context?: ValidationContext): Promise<ValidationResult> => {
      if (!value || !(value instanceof File)) {
        return { isValid: true, errors: [], warnings: [], infos: [] };
      }

      try {
        const result = await uploadFunction(value);

        if (!result.success) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message: result.error || message,
              code: 'FILE_UPLOAD_FAILED',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return {
          isValid: true,
          errors: [],
          warnings: [],
          infos: result.url ? [{
            field: context?.field || '',
            message: `File uploaded successfully: ${result.url}`,
            code: 'FILE_UPLOAD_SUCCESS',
            severity: 'info',
          }] : []
        };
      } catch {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: 'File upload error occurred',
            code: 'FILE_UPLOAD_ERROR',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },

  /**
   * Validate API endpoint
   */
  apiEndpoint: (
    testFunction: (url: string) => Promise<boolean>,
    message = 'API endpoint is not reachable'
  ): AsyncValidator<string> => {
    return async (value: string, context?: ValidationContext): Promise<ValidationResult> => {
      if (!value || typeof value !== 'string') {
        return { isValid: true, errors: [], warnings: [], infos: [] };
      }

      try {
        const isReachable = await testFunction(value);

        if (!isReachable) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message,
              code: 'API_ENDPOINT_UNREACHABLE',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return { isValid: true, errors: [], warnings: [], infos: [] };
      } catch {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: 'Unable to test API endpoint',
            code: 'API_ENDPOINT_TEST_FAILED',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },

  /**
   * Custom async validator
   */
  custom: <T>(
    validatorFunction: (value: T) => Promise<{ isValid: boolean; message?: string; code?: string }>,
    defaultMessage = 'Validation failed'
  ): AsyncValidator<T> => {
    return async (value: T, context?: ValidationContext): Promise<ValidationResult> => {
      try {
        const result = await validatorFunction(value);

        if (!result.isValid) {
          return {
            isValid: false,
            errors: [{
              field: context?.field || '',
              message: result.message || defaultMessage,
              code: result.code || 'CUSTOM_ASYNC_ERROR',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };
        }

        return { isValid: true, errors: [], warnings: [], infos: [] };
      } catch (error) {
        return {
          isValid: false,
          errors: [{
            field: context?.field || '',
            message: error instanceof Error ? error.message : defaultMessage,
            code: 'CUSTOM_ASYNC_ERROR',
            severity: 'error',
          }],
          warnings: [],
          infos: [],
        };
      }
    };
  },
};

/**
 * Validator builder utilities
 */
export const ValidatorBuilder = {
  /**
   * Create a validation rule from a sync validator
   */
  fromSync: <T>(
    name: string,
    validator: SyncValidator<T>,
    message?: string,
    severity: 'error' | 'warning' | 'info' = 'error'
  ): ValidationRule<T> => {
    const rule: ValidationRule<T> = {
      name,
      validator,
      severity,
    };

    if (message) {
      rule.message = message;
    }

    return rule;
  },

  /**
   * Create a validation rule from an async validator
   */
  fromAsync: <T>(
    name: string,
    validator: AsyncValidator<T>,
    message?: string,
    severity: 'error' | 'warning' | 'info' = 'error'
  ): ValidationRule<T> => {
    const rule: ValidationRule<T> = {
      name,
      validator,
      severity,
    };

    if (message) {
      rule.message = message;
    }

    return rule;
  },

  /**
   * Combine multiple validation rules
   */
  combine: <T>(...rules: ValidationRule<T>[]): ValidationRule<T> => {
    return {
      name: 'combined',
      validator: async (value: T, context?: ValidationContext): Promise<ValidationResult> => {
        const errors: ValidationError[] = [];
        const warnings: ValidationWarning[] = [];
        const infos: ValidationInfo[] = [];

        for (const rule of rules) {
          try {
            const result = await Promise.resolve(rule.validator(value, context));

            errors.push(...result.errors);
            warnings.push(...result.warnings);
            infos.push(...result.infos);
          } catch (error) {
            errors.push({
              field: context?.field || '',
              message: `Rule "${rule.name}" failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
              code: 'RULE_ERROR',
              severity: 'error',
            });
          }
        }

        return {
          isValid: errors.length === 0,
          errors,
          warnings,
          infos,
        };
      },
      severity: 'error',
    };
  },

  /**
   * Create conditional validation rule
   */
  conditional: <T>(
    condition: (value: T, context?: ValidationContext) => boolean,
    rule: ValidationRule<T>
  ): ValidationRule<T> => {
    const conditionalRule: ValidationRule<T> = {
      name: `conditional_${rule.name}`,
      validator: async (value: T, context?: ValidationContext): Promise<ValidationResult> => {
        if (!condition(value, context)) {
          return { isValid: true, errors: [], warnings: [], infos: [] };
        }

        return Promise.resolve(rule.validator(value, context));
      },
      severity: rule.severity || 'error',
      enabled: (context) => condition(null as unknown as T, context),
    };

    if (rule.message) {
      conditionalRule.message = rule.message;
    }

    return conditionalRule;
  },

  /**
   * Create debounced validation rule
   */
  debounced: <T>(
    rule: ValidationRule<T>,
    debounceMs: number
  ): ValidationRule<T> => {
    let timeoutId: NodeJS.Timeout | null = null;

    const debouncedRule: ValidationRule<T> = {
      name: `debounced_${rule.name}`,
      validator: (value: T, context?: ValidationContext): Promise<ValidationResult> => {
        return new Promise((resolve) => {
          if (timeoutId) {
            clearTimeout(timeoutId);
          }

          timeoutId = setTimeout(async () => {
            const result = await Promise.resolve(rule.validator(value, context));
            resolve(result);
          }, debounceMs);
        });
      },
      severity: rule.severity || 'error',
    };

    if (rule.message) {
      debouncedRule.message = rule.message;
    }

    return debouncedRule;
  },

  /**
   * Create cached validation rule
   */
  cached: <T>(
    rule: ValidationRule<T>,
    cacheKey?: (value: T, context?: ValidationContext) => string,
    ttlMs = 5 * 60 * 1000 // 5 minutes
  ): ValidationRule<T> => {
    const cache = new Map<string, { result: ValidationResult; timestamp: number }>();

    const cachedRule: ValidationRule<T> = {
      name: `cached_${rule.name}`,
      validator: async (value: T, context?: ValidationContext): Promise<ValidationResult> => {
        const key = cacheKey
          ? cacheKey(value, context)
          : `${context?.field || 'unknown'}:${JSON.stringify(value)}`;

        const cached = cache.get(key);
        if (cached && Date.now() - cached.timestamp < ttlMs) {
          return cached.result;
        }

        const result = await Promise.resolve(rule.validator(value, context));
        cache.set(key, { result, timestamp: Date.now() });

        return result;
      },
      severity: rule.severity || 'error',
    };

    if (rule.message) {
      cachedRule.message = rule.message;
    }

    return cachedRule;
  },
};

/**
 * Common validator combinations
 */
export const CommonValidators = {
  /**
   * Required email validator
   */
  requiredEmail: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.email(message)
    );
  },

  /**
   * Required phone validator
   */
  requiredPhone: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.phone(message)
    );
  },

  /**
   * Required Vietnamese phone validator
   */
  requiredPhoneVN: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.phoneVN(message)
    );
  },

  /**
   * Strong password validator
   */
  strongPassword: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.minLength(8, message),
      ValidationRules.strongPassword(message)
    );
  },

  /**
   * Required positive number validator
   */
  requiredPositiveNumber: (message?: string): ValidationRule<number> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.numeric(message),
      ValidationRules.positive(message)
    );
  },

  /**
   * File upload validator
   */
  fileUpload: (
    maxSizeInBytes: number,
    allowedTypes: string[],
    message?: string
  ): ValidationRule<File> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      ValidationRules.fileSize(maxSizeInBytes, message),
      ValidationRules.fileType(allowedTypes, message)
    );
  },

  /**
   * Vietnamese citizen ID validator
   */
  requiredCitizenIdVN: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      CompositeRules.citizenIdVN()
    );
  },

  /**
   * Vietnamese name validator
   */
  requiredNameVN: (message?: string): ValidationRule<string> => {
    return ValidatorBuilder.combine(
      ValidationRules.required(message),
      CompositeRules.nameVN()
    );
  },
};
