import React from 'react';
import { Card } from '@/shared/components/common';

/**
 * Simple demo page to test Generic Data Page system
 */
const SimpleGenericDemo: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          🚀 Generic Data Page System Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          H<PERSON> thống tạo trang quản lý dữ liệu từ JSON configuration
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-500 rounded-lg flex items-center justify-center text-white text-2xl mx-auto mb-4">
              👥
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Demo Users
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              CRUD đầy đủ với validation và custom renderers
            </p>
            <a
              href="/demo/generic-data-page/users"
              className="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Xem Demo →
            </a>
          </div>
        </Card>

        <Card className="p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-500 rounded-lg flex items-center justify-center text-white text-2xl mx-auto mb-4">
              📦
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Demo Products
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              Custom renderers phức tạp và price formatting
            </p>
            <a
              href="/demo/generic-data-page/products"
              className="inline-flex items-center px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              Xem Demo →
            </a>
          </div>
        </Card>

        <Card className="p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-orange-500 rounded-lg flex items-center justify-center text-white text-2xl mx-auto mb-4">
              📋
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              Demo Orders
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
              Read-only page với complex data display
            </p>
            <a
              href="/demo/generic-data-page/orders"
              className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              Xem Demo →
            </a>
          </div>
        </Card>
      </div>

      <Card className="p-6 mt-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          ✨ Tính năng chính
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[
            'CRUD Operations đầy đủ',
            'Form validation tự động',
            'Custom renderers',
            'Bulk operations',
            'Search & Filter',
            'Sorting & Pagination',
            'Custom handlers',
            'Type-safe TypeScript',
            'Responsive design',
          ].map((feature, index) => (
            <div key={index} className="flex items-center space-x-2">
              <span className="text-green-500">✅</span>
              <span className="text-gray-700 dark:text-gray-300">{feature}</span>
            </div>
          ))}
        </div>
      </Card>

      <Card className="p-6 mt-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          🚀 Quick Start
        </h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              1. Import component
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <code className="text-sm text-gray-800 dark:text-gray-200">
                import {`{ GenericDataPage }`} from '@/shared/components/generic';
              </code>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              2. Tạo configuration
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
{`const config: GenericPageConfig = {
  title: 'Quản lý dữ liệu',
  api: { endpoint: '/api/data', ... },
  table: { columns: [...], rowKey: 'id' },
  form: { fields: [...] }
};`}
              </pre>
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              3. Sử dụng component
            </h3>
            <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
              <code className="text-sm text-gray-800 dark:text-gray-200">
                {`<GenericDataPage config={config} />`}
              </code>
            </div>
          </div>
        </div>
      </Card>

      <div className="text-center mt-8">
        <a
          href="/"
          className="inline-flex items-center px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
        >
          ← Về trang chủ
        </a>
      </div>
    </div>
  );
};

export default SimpleGenericDemo;
