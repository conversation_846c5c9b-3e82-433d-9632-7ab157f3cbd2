import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  ResponsiveGrid,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import {
  MessageSquare,
  Send,
  Users,
  DollarSign,
  Settings,
  FileText,
  BarChart3,
  Plus,
} from 'lucide-react';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

/**
 * Trang tổng quan SMS Marketing
 */
const SmsMarketingPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'sms', 'common']);
  const navigate = useNavigate();

  // Mock data cho demo
  const mockStats = {
    totalSent: 15420,
    totalDelivered: 14890,
    totalFailed: 530,
    totalCost: 771.0,
    deliveryRate: 96.6,
    averageCost: 0.05,
    activeProviders: 3,
    activeCampaigns: 5,
  };

  // Overview stats data
  const overviewStats: OverviewCardProps[] = [
    {
      title: t('sms:overview.stats.totalSent'),
      value: mockStats.totalSent.toLocaleString(),
      description: t('sms:overview.stats.totalSentDescription', '+12% so với tháng trước'),
      icon: Send,
      color: 'blue',
    },
    {
      title: t('sms:overview.stats.totalDelivered'),
      value: mockStats.totalDelivered.toLocaleString(),
      description: t('sms:overview.stats.deliveryRateDescription', '{{rate}}% tỷ lệ thành công', { rate: mockStats.deliveryRate }),
      icon: MessageSquare,
      color: 'green',
    },
    {
      title: t('sms:overview.stats.activeProviders'),
      value: mockStats.activeProviders,
      description: t('sms:overview.stats.activeProvidersDescription', 'Nhà cung cấp hoạt động'),
      icon: Users,
      color: 'orange',
    },
    {
      title: t('sms:overview.stats.totalCost'),
      value: `$${mockStats.totalCost.toFixed(2)}`,
      description: t('sms:overview.stats.averageCostDescription', '${{cost}} trung bình/SMS', { cost: mockStats.averageCost }),
      icon: DollarSign,
      color: 'purple',
    },
  ];

  // Quick actions
  const quickActions = [
    {
      title: t('sms:overview.quickActions.sendSms'),
      description: t('sms:overview.quickActions.sendSmsDescription', 'Gửi tin nhắn SMS nhanh'),
      icon: Send,
      onClick: () => navigate('/marketing/sms/send'),
      color: 'blue' as const,
    },
    {
      title: t('sms:overview.quickActions.createCampaign'),
      description: t('sms:overview.quickActions.createCampaignDescription', 'Tạo chiến dịch SMS mới'),
      icon: Plus,
      onClick: () => navigate('/marketing/sms/campaigns/create'),
      color: 'green' as const,
    },
    {
      title: t('sms:overview.quickActions.createTemplate'),
      description: t('sms:overview.quickActions.createTemplateDescription', 'Tạo mẫu tin nhắn mới'),
      icon: FileText,
      onClick: () => navigate('/marketing/sms/templates/create'),
      color: 'orange' as const,
    },
    {
      title: t('sms:overview.quickActions.manageContacts'),
      description: t('sms:overview.quickActions.manageContactsDescription', 'Quản lý danh sách liên hệ'),
      icon: Users,
      onClick: () => navigate('/marketing/sms/contacts'),
      color: 'purple' as const,
    },
  ];

  // Navigation handlers
  const handleViewCampaigns = () => navigate('/marketing/sms/campaigns');
  const handleViewTemplates = () => navigate('/marketing/sms/templates');
  const handleViewProviders = () => navigate('/integrations/sms');
  const handleViewAnalytics = () => navigate('/marketing/sms/analytics');
  const handleViewSettings = () => navigate('/marketing/sms/settings');

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Stats */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={false}
        skeletonCount={4}
      />

      {/* Quick Actions */}
      <Card className="p-6">
        <Typography variant="h3" className="mb-4">
          {t('sms:overview.quickActions.title', 'Hành động nhanh')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }} gap={4}>
          {quickActions.map((action, index) => (
            <Card
              key={index}
              className="p-4 cursor-pointer hover:shadow-md transition-shadow"
              onClick={action.onClick}
              hoverable
            >
              <div className="flex items-start space-x-3">
                <div className={`p-2 rounded-lg bg-${action.color}-100`}>
                  <action.icon className={`h-5 w-5 text-${action.color}-600`} />
                </div>
                <div className="flex-1">
                  <Typography variant="subtitle2" className="mb-1">
                    {action.title}
                  </Typography>
                  <Typography variant="caption" className="text-muted-foreground">
                    {action.description}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </Card>

      {/* Feature Cards */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 3 }} gap={6}>
        {/* Campaigns */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-blue-100">
                <Send className="h-6 w-6 text-blue-600" />
              </div>
              <Typography variant="h4">
                {t('sms:campaigns.title')}
              </Typography>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewCampaigns}>
              {t('sms:common.view')}
            </Button>
          </div>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('sms:campaigns.description')}
          </Typography>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:campaigns.status.running', 'Đang chạy')}:</span>
              <span className="text-sm font-medium">{mockStats.activeCampaigns}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:campaigns.status.completed', 'Hoàn thành')}:</span>
              <span className="text-sm font-medium">12</span>
            </div>
          </div>
        </Card>

        {/* Templates */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-green-100">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              <Typography variant="h4">
                {t('sms:templates.title')}
              </Typography>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewTemplates}>
              {t('sms:common.view')}
            </Button>
          </div>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('sms:templates.description')}
          </Typography>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:templates.status.active', 'Hoạt động')}:</span>
              <span className="text-sm font-medium">8</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:templates.status.draft', 'Bản nháp')}:</span>
              <span className="text-sm font-medium">3</span>
            </div>
          </div>
        </Card>

        {/* Providers */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-orange-100">
                <Settings className="h-6 w-6 text-orange-600" />
              </div>
              <Typography variant="h4">
                {t('sms:providers.title')}
              </Typography>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewProviders}>
              {t('sms:common.view')}
            </Button>
          </div>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('sms:providers.description')}
          </Typography>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:providers.status.active', 'Hoạt động')}:</span>
              <span className="text-sm font-medium">{mockStats.activeProviders}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">{t('sms:common.total', 'Tổng cộng')}:</span>
              <span className="text-sm font-medium">5</span>
            </div>
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Analytics & Settings */}
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }} gap={6}>
        {/* Analytics */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-purple-100">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <Typography variant="h4">
                {t('sms:analytics.title')}
              </Typography>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewAnalytics}>
              {t('sms:common.view')}
            </Button>
          </div>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('sms:analytics.description')}
          </Typography>
          <Button variant="primary" className="w-full" onClick={handleViewAnalytics}>
            {t('sms:analytics.viewDetailedReport', 'Xem báo cáo chi tiết')}
          </Button>
        </Card>

        {/* Settings */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-gray-100">
                <Settings className="h-6 w-6 text-gray-600" />
              </div>
              <Typography variant="h4">
                {t('sms:settings.title')}
              </Typography>
            </div>
            <Button variant="outline" size="sm" onClick={handleViewSettings}>
              {t('sms:common.view')}
            </Button>
          </div>
          <Typography variant="body2" className="text-muted-foreground mb-4">
            {t('sms:settings.description')}
          </Typography>
          <Button variant="outline" className="w-full" onClick={handleViewSettings}>
            {t('sms:settings.configureSms', 'Cấu hình SMS')}
          </Button>
        </Card>
      </ResponsiveGrid>
    </div>
  );
};

export default SmsMarketingPage;