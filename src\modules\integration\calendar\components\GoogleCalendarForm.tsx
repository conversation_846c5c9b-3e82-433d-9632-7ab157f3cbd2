import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FormItem,
  Input,
  Button,
  Card,
  Typography,
  Toggle,
  Icon,
} from '@/shared/components/common';
import {
  useCreateGoogleCalendarConfiguration,
  useUpdateGoogleCalendarConfiguration,
  useTestGoogleCalendarWithConfiguration,
} from '../hooks';
import type {
  GoogleCalendarConfiguration,
  GoogleCalendarFormData,
  TestGoogleCalendarWithConfigDto,
} from '../types';

interface GoogleCalendarFormProps {
  /**
   * D<PERSON> liệu cấu hình để edit (nếu có)
   */
  initialData?: GoogleCalendarConfiguration;

  /**
   * Callback khi submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;
}

/**
 * Form cấu hình Google Calendar
 */
const GoogleCalendarForm: React.FC<GoogleCalendarFormProps> = ({
  initialData,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Form state
  const [formData, setFormData] = useState<GoogleCalendarFormData>({
    accountName: initialData?.accountName || '',
    clientId: initialData?.clientId || '',
    clientSecret: initialData?.clientSecret || '',
    refreshToken: initialData?.refreshToken || '',
    calendarId: initialData?.calendarId || '',
    isActive: initialData?.isActive ?? true,
    syncEnabled: initialData?.syncEnabled ?? true,
  });

  // Mutations
  const createMutation = useCreateGoogleCalendarConfiguration();
  const updateMutation = useUpdateGoogleCalendarConfiguration();
  const testMutation = useTestGoogleCalendarWithConfiguration();



  // Handle form data change
  const handleChange = useCallback((field: keyof GoogleCalendarFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Handle form submit
  const handleSubmit = useCallback(async (data: GoogleCalendarFormData) => {
    try {
      if (initialData) {
        // Update existing configuration
        await updateMutation.mutateAsync({
          id: initialData.id,
          data,
        });
      } else {
        // Create new configuration
        await createMutation.mutateAsync(data);
      }

      onSuccess?.();
    } catch (err) {
      console.error('Form submission error:', err);
    }
  }, [initialData, updateMutation, createMutation, onSuccess]);

  // Handle test connection
  const handleTestConnection = useCallback(async () => {
    try {
      const testData: TestGoogleCalendarWithConfigDto = {
        calendarConfig: {
          accountName: formData.accountName,
          clientId: formData.clientId,
          clientSecret: formData.clientSecret,
          refreshToken: formData.refreshToken,
          calendarId: formData.calendarId,
        },
        testInfo: {
          testEventTitle: 'Test Event from RedAI',
          testEventDescription: 'This is a test event to verify calendar integration',
        },
      };

      await testMutation.mutateAsync(testData);
    } catch {
      // Error handled by mutation
    }
  }, [formData, testMutation]);

  const isLoading = createMutation.isPending || updateMutation.isPending;
  const isTestLoading = testMutation.isPending;

  return (
    <Card className="w-full">
      <div className="p-6">
        <div className="flex items-center gap-3 mb-6">
          <Icon name="calendar" size="lg" className="text-primary" />
          <Typography variant="h3">
            {initialData
              ? t('integration:calendar.form.editTitle')
              : t('integration:calendar.form.createTitle')}
          </Typography>
        </div>

        <form onSubmit={(e) => {
          e.preventDefault();
          handleSubmit(formData);
        }}>
          {/* Account Name */}
          <FormItem
            label={t('integration:calendar.form.accountName.label')}
            name="accountName"
            required
            helpText={t('integration:calendar.form.accountName.helpText')}
          >
            <Input
              value={formData.accountName}
              onChange={(e) => handleChange('accountName', e.target.value)}
              placeholder={t('integration:calendar.form.accountName.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Client ID */}
          <FormItem
            label={t('integration:calendar.form.clientId.label')}
            name="clientId"
            required
            helpText={t('integration:calendar.form.clientId.helpText')}
          >
            <Input
              value={formData.clientId}
              onChange={(e) => handleChange('clientId', e.target.value)}
              placeholder={t('integration:calendar.form.clientId.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Client Secret */}
          <FormItem
            label={t('integration:calendar.form.clientSecret.label')}
            name="clientSecret"
            required
            helpText={t('integration:calendar.form.clientSecret.helpText')}
          >
            <Input
              type="password"
              value={formData.clientSecret}
              onChange={(e) => handleChange('clientSecret', e.target.value)}
              placeholder={t('integration:calendar.form.clientSecret.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Refresh Token */}
          <FormItem
            label={t('integration:calendar.form.refreshToken.label')}
            name="refreshToken"
            required
            helpText={t('integration:calendar.form.refreshToken.helpText')}
          >
            <Input
              type="password"
              value={formData.refreshToken}
              onChange={(e) => handleChange('refreshToken', e.target.value)}
              placeholder={t('integration:calendar.form.refreshToken.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Calendar ID */}
          <FormItem
            label={t('integration:calendar.form.calendarId.label')}
            name="calendarId"
            helpText={t('integration:calendar.form.calendarId.helpText')}
          >
            <Input
              value={formData.calendarId}
              onChange={(e) => handleChange('calendarId', e.target.value)}
              placeholder={t('integration:calendar.form.calendarId.placeholder')}
              fullWidth
            />
          </FormItem>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              label={t('integration:calendar.form.isActive.label')}
              name="isActive"
              inline
              helpText={t('integration:calendar.form.isActive.helpText')}
            >
              <Toggle
                checked={formData.isActive}
                onChange={(checked) => handleChange('isActive', checked)}
              />
            </FormItem>

            <FormItem
              label={t('integration:calendar.form.syncEnabled.label')}
              name="syncEnabled"
              inline
              helpText={t('integration:calendar.form.syncEnabled.helpText')}
            >
              <Toggle
                checked={formData.syncEnabled}
                onChange={(checked) => handleChange('syncEnabled', checked)}
              />
            </FormItem>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleTestConnection}
              isLoading={isTestLoading}
              disabled={isLoading}
              leftIcon={<Icon name="zap" size="sm" />}
              className="flex-1"
            >
              {t('integration:calendar.form.testConnection')}
            </Button>

            <div className="flex gap-3 flex-1">
              {onCancel && (
                <Button
                  type="button"
                  variant="ghost"
                  onClick={onCancel}
                  disabled={isLoading}
                  className="flex-1"
                >
                  {t('common:cancel')}
                </Button>
              )}

              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
                className="flex-1"
              >
                {initialData ? t('common:update') : t('common:create')}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default GoogleCalendarForm;
