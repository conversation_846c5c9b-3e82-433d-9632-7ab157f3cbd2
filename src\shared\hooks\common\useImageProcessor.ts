/**
 * Hook xử lý ảnh
 */
import { useState, useCallback } from 'react';
import {
  processImage,
  createThumbnail,
  convertToWebP,
  convertToJPEG,
  convertToPNG,
  ImageProcessOptions,
} from '@/shared/utils/image-utils';
import {
  ImageFormatEnum,
  ImageQualityEnum,
  ImageSizeEnum,
  ImageDimension,
} from '@/shared/types/image-format.enum';
import { isImageFile } from '@/shared/utils/file-utils';

/**
 * Kết quả xử lý ảnh
 */
export interface ProcessedImage {
  /**
   * File ảnh đã xử lý
   */
  file: File;

  /**
   * URL tạm thời để hiển thị ảnh
   */
  previewUrl: string;

  /**
   * Kích thước file (bytes)
   */
  size: number;

  /**
   * Chiều rộng ảnh
   */
  width?: number;

  /**
   * Chiều cao ảnh
   */
  height?: number;
}

/**
 * Tham số cho hook useImageProcessor
 */
export interface UseImageProcessorOptions {
  /**
   * Định dạng ảnh đầu ra mặc định
   * @default ImageFormatEnum.JPEG
   */
  defaultFormat?: ImageFormatEnum;

  /**
   * Chất lượng ảnh mặc định
   * @default ImageQualityEnum.HIGH
   */
  defaultQuality?: number;

  /**
   * Kích thước ảnh mặc định
   * @default ImageSizeEnum.LARGE
   */
  defaultSize?: ImageSizeEnum;

  /**
   * Kích thước tùy chỉnh mặc định
   */
  defaultCustomDimension?: ImageDimension;

  /**
   * Kích thước tối đa của file (bytes)
   * @default 2097152 (2MB)
   */
  maxSizeBytes?: number;

  /**
   * Giữ nguyên tỷ lệ khung hình
   * @default true
   */
  maintainAspectRatio?: boolean;

  /**
   * Tự động tạo thumbnail
   * @default false
   */
  autoGenerateThumbnail?: boolean;

  /**
   * Kích thước tối đa của thumbnail
   * @default 200
   */
  thumbnailSize?: number;
}

/**
 * Hook xử lý ảnh với các chức năng nén và chuyển đổi định dạng
 *
 * @example
 * // Sử dụng hook
 * const {
 *   processedImage,
 *   thumbnail,
 *   isProcessing,
 *   error,
 *   processImageFile,
 *   convertFormat,
 *   generateThumbnail,
 *   reset
 * } = useImageProcessor({
 *   defaultFormat: ImageFormatEnum.WEBP,
 *   defaultQuality: ImageQualityEnum.HIGH,
 *   autoGenerateThumbnail: true
 * });
 *
 * // Xử lý khi chọn file
 * const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
 *   if (e.target.files && e.target.files[0]) {
 *     await processImageFile(e.target.files[0]);
 *   }
 * };
 */
export function useImageProcessor(options: UseImageProcessorOptions = {}) {
  const {
    defaultFormat = ImageFormatEnum.JPEG,
    defaultQuality = ImageQualityEnum.HIGH,
    defaultSize = ImageSizeEnum.LARGE,
    defaultCustomDimension,
    maxSizeBytes = 2 * 1024 * 1024, // 2MB
    maintainAspectRatio = true,
    autoGenerateThumbnail = false,
    thumbnailSize = 200,
  } = options;

  // State
  const [processedImage, setProcessedImage] = useState<ProcessedImage | null>(null);
  const [thumbnail, setThumbnail] = useState<ProcessedImage | null>(null);
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Tạo đối tượng ProcessedImage từ File
   */
  const createProcessedImage = useCallback(async (file: File): Promise<ProcessedImage> => {
    const previewUrl = URL.createObjectURL(file);

    // Lấy kích thước ảnh
    return new Promise(resolve => {
      const img = new Image();
      img.onload = () => {
        resolve({
          file,
          previewUrl,
          size: file.size,
          width: img.width,
          height: img.height,
        });

        // Giải phóng bộ nhớ
        img.onload = null;
      };

      img.onerror = () => {
        // Nếu không lấy được kích thước, vẫn trả về thông tin cơ bản
        resolve({
          file,
          previewUrl,
          size: file.size,
        });
      };

      img.src = previewUrl;
    });
  }, []);

  /**
   * Xử lý file ảnh
   */
  const processImageFile = useCallback(
    async (
      file: File,
      customOptions?: {
        outputFormat?: ImageFormatEnum;
        quality?: number;
        size?: ImageSizeEnum;
        customDimension?: ImageDimension;
      }
    ): Promise<ProcessedImage | null> => {
      // Reset state
      setError(null);

      // Kiểm tra file có phải là ảnh không
      if (!isImageFile(file)) {
        setError('File không phải là ảnh');
        return null;
      }

      try {
        setIsProcessing(true);

        // Xử lý ảnh
        const processOptions: ImageProcessOptions = {
          outputFormat: customOptions?.outputFormat || defaultFormat,
          quality: customOptions?.quality || defaultQuality,
          size: customOptions?.size || defaultSize,
          maxSizeBytes,
          maintainAspectRatio,
        };

        const customDimension = customOptions?.customDimension || defaultCustomDimension;
        if (customDimension) {
          processOptions.customDimension = customDimension;
        }

        const processedFile = await processImage(file, processOptions);

        // Tạo đối tượng ProcessedImage
        const result = await createProcessedImage(processedFile);

        // Cập nhật state
        setProcessedImage(result);

        // Tạo thumbnail nếu cần
        if (autoGenerateThumbnail) {
          try {
            const thumbnailFile = await createThumbnail(
              processedFile,
              thumbnailSize,
              defaultFormat,
              ImageQualityEnum.MEDIUM
            );

            const thumbnailResult = await createProcessedImage(thumbnailFile);
            setThumbnail(thumbnailResult);
          } catch (thumbnailError) {
            console.error('Lỗi khi tạo thumbnail:', thumbnailError);
            // Không set error vì đây không phải lỗi chính
          }
        }

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Lỗi không xác định khi xử lý ảnh';
        setError(errorMessage);
        return null;
      } finally {
        setIsProcessing(false);
      }
    },
    [
      defaultFormat,
      defaultQuality,
      defaultSize,
      defaultCustomDimension,
      maxSizeBytes,
      maintainAspectRatio,
      autoGenerateThumbnail,
      thumbnailSize,
      createProcessedImage,
    ]
  );

  /**
   * Chuyển đổi định dạng ảnh
   */
  const convertFormat = useCallback(
    async (
      format: ImageFormatEnum,
      quality: number = defaultQuality
    ): Promise<ProcessedImage | null> => {
      if (!processedImage) {
        setError('Không có ảnh để chuyển đổi');
        return null;
      }

      try {
        setIsProcessing(true);

        // Xử lý ảnh với định dạng mới
        const convertOptions: ImageProcessOptions = {
          outputFormat: format,
          quality,
          size: defaultSize,
          maxSizeBytes,
          maintainAspectRatio,
        };

        if (defaultCustomDimension) {
          convertOptions.customDimension = defaultCustomDimension;
        }

        const convertedFile = await processImage(processedImage.file, convertOptions);

        // Tạo đối tượng ProcessedImage
        const result = await createProcessedImage(convertedFile);

        // Cập nhật state
        setProcessedImage(result);

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Lỗi không xác định khi chuyển đổi định dạng';
        setError(errorMessage);
        return null;
      } finally {
        setIsProcessing(false);
      }
    },
    [
      processedImage,
      defaultQuality,
      defaultSize,
      defaultCustomDimension,
      maxSizeBytes,
      maintainAspectRatio,
      createProcessedImage,
    ]
  );

  /**
   * Tạo thumbnail từ ảnh đã xử lý
   */
  const generateThumbnail = useCallback(
    async (
      size: number = thumbnailSize,
      format: ImageFormatEnum = defaultFormat,
      quality: number = ImageQualityEnum.MEDIUM
    ): Promise<ProcessedImage | null> => {
      if (!processedImage) {
        setError('Không có ảnh để tạo thumbnail');
        return null;
      }

      try {
        setIsProcessing(true);

        // Tạo thumbnail
        const thumbnailFile = await createThumbnail(processedImage.file, size, format, quality);

        // Tạo đối tượng ProcessedImage
        const result = await createProcessedImage(thumbnailFile);

        // Cập nhật state
        setThumbnail(result);

        return result;
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Lỗi không xác định khi tạo thumbnail';
        setError(errorMessage);
        return null;
      } finally {
        setIsProcessing(false);
      }
    },
    [processedImage, thumbnailSize, defaultFormat, createProcessedImage]
  );

  /**
   * Reset state
   */
  const reset = useCallback(() => {
    // Giải phóng URL
    if (processedImage?.previewUrl) {
      URL.revokeObjectURL(processedImage.previewUrl);
    }

    if (thumbnail?.previewUrl) {
      URL.revokeObjectURL(thumbnail.previewUrl);
    }

    // Reset state
    setProcessedImage(null);
    setThumbnail(null);
    setIsProcessing(false);
    setError(null);
  }, [processedImage, thumbnail]);

  return {
    // State
    processedImage,
    thumbnail,
    isProcessing,
    error,

    // Methods
    processImageFile,
    convertFormat,
    generateThumbnail,
    reset,

    // Utility methods
    convertToWebP: async (quality = defaultQuality) => {
      if (!processedImage) return null;
      const webpFile = await convertToWebP(processedImage.file, quality);
      const result = await createProcessedImage(webpFile);
      setProcessedImage(result);
      return result;
    },

    convertToJPEG: async (quality = defaultQuality) => {
      if (!processedImage) return null;
      const jpegFile = await convertToJPEG(processedImage.file, quality);
      const result = await createProcessedImage(jpegFile);
      setProcessedImage(result);
      return result;
    },

    convertToPNG: async () => {
      if (!processedImage) return null;
      const pngFile = await convertToPNG(processedImage.file);
      const result = await createProcessedImage(pngFile);
      setProcessedImage(result);
      return result;
    },
  };
}

export default useImageProcessor;
