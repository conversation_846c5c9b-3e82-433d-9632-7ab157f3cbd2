import React from 'react';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@/shared/components/common';
import { ColumnVisibility } from '@/shared/hooks/table/useColumnVisibility';

interface ColumnVisibilityMenuProps {
  /**
   * <PERSON><PERSON> sách cấu hình hiển thị cột
   */
  columnSettings: ColumnVisibility[];
  
  /**
   * Hàm xử lý thay đổi hiển thị cột
   */
  onColumnVisibilityChange: (columnId: string, visible: boolean) => void;
  
  /**
   * Hàm lấy label cho cột
   */
  getColumnLabel: (column: ColumnVisibility) => string;
}

/**
 * Component hiển thị menu chọn cột hiển thị
 */
const ColumnVisibilityMenu: React.FC<ColumnVisibilityMenuProps> = ({
  columnSettings,
  onColumnVisibilityChange,
  getColumnLabel,
}) => {
  const { t } = useTranslation();

  return (
    <div>
      {/* "Chọn tất cả" option */}
      <div
        className="flex items-center w-full px-3 py-2 text-left transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
        onClick={(e) => e.stopPropagation()}
      >
        <Checkbox
          id="column-all"
          checked={columnSettings?.every(col => col.id === 'all' || col.visible) || false}
          onChange={(checked) => onColumnVisibilityChange('all', checked)}
          variant="filled"
          size="sm"
          className="mr-2"
        />
        <label 
          htmlFor="column-all" 
          className="flex-grow cursor-pointer"
          onClick={(e) => e.stopPropagation()}
        >
          {t('common:selectAll', 'Chọn tất cả')}
        </label>
      </div>

      {/* Column options */}
      {columnSettings &&
        Array.isArray(columnSettings) &&
        columnSettings
          .filter(column => column.id !== 'all')
          .map((column) => (
            <div
              key={column.id}
              className="flex items-center w-full px-3 py-2 text-left transition-colors hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md"
              onClick={(e) => e.stopPropagation()}
            >
              <Checkbox
                id={`column-${column.id}`}
                checked={column.visible}
                onChange={(checked) => onColumnVisibilityChange(column.id, checked)}
                variant="filled"
                size="sm"
                className="mr-2"
              />
              <label
                htmlFor={`column-${column.id}`}
                className="flex-grow cursor-pointer"
                onClick={(e) => e.stopPropagation()}
              >
                {getColumnLabel(column)}
              </label>
            </div>
          ))
      }
    </div>
  );
};

export default ColumnVisibilityMenu;
