/**
 * Component hiển thị panel quản lý task queue
 */
import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import { Task, TaskStatus, TaskType, FileUploadTask } from '@/shared/types/task-queue.types';
import TaskItem from './TaskItem';
import TaskQueueHeader from './TaskQueueHeader';
import TaskQueueEmpty from './TaskQueueEmpty';
import { ScrollArea } from '@/shared/components/common';

/**
 * Props cho TaskQueuePanel
 */
export interface TaskQueuePanelProps {
  /**
   * Có hiển thị panel không
   */
  isVisible?: boolean;

  /**
   * Callback khi đóng panel
   */
  onClose?: () => void;

  /**
   * Chiều rộng của panel
   */
  width?: number;

  /**
   * <PERSON><PERSON><PERSON> cao tối đa của panel
   */
  maxHeight?: number;

  /**
   * <PERSON><PERSON> hiển thị header không
   */
  showHeader?: boolean;

  /**
   * Có hiển thị khi không có task nào không
   */
  showWhenEmpty?: boolean;

  /**
   * Có tự động ẩn khi không có task nào không
   */
  autoHideWhenEmpty?: boolean;

  /**
   * Thời gian tự động ẩn khi không có task nào (ms)
   */
  autoHideDelay?: number;

  /**
   * Cho phép siêu thu gọn panel
   */
  allowSuperCollapse?: boolean;
}

/**
 * Component hiển thị panel quản lý task queue
 */
const TaskQueuePanel: React.FC<TaskQueuePanelProps> = ({
  isVisible = true,
  onClose,
  width = 360,
  maxHeight = 480,
  showHeader = true,
  showWhenEmpty = false,
  autoHideWhenEmpty = true,
  autoHideDelay = 3000,
  allowSuperCollapse = true,
}) => {
  const {
    tasks,
    isRunning,
    runningCount,
    cancelTask,
    retryTask,
    removeTask,
    clearCompletedTasks,
    startQueue,
    pauseQueue,
  } = useTaskQueueContext();

  // Trạng thái mở rộng của panel
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  // Trạng thái siêu thu gọn của panel - mặc định là true (mini)
  const [isSuperCollapsed, setIsSuperCollapsed] = useState<boolean>(true);

  // Trạng thái hiển thị của panel
  const [isPanelVisible, setIsPanelVisible] = useState<boolean>(isVisible);

  // Flag để ngăn auto-show khi user đã manually đóng panel
  const isManuallyClosedRef = useRef<boolean>(false);

  // Tính toán số lượng task theo trạng thái
  const taskCounts = useMemo(() => {
    return {
      total: tasks.length,
      pending: tasks.filter((task: Task) => task.status === TaskStatus.PENDING).length,
      running: tasks.filter((task: Task) => task.status === TaskStatus.RUNNING).length,
      completed: tasks.filter((task: Task) => task.status === TaskStatus.SUCCESS).length,
      failed: tasks.filter((task: Task) => task.status === TaskStatus.ERROR).length,
      cancelled: tasks.filter((task: Task) => task.status === TaskStatus.CANCELLED).length,
    };
  }, [tasks]);

  // Tự động ẩn panel khi không có task nào
  useEffect(() => {
    if (!autoHideWhenEmpty || !isPanelVisible) return undefined;

    if (tasks.length === 0) {
      const timeoutId = setTimeout(() => {
        setIsPanelVisible(false);
        if (onClose) {
          onClose();
        }
      }, autoHideDelay);

      return () => {
        clearTimeout(timeoutId);
      };
    }

    return undefined;
  }, [tasks.length, autoHideWhenEmpty, autoHideDelay, isPanelVisible, onClose]);

  // Xử lý đóng panel khi người dùng nhấn nút đóng
  const handleClosePanel = useCallback(() => {
    try {
      // Đánh dấu là user đã manually đóng panel
      isManuallyClosedRef.current = true;

      // Đóng panel ngay lập tức
      setIsPanelVisible(false);

      // Cleanup tasks sau khi đóng để tránh panel hiện lại
      setTimeout(() => {
        try {
          // Cancel tất cả tasks đang chạy
          tasks.forEach(task => {
            if (task.status === TaskStatus.RUNNING || task.status === TaskStatus.PENDING) {
              try {
                cancelTask(task.id);
              } catch (error) {
                console.warn('Failed to cancel task:', error);
              }
            }
          });

          // Clear completed tasks để cleanup blob URLs
          clearCompletedTasks();
        } catch (error) {
          console.warn('Error during delayed cleanup:', error);
        }
      }, 100);

      if (onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Error closing panel:', error);
      // Vẫn đóng panel ngay cả khi có lỗi
      isManuallyClosedRef.current = true;
      setIsPanelVisible(false);
      if (onClose) {
        onClose();
      }
    }
  }, [onClose, tasks, cancelTask, clearCompletedTasks]);

  // Tự động hiển thị panel ở trạng thái mini khi có task mới
  useEffect(() => {
    if (tasks.length > 0 && !isPanelVisible && !isManuallyClosedRef.current) {
      setIsPanelVisible(true);
      setIsSuperCollapsed(true); // Luôn bắt đầu ở trạng thái mini
    }

    // Reset flag khi tất cả tasks đã được clear (để cho phép auto-show cho task mới)
    if (tasks.length === 0 && isManuallyClosedRef.current) {
      isManuallyClosedRef.current = false;
    }
  }, [tasks.length, isPanelVisible]);

  // Cleanup khi component unmount
  useEffect(() => {
    return () => {
      // Cleanup tất cả blob URLs khi component unmount
      tasks.forEach(task => {
        if (task.type === TaskType.FILE_UPLOAD && (task as FileUploadTask).thumbnail) {
          try {
            URL.revokeObjectURL((task as FileUploadTask).thumbnail!);
          } catch (error) {
            console.warn('Failed to revoke blob URL on unmount:', error);
          }
        }
      });
    };
  }, [tasks]);

  // Nếu không có task nào và không hiển thị khi trống, không render gì cả
  if (tasks.length === 0 && !showWhenEmpty && !isPanelVisible) {
    return null;
  }

  // Tạo CSS cho chế độ siêu thu nhỏ
  const panelStyle = {
    width: isSuperCollapsed ? 'auto' : `${width}px`,
    maxHeight: isSuperCollapsed ? 'auto' : (isExpanded ? '80vh' : `${maxHeight}px`),
    minWidth: isSuperCollapsed ? 'auto' : `${width}px`,
    height: isSuperCollapsed ? 'auto' : 'initial',
  };

  return (
    <AnimatePresence>
      {isPanelVisible && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.2 }}
          className="fixed bottom-4 right-4 z-50 flex flex-col bg-card rounded-lg shadow-md overflow-hidden"
          style={panelStyle}
        >
          {/* Header */}
          {showHeader && (
            <TaskQueueHeader
              taskCounts={taskCounts}
              isRunning={isRunning}
              isExpanded={isExpanded}
              isSuperCollapsed={isSuperCollapsed}
              allowSuperCollapse={allowSuperCollapse}
              onToggleExpand={() => setIsExpanded(!isExpanded)}
              onToggleSuperCollapse={() => {
                console.log('Toggling super collapse from', isSuperCollapsed, 'to', !isSuperCollapsed);
                setIsSuperCollapsed(!isSuperCollapsed);
              }}
              onClose={handleClosePanel}
              onClearCompleted={clearCompletedTasks}
              onToggleQueue={isRunning ? pauseQueue : startQueue}
            />
          )}

          {/* Danh sách task - chỉ hiển thị khi không ở chế độ siêu thu gọn */}
          {!isSuperCollapsed && (
            <>
              <ScrollArea
                className="flex-1 p-2"
                height="auto"
                maxHeight={`${maxHeight - 50}px`}
                autoHide
              >
                {tasks.length === 0 ? (
                  <TaskQueueEmpty />
                ) : (
                  <div className="space-y-2">
                    {tasks.map((task: Task) => (
                      <TaskItem
                        key={task.id}
                        task={task}
                        onCancel={() => cancelTask(task.id)}
                        onRetry={() => retryTask(task.id)}
                        onRemove={() => removeTask(task.id)}
                      />
                    ))}
                  </div>
                )}
              </ScrollArea>

              {/* Footer - chỉ hiển thị khi không ở chế độ siêu thu gọn */}
              {tasks.length > 0 && (
                <div className="p-2 border-t border-border text-xs text-muted">
                  {runningCount > 0 ? (
                    <div>Đang xử lý {runningCount} tác vụ</div>
                  ) : (
                    <div>Tất cả tác vụ đã hoàn thành</div>
                  )}
                </div>
              )}
            </>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default TaskQueuePanel;
