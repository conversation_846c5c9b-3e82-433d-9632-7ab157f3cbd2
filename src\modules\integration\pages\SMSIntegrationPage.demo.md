# SMS Integration Page Demo

## T<PERSON><PERSON> quan
Trang SMS Integration đã được triển khai theo phong cách EmailServerManagementPage với các tính năng:

## Tính năng chính

### 1. Table Management
- **Hiển thị danh sách SMS Providers** với các cột:
  - Tê<PERSON> cấu hình
  - Tên hiển thị  
  - <PERSON><PERSON><PERSON> nhà cung cấp (Twilio, AWS SNS, Viettel, VNPT, FPT, Custom)
  - Tr<PERSON>ng thái (Active, Inactive, Error, Testing, Pending)
  - Mặc định (Yes/No)
  - Actions menu

### 2. MenuIconBar
- **Search**: Tìm kiếm theo tên hoặc loại
- **Add button**: Thêm SMS Provider mới
- **Column visibility**: Ẩn/hiện cột
- **Filters**: Lọc theo trạng thái, loại

### 3. SlideInForm
- **Create form**: <PERSON><PERSON><PERSON> SMS Provider mới
- **Edit form**: Chỉnh sửa SMS Provider
- **View form**: <PERSON><PERSON> chi tiết (read-only)

### 4. CRUD Operations
- **Create**: <PERSON><PERSON><PERSON> SMS Provider mới
- **Read**: Hiển thị danh sách và chi tiết
- **Update**: Cập nhật thông tin
- **Delete**: Xóa với confirm modal

### 5. Actions Menu
- **Edit**: Chỉnh sửa
- **View**: Xem chi tiết
- **Delete**: Xóa với xác nhận

## Cấu trúc Code

### Components sử dụng
- `Table` với pagination và sorting
- `MenuIconBar` cho search và actions
- `ActiveFilters` cho filter management
- `SlideInForm` cho create/edit/view
- `ConfirmDeleteModal` cho delete confirmation
- `ModernMenuTrigger` cho actions menu

### Hooks sử dụng
- `useSmsProviders` - Fetch danh sách providers
- `useSmsIntegration` - CRUD mutations
- `useDataTable` - Table state management
- `useActiveFilters` - Filter management
- `useSlideForm` - Form animation

### Translation Keys
- `admin:integration.sms.*` - Admin namespace
- `integration:sms.*` - Integration namespace
- `common:*` - Common translations

## Styling
- **Full-width layout**: `className="w-full bg-background text-foreground"`
- **Status chips**: Colored badges cho status
- **Provider type badges**: Blue badges cho provider types
- **Responsive design**: Mobile-friendly

## API Integration
- **GET /api/v1/integrations/sms/providers** - List providers
- **POST /api/v1/integrations/sms/providers** - Create provider
- **PUT /api/v1/integrations/sms/providers/:id** - Update provider
- **DELETE /api/v1/integrations/sms/providers/:id** - Delete provider

## Business Logic
- **Validation**: Provider type specific validation
- **Default provider**: Chỉ một provider mặc định
- **Status management**: Active/Inactive states
- **Error handling**: Comprehensive error handling

## Files đã cập nhật
1. `src/modules/integration/pages/SMSIntegrationPage.tsx` - Main component
2. `src/modules/admin/integration/locales/vi.json` - Vietnamese translations
3. `src/modules/admin/integration/locales/en.json` - English translations
4. `src/modules/integration/routes/integrationRoutes.tsx` - Routing

## Cách sử dụng
1. Truy cập `/integrations/sms`
2. Xem danh sách SMS Providers
3. Thêm provider mới bằng nút "Add"
4. Chỉnh sửa/xem/xóa qua actions menu
5. Tìm kiếm và lọc theo nhu cầu

## Tương thích
- ✅ Theo đúng pattern EmailServerManagementPage
- ✅ Sử dụng shared components
- ✅ TypeScript strict mode
- ✅ ESLint compliant
- ✅ Responsive design
- ✅ Internationalization support
- ✅ Accessibility features
