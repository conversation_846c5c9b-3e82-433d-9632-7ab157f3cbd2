import React from 'react';
import { ImageGalleryProvider } from './ImageGalleryContext';
import ImageGalleryGrid from './ImageGalleryGrid';
import ImageGalleryMasonry from './ImageGalleryMasonry';
import ImageGalleryCarousel from './ImageGalleryCarousel';
import ImageGalleryLightbox from './ImageGalleryLightbox';
import { ImageGalleryProps } from './types';

/**
 * ImageGallery component hiển thị bộ sưu tập hình ảnh với nhiều tính năng nâng cao
 *
 * @example
 * // Gallery cơ bản
 * <ImageGallery
 *   images={[
 *     { src: '/images/image1.jpg', alt: 'Image 1' },
 *     { src: '/images/image2.jpg', alt: 'Image 2' },
 *   ]}
 * />
 *
 * @example
 * // Gallery với lightbox và thumbnails
 * <ImageGallery
 *   images={images}
 *   layout="masonry"
 *   columns={{ xs: 1, sm: 2, md: 3, lg: 4 }}
 *   lightbox={true}
 *   thumbnails={true}
 *   zoom={true}
 * />
 */
const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  layout = 'grid',
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 },
  gap = 16,
  lightbox = true,
  lightboxConfig = {},
  thumbnails = true,
  thumbnailsConfig = {},
  zoom = true,
  zoomConfig = {},
  lazyLoad = true,
  onImageClick,
  onImageChange,
  className = '',
}) => {
  // Nếu không có hình ảnh, không render gì cả
  if (!images || images.length === 0) {
    return null;
  }

  return (
    <ImageGalleryProvider
      images={images}
      layout={layout}
      columns={columns}
      gap={gap}
      lightbox={lightbox}
      lightboxConfig={lightboxConfig}
      thumbnails={thumbnails}
      thumbnailsConfig={thumbnailsConfig}
      zoom={zoom}
      zoomConfig={zoomConfig}
      lazyLoad={lazyLoad}
      onImageClick={onImageClick}
      onImageChange={onImageChange}
    >
      <div className={className}>
        {/* Render layout tương ứng */}
        {layout === 'grid' && <ImageGalleryGrid />}
        {layout === 'masonry' && <ImageGalleryMasonry />}
        {layout === 'carousel' && <ImageGalleryCarousel />}

        {/* Lightbox */}
        {lightbox && <ImageGalleryLightbox />}
      </div>
    </ImageGalleryProvider>
  );
};

export default ImageGallery;
