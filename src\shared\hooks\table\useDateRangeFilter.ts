/**
 * Hook quản lý lọc theo khoảng thời gian
 * @module useDateRangeFilter
 */
import { useState, useCallback } from 'react';

/**
 * Kiểu dữ liệu cho khoảng thời gian
 */
export type DateRange = [Date | null, Date | null];

/**
 * Interface cho tham số đầu vào của hook useDateRangeFilter
 */
export interface UseDateRangeFilterProps {
  /**
   * Khoảng thời gian mặc định
   * @default [null, null]
   */
  defaultDateRange?: DateRange;

  /**
   * Callback khi thay đổi khoảng thời gian
   */
  onChange?: (range: DateRange) => void;

  /**
   * Có tự động chuyển đổi thành chuỗi ISO không
   * @default false
   */
  convertToISOString?: boolean;
}

/**
 * Hook quản lý lọc theo khoảng thời gian
 * @param props Tham số đầu vào
 * @returns Các state và hàm xử lý cho lọc theo khoảng thời gian
 */
export function useDateRangeFilter(props: UseDateRangeFilterProps = {}) {
  const { defaultDateRange = [null, null], onChange, convertToISOString = false } = props;

  // State cho khoảng thời gian
  const [dateRange, setDateRange] = useState<DateRange>(defaultDateRange);

  // Xử lý thay đổi ngày bắt đầu
  const handleStartDateChange = useCallback(
    (date: Date | null) => {
      // Nếu ngày bắt đầu lớn hơn ngày kết thúc hiện tại, reset ngày kết thúc
      let endDate = dateRange[1];
      if (date && endDate && date > endDate) {
        endDate = null; // Reset ngày kết thúc nếu ngày bắt đầu lớn hơn
      }

      const newRange: DateRange = [date, endDate];
      setDateRange(newRange);

      if (onChange) {
        onChange(newRange);
      }
    },
    [dateRange, onChange]
  );

  // Xử lý thay đổi ngày kết thúc
  const handleEndDateChange = useCallback(
    (date: Date | null) => {
      // Kiểm tra nếu ngày kết thúc nhỏ hơn ngày bắt đầu
      if (date && dateRange[0] && date < dateRange[0]) {
        // Không cho phép chọn ngày kết thúc nhỏ hơn ngày bắt đầu
        return;
      }

      const newRange: DateRange = [dateRange[0], date];
      setDateRange(newRange);

      if (onChange) {
        onChange(newRange);
      }
    },
    [dateRange, onChange]
  );

  // Xử lý xóa khoảng thời gian
  const handleClearDateRange = useCallback(() => {
    const newRange: DateRange = [null, null];
    setDateRange(newRange);

    if (onChange) {
      onChange(newRange);
    }
  }, [onChange]);

  // Chuyển đổi khoảng thời gian thành chuỗi ISO
  const isoDateRange = useCallback(() => {
    if (!convertToISOString) {
      return undefined;
    }

    return {
      startDate: dateRange[0] ? dateRange[0].toISOString() : undefined,
      endDate: dateRange[1] ? dateRange[1].toISOString() : undefined,
    };
  }, [dateRange, convertToISOString]);

  return {
    // States
    dateRange,

    // Handlers
    handleStartDateChange,
    handleEndDateChange,
    handleClearDateRange,
    setDateRange,

    // Helpers
    isoDateRange,

    // Derived values
    startDate: dateRange[0],
    endDate: dateRange[1],
    hasDateRange: Boolean(dateRange[0] || dateRange[1]),
  };
}
