import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { ZaloService } from '../../services/zalo.service';
import type {
  ZaloFollowerQueryDto,
  ZaloFollowerDto,
} from '../../types/zalo.types';

/**
 * Query keys cho Zalo followers
 */
export const ZALO_FOLLOWER_QUERY_KEYS = {
  all: ['zalo', 'followers'] as const,
  lists: () => [...ZALO_FOLLOWER_QUERY_KEYS.all, 'list'] as const,
  list: (oaId: number, query: ZaloFollowerQueryDto) => [...ZALO_FOLLOWER_QUERY_KEYS.lists(), oaId, query] as const,
  details: () => [...ZALO_FOLLOWER_QUERY_KEYS.all, 'detail'] as const,
  detail: (oaId: number, followerId: string) => [...ZALO_FOLLOWER_QUERY_KEYS.details(), oaId, followerId] as const,
};

/**
 * Hook để lấy danh sách followers của OA
 */
export function useZaloFollowers(oaId: number, query?: ZaloFollowerQueryDto) {
  return useQuery({
    queryKey: ZALO_FOLLOWER_QUERY_KEYS.list(oaId, query || {}),
    queryFn: () => ZaloService.getFollowers(oaId, query),
    select: (response) => response.result,
    enabled: !!oaId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook để lấy chi tiết follower
 */
export function useZaloFollower(oaId: number, followerId: string) {
  return useQuery({
    queryKey: ZALO_FOLLOWER_QUERY_KEYS.detail(oaId, followerId),
    queryFn: () => ZaloService.getFollower(oaId, followerId),
    select: (response) => response.result,
    enabled: !!oaId && !!followerId,
    staleTime: 5 * 60 * 1000,
  });
}

// Removed duplicate functions - using the ones below

/**
 * Hook để lấy danh sách tags phổ biến
 */
export function usePopularTags(oaId: number) {
  return useQuery({
    queryKey: ['zalo', 'popular-tags', oaId],
    queryFn: async () => {
      // Lấy sample followers để tính toán tags phổ biến
      const response = await ZaloService.getFollowers(oaId, { limit: 100 });
      const followers = response.result.items;

      // Đếm frequency của từng tag
      const tagCounts = new Map<string, number>();
      followers.forEach((follower: ZaloFollowerDto) => {
        follower.tags.forEach((tag: string) => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
      });

      // Sort theo frequency và trả về top 10
      return Array.from(tagCounts.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([tag, count]) => ({ tag, count }));
    },
    enabled: !!oaId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook để search followers với debounce
 */
export function useSearchZaloFollowers(oaId: number, searchTerm: string, delay: number = 500) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, delay);

    return () => clearTimeout(timer);
  }, [searchTerm, delay]);

  return useQuery({
    queryKey: ZALO_FOLLOWER_QUERY_KEYS.list(oaId, { search: debouncedSearchTerm }),
    queryFn: () => ZaloService.getFollowers(oaId, { search: debouncedSearchTerm }),
    select: (response) => response.result,
    enabled: !!oaId && debouncedSearchTerm.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });
}

/**
 * Hook để lấy followers statistics
 */
export function useZaloFollowersStats(oaId: number) {
  return useQuery({
    queryKey: ['zalo', 'followers-stats', oaId],
    queryFn: async () => {
      // Lấy tất cả followers để tính toán stats
      const response = await ZaloService.getFollowers(oaId, { limit: 1000 });
      const followers = response.result.items;

      const stats = {
        total: followers.length,
        active: followers.filter((f: ZaloFollowerDto) => f.lastInteractionAt &&
          Date.now() - f.lastInteractionAt < 7 * 24 * 60 * 60 * 1000).length, // Active trong 7 ngày
        newThisWeek: followers.filter((f: ZaloFollowerDto) =>
          Date.now() - f.followedAt < 7 * 24 * 60 * 60 * 1000).length,
        byGender: {
          male: followers.filter((f: ZaloFollowerDto) => f.gender === 'male').length,
          female: followers.filter((f: ZaloFollowerDto) => f.gender === 'female').length,
          unknown: followers.filter((f: ZaloFollowerDto) => f.gender === 'unknown' || !f.gender).length,
        },
        topTags: Array.from(
          followers.reduce((acc: Map<string, number>, follower: ZaloFollowerDto) => {
            follower.tags.forEach((tag: string) => {
              acc.set(tag, (acc.get(tag) || 0) + 1);
            });
            return acc;
          }, new Map<string, number>()).entries()
        )
        .sort((a, b) => (b as [string, number])[1] - (a as [string, number])[1])
        .slice(0, 5)
        .map((entry) => ({ tag: (entry as [string, number])[0], count: (entry as [string, number])[1] })),
      };

      return stats;
    },
    enabled: !!oaId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook để thêm tag cho follower
 */
export function useAddTagToFollower() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ oaId, followerId, tagName }: { oaId: number; followerId: string; tagName: string }) =>
      ZaloService.addTagToFollower(oaId, followerId, tagName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ZALO_FOLLOWER_QUERY_KEYS.lists() });
      NotificationUtil.success({ message: 'Thêm tag thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({ message: 'Thêm tag thất bại', title: error.message });
    },
  });
}

/**
 * Hook để xóa tag khỏi follower
 */
export function useRemoveTagFromFollower() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ oaId, followerId, tagName }: { oaId: number; followerId: string; tagName: string }) =>
      ZaloService.removeTagFromFollower(oaId, followerId, tagName),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ZALO_FOLLOWER_QUERY_KEYS.lists() });
      NotificationUtil.success({ message: 'Xóa tag thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({ message: 'Xóa tag thất bại', title: error.message });
    },
  });
}

/**
 * Hook để thực hiện bulk operations
 */
export function useBulkFollowerOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ oaId, data }: {
      oaId: number;
      data: {
        followerIds: string[];
        operation: 'ADD_TAG' | 'REMOVE_TAG' | 'BLOCK' | 'UNBLOCK';
        tagName?: string;
      }
    }) => ZaloService.bulkFollowerOperation(oaId, data),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ZALO_FOLLOWER_QUERY_KEYS.lists() });
      NotificationUtil.success({
        message: 'Thao tác hàng loạt thành công!',
        title: `Đã xử lý ${response.result.processedCount} followers`
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({ message: 'Thao tác hàng loạt thất bại', title: error.message });
    },
  });
}

/**
 * Hook tổng hợp cho follower management
 */
export function useZaloFollowerManagement() {
  const addTag = useAddTagToFollower();
  const removeTag = useRemoveTagFromFollower();
  const bulkOperation = useBulkFollowerOperation();

  return {
    addTag,
    removeTag,
    bulkOperation,
    isLoading: addTag.isPending || removeTag.isPending || bulkOperation.isPending,
  };
}
