import { useRef, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ChatMessage, ScrollArea } from '@/shared/components/common';
import NotificationMessage from './NotificationMessage';
import { NotificationItem } from './NotificationContainer';

// Message type
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

interface ChatContentProps {
  messages: Message[];
  isLoading?: boolean;
  notifications: NotificationItem[];
  onRemoveNotification: (id: string) => void;
}

const ChatContent = ({
  messages,
  isLoading = false,
  notifications,
  onRemoveNotification,
}: ChatContentProps) => {
  const { t } = useTranslation();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Kết hợp tin nhắn và thông báo thành một luồng duy nhất theo thứ tự thời gian
  const combinedItems = useMemo(() => {
    // Tạo mảng kết hợp tin nhắn và thông báo
    const items: Array<{
      type: 'message' | 'notification';
      id: string;
      timestamp: Date;
      data: Message | NotificationItem;
    }> = [
      // Thêm tin nhắn
      ...messages.map(message => ({
        type: 'message' as const,
        id: message.id,
        timestamp: message.timestamp,
        data: message,
      })),

      // Thêm thông báo với timestamp là thời điểm hiện tại
      ...notifications.map(notification => ({
        type: 'notification' as const,
        id: notification.id,
        // Tạo timestamp từ ID của thông báo (giả định ID có chứa timestamp)
        timestamp: new Date(parseInt(notification.id.split('-')[1] || Date.now().toString())),
        data: notification,
      })),
    ];

    // Sắp xếp theo thời gian tăng dần
    return items.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }, [messages, notifications]);

  // Scroll to bottom when messages or notifications change
  useEffect(() => {
    console.log('[ChatContent] Combined items:', combinedItems.length);
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [combinedItems]);

  // Không cần formatTime nữa vì đã được xử lý trong ChatMessage

  return (
    <div className="relative h-full">
      <ScrollArea
        id="chat-panel-scroll"
        className="h-full p-4"
        height="100%"
        invisible={true}
        isChatPanel={true}
      >
        {combinedItems.length === 0 ? (
          <div
            className="flex flex-col items-center justify-center h-full text-center"
            style={{ marginTop: '20vh' }}
          >
            <div className="text-4xl mb-4">👋</div>
            <h2 className="text-xl font-semibold mb-2">{t('viewPanel.welcome')}</h2>
            <p className="text-gray-500 dark:text-gray-400 max-w-md">{t('chat.typeMessage')}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Hiển thị tin nhắn và thông báo theo thứ tự thời gian */}
            {combinedItems.map(item =>
              item.type === 'message' ? (
                // Hiển thị tin nhắn
                <ChatMessage
                  key={item.id}
                  content={(item.data as Message).content}
                  sender={(item.data as Message).sender}
                  timestamp={(item.data as Message).timestamp}
                  {...((item.data as Message).avatar && { avatar: (item.data as Message).avatar })}
                  className="mb-4"
                />
              ) : (
                // Hiển thị thông báo
                <NotificationMessage
                  key={item.id}
                  id={(item.data as NotificationItem).id}
                  type={(item.data as NotificationItem).type}
                  message={(item.data as NotificationItem).message}
                  {...((item.data as NotificationItem).duration && { duration: (item.data as NotificationItem).duration })}
                  onRemove={onRemoveNotification}
                  className="mb-4"
                />
              )
            )}

            {isLoading && (
              <div className="flex items-center">
                {/* Avatar chỉ hiển thị cho AI */}
                <div className="flex-shrink-0">
                  <div className="w-6 h-6">
                    <img
                      src="/assets/images/ai-agents/assistant-robot.svg"
                      alt="AI Assistant"
                      className="w-full h-full rounded-full"
                    />
                  </div>
                </div>

                {/* Typing indicator nhỏ gọn hơn, không có nền, cùng hàng với avatar */}
                <div className="ml-2 flex space-x-1">
                  <div className="w-1.5 h-1.5 rounded-full bg-gray-500 dark:bg-gray-400 animate-bounce"></div>
                  <div
                    className="w-1.5 h-1.5 rounded-full bg-gray-500 dark:bg-gray-400 animate-bounce"
                    style={{ animationDelay: '0.2s' }}
                  ></div>
                  <div
                    className="w-1.5 h-1.5 rounded-full bg-gray-500 dark:bg-gray-400 animate-bounce"
                    style={{ animationDelay: '0.4s' }}
                  ></div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </ScrollArea>
    </div>
  );
};

export default ChatContent;
