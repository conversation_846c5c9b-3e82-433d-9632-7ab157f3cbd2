import { protocolApi } from '../api';
import {
  ProtocolConfig,
  ProtocolTemplate,
  ProtocolDetectionResult,
  ProtocolValidationResult,
  ProtocolType,
  PaginatedResponse,
} from '../types';

export const protocolService = {
  // Get available protocols
  getProtocols: async (): Promise<ProtocolConfig[]> => {
    return protocolApi.getProtocols();
  },

  // Detect protocol with enhanced logic
  detectProtocol: async (endpoint: string): Promise<ProtocolDetectionResult> => {
    if (!endpoint || endpoint.trim() === '') {
      throw new Error('Endpoint is required for protocol detection');
    }

    // Validate URL format
    try {
      new URL(endpoint);
    } catch {
      throw new Error('Invalid endpoint URL format');
    }

    const result = await protocolApi.detectProtocol(endpoint);

    // Add additional detection logic based on URL patterns
    const url = new URL(endpoint);
    const enhancedResult = { ...result };

    // WebSocket detection
    if (url.protocol === 'ws:' || url.protocol === 'wss:') {
      enhancedResult.detectedProtocol = ProtocolType.WEBSOCKET;
      enhancedResult.confidence = Math.max(enhancedResult.confidence, 0.9);
    }

    // gRPC detection (common patterns)
    if (url.port === '443' || url.port === '9090' || endpoint.includes('grpc')) {
      enhancedResult.supportedFeatures.push('gRPC potential');
    }

    return enhancedResult;
  },

  // Validate protocol configuration
  validateProtocol: async (config: ProtocolConfig): Promise<ProtocolValidationResult> => {
    if (!config.type) {
      return {
        isValid: false,
        errors: ['Protocol type is required'],
      };
    }

    return protocolApi.validateProtocol(config);
  },

  // Protocol template management
  getProtocolTemplates: async (params?: { page?: number; limit?: number }): Promise<PaginatedResponse<ProtocolTemplate>> => {
    const queryParams = {
      page: 1,
      limit: 20,
      ...params,
    };

    if (queryParams.limit > 100) {
      throw new Error('Limit cannot exceed 100');
    }

    return protocolApi.getProtocolTemplates(queryParams);
  },

  getProtocolTemplate: async (id: string): Promise<ProtocolTemplate> => {
    if (!id || id.trim() === '') {
      throw new Error('Template ID is required');
    }

    return protocolApi.getProtocolTemplate(id);
  },

  createProtocolTemplate: async (data: Omit<ProtocolTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProtocolTemplate> => {
    if (!data.name || data.name.trim() === '') {
      throw new Error('Template name is required');
    }

    if (!data.protocol) {
      throw new Error('Protocol type is required');
    }

    return protocolApi.createProtocolTemplate(data);
  },

  updateProtocolTemplate: async (id: string, data: Partial<ProtocolTemplate>): Promise<ProtocolTemplate> => {
    if (!id || id.trim() === '') {
      throw new Error('Template ID is required');
    }

    return protocolApi.updateProtocolTemplate(id, data);
  },

  deleteProtocolTemplate: async (id: string): Promise<void> => {
    if (!id || id.trim() === '') {
      throw new Error('Template ID is required');
    }

    return protocolApi.deleteProtocolTemplate(id);
  },

  // Helper methods
  getProtocolByType: async (type: ProtocolType): Promise<ProtocolConfig | undefined> => {
    const protocols = await protocolService.getProtocols();
    return protocols.find(p => p.type === type);
  },

  getDefaultTemplateForProtocol: async (protocol: ProtocolType): Promise<ProtocolTemplate | undefined> => {
    const templates = await protocolService.getProtocolTemplates({ limit: 100 });
    return templates.items.find(t => t.protocol === protocol && t.isDefault);
  },
};
