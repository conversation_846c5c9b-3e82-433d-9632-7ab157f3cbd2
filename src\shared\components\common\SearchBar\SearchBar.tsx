import React, { useState, useRef, useEffect } from 'react';
import { Icon } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

export interface SearchBarProps {
  /**
   * Giá trị của ô tìm kiếm
   */
  value?: string;

  /**
   * Sự kiện khi giá trị thay đổi
   */
  onChange?: (value: string) => void;

  /**
   * Sự kiện khi nhấn Enter
   */
  onSubmit?: (value: string) => void;

  /**
   * Placeholder cho ô tìm kiếm
   */
  placeholder?: string;

  /**
   * C<PERSON> hiển thị ô tìm kiếm không
   */
  visible?: boolean;

  /**
   * Sự kiện khi click vào icon tìm kiếm
   */
  onToggle?: () => void;

  /**
   * Chiều rộng tối đa của ô tìm kiếm
   */
  maxWidth?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * <PERSON>ariant của SearchBar
   */
  variant?: 'default' | 'flat' | 'filled';

  /**
   * Tự động focus khi hiển thị
   */
  autoFocus?: boolean;

  /**
   * Hiển thị nút xóa
   */
  showClearButton?: boolean;

  /**
   * Hiển thị icon tìm kiếm bên ngoài
   */
  showSearchIcon?: boolean;

  /**
   * Chỉ gọi onChange khi nhấn Enter
   */
  searchOnEnter?: boolean;
}

/**
 * Component SearchBar hiển thị ô tìm kiếm với animation
 */
const SearchBar: React.FC<SearchBarProps> = ({
  value = '',
  onChange,
  onSubmit,
  placeholder,
  visible = false,
  onToggle,
  maxWidth = '300px',
  className = '',
  variant = 'default',
  autoFocus = true,
  showClearButton = true,
  showSearchIcon = true,
  searchOnEnter = false,
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const searchBarRef = useRef<HTMLDivElement>(null);

  // Xác định placeholder
  const placeholderText = placeholder || t('common.searchPlaceholder');

  // Xác định variant classes
  const variantClasses = {
    default: 'bg-white dark:bg-dark-light border border-gray-200 dark:border-gray-700 shadow-sm',
    flat: 'bg-gray-100 dark:bg-dark-lighter border-0',
    filled: 'bg-gray-100 dark:bg-dark-lighter border-0 shadow-inner',
  }[variant];

  // Cập nhật inputValue khi value prop thay đổi
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  // Focus vào input khi visible thay đổi từ false sang true
  useEffect(() => {
    if (visible && autoFocus) {
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 300);
    }
  }, [visible, autoFocus]);

  // Xử lý khi giá trị input thay đổi
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Nếu không cần đợi nhấn Enter thì gọi onChange ngay
    if (onChange && !searchOnEnter) {
      onChange(newValue);
    }
  };

  // Xử lý khi nhấn phím
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      // Khi nhấn Enter, gọi onSubmit nếu có
      if (onSubmit) {
        onSubmit(inputValue);
      }

      // Nếu cần đợi nhấn Enter mới tìm kiếm, gọi onChange
      if (onChange && searchOnEnter) {
        onChange(inputValue);
      }
    }
  };

  // Xử lý khi click vào icon tìm kiếm
  const handleToggle = () => {
    if (onToggle) {
      onToggle();
    }
  };

  // Xử lý khi click vào nút xóa
  const handleClear = () => {
    setInputValue('');

    // Luôn gọi onChange khi xóa, bất kể searchOnEnter là gì
    if (onChange) {
      onChange('');
    }

    // Gọi onSubmit nếu có để đảm bảo API được gọi lại
    if (onSubmit) {
      onSubmit('');
    }

    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <div className="flex items-center">
      {/* Icon tìm kiếm (hiển thị khi ẩn ô tìm kiếm) */}
      {!visible && onToggle && showSearchIcon && (
        <button
          type="button"
          onClick={handleToggle}
          className="p-2 rounded-full text-gray-500 hover:bg-gray-100 dark:hover:bg-dark-lighter"
          aria-label={t('common.search')}
        >
          <Icon name="search" size="md" />
        </button>
      )}

      {/* Ô tìm kiếm */}
      <div
        ref={searchBarRef}
        className={`
          overflow-hidden transition-all duration-300 ease-in-out
          ${visible ? 'w-full opacity-100' : 'w-0 opacity-0'}
          ${className}
        `}
        style={{ maxWidth: visible ? maxWidth : '0' }}
      >
        <div className={`flex items-center rounded-full overflow-hidden ${variantClasses}`}>
          {/* Icon tìm kiếm bên trong ô tìm kiếm */}
          <div className="flex-shrink-0 pl-3">
            <Icon name="search" size="sm" className="text-gray-400" />
          </div>

          {/* Input tìm kiếm */}
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholderText}
            className="w-full py-2 px-2 bg-transparent border-0 focus:ring-0 focus:outline-none text-sm cursor-text"
          />

          {/* Nút xóa */}
          {showClearButton && inputValue && (
            <button
              type="button"
              onClick={handleClear}
              className="flex-shrink-0 p-1 mr-1 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 cursor-pointer"
              aria-label={t('common.clear')}
            >
              <Icon name="close" size="sm" />
            </button>
          )}

          {/* Nút đóng ô tìm kiếm */}
          {onToggle && (
            <button
              type="button"
              onClick={handleToggle}
              className="flex-shrink-0 p-1 mr-2 rounded-full text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 cursor-pointer"
              aria-label={t('common.close')}
            >
              <Icon name="chevron-left" size="sm" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchBar;
