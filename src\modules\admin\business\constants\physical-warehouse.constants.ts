/**
 * Query keys cho physical warehouse admin
 */
export const PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS = {
  ALL: ['admin', 'physical-warehouses'] as const,
  LIST: (params: Record<string, unknown>) => [...PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: number) => [...PHYSICAL_WAREHOUSE_ADMIN_QUERY_KEYS.ALL, 'detail', id] as const,
} as const;

/**
 * API endpoints cho physical warehouse admin
 */
export const PHYSICAL_WAREHOUSE_ADMIN_ENDPOINTS = {
  BASE: '/admin/physical-warehouses',
  DETAIL: (id: number) => `/admin/physical-warehouses/${id}`,
} as const;

/**
 * Default values cho query parameters
 */
export const PHYSICAL_WAREHOUSE_ADMIN_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  SORT_BY: 'createdAt',
  SORT_DIRECTION: 'desc',
} as const;

/**
 * Stale time cho cache (5 phút)
 */
export const PHYSICAL_WAREHOUSE_ADMIN_STALE_TIME = 5 * 60 * 1000;
