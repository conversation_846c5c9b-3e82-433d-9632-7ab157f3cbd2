# Custom Field Integration trong Marketing Segment Form

## Tổng quan

Tài liệu này mô tả việc tích hợp AsyncSelectWithPagination component để chọn trường tùy chỉnh (custom fields) trong Marketing Segment Form.

## Các thay đổi đã thực hiện

### 1. C<PERSON><PERSON> nhật Custom Field Service

**File:** `src/modules/marketing/services/custom-field.service.ts`

- Thêm `CustomFieldBusinessService` - Layer 2 business logic
- Implement `getCustomFieldsForSelect()` method để transform data cho AsyncSelectWithPagination
- Validate limit không vượt quá 100 items
- Transform response format phù hợp với AsyncSelectWithPagination

```typescript
export const CustomFieldBusinessService = {
  getCustomFieldsForSelect: async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    // Business logic implementation
    // Returns: { items, totalItems, totalPages, currentPage }
  }
};
```

### 2. <PERSON><PERSON><PERSON> nhật Custom Field Hooks

**File:** `src/modules/marketing/hooks/useCustomFieldQuery.ts`

- Thêm query key cho select operations
- Implement `useCustomFieldLoadOptions()` hook
- Tích hợp với TanStack Query cho caching và error handling

```typescript
export const useCustomFieldLoadOptions = () => {
  return async (params: { search?: string; page?: number; limit?: number }) => {
    return await CustomFieldBusinessService.getCustomFieldsForSelect(params);
  };
};
```

### 3. Cập nhật Segment Form Component

**File:** `src/modules/marketing/components/forms/SegmentForm.tsx`

- Thay thế Select component bằng AsyncSelectWithPagination trong dropdown "Chọn Trường"
- Tạo function `loadAllFieldOptions()` để kết hợp static fields và custom fields
- Thêm z-index cao cho dropdown để tránh bị che bởi Card
- Sử dụng i18n cho labels và messages

**Thay đổi chính:**
```tsx
// Thay thế Select component cũ
<Select
  value={condition.field}
  onChange={(value) => updateCondition(group.id, condition.id, { field: value as string })}
  options={AVAILABLE_FIELDS}
  placeholder="Chọn Trường"
  fullWidth
/>

// Bằng AsyncSelectWithPagination mới
<AsyncSelectWithPagination
  value={condition.field}
  onChange={(value) => updateCondition(group.id, condition.id, { field: value as string })}
  loadOptions={loadAllFieldOptions}
  placeholder={t('marketing:segment.selectCustomField', 'Chọn trường...')}
  searchOnEnter={true}
  debounceTime={300}
  itemsPerPage={20}
  noOptionsMessage={t('common:noOptionsFound', 'Không tìm thấy tùy chọn')}
  loadingMessage={t('common:loading', 'Đang tải...')}
  fullWidth
/>
```

**Function kết hợp fields:**
```tsx
const loadAllFieldOptions = async (params: { search?: string; page?: number; limit?: number }) => {
  // Load custom fields từ API
  const customFieldsResponse = await loadCustomFieldOptions(params);

  // Static fields (luôn hiển thị ở đầu)
  const staticFields = AVAILABLE_FIELDS.map(field => ({
    value: field.value,
    label: field.label,
    description: 'Trường hệ thống'
  }));

  // Combine static fields với custom fields
  const allItems = [...filteredStaticFields, ...customFieldsResponse.items];

  return { items: allItems, totalItems, totalPages, currentPage };
};
```

### 4. Cập nhật Translation Keys

**Files:**
- `src/modules/marketing/locales/vi.json`
- `src/lib/i18n/locales/vi/common.json`

Thêm các keys:
- `marketing:segment.customFields`
- `marketing:segment.selectCustomField`
- `common:noOptionsFound`

## API Endpoint

Component sử dụng endpoint: `GET /user/marketing/audience-custom-fields`

**Controller:** `UserAudienceCustomFieldDefinitionController`
**Method:** `findAll()`

**Query Parameters:**
- `page`: Số trang (default: 1)
- `limit`: Số items mỗi trang (default: 20, max: 100)
- `search`: Từ khóa tìm kiếm
- `sortBy`: Trường sắp xếp (default: 'id', fallback nếu 'displayName' không hoạt động)
- `sortDirection`: Hướng sắp xếp (default: 'ASC')

**Response Format:**
```json
{
  "result": {
    "items": [
      {
        "id": 1,
        "fieldKey": "custom_field_1",
        "displayName": "Tên trường tùy chỉnh",
        "description": "Mô tả trường",
        "dataType": "string",
        "status": "active"
      }
    ],
    "meta": {
      "total": 100,
      "page": 1,
      "limit": 20,
      "totalPages": 5
    }
  }
}
```

**Error Handling:**
- Nếu `sortBy` parameter không hợp lệ, system sẽ fallback về params đơn giản
- Transform response để map `displayName` → `label` và `fieldKey` → `value`
- Fallback values cho missing fields

## Cách sử dụng

1. **Mở Segment Form**: Truy cập trang tạo/chỉnh sửa segment
2. **Chọn Custom Field**: Trong mỗi nhóm điều kiện, sử dụng dropdown "Trường tùy chỉnh"
3. **Tìm kiếm**: Nhập từ khóa và nhấn Enter để tìm kiếm
4. **Phân trang**: Scroll xuống để load thêm items
5. **Chọn trường**: Click vào trường muốn sử dụng

## Tuân thủ Frontend Rules

✅ **Component bắt buộc**: Sử dụng AsyncSelectWithPagination từ shared components
✅ **3-Layer Pattern**: API → Services → Hooks
✅ **TypeScript**: Interface rõ ràng, không sử dụng `any`
✅ **I18n**: Sử dụng useTranslation với colon syntax
✅ **TanStack Query**: Implement caching và error handling
✅ **ESLint**: Code tuân thủ quy tắc linting

## Testing

Để test tính năng:

1. **Unit Tests**: Test business logic trong CustomFieldBusinessService
2. **Integration Tests**: Test hook useCustomFieldLoadOptions
3. **E2E Tests**: Test user flow trong Segment Form

```bash
# Chạy tests
npm run test

# Chạy linting
npm run lint

# Build project
npm run build
```

## Troubleshooting

### Lỗi thường gặp:

1. **Custom fields không hiển thị trong dropdown**:
   - **Triệu chứng**: Dropdown chỉ hiển thị static fields (Email, Tên, Số điện thoại)
   - **Debug**: Mở console và kiểm tra logs từ `loadAllFieldOptions`
   - **Nguyên nhân có thể**:
     - API response structure khác với expected
     - AsyncSelectWithPagination config sai
     - Error trong transform logic
   - **Giải pháp**:
     - Kiểm tra `autoLoadInitial={true}`
     - Verify API response có `result.data` array
     - Check console logs cho errors

2. **API validation error với sortBy**:
   - **Lỗi**: `Validation failed` với sortBy parameter
   - **Nguyên nhân**: API không support field `label` hoặc `displayName` để sort
   - **Giải pháp**: System tự động fallback về `sortBy: 'id'` hoặc không có sortBy

3. **API response parsing error**:
   - **Lỗi**: `result.items is undefined`
   - **Nguyên nhân**: API trả về `result.data` thay vì `result.items`
   - **Giải pháp**: Updated parsing logic để handle cả hai format

4. **AsyncSelectWithPagination không load data**:
   - **Nguyên nhân**: `searchOnEnter={true}` ngăn auto load
   - **Giải pháp**: Set `searchOnEnter={false}` và `autoLoadInitial={true}`

5. **Field mapping issues**:
   - **Lỗi**: Custom fields không hiển thị đúng tên
   - **Nguyên nhân**: API response có field names khác với expected
   - **Giải pháp**: Transform response mapping `displayName` → `label`, `fieldKey` → `value`

### Debug:

```typescript
// Enable console logs trong CustomFieldService
console.log('API request params:', queryParams);
console.log('API response:', response);
```

## Tương lai

Các cải tiến có thể thực hiện:

1. **Cache optimization**: Implement better caching strategy
2. **Offline support**: Add offline capability
3. **Advanced filtering**: Add more filter options
4. **Bulk operations**: Support selecting multiple fields
5. **Field validation**: Add validation for selected fields
