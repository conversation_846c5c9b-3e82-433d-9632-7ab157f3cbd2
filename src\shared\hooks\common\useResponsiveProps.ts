import { useMemo } from 'react';
import { Breakpoint } from '@/shared/constants/breakpoints.ts';
import { useCurrentBreakpoint } from './useMediaQuery.ts';

type ResponsiveProps<T> = {
  [key in Breakpoint]?: Partial<T>;
} & {
  base: T; // Base props for all breakpoints
};

/**
 * Hook to merge props based on the current breakpoint
 * Returns the base props merged with props for the current breakpoint and all smaller breakpoints
 *
 * @param responsiveProps Object containing props for different breakpoints
 * @returns Merged props for the current breakpoint
 *
 * @example
 * const buttonProps = useResponsiveProps({
 *   base: { size: 'md', variant: 'primary' },
 *   md: { size: 'lg' },
 *   lg: { variant: 'outline' },
 * });
 * // On mobile: { size: 'md', variant: 'primary' }
 * // On tablet: { size: 'lg', variant: 'primary' }
 * // On desktop: { size: 'lg', variant: 'outline' }
 */
function useResponsiveProps<T extends Record<string, unknown>>(
  responsiveProps: ResponsiveProps<T>
): T {
  const currentBreakpoint = useCurrentBreakpoint();

  return useMemo(() => {
    // Start with the base props
    const result = { ...responsiveProps.base };

    // Define the order of breakpoints from smallest to largest
    const breakpointOrder: Breakpoint[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl'];

    // Find the index of the current breakpoint
    const currentIndex = breakpointOrder.indexOf(currentBreakpoint);

    // Apply props from all breakpoints up to and including the current one
    for (let i = 0; i <= currentIndex; i++) {
      const breakpoint = breakpointOrder[i];
      const breakpointProps = breakpoint ? responsiveProps[breakpoint] : undefined;

      if (breakpointProps) {
        Object.assign(result, breakpointProps);
      }
    }

    return result;
  }, [currentBreakpoint, responsiveProps]);
}

export default useResponsiveProps;
