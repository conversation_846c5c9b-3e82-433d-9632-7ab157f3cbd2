import React, { useState, useEffect } from 'react';

export interface ResizerProps {
  /**
   * Hướng của thanh kéo ('vertical' hoặc 'horizontal')
   */
  direction?: 'vertical' | 'horizontal';

  /**
   * Hướng của thanh kéo (tương thích với prop cũ)
   */
  orientation?: 'vertical' | 'horizontal';

  /**
   * Hàm xử lý khi bắt đầu kéo
   */
  onResizeStart?: (e: React.MouseEvent) => void;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * <PERSON><PERSON>u sắc của thanh kéo
   */
  color?: 'default' | 'primary' | 'secondary';

  /**
   * Kích thước của thanh kéo
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * ID của phần tử chat panel để theo dõi sự kiện cuộn
   */
  chatPanelId?: string;
}

/**
 * Component thanh kéo để thay đổi kích thước
 */
const Resizer: React.FC<ResizerProps> = ({
  direction = 'vertical',
  orientation,
  onResizeStart,
  className = '',
  color = 'default',
  size = 'md',
  chatPanelId,
}) => {
  // State để theo dõi trạng thái ẩn/hiện của thanh kéo
  const [isVisible, setIsVisible] = useState(true);
  // State để theo dõi trạng thái đang kéo
  const [isDragging, setIsDragging] = useState(false);
  // State để lưu trữ timeout ID
  const [hideTimeoutId, setHideTimeoutId] = useState<number | null>(null);

  // Xử lý sự kiện cuộn
  useEffect(() => {
    if (!chatPanelId) return;

    const chatPanel = document.getElementById(chatPanelId);
    if (!chatPanel) return;

    // Hàm xử lý sự kiện cuộn
    const handleScroll = () => {
      // Ẩn thanh kéo khi cuộn
      setIsVisible(false);

      // Xóa timeout cũ nếu có
      if (hideTimeoutId) {
        window.clearTimeout(hideTimeoutId);
      }

      // Đặt timeout để hiện lại thanh kéo sau khi ngừng cuộn
      const timeoutId = window.setTimeout(() => {
        setIsVisible(true);
      }, 1000);

      setHideTimeoutId(timeoutId as unknown as number);
    };

    // Đăng ký sự kiện cuộn
    chatPanel.addEventListener('scroll', handleScroll);

    // Hủy đăng ký sự kiện khi component unmount
    return () => {
      chatPanel.removeEventListener('scroll', handleScroll);
      if (hideTimeoutId) {
        window.clearTimeout(hideTimeoutId);
      }
    };
  }, [chatPanelId, hideTimeoutId]);

  // Xử lý sự kiện bắt đầu kéo
  const handleResizeStart = (e: React.MouseEvent) => {
    setIsDragging(true);
    setIsVisible(true); // Luôn hiển thị khi đang kéo
    if (onResizeStart) {
      onResizeStart(e);
    }
  };

  // Xử lý sự kiện kết thúc kéo
  useEffect(() => {
    const handleResizeEnd = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      window.addEventListener('mouseup', handleResizeEnd);
    }

    return () => {
      window.removeEventListener('mouseup', handleResizeEnd);
    };
  }, [isDragging]);

  // Sử dụng orientation nếu được cung cấp, nếu không thì sử dụng direction
  const finalDirection = orientation || direction;

  // Xác định class dựa trên hướng
  const directionClasses = {
    vertical: 'w-1 cursor-col-resize',
    horizontal: 'h-1 cursor-row-resize',
  }[finalDirection];

  // Xác định class dựa trên màu sắc
  const colorClasses = {
    default: 'bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600',
    primary: 'bg-gray-200 dark:bg-gray-700 hover:bg-primary',
    secondary: 'bg-gray-200 dark:bg-gray-700 hover:bg-secondary',
  }[color];

  // Xác định kích thước của thanh kéo
  const sizeClasses = {
    sm: finalDirection === 'vertical' ? 'w-0.5' : 'h-0.5',
    md: finalDirection === 'vertical' ? 'w-1' : 'h-1',
    lg: finalDirection === 'vertical' ? 'w-1.5' : 'h-1.5',
  }[size];

  // Xác định kích thước của handle
  const handleSizeClasses = {
    sm: 'w-3 h-6',
    md: 'w-4 h-8',
    lg: 'w-5 h-10',
  }[size];

  // Xác định kích thước của line trong handle
  const lineSizeClasses = {
    sm: 'w-0.5 h-3',
    md: 'w-0.5 h-4',
    lg: 'w-1 h-5',
  }[size];

  // Xác định opacity dựa trên trạng thái hiển thị
  const visibilityClass = isVisible || isDragging ? 'opacity-100' : 'opacity-0';
  const transitionClass = 'transition-opacity duration-300';

  return (
    <div
      className={`${directionClasses} ${colorClasses} ${sizeClasses} relative z-10 ${visibilityClass} ${transitionClass} ${className}`}
      onMouseDown={handleResizeStart}
    >
      <div
        className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 ${handleSizeClasses} flex items-center justify-center`}
      >
        <div className={`${lineSizeClasses} bg-gray-400 dark:bg-gray-500`}></div>
      </div>
    </div>
  );
};

export default Resizer;
