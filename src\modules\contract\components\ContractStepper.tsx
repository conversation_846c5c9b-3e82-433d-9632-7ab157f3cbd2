/**
 * Component hiển thị tiến trình các b<PERSON><PERSON><PERSON> ký hợp đồng
 */
import React from 'react';
import { Icon } from '@/shared/components/common';
import { ContractStep } from '../types';

interface ContractStepperProps {
  steps: ContractStep[];
  currentStepIndex: number;
}

const ContractStepper: React.FC<ContractStepperProps> = ({
  steps,
  currentStepIndex,
}) => {

  const getStepIcon = (step: ContractStep, index: number): string => {
    if (index < currentStepIndex) {
      return 'check';
    }

    switch (step) {
      case ContractStep.TYPE_SELECTION:
        return 'users';
      case ContractStep.TERMS_ACCEPTANCE:
        return 'file-text';
      case ContractStep.INFO_FORM:
        return 'edit';
      case ContractStep.CONTRACT_DISPLAY:
        return 'eye';
      case ContractStep.HAND_SIGNATURE:
        return 'pen-tool';
      case ContractStep.OTP_VERIFICATION:
        return 'smartphone';
      case ContractStep.COMPLETED:
        return 'check-circle';
      default:
        return 'circle';
    }
  };

  // Lọc bỏ step COMPLETED để không hiển thị trong stepper
  const visibleSteps = steps.filter(step => step !== ContractStep.COMPLETED);

  return (
    <div className="w-full max-w-4xl mx-auto px-4 py-6">
      <div className="flex items-center justify-between relative">
        {/* Progress line */}
        <div className="absolute top-6 left-0 right-0 h-0.5 bg-border z-0">
          <div
            className="h-full bg-primary transition-all duration-500 ease-in-out"
            style={{
              width: `${(currentStepIndex / (visibleSteps.length - 1)) * 100}%`,
            }}
          />
        </div>

        {/* Steps */}
        {visibleSteps.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;

          return (
            <div key={step} className="flex flex-col items-center relative z-10">
              {/* Step circle */}
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                  isCompleted
                    ? 'bg-primary border-primary text-white'
                    : isCurrent
                    ? 'bg-primary/10 border-primary text-primary shadow-lg'
                    : 'bg-background border-border text-muted'
                }`}
              >
                <Icon
                  name={getStepIcon(step, index)}
                  size="sm"
                  className={`${
                    isCompleted
                      ? 'text-white'
                      : isCurrent
                      ? 'text-primary'
                      : 'text-muted'
                  }`}
                />
              </div>


            </div>
          );
        })}
      </div>


    </div>
  );
};

export default ContractStepper;
