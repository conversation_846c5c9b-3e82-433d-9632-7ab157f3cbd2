# Hướng dẫn sử dụng SSE (Server-Sent Events) Utilities

## Tổng quan

Bộ tiện ích SSE cung cấp một giải pháp hoàn chỉnh để làm việc với Server-Sent Events trong ứng dụng React, bao gồm:

- **SSE Client**: Class quản lý kết nối SSE với auto-reconnect
- **React Hooks**: <PERSON><PERSON><PERSON> hooks để tích hợp SSE vào components
- **Context Provider**: Quản lý multiple SSE connections
- **UI Components**: Components hiển thị trạng thái và notifications
- **TaskQueue Integration**: Tích hợp với hệ thống TaskQueue

## Cài đặt và Import

```typescript
// Hooks
import { 
  useSSE, 
  useSSESubscription, 
  useSSEConnection,
  useSSEWithTaskQueue 
} from '@/shared/hooks/common';

// Context
import { 
  SSEProvider, 
  useSSEContext, 
  useSSEFromContext 
} from '@/shared/contexts/sse';

// Components
import { 
  SSEStatus, 
  SSENotification 
} from '@/shared/components/common';

// Utils
import { SSEClient } from '@/shared/utils';

// Types
import type { 
  SSEEvent, 
  SSEConnectionState, 
  UseSSEOptions 
} from '@/shared/types/sse.types';
```

## 1. Sử dụng cơ bản với useSSE Hook

```typescript
import React from 'react';
import { useSSE } from '@/shared/hooks/common';

const BasicSSEExample: React.FC = () => {
  const sse = useSSE('/api/v1/sse/notifications', {
    autoConnect: true,
    autoReconnect: true,
    reconnectDelay: 3000,
    maxReconnectAttempts: 5,
  });

  // Subscribe vào specific events
  React.useEffect(() => {
    const subscriptionId = sse.subscribe('user_notification', (event) => {
      console.log('Received notification:', event.data);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  return (
    <div>
      <h3>SSE Connection Status</h3>
      <p>State: {sse.connectionInfo.state}</p>
      <p>Events received: {sse.connectionInfo.eventsReceived}</p>
      
      <button onClick={sse.connect}>Connect</button>
      <button onClick={sse.disconnect}>Disconnect</button>
      <button onClick={sse.clearEvents}>Clear Events</button>
      
      <div>
        <h4>Recent Events:</h4>
        {sse.events.slice(-5).map((event, index) => (
          <div key={index}>
            {event.type}: {JSON.stringify(event.data)}
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 2. Sử dụng useSSESubscription cho specific events

```typescript
import React from 'react';
import { useSSESubscription } from '@/shared/hooks/common';

const NotificationComponent: React.FC = () => {
  const {
    events,
    lastEvent,
    isSubscribed,
    connectionInfo,
    subscribe,
    unsubscribe,
  } = useSSESubscription(
    '/api/v1/sse/notifications',
    'user_notification',
    (event) => {
      // Handler cho mỗi notification
      console.log('New notification:', event.data);
    },
    {
      autoSubscribe: true,
      maxEvents: 50,
      filter: {
        // Chỉ nhận notifications cho user hiện tại
        data: { userId: getCurrentUserId() }
      }
    }
  );

  return (
    <div>
      <h3>User Notifications</h3>
      <p>Subscribed: {isSubscribed ? 'Yes' : 'No'}</p>
      <p>Connection: {connectionInfo.state}</p>
      
      {!isSubscribed && (
        <button onClick={subscribe}>Subscribe to Notifications</button>
      )}
      
      {lastEvent && (
        <div className="latest-notification">
          <h4>Latest:</h4>
          <p>{lastEvent.data.message}</p>
        </div>
      )}
      
      <div className="notification-list">
        {events.map((event, index) => (
          <div key={index} className="notification-item">
            <span>{new Date(event.timestamp).toLocaleTimeString()}</span>
            <span>{event.data.message}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 3. Sử dụng SSE Context cho multiple connections

```typescript
import React from 'react';
import { SSEProvider, useSSEFromContext } from '@/shared/contexts/sse';

// App component với SSE Provider
const App: React.FC = () => {
  return (
    <SSEProvider defaultOptions={{ autoReconnect: true }}>
      <Dashboard />
    </SSEProvider>
  );
};

// Component sử dụng SSE từ context
const Dashboard: React.FC = () => {
  // Multiple SSE connections
  const notifications = useSSEFromContext(
    'notifications',
    '/api/v1/sse/notifications'
  );
  
  const systemUpdates = useSSEFromContext(
    'system',
    '/api/v1/sse/system-updates'
  );

  React.useEffect(() => {
    // Subscribe to different event types
    const notifSub = notifications.subscribe('user_notification', (event) => {
      showToast(event.data.message);
    });

    const systemSub = systemUpdates.subscribe('system_update', (event) => {
      if (event.data.type === 'maintenance') {
        showMaintenanceWarning(event.data);
      }
    });

    return () => {
      notifications.unsubscribe(notifSub);
      systemUpdates.unsubscribe(systemSub);
    };
  }, [notifications, systemUpdates]);

  return (
    <div>
      <h1>Dashboard</h1>
      {/* Your dashboard content */}
    </div>
  );
};
```

## 4. Sử dụng SSE Components

```typescript
import React from 'react';
import { SSEStatus, SSENotification } from '@/shared/components/common';
import { useSSE } from '@/shared/hooks/common';

const SSEDashboard: React.FC = () => {
  const sse = useSSE('/api/v1/sse/notifications');
  const [notifications, setNotifications] = React.useState([]);

  React.useEffect(() => {
    const subscriptionId = sse.subscribe('notification', (event) => {
      const notification = {
        id: event.id || Date.now().toString(),
        type: event.data.type || 'info',
        title: event.data.title,
        message: event.data.message,
        event,
        timestamp: new Date(),
        autoHide: true,
        hideAfter: 5000,
      };
      
      setNotifications(prev => [...prev, notification]);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  const handleDismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div>
      {/* Status indicator */}
      <SSEStatus
        connectionInfo={sse.connectionInfo}
        variant="badge"
        showDetails={true}
        onClick={() => {
          if (sse.connectionInfo.state === 'disconnected') {
            sse.connect();
          }
        }}
      />

      {/* Notifications */}
      <SSENotification
        notifications={notifications}
        onDismiss={handleDismissNotification}
        onClearAll={() => setNotifications([])}
        position="top-right"
        maxVisible={5}
        showTimestamp={true}
      />

      {/* Your app content */}
    </div>
  );
};
```

## 5. Tích hợp với TaskQueue

```typescript
import React from 'react';
import { useSSEWithTaskQueue } from '@/shared/hooks/common';

const TaskQueueSSEExample: React.FC = () => {
  const sseTaskQueue = useSSEWithTaskQueue('/api/v1/sse/tasks', {
    autoCreateTasks: true,
    taskFilter: (event) => event.type === 'background_task',
    eventToTask: (event) => ({
      title: `Process ${event.data.taskType}`,
      description: `Processing task: ${event.data.id}`,
    }),
    defaultEventHandler: async (event) => {
      // Xử lý task từ SSE event
      console.log('Processing task:', event.data);
      
      // Simulate async work
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Task completed:', event.data.id);
    },
  });

  return (
    <div>
      <h3>SSE Task Queue</h3>
      
      <div className="connection-info">
        <p>Connection: {sseTaskQueue.connectionInfo.state}</p>
        <p>Queue Running: {sseTaskQueue.isRunning ? 'Yes' : 'No'}</p>
        <p>Running Tasks: {sseTaskQueue.runningCount}</p>
      </div>

      <div className="controls">
        <button onClick={sseTaskQueue.connect}>Connect SSE</button>
        <button onClick={sseTaskQueue.disconnect}>Disconnect SSE</button>
        <button onClick={sseTaskQueue.startQueue}>Start Queue</button>
        <button onClick={sseTaskQueue.pauseQueue}>Pause Queue</button>
      </div>

      <div className="tasks">
        <h4>Tasks ({sseTaskQueue.tasks.length})</h4>
        {sseTaskQueue.tasks.map(task => (
          <div key={task.id} className="task-item">
            <span>{task.title}</span>
            <span>{task.status}</span>
            <span>{task.progress}%</span>
          </div>
        ))}
      </div>

      <div className="sse-tasks">
        <h4>SSE Tasks</h4>
        {sseTaskQueue.getSSETasks().map(task => (
          <div key={task.id} className="sse-task-item">
            <span>{task.event.type}</span>
            <span>{task.status}</span>
            <span>{task.createdAt.toLocaleTimeString()}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## 6. Sử dụng SSE Client trực tiếp

```typescript
import { SSEClient } from '@/shared/utils';

// Tạo SSE client
const client = new SSEClient({
  url: '/api/v1/sse/notifications',
  headers: {
    'Authorization': `Bearer ${token}`,
  },
  autoReconnect: true,
  reconnectDelay: 3000,
  maxReconnectAttempts: 5,
  onOpen: (event) => {
    console.log('SSE connected');
  },
  onError: (error) => {
    console.error('SSE error:', error);
  },
  onMessage: (event) => {
    console.log('SSE message:', event.data);
  },
});

// Kết nối
client.connect();

// Subscribe vào events
const subscriptionId = client.subscribe('notification', (event) => {
  console.log('Notification:', event.data);
});

// Unsubscribe
client.unsubscribe(subscriptionId);

// Ngắt kết nối
client.disconnect();

// Lấy thông tin
const connectionInfo = client.getConnectionInfo();
const metrics = client.getMetrics();
```

## Best Practices

1. **Sử dụng Context cho multiple connections**: Khi cần nhiều kết nối SSE, sử dụng SSEProvider
2. **Filter events hiệu quả**: Sử dụng filter để chỉ xử lý events cần thiết
3. **Cleanup subscriptions**: Luôn unsubscribe trong useEffect cleanup
4. **Handle errors gracefully**: Implement error handling cho connection failures
5. **Limit event storage**: Sử dụng maxEvents để tránh memory leaks
6. **Use TaskQueue for heavy processing**: Tích hợp với TaskQueue cho các tác vụ nặng

## Troubleshooting

### Connection Issues
- Kiểm tra URL endpoint
- Verify authentication headers
- Check CORS settings
- Monitor network connectivity

### Performance Issues
- Limit số lượng events stored
- Use event filtering
- Implement proper cleanup
- Monitor memory usage

### Event Handling
- Validate event data structure
- Handle malformed JSON
- Implement retry logic
- Log errors for debugging
