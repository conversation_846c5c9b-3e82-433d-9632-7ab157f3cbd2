import React from 'react';
import {
  Card,
  FormGrid,
  FormItem,
  Input,
  // Button,
  // Icon,
} from '@/shared/components/common';

/**
 * Demo page cho Enhanced FormGrid Component
 */
const FormGridDemo: React.FC = () => {
  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Enhanced FormGrid Demo
        </h1>
        <p className="text-muted">
          Demonstration of enhanced FormGrid features with responsive layouts and advanced grid options
        </p>
      </div>

      {/* Basic Grid */}
      <Card title="Basic Grid Layout" className="mb-8">
        <FormGrid columns={3} columnsMd={2} columnsSm={1} gap="md">
          <FormItem name="firstName" label="First Name">
            <Input placeholder="Enter first name" />
          </FormItem>
          <FormItem name="lastName" label="Last Name">
            <Input placeholder="Enter last name" />
          </FormItem>
          <FormItem name="email" label="Email" className="col-span-1 md:col-span-2 lg:col-span-1">
            <Input type="email" placeholder="Enter email" />
          </FormItem>
          <FormItem name="phone" label="Phone">
            <Input type="tel" placeholder="Enter phone" />
          </FormItem>
          <FormItem name="company" label="Company">
            <Input placeholder="Enter company" />
          </FormItem>
          <FormItem name="position" label="Position">
            <Input placeholder="Enter position" />
          </FormItem>
        </FormGrid>
      </Card>

      {/* Responsive Grid */}
      <Card title="Responsive Grid" className="mb-8">
        <FormGrid
          columns={4}
          columnsMd={3}
          columnsSm={2}
          columnsXs={1}
          gap="lg"
          responsive
          minColumns={1}
          maxColumns={4}
        >
          <FormItem name="product1" label="Product 1">
            <Input placeholder="Product name" />
          </FormItem>
          <FormItem name="product2" label="Product 2">
            <Input placeholder="Product name" />
          </FormItem>
          <FormItem name="product3" label="Product 3">
            <Input placeholder="Product name" />
          </FormItem>
          <FormItem name="product4" label="Product 4">
            <Input placeholder="Product name" />
          </FormItem>
          <FormItem name="description" label="Description" className="col-span-full">
            <Input placeholder="Product description" />
          </FormItem>
        </FormGrid>
      </Card>

      {/* Grid with Areas */}
      <Card title="Grid Template Areas" className="mb-8">
        <FormGrid
          columns={3}
          gap="md"
          areas={[
            'header header header',
            'sidebar content content',
            'footer footer footer'
          ]}
        >
          <FormItem name="title" label="Title" className="grid-area-[header]">
            <Input placeholder="Enter title" />
          </FormItem>
          <FormItem name="category" label="Category" className="grid-area-[sidebar]">
            <Input placeholder="Select category" />
          </FormItem>
          <FormItem name="content" label="Content" className="grid-area-[content]">
            <Input placeholder="Enter content" />
          </FormItem>
          <FormItem name="tags" label="Tags" className="grid-area-[footer]">
            <Input placeholder="Enter tags" />
          </FormItem>
        </FormGrid>
      </Card>

      {/* Dense Grid */}
      <Card title="Dense Grid Layout" className="mb-8">
        <FormGrid columns={4} gap="sm" dense>
          <FormItem name="field1" label="Field 1">
            <Input placeholder="Field 1" />
          </FormItem>
          <FormItem name="field2" label="Field 2 (Span 2)" className="col-span-2">
            <Input placeholder="Field 2" />
          </FormItem>
          <FormItem name="field3" label="Field 3">
            <Input placeholder="Field 3" />
          </FormItem>
          <FormItem name="field4" label="Field 4">
            <Input placeholder="Field 4" />
          </FormItem>
          <FormItem name="field5" label="Field 5 (Span 3)" className="col-span-3">
            <Input placeholder="Field 5" />
          </FormItem>
          <FormItem name="field6" label="Field 6">
            <Input placeholder="Field 6" />
          </FormItem>
        </FormGrid>
      </Card>

      {/* Alignment Variations */}
      <Card title="Alignment Options" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Items Start</h3>
            <FormGrid columns={3} gap="md" alignItems="start" className="min-h-24">
              <FormItem name="start1" label="Field 1">
                <Input placeholder="Start aligned" />
              </FormItem>
              <FormItem name="start2" label="Field 2 with longer label text">
                <Input placeholder="Start aligned" />
              </FormItem>
              <FormItem name="start3" label="Field 3">
                <Input placeholder="Start aligned" />
              </FormItem>
            </FormGrid>
          </div>

          <div>
            <h3 className="font-medium mb-3">Items Center</h3>
            <FormGrid columns={3} gap="md" alignItems="center" className="min-h-24">
              <FormItem name="center1" label="Field 1">
                <Input placeholder="Center aligned" />
              </FormItem>
              <FormItem name="center2" label="Field 2 with longer label text">
                <Input placeholder="Center aligned" />
              </FormItem>
              <FormItem name="center3" label="Field 3">
                <Input placeholder="Center aligned" />
              </FormItem>
            </FormGrid>
          </div>

          <div>
            <h3 className="font-medium mb-3">Items End</h3>
            <FormGrid columns={3} gap="md" alignItems="end" className="min-h-24">
              <FormItem name="end1" label="Field 1">
                <Input placeholder="End aligned" />
              </FormItem>
              <FormItem name="end2" label="Field 2 with longer label text">
                <Input placeholder="End aligned" />
              </FormItem>
              <FormItem name="end3" label="Field 3">
                <Input placeholder="End aligned" />
              </FormItem>
            </FormGrid>
          </div>
        </div>
      </Card>

      {/* Gap Variations */}
      <Card title="Gap Variations" className="mb-8">
        <div className="space-y-6">
          <div>
            <h3 className="font-medium mb-3">Small Gap</h3>
            <FormGrid columns={4} gap="sm">
              <FormItem name="gap1" label="Field 1">
                <Input placeholder="Small gap" />
              </FormItem>
              <FormItem name="gap2" label="Field 2">
                <Input placeholder="Small gap" />
              </FormItem>
              <FormItem name="gap3" label="Field 3">
                <Input placeholder="Small gap" />
              </FormItem>
              <FormItem name="gap4" label="Field 4">
                <Input placeholder="Small gap" />
              </FormItem>
            </FormGrid>
          </div>

          <div>
            <h3 className="font-medium mb-3">Large Gap</h3>
            <FormGrid columns={4} gap="lg">
              <FormItem name="gap5" label="Field 1">
                <Input placeholder="Large gap" />
              </FormItem>
              <FormItem name="gap6" label="Field 2">
                <Input placeholder="Large gap" />
              </FormItem>
              <FormItem name="gap7" label="Field 3">
                <Input placeholder="Large gap" />
              </FormItem>
              <FormItem name="gap8" label="Field 4">
                <Input placeholder="Large gap" />
              </FormItem>
            </FormGrid>
          </div>

          <div>
            <h3 className="font-medium mb-3">Different Row Gap</h3>
            <FormGrid columns={2} gap="md" rowGap="xl">
              <FormItem name="rowgap1" label="Field 1">
                <Input placeholder="Different row gap" />
              </FormItem>
              <FormItem name="rowgap2" label="Field 2">
                <Input placeholder="Different row gap" />
              </FormItem>
              <FormItem name="rowgap3" label="Field 3">
                <Input placeholder="Different row gap" />
              </FormItem>
              <FormItem name="rowgap4" label="Field 4">
                <Input placeholder="Different row gap" />
              </FormItem>
            </FormGrid>
          </div>
        </div>
      </Card>

      {/* Auto Sizing */}
      <Card title="Auto Sizing" className="mb-8">
        <FormGrid columns={3} gap="md" autoRows autoColumns>
          <FormItem name="auto1" label="Auto Field 1">
            <Input placeholder="Auto sized" />
          </FormItem>
          <FormItem name="auto2" label="Auto Field 2 with much longer content">
            <Input placeholder="Auto sized with longer content" />
          </FormItem>
          <FormItem name="auto3" label="Auto Field 3">
            <Input placeholder="Auto sized" />
          </FormItem>
        </FormGrid>
      </Card>

      {/* Usage Guide */}
      <Card title="Usage Guide" className="mb-8">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">Enhanced FormGrid Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Responsive Columns:</strong> Different column counts for different screen sizes</li>
              <li><strong>Grid Template Areas:</strong> Define named grid areas for complex layouts</li>
              <li><strong>Dense Packing:</strong> Automatically fill gaps in the grid</li>
              <li><strong>Auto Sizing:</strong> Automatically adjust row and column sizes</li>
              <li><strong>Flexible Alignment:</strong> Control alignment of items and content</li>
              <li><strong>Gap Control:</strong> Separate control for row and column gaps</li>
              <li><strong>Responsive Constraints:</strong> Set minimum and maximum columns for responsive behavior</li>
            </ul>
          </div>

          <div>
            <h3 className="font-medium text-lg mb-2">CSS Grid Classes</h3>
            <p className="text-sm text-muted mb-2">
              You can use standard Tailwind CSS grid classes with FormGrid items:
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><code>col-span-2</code> - Span 2 columns</li>
              <li><code>row-span-2</code> - Span 2 rows</li>
              <li><code>col-start-2</code> - Start at column 2</li>
              <li><code>row-start-2</code> - Start at row 2</li>
              <li><code>col-span-full</code> - Span all columns</li>
              <li><code>row-span-full</code> - Span all rows</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default FormGridDemo;
