import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';
import { useImageGallery } from './useImageGallery';
import { useKeyboardNavigation, useZoom } from './hooks';
import { Icon } from '@/shared/components/common';
import ImageGalleryThumbnails from './ImageGalleryThumbnails';

interface ImageGalleryLightboxProps {
  className?: string;
}

/**
 * Component hiển thị lightbox cho Image Gallery
 */
const ImageGalleryLightbox: React.FC<ImageGalleryLightboxProps> = ({ className = '' }) => {
  const { t } = useTranslation();
  const {
    images,
    lightboxConfig,
    isLightboxOpen,
    currentImageIndex,
    closeLightbox,
    goToNext,
    goToPrev,
    goToImage,
    thumbnailsEnabled,
    zoomEnabled,
    zoomConfig,
    zoomLevel,
    setZoomLevel,
    resetZoom,
  } = useImageGallery();

  const currentImage = images[currentImageIndex];
  const lightboxRef = useRef<HTMLDivElement>(null);

  // Sử dụng keyboard navigation
  useKeyboardNavigation({
    isActive: isLightboxOpen,
    onNext: goToNext,
    onPrev: goToPrev,
    onClose: closeLightbox,
    onZoomIn: zoomEnabled
      ? () => setZoomLevel(zoomLevel + (zoomConfig.zoomStep || 0.5))
      : undefined,
    onZoomOut: zoomEnabled
      ? () => setZoomLevel(zoomLevel - (zoomConfig.zoomStep || 0.5))
      : undefined,
    onResetZoom: zoomEnabled ? resetZoom : undefined,
  });

  // Sử dụng zoom functionality
  const {
    zoomLevel: localZoomLevel,
    setZoomLevel: setLocalZoomLevel,
    zoomIn,
    zoomOut,
    resetZoom: resetLocalZoom,
    handleWheel,
    touchStartHandler,
    touchMoveHandler,
    touchEndHandler,
    position,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    isPanning,
  } = useZoom({
    config: zoomConfig,
    initialZoom: zoomLevel,
    onZoomChange: setZoomLevel,
  });

  // Đồng bộ zoom level từ context
  useEffect(() => {
    setLocalZoomLevel(zoomLevel);
  }, [zoomLevel, setLocalZoomLevel]);

  // Focus trap khi lightbox mở
  useEffect(() => {
    if (isLightboxOpen && lightboxRef.current) {
      lightboxRef.current.focus();

      // Prevent body scrolling
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isLightboxOpen]);

  // Nếu lightbox không mở, không render gì cả
  if (!isLightboxOpen) return null;

  // Render lightbox trong portal
  // Xử lý click vào backdrop để đóng lightbox
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    // Chỉ đóng khi click vào backdrop, không phải vào nội dung bên trong
    if (e.target === e.currentTarget && lightboxConfig.closeOnBackdropClick) {
      closeLightbox();
    }
  };

  return createPortal(
    <div
      ref={lightboxRef}
      className={`fixed inset-0 z-50 flex flex-col items-center justify-center bg-black ${className}`}
      style={{ backgroundColor: `rgba(0, 0, 0, ${lightboxConfig.backdropOpacity || 0.9})` }}
      tabIndex={0}
      role="dialog"
      aria-modal="true"
      aria-label="Image lightbox"
      onClick={handleBackdropClick}
      data-testid="image-gallery-lightbox"
    >
      {/* Toolbar */}
      <div className="absolute top-0 left-0 right-0 flex items-center justify-between p-4 z-10">
        {/* Counter */}
        {lightboxConfig.showCounter && images.length > 1 && (
          <div className="text-white text-sm">
            {t('components.imageGallery.imageCount', {
              current: currentImageIndex + 1,
              total: images.length,
            })}
          </div>
        )}

        {/* Zoom controls */}
        {zoomEnabled && lightboxConfig.showNavigation && (
          <div className="flex items-center space-x-2">
            <button
              className="text-white p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
              onClick={zoomOut}
              disabled={localZoomLevel <= (zoomConfig.minZoom || 1)}
              aria-label={t('components.imageGallery.zoomOut')}
            >
              <Icon name="close" size="sm" />
            </button>

            <div className="text-white text-sm">{Math.round(localZoomLevel * 100)}%</div>

            <button
              className="text-white p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
              onClick={zoomIn}
              disabled={localZoomLevel >= (zoomConfig.maxZoom || 3)}
              aria-label={t('components.imageGallery.zoomIn')}
            >
              <Icon name="plus" size="sm" />
            </button>

            <button
              className="text-white p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
              onClick={resetLocalZoom}
              disabled={localZoomLevel === 1}
              aria-label={t('components.imageGallery.resetZoom')}
            >
              <Icon name="settings" size="sm" />
            </button>
          </div>
        )}

        {/* Close button */}
        {lightboxConfig.showCloseButton && (
          <button
            className="text-white p-2 rounded-full hover:bg-white hover:bg-opacity-20 transition-colors focus:outline-none focus:ring-2 focus:ring-white"
            onClick={closeLightbox}
            aria-label={t('components.imageGallery.closeGallery')}
          >
            <Icon name="close" size="md" />
          </button>
        )}
      </div>

      {/* Main content */}
      <div
        className="relative flex-1 w-full flex items-center justify-center overflow-hidden"
        onWheel={zoomEnabled ? handleWheel : undefined}
        onMouseDown={zoomEnabled && localZoomLevel > 1 ? handleMouseDown : undefined}
        onMouseMove={zoomEnabled && isPanning ? handleMouseMove : undefined}
        onMouseUp={zoomEnabled ? handleMouseUp : undefined}
        onMouseLeave={zoomEnabled ? handleMouseUp : undefined}
        onTouchStart={zoomEnabled ? touchStartHandler : undefined}
        onTouchMove={zoomEnabled ? touchMoveHandler : undefined}
        onTouchEnd={zoomEnabled ? touchEndHandler : undefined}
        style={{ cursor: isPanning ? 'grabbing' : localZoomLevel > 1 ? 'grab' : 'default' }}
      >
        {/* Navigation buttons */}
        {lightboxConfig.showNavigation && images.length > 1 && (
          <>
            <button
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-3 opacity-70 hover:opacity-100 transition-opacity focus:outline-none focus:ring-2 focus:ring-white z-10"
              onClick={goToPrev}
              aria-label={t('components.imageGallery.previousImage')}
            >
              <Icon name="chevron-left" size="md" />
            </button>

            <button
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white rounded-full p-3 opacity-70 hover:opacity-100 transition-opacity focus:outline-none focus:ring-2 focus:ring-white z-10"
              onClick={goToNext}
              aria-label={t('components.imageGallery.nextImage')}
            >
              <Icon name="chevron-right" size="md" />
            </button>
          </>
        )}

        {/* Image */}
        {currentImage && (
          <div
            className="transition-transform duration-200 ease-out"
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${localZoomLevel})`,
              maxWidth: '100%',
              maxHeight: '100%',
            }}
          >
            <img
              src={currentImage.src}
              alt={currentImage.alt || `Image ${currentImageIndex + 1}`}
              className="max-w-full max-h-[calc(100vh-200px)] object-contain"
              draggable={false}
            />
          </div>
        )}
      </div>

      {/* Caption */}
      {lightboxConfig.showCaption && currentImage?.caption && (
        <div className="w-full p-4 text-center text-white">{currentImage.caption}</div>
      )}

      {/* Thumbnails */}
      {thumbnailsEnabled && images.length > 1 && (
        <div className="w-full p-4">
          <ImageGalleryThumbnails currentIndex={currentImageIndex} onSelect={goToImage} />
        </div>
      )}
    </div>,
    document.body
  );
};

export default ImageGalleryLightbox;
