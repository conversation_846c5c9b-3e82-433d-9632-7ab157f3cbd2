export const EXTERNAL_AGENT_ENDPOINTS = {
  // External Agents
  AGENTS: '/api/external-agents',
  AGENT_DETAIL: (id: string) => `/api/external-agents/${id}`,
  AGENT_TEST: (id: string) => `/api/external-agents/${id}/test`,
  AGENT_PERFORMANCE: (id: string) => `/api/external-agents/${id}/performance`,
  
  // Protocols
  PROTOCOLS: '/api/external-agents/protocols',
  PROTOCOL_DETECT: '/api/external-agents/protocols/detect',
  PROTOCOL_VALIDATE: '/api/external-agents/protocols/validate',
  
  // Protocol Templates
  PROTOCOL_TEMPLATES: '/api/external-agents/protocol-templates',
  PROTOCOL_TEMPLATE_DETAIL: (id: string) => `/api/external-agents/protocol-templates/${id}`,
  
  // Messages
  MESSAGES: '/api/external-agents/messages',
  MESSAGE_HISTORY: (agentId: string) => `/api/external-agents/${agentId}/messages`,
  MESSAGE_STATS: (agentId: string) => `/api/external-agents/${agentId}/stats`,
  
  // Webhooks
  WEBHOOKS: '/api/external-agents/webhooks',
  WEBHOOK_DETAIL: (id: string) => `/api/external-agents/webhooks/${id}`,
  WEBHOOK_DELIVERIES: (id: string) => `/api/external-agents/webhooks/${id}/deliveries`,
  
  // WebSocket
  WEBSOCKET: '/ws/external-agents',
} as const;
