import React from 'react';
import { useMenu } from './MenuContext';

export interface MenuDividerProps {
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component tạo đường phân cách giữa các menu item
 */
const MenuDivider: React.FC<MenuDividerProps> = ({ className = '' }) => {
  const { theme, collapsed, mode } = useMenu();

  // Nếu menu đang ở trạng thái collapsed, không hiển thị divider
  if (collapsed && mode !== 'horizontal') {
    return null;
  }

  // Xác định class dựa trên theme
  const themeClasses = {
    default: 'border-gray-200 dark:border-gray-700',
    dark: 'border-gray-700',
    light: 'border-gray-200',
  }[theme];

  return (
    <div
      className={`my-1 border-t ${themeClasses} ${className}`}
      role="separator"
      aria-orientation="horizontal"
    />
  );
};

export default React.memo(MenuDivider);
