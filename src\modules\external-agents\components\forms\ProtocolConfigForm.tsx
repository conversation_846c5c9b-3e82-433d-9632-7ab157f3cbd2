import React from 'react';
import { useTranslation } from 'react-i18next';
import { FormItem, Input, Select, Typography, Button } from '@/shared/components/common';
import { ProtocolConfig, ProtocolType } from '../../types';
import { getProtocolLabel } from '../../utils';

interface ProtocolConfigFormProps {
  value: ProtocolConfig;
  onChange: (value: ProtocolConfig) => void;
  onDetect?: (endpoint: string) => void;
  isDetecting?: boolean;
  className?: string;
}

const ProtocolConfigForm: React.FC<ProtocolConfigFormProps> = ({
  value,
  onChange,
  onDetect,
  isDetecting = false,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);

  const protocolOptions = Object.values(ProtocolType).map(type => ({
    value: type,
    label: getProtocolLabel(type),
  }));

  const handleTypeChange = (type: ProtocolType) => {
    onChange({
      ...value,
      type,
      specifications: getDefaultSpecifications(type),
    });
  };

  const handleSpecificationChange = (key: string, specValue: unknown) => {
    onChange({
      ...value,
      specifications: {
        ...value.specifications,
        [key]: specValue,
      },
    });
  };

  const handleMethodsChange = (methods: string[]) => {
    onChange({
      ...value,
      supportedMethods: methods,
    });
  };

  const handleHeadersChange = (headers: string[], type: 'required' | 'optional') => {
    onChange({
      ...value,
      [type === 'required' ? 'requiredHeaders' : 'optionalHeaders']: headers,
    });
  };

  const getDefaultSpecifications = (type: ProtocolType): Record<string, unknown> => {
    switch (type) {
      case ProtocolType.REST_API:
        return {
          version: '1.0',
          contentType: 'application/json',
          acceptType: 'application/json',
        };
      case ProtocolType.WEBSOCKET:
        return {
          subprotocols: [],
          heartbeatInterval: 30000,
        };
      case ProtocolType.GRPC:
        return {
          version: '1.0',
          compression: 'gzip',
        };
      case ProtocolType.MCP:
        return {
          version: '1.0',
          capabilities: [],
        };
      default:
        return {};
    }
  };

  const renderProtocolSpecificFields = () => {
    switch (value.type) {
      case ProtocolType.REST_API:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem label={t('external-agents:protocol.version')} name="version">
                <Input
                  value={value.version || ''}
                  onChange={(e) => onChange({ ...value, version: e.target.value })}
                  placeholder="1.0"
                />
              </FormItem>
              <FormItem label={t('external-agents:protocol.contentType')} name="contentType">
                <Select
                  value={(value.specifications?.contentType as string) || 'application/json'}
                  onChange={(contentType) => handleSpecificationChange('contentType', contentType)}
                  options={[
                    { value: 'application/json', label: 'application/json' },
                    { value: 'application/xml', label: 'application/xml' },
                    { value: 'text/plain', label: 'text/plain' },
                  ]}
                />
              </FormItem>
            </div>
            
            <FormItem label={t('external-agents:protocol.supportedMethods')} name="methods">
              <div className="flex flex-wrap gap-2">
                {['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].map(method => (
                  <label key={method} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={value.supportedMethods?.includes(method) || false}
                      onChange={(e) => {
                        const methods = value.supportedMethods || [];
                        if (e.target.checked) {
                          handleMethodsChange([...methods, method]);
                        } else {
                          handleMethodsChange(methods.filter(m => m !== method));
                        }
                      }}
                      className="rounded"
                    />
                    <Typography variant="body2">{method}</Typography>
                  </label>
                ))}
              </div>
            </FormItem>
          </div>
        );

      case ProtocolType.WEBSOCKET:
        return (
          <div className="space-y-4">
            <FormItem label={t('external-agents:protocol.subprotocols')} name="subprotocols">
              <Input
                value={(value.specifications?.subprotocols as string[])?.join(', ') || ''}
                onChange={(e) => handleSpecificationChange('subprotocols', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                placeholder="chat, echo"
              />
            </FormItem>
            <FormItem label={t('external-agents:protocol.heartbeatInterval')} name="heartbeatInterval">
              <Input
                type="number"
                value={(value.specifications?.heartbeatInterval as number) || 30000}
                onChange={(e) => handleSpecificationChange('heartbeatInterval', parseInt(e.target.value))}
                placeholder="30000"
              />
            </FormItem>
          </div>
        );

      case ProtocolType.GRPC:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem label={t('external-agents:protocol.version')} name="version">
                <Input
                  value={value.version || ''}
                  onChange={(e) => onChange({ ...value, version: e.target.value })}
                  placeholder="1.0"
                />
              </FormItem>
              <FormItem label={t('external-agents:protocol.compression')} name="compression">
                <Select
                  value={(value.specifications?.compression as string) || 'gzip'}
                  onChange={(compression) => handleSpecificationChange('compression', compression)}
                  options={[
                    { value: 'none', label: 'None' },
                    { value: 'gzip', label: 'gzip' },
                    { value: 'deflate', label: 'deflate' },
                  ]}
                />
              </FormItem>
            </div>
          </div>
        );

      case ProtocolType.MCP:
        return (
          <div className="space-y-4">
            <FormItem label={t('external-agents:protocol.version')} name="version">
              <Input
                value={value.version || ''}
                onChange={(e) => onChange({ ...value, version: e.target.value })}
                placeholder="1.0"
              />
            </FormItem>
            <FormItem label={t('external-agents:protocol.capabilities')} name="capabilities">
              <Input
                value={(value.specifications?.capabilities as string[])?.join(', ') || ''}
                onChange={(e) => handleSpecificationChange('capabilities', e.target.value.split(',').map(s => s.trim()).filter(Boolean))}
                placeholder="tools, resources, prompts"
              />
            </FormItem>
          </div>
        );

      case ProtocolType.CUSTOM:
        return (
          <div className="space-y-4">
            <Typography variant="body2" className="text-muted-foreground">
              {t('external-agents:protocol.customDescription')}
            </Typography>
            <FormItem label={t('external-agents:protocol.customSpecs')} name="customSpecs">
              <textarea
                className="w-full p-3 border rounded-md bg-background"
                rows={6}
                value={JSON.stringify(value.specifications || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const specifications = JSON.parse(e.target.value);
                    onChange({ ...value, specifications });
                  } catch {
                    // Invalid JSON, ignore
                  }
                }}
                placeholder='{"key": "value"}'
              />
            </FormItem>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Protocol Type Selection */}
      <div className="flex items-end gap-3">
        <div className="flex-1">
          <FormItem label={t('external-agents:protocol.type')} name="protocolType" required>
            <Select
              value={value.type}
              onChange={(value) => handleTypeChange(value as ProtocolType)}
              options={protocolOptions}
            />
          </FormItem>
        </div>
        {onDetect && (
          <Button
            type="button"
            variant="outline"
            onClick={() => onDetect('')}
            disabled={isDetecting}
            className="mb-1"
          >
            {isDetecting ? t('external-agents:protocol.detecting') : t('external-agents:protocol.autoDetect')}
          </Button>
        )}
      </div>

      {/* Protocol-specific Configuration */}
      {renderProtocolSpecificFields()}

      {/* Headers Configuration */}
      <div className="space-y-4">
        <Typography variant="h4">
          {t('external-agents:protocol.headers')}
        </Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('external-agents:protocol.requiredHeaders')} name="requiredHeaders">
            <Input
              value={value.requiredHeaders?.join(', ') || ''}
              onChange={(e) => handleHeadersChange(e.target.value.split(',').map(s => s.trim()).filter(Boolean), 'required')}
              placeholder="Authorization, Content-Type"
            />
          </FormItem>

          <FormItem label={t('external-agents:protocol.optionalHeaders')} name="optionalHeaders">
            <Input
              value={value.optionalHeaders?.join(', ') || ''}
              onChange={(e) => handleHeadersChange(e.target.value.split(',').map(s => s.trim()).filter(Boolean), 'optional')}
              placeholder="User-Agent, Accept-Language"
            />
          </FormItem>
        </div>
      </div>
    </div>
  );
};

export default ProtocolConfigForm;
