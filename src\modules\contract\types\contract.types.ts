/**
 * Contract module types
 */

export enum ContractType {
  BUSINESS = 'business',
  PERSONAL = 'personal',
}

export enum ContractStep {
  TYPE_SELECTION = 'type_selection',
  TERMS_ACCEPTANCE = 'terms_acceptance',
  INFO_FORM = 'info_form',
  CONTRACT_DISPLAY = 'contract_display',
  CONTRACT_SIGNING = 'contract_signing',
  HAND_SIGNATURE = 'hand_signature',
  OTP_VERIFICATION = 'otp_verification',
  COMPLETED = 'completed',
}

export interface BusinessInfo {
  companyName: string;
  taxCode: string;
  companyEmail: string;
  companyAddress: string;
  companyPhone: string;
  representative: string;
  position: string;
}

export interface PersonalInfo {
  fullName: string;
  dateOfBirth: string;
  idNumber: string;
  idIssuedDate: string;
  idIssuedPlace: string;
  phone: string;
  address: string;
  taxCode?: string;
}

export interface ContractData {
  type?: ContractType;
  termsAccepted: boolean;
  businessInfo?: BusinessInfo;
  personalInfo?: PersonalInfo;
  contractUrl?: string;
  contractBase64?: string;
  handSignature?: string;
  otpCode?: string;
  isCompleted?: boolean;
}

export interface ContractStepProps {
  data: ContractData;
  onNext: (updatedData: Partial<ContractData>) => void;
  onPrevious: () => void;
  isLoading?: boolean;
}
