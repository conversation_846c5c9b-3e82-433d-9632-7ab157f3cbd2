import { useState, useCallback } from 'react';
import { FieldValues } from 'react-hook-form';
import { useFormErrors } from './useFormErrors';

/**
 * Kết quả trả về từ API
 */
export interface ApiResponse<TData = unknown> {
  success: boolean;
  data?: TData;
  errors?: Record<string, string>;
  message?: string;
}

/**
 * Tham số cho hook useApiForm
 */
export interface UseApiFormOptions<TFormValues extends FieldValues, TResponseData = unknown> {
  /**
   * Hàm gọi API
   */
  apiCall: (values: TFormValues) => Promise<ApiResponse<TResponseData>>;

  /**
   * Callback khi gọi API thành công
   */
  onSuccess?: (data: TResponseData) => void;

  /**
   * Callback khi gọi API thất bại
   */
  onError?: (errors: Record<string, string> | undefined, message?: string) => void;

  /**
   * Có reset form sau khi gọi API thành công hay không
   */
  resetOnSuccess?: boolean;
}

/**
 * Hook để xử lý form với API một cách đầy đủ
 *
 * @example
 * const { formRef, handleSubmit, isSubmitting, isSuccess, error } = useApiForm<LoginFormValues>({
 *   apiCall: api.login,
 *   onSuccess: (data) => {
 *     // Xử lý khi đăng nhập thành công
 *     navigate('/dashboard');
 *   },
 *   resetOnSuccess: true
 * });
 *
 * // Trong JSX
 * <Form ref={formRef} onSubmit={handleSubmit}>
 *   ...
 * </Form>
 */
export function useApiForm<TFormValues extends FieldValues, TResponseData = unknown>({
  apiCall,
  onSuccess,
  onError,
  resetOnSuccess = false,
}: UseApiFormOptions<TFormValues, TResponseData>) {
  // Sử dụng hook useFormErrors
  const { formRef, setFormErrors, resetForm } = useFormErrors<TFormValues>();

  // State cho trạng thái form
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<TResponseData | null>(null);

  /**
   * Xử lý submit form
   */
  const handleSubmit = useCallback(
    async (values: TFormValues) => {
      setIsSubmitting(true);
      setIsSuccess(false);
      setError(null);

      try {
        // Gọi API
        const response = await apiCall(values);

        if (response.success) {
          // Xử lý khi API thành công
          setIsSuccess(true);
          if (response.data) {
            setData(response.data);
          }

          // Gọi callback onSuccess nếu có
          if (onSuccess && response.data) {
            onSuccess(response.data);
          }

          // Reset form nếu cần
          if (resetOnSuccess) {
            resetForm();
          }
        } else {
          // Xử lý khi API thất bại
          if (response.errors) {
            setFormErrors(response.errors);
          }

          if (response.message) {
            setError(response.message);
          }

          // Gọi callback onError nếu có
          if (onError) {
            onError(response.errors, response.message);
          }
        }
      } catch (err) {
        // Xử lý khi có lỗi không mong muốn
        const errorMessage = err instanceof Error ? err.message : 'Đã xảy ra lỗi không xác định';
        setError(errorMessage);

        // Gọi callback onError nếu có
        if (onError) {
          onError(undefined, errorMessage);
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [apiCall, onSuccess, onError, resetOnSuccess, resetForm, setFormErrors]
  );

  return {
    formRef,
    handleSubmit,
    isSubmitting,
    isSuccess,
    error,
    data,
    resetForm,
    setFormErrors,
  };
}

export default useApiForm;
