import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Button,
  Card,
  Icon,
  Input,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useGetWebsites, useCreateWebsite } from '@/modules/integration/website/hooks/useWebsite';
import { WebsiteDto, WebsiteQueryDto, CreateWebsiteDto, WebsiteSortBy, SortDirection } from '@/modules/integration/website/types/website.types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Website - mapped từ WebsiteDto API
 */
interface Website {
  id: string;
  name: string;
  url: string;
  icon?: string;
  category?: string;
  isConnected?: boolean;
  status?: 'active' | 'pending' | 'error';
}

/**
 * Props cho component WebsiteSlideInForm
 */
interface WebsiteSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các website
   */
  onSelect: (selectedWebsites: Website[]) => void;

  /**
   * Danh sách ID của các website đã chọn
   */
  selectedWebsiteIds?: string[];
}

/**
 * Component form trượt để chọn các website để tích hợp - sử dụng API thực
 */
const WebsiteSlideInForm: React.FC<WebsiteSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedWebsiteIds = [],
}) => {
  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedWebsiteIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<WebsiteQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: WebsiteSortBy.HOST,
    sortDirection: SortDirection.ASC,
  });

  // State cho thêm website mới
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newWebsiteName, setNewWebsiteName] = useState<string>('');
  const [newWebsiteUrl, setNewWebsiteUrl] = useState<string>('');

  // API hooks
  const { data: websitesData, isLoading, refetch } = useGetWebsites(queryParams);
  const createWebsiteMutation = useCreateWebsite();

  // Transform API data to component format
  const websites: Website[] = useMemo(() => {
    if (!websitesData?.result?.items) return [];

    return websitesData.result.items.map((website: WebsiteDto): Website => ({
      id: website.id,
      name: website.host, // API chỉ có host, không có name riêng
      url: `https://${website.host}`,
      icon: website.logo || undefined,
      category: undefined, // API không có category
      isConnected: !!website.agentId,
      status: website.verify ? 'active' : 'pending',
    }));
  }, [websitesData]);

  // Pagination data
  const totalItems = websitesData?.result?.meta?.totalItems || 0;
  const currentPage = queryParams.page || 1;
  const itemsPerPage = queryParams.limit || 10;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Website>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'website',
      title: 'Website',
      dataIndex: 'name',
      width: '50%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.icon ? (
            <img
              src={record.icon}
              alt={record.name}
              className="w-10 h-10 rounded-md mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-md bg-green-100 flex items-center justify-center mr-3">
              <Icon name="globe" size="md" className="text-green-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.url}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '25%',
    },
    {
      key: 'status',
      title: 'Trạng thái',
      dataIndex: 'status',
      width: '25%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đã kết nối
            </span>
          ) : record.status === 'pending' ? (
            <span className="text-yellow-500 text-sm flex items-center">
              <Icon name="clock" size="sm" className="mr-1" />
              Đang xử lý
            </span>
          ) : record.status === 'error' ? (
            <span className="text-red-500 text-sm flex items-center">
              <Icon name="alert-circle" size="sm" className="mr-1" />
              Lỗi kết nối
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              Chưa kết nối
            </span>
          )}
        </div>
      ),
    },
  ];





  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedWebsiteIds.length ||
      selectedIds.some(id => !selectedWebsiteIds.includes(id)) ||
      selectedWebsiteIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedWebsiteIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    const sortBy = column === 'name' ? WebsiteSortBy.HOST : WebsiteSortBy.HOST;
    setQueryParams((prev: WebsiteQueryDto) => ({
      ...prev,
      sortBy,
      sortDirection: direction as SortDirection,
    }));
  };

  // Xử lý thêm website mới (API thực)
  const handleAddWebsite = async () => {
    if (!newWebsiteName || !newWebsiteUrl) {
      alert('Vui lòng nhập đầy đủ thông tin website');
      return;
    }

    // Kiểm tra URL hợp lệ và extract host
    let host: string;
    try {
      const url = new URL(newWebsiteUrl);
      host = url.hostname;
    } catch {
      alert('URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)');
      return;
    }

    try {
      // Gọi API để tạo website mới
      const createData: CreateWebsiteDto = {
        websiteName: newWebsiteName,
        host: host,
      };

      await createWebsiteMutation.mutateAsync(createData);

      // Reset form
      setNewWebsiteName('');
      setNewWebsiteUrl('');
      setShowAddForm(false);

      // Refetch data để cập nhật danh sách
      refetch();

      alert('Thêm website thành công!');
    } catch (error) {
      console.error('Error adding website:', error);
      alert('Có lỗi xảy ra khi thêm website');
    }
  };

  // Xử lý lưu (API thực)
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Lấy thông tin đầy đủ của các website đã chọn từ API data
      const selectedWebsites = websites.filter(website =>
        selectedIds.includes(website.id)
      );

      // Gọi callback với dữ liệu đã chọn
      onSelect(selectedWebsites);
      onClose();
    } catch (error) {
      console.error('Error saving selected websites:', error);
      alert('Có lỗi xảy ra khi lưu dữ liệu');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, search: '' }));
    setShowAddForm(false);
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-host',
      label: 'Host',
      onClick: () => handleSortChange('host', queryParams.sortDirection === SortDirection.ASC ? 'DESC' : 'ASC'),
    },
    {
      id: 'sort-created',
      label: 'Ngày tạo',
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({
          ...prev,
          sortBy: WebsiteSortBy.CREATED_AT,
          sortDirection: prev.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC,
        }));
      },
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, verify: undefined }));
      },
    },
    {
      id: 'filter-verified',
      label: 'Đã xác minh',
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, verify: true }));
      },
    },
    {
      id: 'filter-not-verified',
      label: 'Chưa xác minh',
      onClick: () => {
        setQueryParams((prev: WebsiteQueryDto) => ({ ...prev, verify: false }));
      },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn website</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            onAdd={() => setShowAddForm(!showAddForm)}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Form thêm website mới */}
        {showAddForm && (
          <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <Typography variant="subtitle1" className="mb-3">Thêm website mới</Typography>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <label htmlFor="websiteName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Tên website
                </label>
                <Input
                  id="websiteName"
                  value={newWebsiteName}
                  onChange={(e) => setNewWebsiteName(e.target.value)}
                  placeholder="Nhập tên website"
                  fullWidth
                />
              </div>
              <div>
                <label htmlFor="websiteUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  URL
                </label>
                <Input
                  id="websiteUrl"
                  value={newWebsiteUrl}
                  onChange={(e) => setNewWebsiteUrl(e.target.value)}
                  placeholder="https://example.com"
                  fullWidth
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button
                variant="outline"
                onClick={() => setShowAddForm(false)}
                className="mr-2"
              >
                Hủy
              </Button>
              <Button
                variant="primary"
                onClick={handleAddWebsite}
                disabled={!newWebsiteName || !newWebsiteUrl}
                isLoading={createWebsiteMutation.isPending}
              >
                Thêm
              </Button>
            </div>
          </div>
        )}

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<Website>
            columns={columns}
            data={websites}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== itemsPerPage) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default WebsiteSlideInForm;
