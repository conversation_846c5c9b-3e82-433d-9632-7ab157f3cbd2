import React from 'react';
import Radio, { RadioVariant } from './Radio';
import {
  FormControlSize,
  FormControlColor,
} from '@/shared/components/common/Form/utils/formControlUtils';
import { useFormControlGroup } from '@/shared/components/common/Form/hooks/useFormControlGroup';

export interface RadioOption {
  /**
   * Nhãn hiển thị cho tùy chọn
   */
  label: string | React.ReactNode;

  /**
   * Giá trị của tùy chọn
   */
  value: string | number | readonly string[];

  /**
   * Vô hiệu hóa tùy chọn này
   */
  disabled?: boolean;
}

export interface RadioGroupProps {
  /**
   * Danh sách các tùy chọn
   */
  options: RadioOption[];

  /**
   * Giá trị đã chọn
   */
  value?: string | number | readonly string[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | number | readonly string[]) => void;

  /**
   * <PERSON><PERSON> hiệu hóa toàn bộ group
   */
  disabled?: boolean;

  /**
   * CSS classes tùy chỉnh
   */
  className?: string;

  /**
   * Hướng hiển thị: ngang hoặc dọc
   */
  direction?: 'horizontal' | 'vertical';

  /**
   * Kích thước của các radio button
   */
  size?: FormControlSize;

  /**
   * Biến thể của radio button
   */
  variant?: RadioVariant;

  /**
   * Màu sắc của radio button
   */
  color?: FormControlColor;

  /**
   * Tên của group, dùng khi submit form
   */
  name?: string;
}

/**
 * Component RadioGroup cho phép người dùng chọn một tùy chọn từ một danh sách
 */
const RadioGroup: React.FC<RadioGroupProps> = ({
  options = [],
  value,
  onChange,
  disabled = false,
  className = '',
  direction = 'vertical',
  size = 'md',
  variant = 'default',
  color = 'primary',
  name,
}) => {
  // Sử dụng custom hook để xử lý logic chung
  const { selectedValue, handleItemChange, selectedClass } = useFormControlGroup<
    string | number | readonly string[]
  >({
    value: value || '',
    ...(onChange && { onChange }),
    disabled,
    direction,
  });

  // Xử lý khi một radio button thay đổi
  const handleRadioChange = (optionValue: string | number | readonly string[]) => {
    handleItemChange(optionValue);
  };

  return (
    <div className={`${selectedClass} ${className}`}>
      {options.map((option, index) => (
        <Radio
          key={index}
          label={option.label}
          value={option.value}
          checked={selectedValue === option.value}
          onChange={() => handleRadioChange(option.value)}
          disabled={disabled || option.disabled || false}
          size={size}
          variant={variant}
          color={color}
          name={name}
        />
      ))}
    </div>
  );
};

export default RadioGroup;
