import { createContext, useContext } from 'react';

export type MenuMode = 'horizontal' | 'vertical' | 'inline';
export type MenuTheme = 'default' | 'dark' | 'light';

export interface MenuContextType {
  /**
   * Chế độ hiển thị của menu
   */
  mode: MenuMode;

  /**
   * Theme của menu
   */
  theme: MenuTheme;

  /**
   * Trạng thái thu gọn
   */
  collapsed: boolean;

  /**
   * Key của menu item đang được chọn
   */
  selectedKey: string | undefined;

  /**
   * Các key của menu items đang được mở rộng (cho submenu)
   */
  expandedKeys: string[];

  /**
   * Cập nhật selectedKey
   */
  setSelectedKey: (key: string) => void;

  /**
   * Mở rộng/thu gọn một submenu
   */
  toggleExpand: (key: string) => void;

  /**
   * Kiểm tra xem một key có đang được mở rộng không
   */
  isExpanded: (key: string) => boolean;
}

export const MenuContext = createContext<MenuContextType | undefined>(undefined);

export const useMenu = (): MenuContextType => {
  const context = useContext(MenuContext);
  if (!context) {
    throw new Error('useMenu must be used within a MenuProvider');
  }
  return context;
};
