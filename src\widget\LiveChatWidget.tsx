import { useEffect, useState } from 'react';
import Button from '@/shared/components/common/Button';
import Input from '@/shared/components/common/Input';
import { IoMdClose } from 'react-icons/io';
import { IoSend } from 'react-icons/io5';
import { FaComments } from 'react-icons/fa';
import './LiveChatWidget.css';

interface LiveChatWidgetProps {
  apiKey?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  primaryColor?: string;
  title?: string;
  greeting?: string;
  darkMode?: boolean;
}

/**
 * LiveChatWidget - Component cho phép nhúng live chat vào các website khác
 *
 * @param apiKey - Khóa API để kết nối với dịch vụ chat
 * @param position - Vị trí hiển thị của widget (mặc định: bottom-right)
 * @param primaryColor - <PERSON><PERSON>u chủ đạo của widget
 * @param title - <PERSON><PERSON><PERSON><PERSON> đề của cửa sổ chat
 * @param greeting - <PERSON><PERSON><PERSON> chào khi mở chat
 * @param darkMode - Chế độ tối (mặc định: false)
 */
export const LiveChatWidget = ({
  apiKey,
  position = 'bottom-right',
  primaryColor = '#ff3333',
  title = 'RedAI Chat',
  greeting = 'Xin chào! Tôi có thể giúp gì cho bạn?',
  darkMode = false,
}: LiveChatWidgetProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<
    Array<{ text: string; isUser: boolean; timestamp: Date }>
  >([{ text: greeting, isUser: false, timestamp: new Date() }]);
  const [inputValue, setInputValue] = useState('');

  console.log(apiKey);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  const sendMessage = () => {
    if (!inputValue.trim()) return;

    // Thêm tin nhắn của người dùng
    setMessages([...messages, { text: inputValue, isUser: true, timestamp: new Date() }]);

    // Giả lập phản hồi từ bot (trong thực tế sẽ gọi API)
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        {
          text: 'Cảm ơn bạn đã liên hệ. Chúng tôi sẽ phản hồi sớm nhất có thể.',
          isUser: false,
          timestamp: new Date(),
        },
      ]);
    }, 1000);

    setInputValue('');
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      sendMessage();
    }
  };

  // Áp dụng CSS variables cho màu sắc tùy chỉnh và chế độ tối
  useEffect(() => {
    document.documentElement.style.setProperty('--redai-chat-primary-color', primaryColor);

    // Thêm hoặc xóa class dark cho container
    const container = document.getElementById('redai-chat-widget-container');
    if (container) {
      if (darkMode) {
        container.classList.add('dark');
      } else {
        container.classList.remove('dark');
      }
    }
  }, [primaryColor, darkMode]);

  // Format timestamp
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className={`redai-chat-widget ${position}`}>
      {isOpen ? (
        <div className="redai-chat-container">
          <div className="redai-chat-header">
            <h3>{title}</h3>
            <Button variant="ghost" size="sm" className="redai-chat-close" onClick={toggleChat}>
              <IoMdClose size={20} />
            </Button>
          </div>
          <div className="redai-chat-messages">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`redai-chat-message ${msg.isUser ? 'redai-user-message' : 'redai-bot-message'}`}
              >
                <div className="redai-chat-message-content">{msg.text}</div>
                <div className="redai-chat-message-time">{formatTime(msg.timestamp)}</div>
              </div>
            ))}
          </div>
          <div className="redai-chat-input-container">
            <Input
              value={inputValue}
              onChange={e => setInputValue(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Nhập tin nhắn..."
              className="redai-chat-input"
              rightIcon={
                <Button
                  variant="primary"
                  size="sm"
                  className="redai-chat-send"
                  onClick={sendMessage}
                >
                  <IoSend size={16} />
                </Button>
              }
            />
          </div>
        </div>
      ) : (
        <Button variant="primary" className="redai-chat-button" onClick={toggleChat}>
          <FaComments size={24} />
        </Button>
      )}
    </div>
  );
};

export default LiveChatWidget;
