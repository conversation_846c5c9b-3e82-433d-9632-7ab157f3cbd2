import { useEffect, useRef } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

/**
 * Loại dependency giữa các field
 */
export enum DependencyType {
  /**
   * G<PERSON> giá trị trực tiếp
   */
  SET_VALUE = 'setValue',

  /**
   * G<PERSON> giá trị dựa trên hàm transform
   */
  TRANSFORM = 'transform',

  /**
   * Gán giá trị từ danh sách options (cascading select)
   */
  OPTIONS = 'options',

  /**
   * Gán giá trị mặc định
   */
  DEFAULT = 'default',
}

/**
 * Định nghĩa một dependency đơn giản
 */
export interface SimpleDependency {
  /**
   * Loại dependency
   */
  type: DependencyType.SET_VALUE | DependencyType.DEFAULT;

  /**
   * Field nguồn (field mà giá trị của nó ảnh hưởng đến field đích)
   */
  sourceField: string;

  /**
   * Field đích (field bị ảnh hưởng)
   */
  targetField: string;

  /**
   * <PERSON>i<PERSON> trị mặc định khi sourceField thay đổi (chỉ dùng với type = DEFAULT)
   */
  defaultValue?: unknown;

  /**
   * Có reset giá trị khi sourceField thay đổi hay không
   */
  resetOnChange?: boolean;
}

/**
 * Định nghĩa một dependency với transform function
 */
export interface TransformDependency {
  /**
   * Loại dependency
   */
  type: DependencyType.TRANSFORM;

  /**
   * Field nguồn (field mà giá trị của nó ảnh hưởng đến field đích)
   */
  sourceField: string | string[];

  /**
   * Field đích (field bị ảnh hưởng)
   */
  targetField: string;

  /**
   * Hàm transform giá trị từ sourceField sang targetField
   */
  transform: (sourceValue: unknown, formValues: Record<string, unknown>) => unknown;

  /**
   * Có reset giá trị khi sourceField thay đổi hay không
   */
  resetOnChange?: boolean;
}

/**
 * Định nghĩa một dependency với options (cascading select)
 */
export interface OptionsDependency {
  /**
   * Loại dependency
   */
  type: DependencyType.OPTIONS;

  /**
   * Field nguồn (field mà giá trị của nó ảnh hưởng đến field đích)
   */
  sourceField: string;

  /**
   * Field đích (field bị ảnh hưởng)
   */
  targetField: string;

  /**
   * Hàm lấy options dựa trên giá trị của sourceField
   */
  getOptions: (sourceValue: unknown, formValues: Record<string, unknown>) => unknown[];

  /**
   * Có reset giá trị khi sourceField thay đổi hay không
   * @default true
   */
  resetOnChange?: boolean;

  /**
   * Tên của state để lưu options
   * Nếu không cung cấp, sẽ tự động tạo dựa trên targetField
   */
  optionsStateName?: string;
}

/**
 * Kiểu dependency tổng hợp
 */
export type Dependency = SimpleDependency | TransformDependency | OptionsDependency;

/**
 * Tham số cho hook useFieldDependency
 */
export interface UseFieldDependencyOptions {
  /**
   * Danh sách các dependency
   */
  dependencies: Dependency[];

  /**
   * Có kích hoạt dependency ngay khi mount hay không
   * @default true
   */
  triggerOnMount?: boolean;
}

/**
 * Kết quả trả về từ hook useFieldDependency
 */
export interface UseFieldDependencyResult {
  /**
   * Danh sách options cho các field (chỉ dùng với dependency type = OPTIONS)
   */
  options: Record<string, unknown[]>;
}

/**
 * Hook để quản lý dependencies giữa các field trong form
 *
 * @example
 * // Cập nhật giá trị field dựa trên giá trị của field khác
 * const { options } = useFieldDependency({
 *   dependencies: [
 *     {
 *       type: DependencyType.SET_VALUE,
 *       sourceField: 'firstName',
 *       targetField: 'fullName',
 *     }
 *   ]
 * });
 *
 * // Cập nhật giá trị field với transform function
 * const { options } = useFieldDependency({
 *   dependencies: [
 *     {
 *       type: DependencyType.TRANSFORM,
 *       sourceField: ['firstName', 'lastName'],
 *       targetField: 'fullName',
 *       transform: (_, formValues) => `${formValues.firstName || ''} ${formValues.lastName || ''}`.trim(),
 *     }
 *   ]
 * });
 *
 * // Cascading select
 * const { options } = useFieldDependency({
 *   dependencies: [
 *     {
 *       type: DependencyType.OPTIONS,
 *       sourceField: 'country',
 *       targetField: 'city',
 *       getOptions: (country) => getCitiesByCountry(country),
 *       resetOnChange: true,
 *     }
 *   ]
 * });
 */
export function useFieldDependency({
  dependencies,
  triggerOnMount = true,
}: UseFieldDependencyOptions): UseFieldDependencyResult {
  // Luôn gọi các hooks ở đầu component, không đặt trong điều kiện
  const formContextResult = useFormContext();

  // Lưu trữ options cho các field - luôn gọi useRef trước
  const optionsRef = useRef<Record<string, unknown[]>>({});

  // Lấy danh sách các field cần theo dõi
  const fieldsToWatch = dependencies.flatMap(dependency => {
    if (Array.isArray(dependency.sourceField)) {
      return dependency.sourceField;
    }
    return [dependency.sourceField];
  });

  // Theo dõi các field nguồn - luôn gọi useWatch sau useFormContext
  // Sử dụng giá trị mặc định cho control nếu formContextResult không tồn tại
  const watchedValues = useWatch({
    control: formContextResult?.control,
    name: fieldsToWatch,
  });

  // Xử lý dependencies
  useEffect(() => {
    // Nếu không có formContext, không thực hiện gì cả
    if (!formContextResult) {
      console.warn('useFieldDependency must be used within a Form component');
      return;
    }

    const { getValues, setValue, trigger } = formContextResult;

    dependencies.forEach(dependency => {
      const formValues = getValues();

      // Xử lý dependency dựa trên loại
      switch (dependency.type) {
        case DependencyType.SET_VALUE: {
          // Lấy giá trị của field nguồn
          const sourceValue = formValues[dependency.sourceField];

          // Cập nhật giá trị cho field đích
          setValue(dependency.targetField, sourceValue);
          break;
        }

        case DependencyType.TRANSFORM: {
          // Lấy giá trị của field nguồn
          let sourceValue: unknown;
          if (Array.isArray(dependency.sourceField)) {
            sourceValue = dependency.sourceField.map(field => formValues[field]);
          } else {
            sourceValue = formValues[dependency.sourceField];
          }

          // Áp dụng hàm transform
          const transformedValue = dependency.transform(sourceValue, formValues);

          // Cập nhật giá trị cho field đích
          setValue(dependency.targetField, transformedValue);
          break;
        }

        case DependencyType.OPTIONS: {
          // Lấy giá trị của field nguồn
          const sourceValue = formValues[dependency.sourceField];

          // Lấy options dựa trên giá trị của field nguồn
          const newOptions = dependency.getOptions(sourceValue, formValues);

          // Tên của state để lưu options
          const optionsStateName =
            dependency.optionsStateName || `${dependency.targetField}Options`;

          // Cập nhật options
          optionsRef.current[optionsStateName] = newOptions;

          // Reset giá trị của field đích nếu cần
          if (dependency.resetOnChange !== false) {
            setValue(dependency.targetField, undefined);
          }
          break;
        }

        case DependencyType.DEFAULT: {
          // Nếu field nguồn thay đổi, reset field đích về giá trị mặc định
          if (dependency.resetOnChange !== false) {
            setValue(dependency.targetField, dependency.defaultValue);
          }
          break;
        }
      }

      // Trigger validation cho field đích
      trigger(dependency.targetField);
    });
  }, [watchedValues, dependencies, formContextResult]);

  // Kích hoạt dependencies khi mount nếu cần
  useEffect(() => {
    // Nếu không có formContext hoặc không cần trigger khi mount, không thực hiện gì cả
    if (!formContextResult || !triggerOnMount) return;

    const { getValues } = formContextResult;
    const formValues = getValues();

    dependencies.forEach(dependency => {
      // Xử lý dependency dựa trên loại
      switch (dependency.type) {
        case DependencyType.OPTIONS: {
          // Lấy giá trị của field nguồn
          const sourceValue = formValues[dependency.sourceField];

          // Lấy options dựa trên giá trị của field nguồn
          const newOptions = dependency.getOptions(sourceValue, formValues);

          // Tên của state để lưu options
          const optionsStateName =
            dependency.optionsStateName || `${dependency.targetField}Options`;

          // Cập nhật options
          optionsRef.current[optionsStateName] = newOptions;
          break;
        }
      }
    });
  }, [triggerOnMount, dependencies, formContextResult]);

  // Nếu không có formContext, trả về giá trị mặc định
  if (!formContextResult) {
    return { options: {} };
  }

  return {
    options: optionsRef.current,
  };
}

export default useFieldDependency;
