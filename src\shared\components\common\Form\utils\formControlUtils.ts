/**
 * Utility functions for form control components (Checkbox, Radio, etc.)
 */

// Common types
export type FormControlSize = 'sm' | 'md' | 'lg';
export type FormControlColor = 'primary' | 'success' | 'warning' | 'danger' | 'info';

// Size classes
export const CONTROL_SIZE_CLASSES = {
  sm: 'w-3.5 h-3.5 min-w-[0.875rem] min-h-[0.875rem]',
  md: 'w-4 h-4 min-w-[1rem] min-h-[1rem]',
  lg: 'w-5 h-5 min-w-[1.25rem] min-h-[1.25rem]',
};

// Label size classes
export const LABEL_SIZE_CLASSES = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
};

// Dot size classes for Radio
export const DOT_SIZE_CLASSES = {
  sm: 'w-1.5 h-1.5',
  md: 'w-2 h-2',
  lg: 'w-2.5 h-2.5',
};

// Color classes for different states
export interface ColorClasses {
  border: string;
  bg: string;
  fill: string;
  filledBg: string;
  filledText: string;
  outlinedBorder: string;
  ring: string;
  dot?: string; // For Radio
}

/**
 * Get color classes based on color prop and checked state
 * @param color The color prop
 * @param checked The checked state
 * @returns Object with color classes
 */
export function getColorClasses(color: FormControlColor, checked: boolean): ColorClasses {
  const colorMap: Record<FormControlColor, ColorClasses> = {
    primary: {
      border: checked ? 'border-primary' : '',
      bg: checked ? 'bg-primary' : '',
      fill: 'fill-primary',
      filledBg: 'bg-primary',
      filledText: 'text-primary-foreground',
      outlinedBorder: 'border-primary',
      ring: 'focus:ring-primary/30',
      dot: 'bg-primary',
    },
    success: {
      border: checked ? 'border-success' : '',
      bg: checked ? 'bg-success' : '',
      fill: 'fill-success',
      filledBg: 'bg-success',
      filledText: 'text-success-foreground',
      outlinedBorder: 'border-success',
      ring: 'focus:ring-success/30',
      dot: 'bg-success',
    },
    warning: {
      border: checked ? 'border-warning' : '',
      bg: checked ? 'bg-warning' : '',
      fill: 'fill-warning',
      filledBg: 'bg-warning',
      filledText: 'text-warning-foreground',
      outlinedBorder: 'border-warning',
      ring: 'focus:ring-warning/30',
      dot: 'bg-warning',
    },
    danger: {
      border: checked ? 'border-error' : '',
      bg: checked ? 'bg-error' : '',
      fill: 'fill-error',
      filledBg: 'bg-error',
      filledText: 'text-error-foreground',
      outlinedBorder: 'border-error',
      ring: 'focus:ring-error/30',
      dot: 'bg-error',
    },
    info: {
      border: checked ? 'border-info' : '',
      bg: checked ? 'bg-info' : '',
      fill: 'fill-info',
      filledBg: 'bg-info',
      filledText: 'text-info-foreground',
      outlinedBorder: 'border-info',
      ring: 'focus:ring-info/30',
      dot: 'bg-info',
    },
  };

  return colorMap[color] || colorMap.primary;
}

// Checkbox variant classes
export type CheckboxVariant = 'default' | 'rounded' | 'filled' | 'outlined';

/**
 * Get variant classes for Checkbox
 * @param variant The variant prop
 * @returns CSS classes for the variant
 */
export function getCheckboxVariantClasses(variant: CheckboxVariant): string {
  switch (variant) {
    case 'rounded':
      return 'rounded-md';
    case 'filled':
      return 'rounded-sm';
    case 'outlined':
      return 'rounded-sm border-2';
    default:
      return 'rounded-sm';
  }
}

// Radio variant classes
export type RadioVariant = 'default' | 'filled' | 'outlined';

/**
 * Get variant classes for Radio
 * @param variant The variant prop
 * @returns CSS classes for the variant
 */
export function getRadioVariantClasses(variant: RadioVariant): string {
  switch (variant) {
    case 'filled':
      return '';
    case 'outlined':
      return 'border-2';
    default:
      return '';
  }
}

/**
 * Generate ARIA attributes for form controls
 * @param checked The checked state
 * @param disabled The disabled state
 * @param indeterminate The indeterminate state (for Checkbox)
 * @returns Object with ARIA attributes
 */
export function getAriaAttributes(checked: boolean, disabled: boolean, indeterminate?: boolean) {
  const attributes: Record<string, string | boolean> = {
    'aria-disabled': disabled,
  };

  if (indeterminate !== undefined) {
    // For Checkbox
    attributes['aria-checked'] = indeterminate ? 'mixed' : checked;
  } else {
    // For Radio
    attributes['aria-checked'] = checked;
  }

  return attributes;
}
