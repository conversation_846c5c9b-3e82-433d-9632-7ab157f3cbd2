/**
 * Glassmorphism Pricing Card - Modern glass effect design
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon } from '@/shared/components/common';
import { ServicePackage, SubscriptionDuration } from '../../types';
import { calculateMonthlyPrice, calculateSavingPercentage } from '../../utils';

interface GlassmorphismCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

const GlassmorphismCard: React.FC<GlassmorphismCardProps> = ({
  package: pkg,
  duration,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const price = pkg.prices[duration];
  const monthlyPrice = calculateMonthlyPrice(price, duration);
  const savingPercentage = calculateSavingPercentage(
    pkg.prices[SubscriptionDuration.MONTHLY],
    price,
    duration
  );

  const isPopular = pkg.isPopular;

  return (
    <div className={`relative ${className}`}>
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 via-purple-400/20 to-pink-400/20 rounded-2xl blur-xl"></div>

      <div
        className={`relative backdrop-blur-lg bg-white/10 dark:bg-gray-900/10 border border-white/20 dark:border-gray-700/20 rounded-2xl p-8 transition-all duration-300 hover:bg-white/20 hover:border-white/30 hover:shadow-2xl hover:-translate-y-2 h-full flex flex-col ${
          isPopular ? 'ring-2 ring-blue-400/50' : ''
        }`}
      >
        {/* Popular Badge */}
        {isPopular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-semibold backdrop-blur-sm">
              ✨ Popular
            </div>
          </div>
        )}

        {/* Header */}
        <div className="text-center mb-6">
          <div className={`inline-flex items-center justify-center w-14 h-14 rounded-xl mb-4 ${
            isPopular
              ? 'bg-gradient-to-br from-blue-500/20 to-purple-600/20 backdrop-blur-sm'
              : 'bg-white/20 backdrop-blur-sm'
          }`}>
            <Icon
              name={pkg.icon || 'package'}
              size="lg"
              className={isPopular ? 'text-blue-400' : 'text-gray-700 dark:text-gray-300'}
            />
          </div>

          <Typography variant="h4" className="font-bold mb-2 text-gray-900 dark:text-white">
            {t(`subscription:packages.${pkg.name.toLowerCase()}`, { defaultValue: pkg.name })}
          </Typography>

          {pkg.description && (
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {t(`subscription:packages.description.${pkg.id}`, { defaultValue: pkg.description })}
            </Typography>
          )}
        </div>

        {/* Pricing */}
        <div className="text-center mb-6">
          <div className="flex items-baseline justify-center mb-2">
            <span className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {Math.round(monthlyPrice / 1000)}
            </span>
            <span className="text-lg text-gray-600 dark:text-gray-400 ml-2">R-Point</span>
            <span className="text-gray-500 ml-1">/mo</span>
          </div>

          {duration !== SubscriptionDuration.MONTHLY && savingPercentage > 0 && (
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-400/20 backdrop-blur-sm text-green-700 dark:text-green-300 text-sm font-medium">
              💰 Save {savingPercentage}%
            </div>
          )}
        </div>

        {/* Features */}
        <div className="space-y-3 mb-6 flex-grow">
          {pkg.features.slice(0, 6).map((feature, index) => (
            <div key={index} className="flex items-center">
              <div className="flex-shrink-0">
                {typeof feature.value === 'boolean' ? (
                  feature.value ? (
                    <div className="w-4 h-4 rounded-full bg-green-400/80 backdrop-blur-sm flex items-center justify-center">
                      <Icon name="check" size="xs" className="text-white" />
                    </div>
                  ) : (
                    <div className="w-4 h-4 rounded-full bg-gray-400/50 backdrop-blur-sm flex items-center justify-center">
                      <Icon name="x" size="xs" className="text-gray-500" />
                    </div>
                  )
                ) : (
                  <div className="w-4 h-4 rounded-full bg-blue-400/80 backdrop-blur-sm flex items-center justify-center">
                    <Icon name="check" size="xs" className="text-white" />
                  </div>
                )}
              </div>
              <div className="ml-3">
                <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                  {t(`subscription:packages.features.${feature.name}`, {
                    defaultValue: feature.name,
                  })}
                  {typeof feature.value !== 'boolean' && (
                    <span className="font-semibold text-gray-900 dark:text-white ml-1">
                      {feature.value}
                    </span>
                  )}
                </Typography>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="mt-auto">
          <Button
            variant={isPopular ? 'primary' : 'outline'}
            fullWidth
            size="lg"
            onClick={() => onSelect(pkg)}
            className={`font-semibold transition-all duration-300 backdrop-blur-sm ${
              isPopular
                ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 border-0 text-white shadow-lg hover:shadow-xl'
                : 'bg-white/20 hover:bg-white/30 border-white/30 hover:border-white/50 text-gray-900 dark:text-white'
            }`}
          >
            {isPopular ? '🎯 Choose Plan' : 'Select'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default GlassmorphismCard;
