import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { EmailService } from '../../services/email.service';
import { EmailCampaignOverviewBusinessService } from '../../services/email-campaign-overview-business.service';
import { NotificationUtil } from '@/shared/utils/notification';
import type {
  EmailCampaignQueryDto,
  CreateEmailCampaignDto,
} from '../../types/email.types';

/**
 * Query keys cho email campaigns
 */
export const EMAIL_CAMPAIGN_QUERY_KEYS = {
  ALL: ['email-campaigns'] as const,
  LIST: (params: EmailCampaignQueryDto) => [...EMAIL_CAMPAIGN_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...EMAIL_CAMPAIGN_QUERY_KEYS.ALL, 'detail', id] as const,
  EMAILS: (id: string, params?: { page?: number; limit?: number; status?: string }) =>
    [...EMAIL_CAMPAIGN_QUERY_KEYS.ALL, 'emails', id, params] as const,
  ANALYTICS: (id: string) => [...EMAIL_CAMPAIGN_QUERY_KEYS.ALL, 'analytics', id] as const,
  RECENT: ['email-campaigns', 'recent'] as const,
  OVERVIEW: ['email-campaigns', 'overview'] as const,
};

/**
 * Hook để lấy danh sách email campaigns
 */
export function useEmailCampaigns(query?: EmailCampaignQueryDto) {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.LIST(query || {}),
    queryFn: () => EmailService.getCampaigns(query),
    select: (response) => response.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook để lấy chi tiết email campaign
 */
export function useEmailCampaign(id: string) {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
    queryFn: () => EmailService.getCampaign(id),
    select: (response) => response.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook để tạo email campaign mới
 */
export function useCreateEmailCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmailCampaignDto) => {
      // Nếu có templateId, sử dụng createCampaignWithTemplate
      if (data.templateId) {
        // Map dữ liệu từ frontend format sang API format
        const segmentId = Array.isArray(data.segmentIds) && data.segmentIds.length > 0
          ? data.segmentIds[0]
          : '';

        const apiData = {
          title: data.name, // API dùng 'title' thay vì 'name'
          description: data.description || '',
          templateEmailId: data.templateId,
          segmentId: segmentId || '',
          scheduledAt: data.scheduledAt ? Math.floor(new Date(data.scheduledAt).getTime() / 1000) : undefined, // Convert to Unix timestamp
          serverId: data.emailServerId,
          templateVariables: data.templateVariables,
        };

        return EmailService.createCampaignWithTemplate(apiData);
      } else {
        // Sử dụng createCampaign thông thường
        return EmailService.createCampaign(data);
      }
    },
    onSuccess: (response) => {
      const campaign = response.result;

      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ALL,
      });

      NotificationUtil.success({
        message: 'Tạo chiến dịch email thành công!',
        title: `Chiến dịch "${campaign.name || campaign.title}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo chiến dịch email thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để cập nhật email campaign
 */
export function useUpdateEmailCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateEmailCampaignDto> }) =>
      EmailService.updateCampaign(id, data),
    onSuccess: (response, { id }) => {
      const campaign = response.result;

      // Update cache
      queryClient.setQueryData(EMAIL_CAMPAIGN_QUERY_KEYS.DETAIL(id), response);

      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ALL,
      });

      NotificationUtil.success({
        message: 'Cập nhật chiến dịch email thành công!',
        title: `Chiến dịch "${campaign.name}" đã được cập nhật`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật chiến dịch email thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để xóa email campaign
 */
export function useDeleteEmailCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmailService.deleteCampaign(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });

      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ALL,
      });

      NotificationUtil.success({
        message: 'Xóa chiến dịch email thành công!',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa chiến dịch email thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để gửi email campaign
 */
export function useSendEmailCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmailService.sendCampaign(id),
    onSuccess: (response, id) => {
      const { totalRecipients } = response.result;

      // Invalidate campaign detail and list
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.DETAIL(id),
      });
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ALL,
      });

      NotificationUtil.success({
        message: 'Gửi chiến dịch email thành công!',
        title: `Đã gửi đến ${totalRecipients} người nhận`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Gửi chiến dịch email thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để lấy danh sách emails của campaign
 */
export function useCampaignEmails(
  campaignId: string,
  query?: { page?: number; limit?: number; status?: string }
) {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.EMAILS(campaignId, query),
    queryFn: () => EmailService.getCampaignEmails(campaignId, query),
    select: (response) => response.result,
    enabled: !!campaignId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook để lấy analytics của campaign
 */
export function useCampaignAnalytics(campaignId: string) {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ANALYTICS(campaignId),
    queryFn: () => EmailService.getCampaignAnalytics(campaignId),
    select: (response) => response.result,
    enabled: !!campaignId,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook để pause/resume campaign
 */
export function usePauseResumeEmailCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, action }: { id: string; action: 'PAUSE' | 'RESUME' }) =>
      EmailService.pauseResumeCampaign(id, action),
    onSuccess: (response, { id, action }) => {
      const campaign = response.result;

      // Update cache
      queryClient.setQueryData(EMAIL_CAMPAIGN_QUERY_KEYS.DETAIL(id), response);

      // Invalidate campaigns list
      queryClient.invalidateQueries({
        queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.ALL,
      });

      const actionText = action === 'PAUSE' ? 'tạm dừng' : 'tiếp tục';
      NotificationUtil.success({
        message: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} chiến dịch thành công!`,
        title: `Chiến dịch "${campaign.name}" đã được ${actionText}`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Thao tác thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để lấy danh sách chiến dịch email gần đây
 */
export function useRecentCampaigns() {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.RECENT,
    queryFn: () => EmailService.getRecentCampaigns(),
    select: (response) => response.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook để lấy thống kê tổng quan email campaigns
 */
export function useEmailCampaignOverview() {
  return useQuery({
    queryKey: EMAIL_CAMPAIGN_QUERY_KEYS.OVERVIEW,
    queryFn: () => EmailCampaignOverviewBusinessService.getEmailCampaignOverview(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });
}

/**
 * Hook tổng hợp cho email campaign management
 */
export function useEmailCampaignManagement() {
  const createCampaign = useCreateEmailCampaign();
  const updateCampaign = useUpdateEmailCampaign();
  const deleteCampaign = useDeleteEmailCampaign();
  const sendCampaign = useSendEmailCampaign();
  const pauseResumeCampaign = usePauseResumeEmailCampaign();

  return {
    createCampaign,
    updateCampaign,
    deleteCampaign,
    sendCampaign,
    pauseResumeCampaign,
    isLoading: createCampaign.isPending ||
               updateCampaign.isPending ||
               deleteCampaign.isPending ||
               sendCampaign.isPending ||
               pauseResumeCampaign.isPending,
  };
}
