/**
 * Hook tích hợp SSE với TaskQueue
 */
import { useCallback, useEffect, useRef } from 'react';
import { useSSE } from './useSSE';
import { useTaskQueue } from './useTaskQueue';
import {
  SSEEvent,
  UseSSEOptions,
  SSETask
} from '@/shared/types/sse.types';
import {
  CreateCustomTaskParams
} from '@/shared/types/task-queue.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * Options cho useSSEWithTaskQueue
 */
export interface UseSSEWithTaskQueueOptions extends UseSSEOptions {
  /**
   * Có tự động tạo task cho mỗi SSE event không
   */
  autoCreateTasks?: boolean;

  /**
   * Filter để quyết định event nào sẽ tạo task
   */
  taskFilter?: (event: SSEEvent) => boolean;

  /**
   * Transform SSE event thành task params
   */
  eventToTask?: (event: SSEEvent) => Omit<CreateCustomTaskParams, 'execute'>;

  /**
   * Handler mặc định cho SSE events
   */
  defaultEventHandler?: (event: SSEEvent) => Promise<void> | void;

  /**
   * Có queue các SSE events không
   */
  queueEvents?: boolean;

  /**
   * Số lượng events tối đa trong queue
   */
  maxQueueSize?: number;

  /**
   * Có log debug không
   */
  debug?: boolean;
}

/**
 * Return type của useSSEWithTaskQueue
 */
export interface UseSSEWithTaskQueueReturn {
  // SSE methods
  connectionInfo: ReturnType<typeof useSSE>['connectionInfo'];
  events: SSEEvent[];
  lastEvent: SSEEvent | null;
  connect: () => void;
  disconnect: () => void;
  subscribe: (eventType: string, handler: (event: SSEEvent) => void) => string;
  unsubscribe: (subscriptionId: string) => void;
  clearEvents: () => void;

  // TaskQueue methods
  tasks: ReturnType<typeof useTaskQueue>['tasks'];
  isRunning: boolean;
  runningCount: number;
  addSSETask: (event: SSEEvent, handler: (event: SSEEvent) => Promise<void> | void) => string;
  removeTask: (id: string) => void;
  cancelTask: (id: string) => void;
  retryTask: (id: string) => void;
  clearCompletedTasks: () => void;
  startQueue: () => void;
  pauseQueue: () => void;

  // Combined methods
  processEventAsTask: (event: SSEEvent) => string | null;
  getSSETasks: () => SSETask[];
  getTaskByEventId: (eventId: string) => SSETask | null;
}

/**
 * Hook tích hợp SSE với TaskQueue
 */
export function useSSEWithTaskQueue(
  url: string,
  options: UseSSEWithTaskQueueOptions = {}
): UseSSEWithTaskQueueReturn {
  const {
    autoCreateTasks = false,
    taskFilter,
    eventToTask,
    defaultEventHandler,
    queueEvents = true,
    maxQueueSize = 100,
    debug = false,
    ...sseOptions
  } = options;

  // Refs
  const sseTasksRef = useRef<Map<string, SSETask>>(new Map());
  const eventToTaskMapRef = useRef<Map<string, string>>(new Map());

  // TaskQueue hook
  const taskQueue = useTaskQueue({
    concurrency: 3,
    defaultMaxRetries: 3,
    autoRemoveCompletedAfter: 60000,
    autoStart: true,
  });

  // SSE hook với custom message handler
  const sse = useSSE(url, {
    ...sseOptions,
    onMessage: (event) => {
      if (debug) {
        console.log('SSE Event received:', event);
      }

      // Gọi original handler nếu có
      sseOptions.onMessage?.(event);

      // Xử lý event với TaskQueue nếu cần
      if (autoCreateTasks) {
        const sseEvent: SSEEvent = {
          id: event.lastEventId || uuidv4(),
          type: event.type || 'message',
          data: JSON.parse(event.data),
          timestamp: Date.now(),
        };

        processEventAsTask(sseEvent);
      }
    },
  });

  /**
   * Thêm SSE task vào queue
   */
  const addSSETask = useCallback((
    event: SSEEvent,
    handler: (event: SSEEvent) => Promise<void> | void
  ): string => {
    const taskId = uuidv4();

    // Tạo task params
    const taskParams = eventToTask ? eventToTask(event) : {
      title: `SSE Event: ${event.type}`,
      description: `Processing SSE event ${event.id || 'unknown'}`,
    };

    // Thêm task vào queue
    taskQueue.addCustomTask({
      ...taskParams,
      execute: async () => {
        try {
          await handler(event);

          // Cập nhật SSE task status
          const sseTask = sseTasksRef.current.get(taskId);
          if (sseTask) {
            sseTask.status = 'completed';
            sseTask.completedAt = new Date();
          }
        } catch (error) {
          // Cập nhật SSE task status
          const sseTask = sseTasksRef.current.get(taskId);
          if (sseTask) {
            sseTask.status = 'failed';
            sseTask.error = error instanceof Error ? error : new Error(String(error));
            sseTask.completedAt = new Date();
          }
          throw error;
        }
      },
    });

    // Tạo SSE task
    const sseTask: SSETask = {
      id: taskId,
      type: 'sse_event',
      event,
      handler,
      status: 'pending',
      createdAt: new Date(),
    };

    // Lưu trữ
    sseTasksRef.current.set(taskId, sseTask);
    if (event.id) {
      eventToTaskMapRef.current.set(event.id, taskId);
    }

    if (debug) {
      console.log(`Created SSE task ${taskId} for event ${event.type}`);
    }

    return taskId;
  }, [taskQueue, eventToTask, debug]);

  /**
   * Xử lý event thành task
   */
  const processEventAsTask = useCallback((event: SSEEvent): string | null => {
    // Kiểm tra filter
    if (taskFilter && !taskFilter(event)) {
      return null;
    }

    // Sử dụng default handler hoặc empty handler
    const handler = defaultEventHandler || (() => {
      if (debug) {
        console.log('Processing SSE event:', event);
      }
    });

    return addSSETask(event, handler);
  }, [taskFilter, defaultEventHandler, addSSETask, debug]);

  /**
   * Lấy danh sách SSE tasks
   */
  const getSSETasks = useCallback((): SSETask[] => {
    return Array.from(sseTasksRef.current.values());
  }, []);

  /**
   * Lấy task theo event ID
   */
  const getTaskByEventId = useCallback((eventId: string): SSETask | null => {
    const taskId = eventToTaskMapRef.current.get(eventId);
    if (taskId) {
      return sseTasksRef.current.get(taskId) || null;
    }
    return null;
  }, []);

  // Cleanup effect
  useEffect(() => {
    const sseTasksMap = sseTasksRef.current;
    const eventToTaskMap = eventToTaskMapRef.current;

    return () => {
      // Clear refs
      sseTasksMap.clear();
      eventToTaskMap.clear();
    };
  }, []);

  // Giới hạn số lượng events nếu cần
  useEffect(() => {
    if (queueEvents && maxQueueSize > 0 && sse.events.length > maxQueueSize) {
      sse.clearEvents();
    }
  }, [sse.events.length, queueEvents, maxQueueSize, sse]);

  return {
    // SSE methods
    connectionInfo: sse.connectionInfo,
    events: sse.events,
    lastEvent: sse.lastEvent,
    connect: sse.connect,
    disconnect: sse.disconnect,
    subscribe: sse.subscribe,
    unsubscribe: sse.unsubscribe,
    clearEvents: sse.clearEvents,

    // TaskQueue methods
    tasks: taskQueue.tasks,
    isRunning: taskQueue.isRunning,
    runningCount: taskQueue.runningCount,
    addSSETask,
    removeTask: taskQueue.removeTask,
    cancelTask: taskQueue.cancelTask,
    retryTask: taskQueue.retryTask,
    clearCompletedTasks: taskQueue.clearCompletedTasks,
    startQueue: taskQueue.startQueue,
    pauseQueue: taskQueue.pauseQueue,

    // Combined methods
    processEventAsTask,
    getSSETasks,
    getTaskByEventId,
  };
}

export default useSSEWithTaskQueue;
