/**
 * Cloud Storage Integration Types
 */

import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Cloud Storage Provider Types
 */
export type CloudStorageProviderType = 'google-drive' | 'onedrive' | 'dropbox' | 'box';

/**
 * Cloud Storage Provider Configuration
 */
export interface CloudStorageProviderConfiguration {
  id: number;
  userId: number;
  providerType: CloudStorageProviderType;
  providerName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  accessToken?: string;
  rootFolderId?: string;
  isActive: boolean;
  autoSync: boolean;
  syncFolders?: string[];
  lastSyncAt?: string;
  storageQuota?: CloudStorageQuota;
  createdAt: string;
  updatedAt: string;
}

/**
 * Cloud Storage Quota Information
 */
export interface CloudStorageQuota {
  totalBytes: number;
  usedBytes: number;
  availableBytes: number;
  lastUpdated: string;
}

/**
 * Create Cloud Storage Provider DTO
 */
export interface CreateCloudStorageProviderDto {
  providerType: CloudStorageProviderType;
  providerName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  rootFolderId?: string;
  isActive: boolean;
  autoSync: boolean;
  syncFolders?: string[];
}

/**
 * Update Cloud Storage Provider DTO
 */
export interface UpdateCloudStorageProviderDto {
  providerName?: string;
  clientId?: string;
  clientSecret?: string;
  refreshToken?: string;
  rootFolderId?: string;
  isActive?: boolean;
  autoSync?: boolean;
  syncFolders?: string[];
}

/**
 * Test Cloud Storage Provider DTO
 */
export interface TestCloudStorageProviderDto {
  testFolderName?: string;
  testFileName?: string;
}

/**
 * Cloud Storage Provider Config for testing
 */
export interface CloudStorageProviderConfigDto {
  providerType: CloudStorageProviderType;
  providerName: string;
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  rootFolderId?: string;
}

/**
 * Test Cloud Storage Provider with config DTO
 */
export interface TestCloudStorageProviderWithConfigDto {
  storageConfig: CloudStorageProviderConfigDto;
  testInfo: TestCloudStorageProviderDto;
}

/**
 * Query parameters for Cloud Storage Providers
 */
export interface CloudStorageProviderQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: SortDirection;
  providerType?: CloudStorageProviderType;
  isActive?: boolean;
}

/**
 * Cloud Storage Provider test result
 */
export interface CloudStorageProviderTestResult {
  success: boolean;
  message: string;
  details?: {
    error?: string;
    statusCode?: number;
    responseTime?: number;
    storageInfo?: {
      accountName: string;
      email: string;
      totalSpace: string;
      usedSpace: string;
      availableSpace: string;
    };
  };
}

/**
 * Form data for Cloud Storage Provider
 */
export interface CloudStorageProviderFormData extends Omit<CreateCloudStorageProviderDto, 'syncFolders'> {
  syncFolders?: string; // JSON string for form handling
}

/**
 * Cloud Storage File
 */
export interface CloudStorageFile {
  id: string;
  name: string;
  type: 'file' | 'folder';
  size?: number;
  mimeType?: string;
  parentId?: string;
  webViewLink?: string;
  downloadLink?: string;
  thumbnailLink?: string;
  createdAt: string;
  modifiedAt: string;
  owner?: {
    name: string;
    email: string;
  };
  permissions?: CloudStoragePermission[];
}

/**
 * Cloud Storage Permission
 */
export interface CloudStoragePermission {
  id: string;
  type: 'user' | 'group' | 'domain' | 'anyone';
  role: 'owner' | 'writer' | 'reader' | 'commenter';
  emailAddress?: string;
  displayName?: string;
}

/**
 * Cloud Storage Folder
 */
export interface CloudStorageFolder {
  id: string;
  name: string;
  parentId?: string;
  webViewLink?: string;
  createdAt: string;
  modifiedAt: string;
  fileCount: number;
  folderCount: number;
}

/**
 * File Upload Request
 */
export interface FileUploadRequest {
  fileName: string;
  fileContent: File | Blob;
  parentFolderId?: string;
  description?: string;
  mimeType?: string;
}

/**
 * File Download Request
 */
export interface FileDownloadRequest {
  fileId: string;
  fileName?: string;
}

/**
 * Folder Create Request
 */
export interface FolderCreateRequest {
  folderName: string;
  parentFolderId?: string;
  description?: string;
}

/**
 * Sync Status
 */
export interface CloudStorageSyncStatus {
  configId: number;
  lastSyncAt?: string;
  syncInProgress: boolean;
  filesCount: number;
  foldersCount: number;
  totalSize: number;
  errorMessage?: string;
  syncedFolders: string[];
}

/**
 * File Search Request
 */
export interface FileSearchRequest {
  query: string;
  fileType?: 'file' | 'folder';
  mimeType?: string;
  parentFolderId?: string;
  maxResults?: number;
}

/**
 * Batch Operation Request
 */
export interface BatchOperationRequest {
  operation: 'copy' | 'move' | 'delete';
  fileIds: string[];
  targetFolderId?: string;
}

/**
 * Share Link Request
 */
export interface ShareLinkRequest {
  fileId: string;
  permission: 'reader' | 'writer' | 'commenter';
  expirationTime?: string;
  password?: string;
}

/**
 * Share Link Response
 */
export interface ShareLinkResponse {
  shareLink: string;
  expirationTime?: string;
  permission: string;
}
