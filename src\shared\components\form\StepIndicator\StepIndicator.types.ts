import { ReactNode } from 'react';
import { StepStatus } from '../FormWizard/FormWizard.types';

/**
 * Thông tin về một bước trong step indicator
 */
export interface Step {
  /**
   * ID của bước
   */
  id: string;

  /**
   * Tiêu đề của bước
   */
  title: string;

  /**
   * Mô tả của bước
   */
  description?: string;

  /**
   * Icon của bước
   */
  icon?: ReactNode;
}

/**
 * Props cho StepIndicator component
 */
export interface StepIndicatorProps {
  /**
   * Mảng các bước
   */
  steps: Step[];

  /**
   * ID của bước hiện tại
   */
  currentStepId: string;

  /**
   * Trạng thái của các bước
   */
  stepStatus: Record<string, StepStatus>;

  /**
   * Hướng của indicator
   */
  orientation?: 'horizontal' | 'vertical';

  /**
   * Callback khi click vào bước
   */
  onStepClick?: (stepId: string) => void;

  /**
   * <PERSON><PERSON> cho phép click vào bước không
   */
  clickable?: boolean;

  /**
   * <PERSON><PERSON> hiển thị tiêu đề không
   */
  showTitle?: boolean;

  /**
   * Có hiển thị mô tả không
   */
  showDescription?: boolean;

  /**
   * Có hiển thị icon không
   */
  showIcon?: boolean;

  /**
   * Có hiển thị số thứ tự không
   */
  showNumber?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}
