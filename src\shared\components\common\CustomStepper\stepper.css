/* CSS for CustomStepper component */

/* Arrow shape for arrow variant */
.clip-path-arrow {
  clip-path: polygon(0% 0%, 75% 0%, 100% 50%, 75% 100%, 0% 100%);
}

/* Card shape for card variant */
.step-card {
  border-radius: 0.375rem;
}

.step-item:not(:last-child) .step-card {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.step-item:not(:first-child) .step-card {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px;
}

/* Animation for active step */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
