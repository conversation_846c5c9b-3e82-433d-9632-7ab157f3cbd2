import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Checkbox,
  Typography,
  Icon,
} from '@/shared/components/common';

export interface SearchableDropdownItem {
  id: string | number;
  label: string;
  description?: string;
  badge?: string;
  required?: boolean;
  data?: Record<string, unknown>;
}

export interface SearchableDropdownProps<T extends SearchableDropdownItem> {
  items: T[];
  selectedIds: (string | number)[];
  onItemSelect: (item: T) => void;
  onSearch?: (searchTerm: string, page: number) => Promise<T[]>;
  placeholder?: string;
  title?: string;
  loading?: boolean;
  multiSelect?: boolean;
  showSearch?: boolean;
  maxHeight?: string;
  className?: string;
}

/**
 * Component dropdown có thể tìm kiếm và chọn nhiều items
 * Hỗ trợ infinite scroll và lazy loading
 */
function SearchableDropdown<T extends SearchableDropdownItem>({
  items: initialItems = [],
  selectedIds = [],
  onItemSelect,
  onSearch,
  placeholder = 'Tìm kiếm...',
  title,
  loading = false,
  multiSelect = true,
  showSearch = true,
  maxHeight = '240px',
  className = '',
}: SearchableDropdownProps<T>) {
  const { t } = useTranslation(['common']);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [items, setItems] = useState<T[]>(initialItems);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Use refs to avoid stale closure issues
  const currentPageRef = useRef(currentPage);
  const isLoadingRef = useRef(isLoading);

  // Update refs when state changes
  useEffect(() => {
    currentPageRef.current = currentPage;
  }, [currentPage]);

  useEffect(() => {
    isLoadingRef.current = isLoading;
  }, [isLoading]);

  // Load items function
  const loadItems = useCallback(async (reset = false) => {
    if (!onSearch) return;

    // Prevent multiple concurrent requests
    if (isLoadingRef.current) return;

    try {
      setIsLoading(true);

      const page = reset ? 1 : currentPageRef.current;
      const newItems = await onSearch(searchTerm, page);

      if (reset) {
        setItems(newItems);
        setCurrentPage(2);
      } else {
        setItems(prev => [...prev, ...newItems]);
        setCurrentPage(prev => prev + 1);
      }

      setHasMore(newItems.length > 0);
    } catch (error) {
      console.error('Error loading items:', error);
      setHasMore(false);
    } finally {
      setIsLoading(false);
    }
  }, [onSearch, searchTerm]); // Remove currentPage and isLoading from deps

  // Load items when search term changes (with debounce)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (onSearch) {
        loadItems(true);
      } else {
        // Filter local items
        const filtered = initialItems.filter(item =>
          item.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.badge?.toLowerCase().includes(searchTerm.toLowerCase())
        );
        setItems(filtered);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm, initialItems, onSearch, loadItems]);

  // Handle scroll for infinite loading
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !isLoadingRef.current &&
      onSearch
    ) {
      loadItems(false);
    }
  }, [hasMore, onSearch, loadItems]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle item selection
  const handleItemSelect = (item: T) => {
    console.log('Item selected:', item);
    onItemSelect(item);

    if (!multiSelect) {
      setIsOpen(false);
    }
  };

  // Handle input focus
  const handleInputFocus = () => {
    setIsOpen(true);
    // Only load if we have onSearch, no items, and not currently loading
    if (onSearch && items.length === 0 && !isLoadingRef.current && searchTerm === '') {
      loadItems(true);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setCurrentPage(1);
    setHasMore(true);
  };

  return (
    <div ref={containerRef} className={`relative w-full ${className}`}>
      {/* Input */}
      <div
        className={`
          flex items-center
          rounded-md border border-gray-300 dark:border-gray-600
          bg-white dark:bg-gray-800
          py-2 px-3
          cursor-pointer
          transition-all duration-200
          ${isOpen ? 'ring-2 ring-primary/30 border-primary' : 'hover:border-gray-400 dark:hover:border-gray-500'}
        `}
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="flex-shrink-0 mr-2 text-gray-500">
          <Icon name="search" size="sm" />
        </div>

        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleSearchChange}
          onFocus={handleInputFocus}
          onClick={(e) => e.stopPropagation()}
          placeholder={placeholder}
          className="w-full bg-transparent border-none focus:outline-none focus:ring-0 text-foreground placeholder-gray-500"
          disabled={!showSearch}
        />

        <div className="flex-shrink-0 ml-2 text-gray-500">
          <Icon name={isOpen ? "chevron-up" : "chevron-down"} size="sm" />
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div
          ref={dropdownRef}
          className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-600 overflow-hidden"
        >
          {/* Header */}
          {title && (
            <div className="bg-gradient-to-r from-red-500 to-orange-500 p-3 text-white">
              <Typography variant="subtitle2" className="font-medium">
                {title}
              </Typography>
            </div>
          )}

          {/* Content */}
          <div
            ref={scrollRef}
            className="overflow-auto p-2"
            style={{ maxHeight }}
            onScroll={handleScroll}
          >
            {(loading || isLoading) && items.length === 0 ? (
              <div className="flex items-center justify-center p-4 text-gray-500">
                <div className="animate-spin mr-2">
                  <Icon name="loader" size="sm" />
                </div>
                <span>{t('common:loading', 'Đang tải...')}</span>
              </div>
            ) : items.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                {t('common:noResults', 'Không có kết quả')}
              </div>
            ) : (
              <div className="space-y-1">
                {items.map((item) => {
                  const isSelected = selectedIds.includes(item.id);

                  return (
                    <div
                      key={item.id}
                      className={`
                        flex items-center p-2 rounded-lg cursor-pointer
                        hover:bg-gray-50 dark:hover:bg-gray-700
                        transition-colors duration-150
                        ${isSelected ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                      `}
                      onClick={() => handleItemSelect(item)}
                    >
                      {multiSelect && (
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleItemSelect(item)}
                          variant="filled"
                          className="mr-3"
                        />
                      )}

                      <div className="flex-grow">
                        <div className="flex items-center justify-between">
                          <Typography variant="body2" className="font-medium">
                            {item.label}
                          </Typography>
                          {item.required && (
                            <span className="text-red-500 text-xs ml-2">*</span>
                          )}
                        </div>

                        {(item.description || item.badge) && (
                          <div className="flex items-center space-x-2 mt-1">
                            {item.badge && (
                              <span className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
                                {item.badge}
                              </span>
                            )}
                            {item.description && (
                              <span className="text-xs text-gray-500">
                                {item.description}
                              </span>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}

                {/* Loading more indicator */}
                {isLoading && items.length > 0 && (
                  <div className="flex items-center justify-center p-2 text-gray-500">
                    <div className="animate-spin mr-2">
                      <Icon name="loader" size="xs" />
                    </div>
                    <span className="text-xs">{t('common:loadingMore', 'Đang tải thêm...')}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchableDropdown;
