import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';

export interface UseSingleDeleteOptions<T> {
  /**
   * Hàm xóa một item
   */
  deleteMutation: (id: string) => Promise<void>;
  
  /**
   * Namespace cho i18n messages
   */
  i18nNamespace?: string;
  
  /**
   * Tên entity để hiển thị trong thông báo (ví dụ: 'URL', 'File', 'User')
   */
  entityName?: string;
  
  /**
   * Custom messages cho thông báo
   */
  messages?: {
    confirmMessage?: string;
    successMessage?: string;
    errorMessage?: string;
  };
  
  /**
   * Callback sau khi xóa thành công
   */
  onSuccess?: (item: T) => void;
  
  /**
   * Callback khi có lỗi
   */
  onError?: (error: unknown, item: T) => void;
}

export interface UseSingleDeleteResult<T> {
  /**
   * Item đang được xóa
   */
  itemToDelete: T | null;
  
  /**
   * Trạng thái hiển thị modal xác nhận
   */
  showDeleteConfirm: boolean;
  
  /**
   * Hiển thị modal xác nhận xóa
   */
  handleShowDeleteConfirm: (item: T) => void;
  
  /**
   * Hủy xóa
   */
  handleCancelDelete: () => void;
  
  /**
   * Xác nhận xóa
   */
  handleConfirmDelete: () => Promise<void>;
  
  /**
   * Message để hiển thị trong modal
   */
  getConfirmMessage: () => string;
}

/**
 * Hook tái sử dụng cho logic single delete
 */
export function useSingleDelete<T extends { id: string; [key: string]: unknown }>({
  deleteMutation,
  i18nNamespace = 'common',
  entityName = 'item',
  messages,
  onSuccess,
  onError,
}: UseSingleDeleteOptions<T>): UseSingleDeleteResult<T> {
  const { t } = useTranslation([i18nNamespace, 'common']);
  
  const [itemToDelete, setItemToDelete] = useState<T | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Hiển thị modal xác nhận xóa
  const handleShowDeleteConfirm = useCallback((item: T) => {
    setItemToDelete(item);
    setShowDeleteConfirm(true);
  }, []);

  // Hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setItemToDelete(null);
  }, []);

  // Xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!itemToDelete) return;

    try {
      await deleteMutation(itemToDelete.id);
      
      setShowDeleteConfirm(false);
      setItemToDelete(null);

      NotificationUtil.success({
        message: messages?.successMessage || 
          t(`${i18nNamespace}:deleteSuccess`, `Xóa ${entityName} thành công`),
      });

      onSuccess?.(itemToDelete);
    } catch (error) {
      console.error('Error deleting item:', error);
      
      NotificationUtil.error({
        message: messages?.errorMessage || 
          t(`${i18nNamespace}:deleteError`, `Lỗi khi xóa ${entityName}`),
      });

      onError?.(error, itemToDelete);
    }
  }, [itemToDelete, deleteMutation, messages, t, i18nNamespace, entityName, onSuccess, onError]);

  // Lấy message để hiển thị trong modal
  const getConfirmMessage = useCallback(() => {
    return messages?.confirmMessage || 
      t(`${i18nNamespace}:confirmDeleteMessage`, `Bạn có chắc chắn muốn xóa ${entityName} này?`);
  }, [messages?.confirmMessage, t, i18nNamespace, entityName]);

  return {
    itemToDelete,
    showDeleteConfirm,
    handleShowDeleteConfirm,
    handleCancelDelete,
    handleConfirmDelete,
    getConfirmMessage,
  };
}
