/**
 * Modern Pricing Card - Inspired by Stripe, Vercel, and modern SaaS platforms
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon } from '@/shared/components/common';
import { ServicePackage, SubscriptionDuration } from '../../types';
import { calculateMonthlyPrice, calculateSavingPercentage } from '../../utils';

interface ModernPricingCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

const ModernPricingCard: React.FC<ModernPricingCardProps> = ({
  package: pkg,
  duration,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const price = pkg.prices[duration];
  const monthlyPrice = calculateMonthlyPrice(price, duration);
  const savingPercentage = calculateSavingPercentage(
    pkg.prices[SubscriptionDuration.MONTHLY],
    price,
    duration
  );

  const isPopular = pkg.isPopular;
  const isRecommended = pkg.isRecommended;

  return (
    <div className={`relative ${className}`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
            🔥 {t('subscription:packages.mostPopular', 'Most Popular')}
          </div>
        </div>
      )}

      <Card
        className={`relative overflow-hidden transition-all duration-300 hover:shadow-2xl hover:-translate-y-1 h-full flex flex-col ${
          isPopular
            ? 'border-2 border-blue-500 shadow-xl'
            : 'border border-gray-200 dark:border-gray-700 hover:border-blue-300'
        } ${isRecommended ? 'ring-2 ring-green-500 ring-opacity-50' : ''}`}
      >
        {/* Background Gradient for Popular */}
        {isPopular && (
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-blue-600 to-purple-600"></div>
        )}

        <div className="p-8 flex flex-col h-full">
          {/* Header */}
          <div className="text-center mb-8">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 ${
              isPopular
                ? 'bg-gradient-to-br from-blue-500 to-purple-600'
                : 'bg-gray-100 dark:bg-gray-800'
            }`}>
              <Icon
                name={pkg.icon || 'package'}
                size="lg"
                className={isPopular ? 'text-white' : 'text-gray-600 dark:text-gray-300'}
              />
            </div>

            <Typography variant="h4" className="font-bold mb-2">
              {t(`subscription:packages.${pkg.name.toLowerCase()}`, { defaultValue: pkg.name })}
            </Typography>

            {pkg.description && (
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                {t(`subscription:packages.description.${pkg.id}`, { defaultValue: pkg.description })}
              </Typography>
            )}
          </div>

          {/* Pricing */}
          <div className="text-center mb-8">
            <div className="flex items-baseline justify-center mb-2">
              <span className="text-5xl font-bold text-gray-900 dark:text-white">
                {Math.round(monthlyPrice / 1000)}
              </span>
              <span className="text-xl text-gray-500 ml-2">R-Point</span>
              <span className="text-gray-500 ml-1">/month</span>
            </div>

            {duration !== SubscriptionDuration.MONTHLY && savingPercentage > 0 && (
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-sm font-medium">
                <Icon name="trending-down" size="sm" className="mr-1" />
                Save {savingPercentage}%
              </div>
            )}
          </div>

          {/* Features */}
          <div className="space-y-4 mb-8 flex-grow">
            {pkg.features.map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 mt-1">
                  {typeof feature.value === 'boolean' ? (
                    feature.value ? (
                      <div className="w-5 h-5 rounded-full bg-green-500 flex items-center justify-center">
                        <Icon name="check" size="xs" className="text-white" />
                      </div>
                    ) : (
                      <div className="w-5 h-5 rounded-full bg-gray-300 flex items-center justify-center">
                        <Icon name="x" size="xs" className="text-gray-500" />
                      </div>
                    )
                  ) : (
                    <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
                      <Icon name="check" size="xs" className="text-white" />
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                    {t(`subscription:packages.features.${feature.name}`, {
                      defaultValue: feature.name,
                    })}
                    {typeof feature.value !== 'boolean' && (
                      <span className="font-semibold text-gray-900 dark:text-white ml-1">
                        {feature.value}
                      </span>
                    )}
                  </Typography>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="mt-auto">
            <Button
              variant={isPopular ? 'primary' : 'outline'}
              fullWidth
              size="lg"
              onClick={() => onSelect(pkg)}
              className={`font-semibold transition-all duration-200 ${
                isPopular
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl'
                  : 'hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600'
              }`}
            >
              {isPopular ? '🚀 Get Started' : 'Choose Plan'}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ModernPricingCard;
