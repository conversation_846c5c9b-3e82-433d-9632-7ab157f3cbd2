import { useMutation, useQueryClient } from '@tanstack/react-query';
import { DatabaseConnectionFormData, DatabaseTestRequest, DatabaseConnectionConfig } from '../types';
import { databaseIntegrationService, DatabaseIntegrationService } from '../services';
import { DATABASE_INTEGRATION_QUERY_KEYS } from '../constants';

/**
 * Hook for database integration mutations
 */
export const useDatabaseIntegration = () => {
  const queryClient = useQueryClient();

  /**
   * Create database connection mutation
   */
  const createConnection = useMutation({
    mutationFn: (data: DatabaseConnectionFormData) =>
      databaseIntegrationService.createConnectionWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate and refetch connections list
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error creating database connection:', error);
    },
  });

  /**
   * Update database connection mutation
   */
  const updateConnection = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<DatabaseConnectionFormData> }) =>
      databaseIntegrationService.updateConnectionWithBusinessLogic(id, data),
    onSuccess: (updatedConnection) => {
      // Update specific connection in cache
      queryClient.setQueryData(
        DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(updatedConnection.id),
        updatedConnection
      );

      // Invalidate connections list to reflect changes
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error updating database connection:', error);
    },
  });

  /**
   * Delete database connection mutation
   */
  const deleteConnection = useMutation({
    mutationFn: (id: string) => databaseIntegrationService.deleteConnectionWithBusinessLogic(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(deletedId),
      });

      // Invalidate connections list
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error deleting database connection:', error);
    },
  });

  /**
   * Test database connection mutation
   */
  const testConnection = useMutation({
    mutationFn: (id: string) => databaseIntegrationService.testConnectionWithBusinessLogic(id),
    onSuccess: (result, connectionId) => {
      // Update connection cache with test result
      queryClient.setQueryData(
        DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(connectionId),
        (oldData: DatabaseConnectionConfig | undefined) => {
          if (oldData) {
            return {
              ...oldData,
              lastTestedAt: new Date().toISOString(),
              testResult: result,
            };
          }
          return oldData;
        }
      );

      // Invalidate connections list to show updated test status
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error testing database connection:', error);
    },
  });

  /**
   * Test connection with custom query mutation
   */
  const testConnectionWithQuery = useMutation({
    mutationFn: (data: DatabaseTestRequest) =>
      DatabaseIntegrationService.testConnectionWithQuery(data),
    onError: (error) => {
      console.error('Error testing database connection with query:', error);
    },
  });

  /**
   * Update connection status mutation
   */
  const updateConnectionStatus = useMutation({
    mutationFn: ({ id, status }: { id: string; status: string }) =>
      DatabaseIntegrationService.updateConnectionStatus(id, status),
    onSuccess: (updatedConnection) => {
      // Update specific connection in cache
      queryClient.setQueryData(
        DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(updatedConnection.id),
        updatedConnection
      );

      // Invalidate connections list
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error updating connection status:', error);
    },
  });

  return {
    createConnection,
    updateConnection,
    deleteConnection,
    testConnection,
    testConnectionWithQuery,
    updateConnectionStatus,
  };
};

/**
 * Hook for bulk operations on database connections
 */
export const useDatabaseConnectionsBulk = () => {
  const queryClient = useQueryClient();

  /**
   * Bulk delete connections mutation
   */
  const bulkDeleteConnections = useMutation({
    mutationFn: async (ids: string[]) => {
      const deletePromises = ids.map(id => 
        databaseIntegrationService.deleteConnectionWithBusinessLogic(id)
      );
      await Promise.all(deletePromises);
      return ids;
    },
    onSuccess: (deletedIds) => {
      // Remove all deleted connections from cache
      deletedIds.forEach(id => {
        queryClient.removeQueries({
          queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(id),
        });
      });

      // Invalidate connections list
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error bulk deleting database connections:', error);
    },
  });

  /**
   * Bulk update status mutation
   */
  const bulkUpdateStatus = useMutation({
    mutationFn: async ({ ids, status }: { ids: string[]; status: string }) => {
      const updatePromises = ids.map(id => 
        DatabaseIntegrationService.updateConnectionStatus(id, status)
      );
      return Promise.all(updatePromises);
    },
    onSuccess: () => {
      // Invalidate all connections data
      queryClient.invalidateQueries({
        queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(),
      });
    },
    onError: (error) => {
      console.error('Error bulk updating connection status:', error);
    },
  });

  return {
    bulkDeleteConnections,
    bulkUpdateStatus,
  };
};
