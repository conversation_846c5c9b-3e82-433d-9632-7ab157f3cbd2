/**
 * Hook để quản lý SSE connection state và lifecycle
 */
import { useState, useEffect, useCallback, useRef } from 'react';
import {
  SSEConnectionState,
  SSEConnectionInfo,
  SSEMetrics,
  UseSSEOptions
} from '@/shared/types/sse.types';
import { useSSE } from './useSSE';

/**
 * Options cho useSSEConnection
 */
export interface UseSSEConnectionOptions extends UseSSEOptions {
  /**
   * Có theo dõi metrics không
   */
  trackMetrics?: boolean;

  /**
   * Interval để cập nhật metrics (ms)
   */
  metricsInterval?: number;

  /**
   * Callback khi connection state thay đổi
   */
  onStateChange?: (state: SSEConnectionState) => void;

  /**
   * Callback khi có lỗi kết nối
   */
  onConnectionError?: (error: Error) => void;

  /**
   * Callback khi kết nối thành công
   */
  onConnectionSuccess?: () => void;

  /**
   * <PERSON><PERSON> tự động retry khi lỗi không
   */
  autoRetry?: boolean;

  /**
   * Thời gian chờ giữa các lần retry (ms)
   */
  retryDelay?: number;

  /**
   * Số lần retry tối đa
   */
  maxRetries?: number;
}

/**
 * Return type của useSSEConnection
 */
export interface UseSSEConnectionReturn {
  /**
   * Thông tin kết nối
   */
  connectionInfo: SSEConnectionInfo;

  /**
   * Metrics (nếu được bật)
   */
  metrics: SSEMetrics | null;

  /**
   * Có đang kết nối không
   */
  isConnecting: boolean;

  /**
   * Có đã kết nối không
   */
  isConnected: boolean;

  /**
   * Có đang reconnecting không
   */
  isReconnecting: boolean;

  /**
   * Có lỗi không
   */
  hasError: boolean;

  /**
   * Lỗi cuối cùng
   */
  lastError: Error | null;

  /**
   * Kết nối
   */
  connect: () => void;

  /**
   * Ngắt kết nối
   */
  disconnect: () => void;

  /**
   * Retry kết nối
   */
  retry: () => void;

  /**
   * Reset connection state
   */
  reset: () => void;

  /**
   * Kiểm tra connection health
   */
  checkHealth: () => boolean;

  /**
   * Lấy uptime (ms)
   */
  getUptime: () => number;
}

/**
 * Hook để quản lý SSE connection
 */
export function useSSEConnection(
  url: string,
  options: UseSSEConnectionOptions = {}
): UseSSEConnectionReturn {
  const {
    trackMetrics = true,
    metricsInterval = 5000,
    onStateChange,
    onConnectionError,
    onConnectionSuccess,
    autoRetry = true,
    retryDelay = 3000,
    maxRetries = 5,
    ...sseOptions
  } = options;

  // State
  const [metrics, setMetrics] = useState<SSEMetrics | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<Error | null>(null);

  // Refs
  const previousStateRef = useRef<SSEConnectionState>(SSEConnectionState.DISCONNECTED);
  const retryTimerRef = useRef<NodeJS.Timeout | null>(null);
  const metricsTimerRef = useRef<NodeJS.Timeout | null>(null);

  // SSE hook với custom callbacks
  const sse = useSSE(url, {
    ...sseOptions,
    onOpen: (event) => {
      setLastError(null);
      setRetryCount(0);
      onConnectionSuccess?.();
      sseOptions.onOpen?.(event);
    },
    onError: (error) => {
      const errorObj = error instanceof Error ? error : new Error('SSE connection error');
      setLastError(errorObj);
      onConnectionError?.(errorObj);

      if (autoRetry && retryCount < maxRetries) {
        scheduleRetry();
      }

      sseOptions.onError?.(error);
    },
    onClose: (event) => {
      if (autoRetry && retryCount < maxRetries && sse.connectionInfo.state !== SSEConnectionState.DISCONNECTED) {
        scheduleRetry();
      }
      sseOptions.onClose?.(event);
    },
  });

  /**
   * Lên lịch retry
   */
  const scheduleRetry = useCallback(() => {
    if (retryTimerRef.current) {
      clearTimeout(retryTimerRef.current);
    }

    retryTimerRef.current = setTimeout(() => {
      setRetryCount(prev => prev + 1);
      sse.connect();
    }, retryDelay);
  }, [sse, retryDelay]);

  /**
   * Retry kết nối
   */
  const retry = useCallback(() => {
    setRetryCount(0);
    setLastError(null);
    sse.connect();
  }, [sse]);

  /**
   * Reset connection state
   */
  const reset = useCallback(() => {
    setRetryCount(0);
    setLastError(null);
    setMetrics(null);

    if (retryTimerRef.current) {
      clearTimeout(retryTimerRef.current);
      retryTimerRef.current = null;
    }
  }, []);

  /**
   * Kiểm tra connection health
   */
  const checkHealth = useCallback((): boolean => {
    const { state, connectedAt } = sse.connectionInfo;

    if (state !== SSEConnectionState.CONNECTED) {
      return false;
    }

    // Kiểm tra thời gian kết nối
    if (connectedAt) {
      const uptime = Date.now() - connectedAt.getTime();
      // Nếu kết nối quá lâu mà không có activity, có thể có vấn đề
      return uptime < 300000; // 5 phút
    }

    return true;
  }, [sse.connectionInfo]);

  /**
   * Lấy uptime
   */
  const getUptime = useCallback((): number => {
    const { connectedAt } = sse.connectionInfo;
    if (!connectedAt) return 0;
    return Date.now() - connectedAt.getTime();
  }, [sse.connectionInfo]);

  /**
   * Cập nhật metrics
   */
  const updateMetrics = useCallback(() => {
    if (!trackMetrics) return;

    // Tạo metrics từ connection info và events
    const newMetrics: SSEMetrics = {
      totalEvents: sse.connectionInfo.eventsReceived,
      eventsByType: {}, // Sẽ được cập nhật từ events
      averageConnectionTime: getUptime(),
      reconnectCount: retryCount,
      errorCount: lastError ? 1 : 0,
      bytesReceived: 0, // Sẽ được tính từ events
    };

    // Tính toán từ events
    sse.events.forEach(event => {
      newMetrics.eventsByType[event.type] =
        (newMetrics.eventsByType[event.type] || 0) + 1;
      newMetrics.bytesReceived += new Blob([JSON.stringify(event.data)]).size;
    });

    setMetrics(newMetrics);
  }, [trackMetrics, sse.connectionInfo, sse.events, getUptime, retryCount, lastError]);

  // Effect để theo dõi state changes
  useEffect(() => {
    const currentState = sse.connectionInfo.state;
    if (currentState !== previousStateRef.current) {
      onStateChange?.(currentState);
      previousStateRef.current = currentState;
    }
  }, [sse.connectionInfo.state, onStateChange]);

  // Effect để cập nhật metrics định kỳ
  useEffect(() => {
    if (trackMetrics) {
      updateMetrics(); // Cập nhật ngay lập tức

      metricsTimerRef.current = setInterval(updateMetrics, metricsInterval);

      return () => {
        if (metricsTimerRef.current) {
          clearInterval(metricsTimerRef.current);
        }
      };
    }
    return undefined;
  }, [trackMetrics, updateMetrics, metricsInterval]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (retryTimerRef.current) {
        clearTimeout(retryTimerRef.current);
      }
      if (metricsTimerRef.current) {
        clearInterval(metricsTimerRef.current);
      }
    };
  }, []);

  // Computed values
  const isConnecting = sse.connectionInfo.state === SSEConnectionState.CONNECTING;
  const isConnected = sse.connectionInfo.state === SSEConnectionState.CONNECTED;
  const isReconnecting = sse.connectionInfo.state === SSEConnectionState.RECONNECTING;
  const hasError = sse.connectionInfo.state === SSEConnectionState.ERROR || lastError !== null;

  return {
    connectionInfo: sse.connectionInfo,
    metrics,
    isConnecting,
    isConnected,
    isReconnecting,
    hasError,
    lastError,
    connect: sse.connect,
    disconnect: sse.disconnect,
    retry,
    reset,
    checkHealth,
    getUptime,
  };
}

export default useSSEConnection;
