/**
 * Types cho Custom Group Form module
 */

/**
 * Enum cho trạng thái nhóm trường tùy chỉnh
 */
export enum CustomGroupFormStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Interface cho dữ liệu nhóm trường tùy chỉnh
 */
export interface CustomGroupFormDto {
  id: number;
  label: string;
  description?: string;
  status: CustomGroupFormStatus;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho danh sách nhóm trường tùy chỉnh
 */
export interface CustomGroupFormListItemDto {
  id: number;
  label: string;
  description?: string;
  status: CustomGroupFormStatus;
  fieldCount: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho response chi tiết nhóm trường tùy chỉnh
 */
export interface CustomGroupFormResponseDto extends CustomGroupFormDto {
  customFields?: CustomFieldInGroupDto[];
}

/**
 * Interface cho trường tùy chỉnh trong nhóm
 */
export interface CustomFieldInGroupDto {
  id: number;
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>;
  order: number;
}

/**
 * Interface cho tạo nhóm trường tùy chỉnh
 */
export interface CreateCustomGroupFormDto {
  label: string;
  description?: string;
  userId: number;
}

/**
 * Interface cho cập nhật nhóm trường tùy chỉnh
 */
export interface UpdateCustomGroupFormDto {
  label?: string;
  description?: string;
  status?: CustomGroupFormStatus;
  userId: number;
}

/**
 * Interface cho response sau khi tạo/cập nhật
 */
export interface CustomGroupFormCreatedResponseDto {
  id: number;
  label: string;
  description?: string;
  status: CustomGroupFormStatus;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho query parameters
 */
export interface QueryCustomGroupFormDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: CustomGroupFormStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  userId?: number;
}

/**
 * Interface cho form values
 */
export interface CustomGroupFormFormValues {
  label: string;
  description?: string;
}

/**
 * Interface cho filter options
 */
export interface CustomGroupFormFilterOptions {
  status: CustomGroupFormStatus | 'all';
  search: string;
}

/**
 * Interface cho table data
 */
export interface CustomGroupFormTableData {
  id: string;
  label: string;
  description?: string;
  status: CustomGroupFormStatus;
  fieldCount: number;
  createdAt: string;
  updatedAt: string;
}
