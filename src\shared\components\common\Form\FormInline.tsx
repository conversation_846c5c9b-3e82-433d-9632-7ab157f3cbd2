import React, { ReactNode, isValidElement, cloneElement } from 'react';

export type InlineGap = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export type InlineAlign = 'start' | 'center' | 'end' | 'baseline' | 'stretch';
export type InlineJustify = 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
export type InlineWrap = 'wrap' | 'nowrap' | 'wrap-reverse';

export interface FormInlineProps {
  /**
   * Nội dung của FormInline (thường là các FormItem)
   */
  children: ReactNode;

  /**
   * Khoảng cách giữa các phần tử
   * @default 'md'
   */
  gap?: InlineGap;

  /**
   * Căn chỉnh theo chiều dọc
   * @default 'center'
   */
  align?: InlineAlign;

  /**
   * Căn chỉnh theo chiều ngang
   * @default 'start'
   */
  justify?: InlineJustify;

  /**
   * <PERSON><PERSON> lý khi nội dung tràn
   * @default 'wrap'
   */
  wrap?: InlineWrap;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Tự động chuyển sang layout dọc trên mobile
   * @default false
   */
  responsive?: boolean;

  /**
   * Breakpoint để chuyển sang layout dọc
   * @default 'sm'
   */
  responsiveBreakpoint?: 'sm' | 'md' | 'lg';

  /**
   * Hiển thị label inline với input
   * @default false
   */
  labelInline?: boolean;

  /**
   * Kích thước của form items
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Compact mode với reduced spacing
   * @default false
   */
  compact?: boolean;

  /**
   * Direction của flex layout
   * @default 'row'
   */
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';

  /**
   * Grow behavior cho flex items
   * @default false
   */
  grow?: boolean;

  /**
   * Shrink behavior cho flex items
   * @default true
   */
  shrink?: boolean;

  /**
   * Equal width cho tất cả items
   * @default false
   */
  equalWidth?: boolean;

  /**
   * Minimum width cho mỗi item
   */
  minItemWidth?: string;

  /**
   * Maximum width cho mỗi item
   */
  maxItemWidth?: string;
}

/**
 * Component tạo inline layout cho form
 *
 * @example
 * <FormInline gap="md" align="center">
 *   <FormItem name="search" label="Tìm kiếm">
 *     <Input />
 *   </FormItem>
 *   <Button type="submit">Tìm</Button>
 * </FormInline>
 */
const FormInline: React.FC<FormInlineProps> = ({
  children,
  gap = 'md',
  align = 'center',
  justify = 'start',
  wrap = 'wrap',
  className = '',
  responsive = false,
  responsiveBreakpoint = 'sm',
  labelInline = false,
  size = 'md',
  compact = false,
  direction = 'row',
  grow = false,
  shrink = true,
  equalWidth = false,
  minItemWidth,
  maxItemWidth,
}) => {
  // Map gap size to Tailwind class
  const gapMap: Record<InlineGap, string> = {
    none: 'gap-0',
    xs: 'gap-1',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  // Map align to Tailwind class
  const alignMap: Record<InlineAlign, string> = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    baseline: 'items-baseline',
    stretch: 'items-stretch',
  };

  // Map justify to Tailwind class
  const justifyMap: Record<InlineJustify, string> = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly',
  };

  // Map wrap to Tailwind class
  const wrapMap: Record<InlineWrap, string> = {
    wrap: 'flex-wrap',
    nowrap: 'flex-nowrap',
    'wrap-reverse': 'flex-wrap-reverse',
  };

  // Direction classes
  const directionMap = {
    row: 'flex-row',
    column: 'flex-col',
    'row-reverse': 'flex-row-reverse',
    'column-reverse': 'flex-col-reverse',
  };

  // Responsive breakpoint classes
  const responsiveBreakpointMap = {
    sm: 'sm:flex-row',
    md: 'md:flex-row',
    lg: 'lg:flex-row',
  };

  // Size classes for gap adjustment
  const sizeGapMap = {
    sm: compact ? 'gap-1' : 'gap-2',
    md: compact ? 'gap-2' : 'gap-4',
    lg: compact ? 'gap-3' : 'gap-6',
  };

  // Build class string
  const inlineClass = [
    'flex',
    responsive ? 'flex-col' : directionMap[direction],
    responsive && responsiveBreakpoint ? responsiveBreakpointMap[responsiveBreakpoint] : '',
    compact ? sizeGapMap[size] : gapMap[gap],
    alignMap[align],
    justifyMap[justify],
    wrapMap[wrap],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Item style for width constraints
  const itemStyle: React.CSSProperties = {};
  if (minItemWidth) itemStyle.minWidth = minItemWidth;
  if (maxItemWidth) itemStyle.maxWidth = maxItemWidth;

  // Clone children to add inline layout props
  let modifiedChildren;

  try {
    if (React.Children && React.Children.map) {
      modifiedChildren = React.Children.map(children, (child) => {
        if (isValidElement(child)) {
          // Kiểm tra xem child có phải là FormItem không
          const childType = child.type as React.ComponentType<unknown> & {
            displayName?: string;
            name?: string;
          };
          const isFormItem =
            childType &&
            (childType.displayName === 'FormItem' ||
              (typeof childType === 'function' && childType.name === 'FormItem'));

          if (isFormItem) {
            // Lấy props hiện tại của child
            const childProps = child.props as Record<string, unknown>;

            // Tạo item class
            const itemClass = [
              equalWidth ? 'flex-1' : '',
              grow ? 'flex-grow' : '',
              shrink ? 'flex-shrink' : 'flex-shrink-0',
              (childProps['className'] as string) || '',
            ]
              .filter(Boolean)
              .join(' ');

            // Tạo props mới
            const newProps = {
              ...childProps,
              className: itemClass,
              // Set inline to true for inline layout
              inline: labelInline,
              // Pass through size
              size: size,
              // Apply item style if width constraints are set
              style: Object.keys(itemStyle).length > 0 ? { ...itemStyle, ...((childProps['style'] as React.CSSProperties) || {}) } : childProps['style'],
            };

            return cloneElement(child, newProps);
          }
        }
        return child;
      });
    } else {
      // Fallback khi React.Children không khả dụng
      console.warn('React.Children API không khả dụng, sử dụng fallback');
      modifiedChildren = children;
    }
  } catch (error) {
    // Xử lý lỗi khi React.Children.map gặp vấn đề
    console.error('Lỗi khi sử dụng React.Children.map:', error);
    modifiedChildren = children;
  }

  return <div className={inlineClass}>{modifiedChildren}</div>;
};

export default FormInline;
