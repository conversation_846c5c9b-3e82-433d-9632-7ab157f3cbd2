import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Input,
  Icon,
  ResponsiveGrid,
  Loading,
  EmptyState,
} from '@/shared/components/common';
import { useGoogleCalendarConfigurations } from '../hooks';
import GoogleCalendarCard from './GoogleCalendarCard';
import type { GoogleCalendarQueryParams, GoogleCalendarConfiguration } from '../types';

interface GoogleCalendarListProps {
  /**
   * Callback khi click tạo mới
   */
  onCreateNew?: () => void;

  /**
   * Callback khi có thay đổi dữ liệu
   */
  onDataChange?: () => void;
}

/**
 * Component hiển thị danh sách cấu hình Google Calendar
 */
const GoogleCalendarList: React.FC<GoogleCalendarListProps> = ({
  onCreateNew,
  onDataChange,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<boolean | undefined>(undefined);

  // Query parameters
  const queryParams = useMemo<GoogleCalendarQueryParams>(() => ({
    search: searchTerm || '',
    isActive: activeFilter || false,
    limit: 20,
  }), [searchTerm, activeFilter]);

  // Fetch configurations
  const { data, isLoading, error, refetch } = useGoogleCalendarConfigurations(queryParams);

  // Handle search
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
  };

  // Handle filter change
  const handleFilterChange = (filter: boolean | undefined) => {
    setActiveFilter(filter);
  };

  // Handle data change
  const handleDataChange = () => {
    refetch();
    onDataChange?.();
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <div className="p-8">
          <Loading size="lg" />
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="w-full">
        <div className="p-8">
          <EmptyState
            icon="alert-circle"
            title={t('integration:calendar.error.loadFailed')}
            description={error.message}
            actions={
              <Button variant="outline" onClick={() => refetch()}>
                {t('common:retry')}
              </Button>
            }
          />
        </div>
      </Card>
    );
  }

  const configurations = data?.items || [];
  const hasConfigurations = configurations.length > 0;

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                {t('integration:calendar.list.title')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:calendar.list.description')}
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('integration:calendar.list.createNew')}
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <div className="flex-1">
              <Input
                placeholder={t('integration:calendar.list.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                leftIcon={<Icon name="search" size="sm" />}
                fullWidth
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant={activeFilter === undefined ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleFilterChange(undefined)}
              >
                {t('integration:calendar.filters.all')}
              </Button>
              <Button
                variant={activeFilter === true ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleFilterChange(true)}
              >
                {t('integration:calendar.filters.active')}
              </Button>
              <Button
                variant={activeFilter === false ? 'primary' : 'outline'}
                size="sm"
                onClick={() => handleFilterChange(false)}
              >
                {t('integration:calendar.filters.inactive')}
              </Button>
            </div>
          </div>

          {/* Results Count */}
          {hasConfigurations && (
            <div className="mt-4 pt-4 border-t border-border">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:calendar.list.resultsCount', {
                  count: configurations.length,
                  total: data?.meta?.totalItems || 0,
                })}
              </Typography>
            </div>
          )}
        </div>
      </Card>

      {/* Configurations Grid */}
      {hasConfigurations ? (
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
          gap={6}
        >
          {configurations.map((configuration: GoogleCalendarConfiguration) => (
            <GoogleCalendarCard
              key={configuration.id}
              configuration={configuration}
              onUpdate={handleDataChange}
            />
          ))}
        </ResponsiveGrid>
      ) : (
        <Card>
          <div className="p-8">
            <EmptyState
              icon="calendar"
              title={
                searchTerm || activeFilter !== undefined
                  ? t('integration:calendar.empty.noResults')
                  : t('integration:calendar.empty.noConfigurations')
              }
              description={
                searchTerm || activeFilter !== undefined
                  ? t('integration:calendar.empty.noResultsDescription')
                  : t('integration:calendar.empty.noConfigurationsDescription')
              }
              actions={
                searchTerm || activeFilter !== undefined ? (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setSearchTerm('');
                      setActiveFilter(undefined);
                    }}
                  >
                    {t('integration:calendar.empty.clearFilters')}
                  </Button>
                ) : (
                  <Button
                    variant="primary"
                    onClick={onCreateNew}
                    leftIcon={<Icon name="plus" size="sm" />}
                  >
                    {t('integration:calendar.empty.createFirst')}
                  </Button>
                )
              }
            />
          </div>
        </Card>
      )}
    </div>
  );
};

export default GoogleCalendarList;
