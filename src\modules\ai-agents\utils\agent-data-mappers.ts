/**
 * Utility functions để mapping dữ liệu giữa các format khác nhau
 * AgentDetailDto <-> AgentConfigData <-> UpdateAgentDto
 */

import {
  AgentDetailDto,
  UpdateAgentDto,
  CreateAgentDto,
  CreateAgentModularDto,
  ProfileDto,
  ProfileModularDto,
  ModelConfigDto
} from '../types/dto';
import {
  AgentConfigData,
  ProfileData,
  ModelConfigData as ModelConfigDataType,
  IntegrationsData,
  StrategyData,
  ResponseData,
  ConvertData,
  MultiAgentConfigData,
  TypeProviderEnum
} from '../types';
import { ExtendedAgentConfigData } from '../components/agent-config/AgentConfigurationForm';

/**
 * Chuyển đổi ProfileDto từ API sang ProfileData cho UI
 */
export const mapProfileDtoToProfileData = (profileDto: ProfileDto): ProfileData => {
  return {
    birthDate: profileDto.dateOfBirth || '01/01/2000',
    gender: profileDto.gender === 'male' ? 'Nam' : profileDto.gender === 'female' ? 'Nữ' : 'Khác',
    language: profileDto.languages?.[0] || 'Tiếng Việt',
    education: profileDto.education || 'Đại học',
    country: profileDto.country || 'Việt Nam',
    position: profileDto.position || 'AI Assistant',
    skills: profileDto.skills || [],
    personality: Array.isArray(profileDto.personality)
      ? profileDto.personality.join(', ')
      : 'Thân thiện, nhiệt tình, chuyên nghiệp',
  };
};

/**
 * Chuyển đổi ProfileData từ UI sang ProfileDto cho API
 */
export const mapProfileDataToProfileDto = (profileData: ProfileData): Partial<ProfileDto> => {
  // Map gender từ tiếng Việt sang English
  let gender: 'male' | 'female' | 'other' | undefined;
  if (profileData.gender === 'Nam') gender = 'male';
  else if (profileData.gender === 'Nữ') gender = 'female';
  else gender = 'other';

  return {
    dateOfBirth: profileData.birthDate,
    gender: gender,
    languages: [profileData.language],
    education: profileData.education,
    country: profileData.country,
    position: profileData.position,
    skills: profileData.skills,
    personality: profileData.personality.split(', ').filter(p => p.trim()),
  };
};

/**
 * Chuyển đổi ModelConfigDto từ API sang ModelConfigData cho UI
 */
export const mapModelConfigDtoToModelConfigData = (modelConfigDto: ModelConfigDto, instruction?: string): ModelConfigDataType => {
  return {
    provider: TypeProviderEnum.OPENAI, // Default provider
    modelId: 'gpt-4', // Default model, actual modelId comes from separate field
    vectorStore: 'pinecone', // Default value, có thể cần mapping từ vectorStores
    maxTokens: modelConfigDto.max_tokens || 1000,
    temperature: modelConfigDto.temperature || 0.7,
    topP: modelConfigDto.top_p || 0.9,
    topK: modelConfigDto.top_k || 40,
    instruction: instruction || '',
  };
};

/**
 * Chuyển đổi ModelConfigData từ UI sang ModelConfigDto cho API
 */
export const mapModelConfigDataToModelConfigDto = (modelConfigData: ModelConfigDataType): ModelConfigDto => {
  return {
    max_tokens: modelConfigData.maxTokens,
    temperature: modelConfigData.temperature,
    top_p: modelConfigData.topP,
    top_k: modelConfigData.topK,
    // Note: modelId is now handled separately in model selection scenarios
  };
};

/**
 * Chuyển đổi AgentDetailDto từ API sang AgentConfigData cho UI
 */
export const mapAgentDetailDtoToAgentConfigData = (agentDetailDto: AgentDetailDto): AgentConfigData => {
  return {
    id: agentDetailDto.id,
    name: agentDetailDto.name,
    avatar: agentDetailDto.avatar || '',
    profile: mapProfileDtoToProfileData(agentDetailDto.profile),
    modelConfig: mapModelConfigDtoToModelConfigData(agentDetailDto.modelConfig, agentDetailDto.instruction),

    // Default values cho các phần chưa có mapping từ API
    integrations: {
      integrations: []
    } as IntegrationsData,

    strategy: {
      strategyId: null // Mặc định không chọn strategy nào
    } as StrategyData,

    response: {
      media: [],
      urls: [],
      products: []
    } as ResponseData,

    convert: {
      fields: []
    } as ConvertData,

    multiAgent: {
      agents: []
    } as MultiAgentConfigData,
  };
};

/**
 * Chuyển đổi ProfileData từ UI sang ProfileModularDto cho API
 */
export const mapProfileDataToProfileModularDto = (profileData: ProfileData): ProfileModularDto => {
  // Map gender từ tiếng Việt sang English
  let gender: string | undefined;
  if (profileData.gender === 'Nam') gender = 'MALE';
  else if (profileData.gender === 'Nữ') gender = 'FEMALE';
  else gender = 'OTHER';

  // Convert birthDate string to timestamp millis
  let dateOfBirth: number | null | undefined;
  if (profileData.birthDate) {
    const [day, month, year] = profileData.birthDate.split('/');
    const date = new Date(parseInt(year || '0'), parseInt(month || '0') - 1, parseInt(day || '0'));
    dateOfBirth = date.getTime();
  }

  return {
    gender,
    dateOfBirth,
    position: profileData.position,
    education: profileData.education,
    skills: profileData.skills,
    personality: profileData.personality.split(', ').filter(p => p.trim()),
    languages: [profileData.language],
    nations: profileData.country,
  };
};

/**
 * Chuyển đổi AgentConfigData từ UI sang CreateAgentDto cho API (Legacy)
 */
export const mapAgentConfigDataToCreateAgentDto = (
  agentConfigData: ExtendedAgentConfigData,
  typeAgentId: number
): CreateAgentDto => {
  return {
    name: agentConfigData.name,
    typeId: typeAgentId,
    instruction: agentConfigData.modelConfig.instruction || '',
    modelConfig: mapModelConfigDataToModelConfigDto(agentConfigData.modelConfig),
    profile: mapProfileDataToProfileDto(agentConfigData.profile),
  };
};

/**
 * Chuyển đổi AgentConfigData từ UI sang CreateAgentModularDto cho API (Recommended)
 */
export const mapAgentConfigDataToCreateAgentModularDto = (
  agentConfigData: ExtendedAgentConfigData,
  typeAgentId: number,
  typeAgentConfig?: { hasProfile?: boolean; hasOutput?: boolean; hasResources?: boolean; hasStrategy?: boolean; hasMultiAgent?: boolean; hasConversion?: boolean; },
  avatarFile?: File | null
): CreateAgentModularDto => {
  const payload: CreateAgentModularDto = {
    name: agentConfigData.name,
    typeId: typeAgentId,
    instruction: agentConfigData.modelConfig.instruction || '',
  };

  // Model Selection Logic theo yêu cầu mới
  const keyLlmId = (agentConfigData.modelConfig as ModelConfigDataType & { keyLlmId?: string }).keyLlmId;
  const modelId = agentConfigData.modelConfig.modelId;

  if (keyLlmId === 'redai') {
    // Nếu chọn RedAI
    payload.keyLlmId = null;
    payload.userModelId = null;
    payload.systemModelId = modelId;
  } else if (keyLlmId) {
    // Nếu chọn Key LLM khác
    payload.keyLlmId = keyLlmId;
    payload.userModelId = modelId;
    payload.systemModelId = null;
  } else {
    // Fallback - sử dụng systemModelId
    payload.keyLlmId = null;
    payload.userModelId = null;
    payload.systemModelId = modelId;
  }

  // Model Config - chỉ gửi khi showAdvancedConfig = true
  const showAdvancedConfig = (agentConfigData.modelConfig as ModelConfigDataType & { showAdvancedConfig?: boolean }).showAdvancedConfig;
  if (showAdvancedConfig) {
    payload.modelConfig = {
      temperature: agentConfigData.modelConfig.temperature,
      top_p: agentConfigData.modelConfig.topP,
      top_k: agentConfigData.modelConfig.topK,
      max_tokens: agentConfigData.modelConfig.maxTokens,
    };
  }

  // Thêm avatarMimeType nếu có avatar
  if (agentConfigData.avatar) {
    if (avatarFile) {
      // Sử dụng MIME type thực tế từ file
      payload.avatarMimeType = avatarFile.type;
    } else if (agentConfigData.avatar.includes('logo.png')) {
      payload.avatarMimeType = 'image/png'; // Logo mặc định
    } else {
      payload.avatarMimeType = 'image/png'; // Fallback
    }
  }

  // Profile Data
  if (typeAgentConfig?.hasProfile && agentConfigData.profile) {
    payload.profile = mapProfileDataToProfileModularDto(agentConfigData.profile);
  }

  // Conversion Data
  if (typeAgentConfig?.hasConversion && agentConfigData.convert?.fields?.length) {
    payload.conversion = agentConfigData.convert.fields.map(field => ({
      name: field.name,
      type: field.type || 'string',
      description: field.description,
      required: field.required || false,
      active: field.enabled
    }));
  }

  // Integration Data - tách Facebook và Website
  if (agentConfigData.integrations?.integrations?.length) {
    const facebookIntegrations = agentConfigData.integrations.integrations.filter(item => item.type === 'facebook');
    const websiteIntegrations = agentConfigData.integrations.integrations.filter(item => item.type === 'website');

    if (facebookIntegrations.length > 0 || websiteIntegrations.length > 0) {
      payload.outputMessenger = {
        facebookPageIds: facebookIntegrations.map(item => item.id)
      };
      payload.outputWebsite = {
        userWebsiteIds: websiteIntegrations.map(item => item.id)
      };
    }
  }

  // Resources Data
  if (typeAgentConfig?.hasResources && agentConfigData.response) {
    payload.resources = {
      urlIds: agentConfigData.response.urls?.map(url => url.id) || [],
      mediaIds: agentConfigData.response.media?.map(media => media.id) || [],
      productIds: agentConfigData.response.products?.map(product => String(product.id)) || [],
    };
  }

  // Strategy Data
  if (typeAgentConfig?.hasStrategy && agentConfigData.strategy?.strategyId) {
    payload.strategy = {
      strategyId: agentConfigData.strategy.strategyId,
    };
  }

  // Multi-Agent Data
  if (typeAgentConfig?.hasMultiAgent && agentConfigData.multiAgent?.agents?.length) {
    payload.multiAgent = {
      multiAgent: agentConfigData.multiAgent.agents.map(agent => ({
        agent_id: agent.id,
        prompt: (agent as { prompt?: string }).prompt || `Bạn là trợ lý chuyên về ${agent.name}`
      }))
    };
  }

  return payload;
};

/**
 * Chuyển đổi AgentConfigData từ UI sang UpdateAgentDto cho API
 */
export const mapAgentConfigDataToUpdateAgentDto = (agentConfigData: ExtendedAgentConfigData): UpdateAgentDto => {
  return {
    name: agentConfigData.name,
    instruction: agentConfigData.modelConfig.instruction || '',
    modelConfig: mapModelConfigDataToModelConfigDto(agentConfigData.modelConfig),
    profile: mapProfileDataToProfileDto(agentConfigData.profile),
    // Các field khác sẽ được thêm khi có mapping đầy đủ
    conversionFields: [],
    modules: [],
    resources: [],
    vector: undefined,
  };
};

/**
 * Xác định các component nào sẽ được hiển thị dựa trên type agent
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getComponentVisibilityFromAgentDetail = (_agentDetailDto: AgentDetailDto) => {
  return {
    hasProfile: true, // Luôn có profile
    hasModel: true, // Luôn có model config
    hasIntegrations: true, // Mặc định có integrations
    hasStrategy: false, // Tạm thời false, sẽ cập nhật khi có API
    hasConvert: false, // Tạm thời false, sẽ cập nhật khi có API
    hasResponse: false, // Tạm thời false, sẽ cập nhật khi có API
    hasMultiAgent: false, // Tạm thời false, sẽ cập nhật khi có API
  };
};

/**
 * Tạo dữ liệu mặc định cho AgentConfigData khi tạo mới
 */
export const createDefaultAgentConfigData = (): AgentConfigData => {
  return {
    name: 'Agent mới',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    profile: {
      birthDate: '01/01/2000',
      gender: 'Nam',
      language: 'Tiếng Việt',
      education: 'Đại học',
      country: 'Việt Nam',
      position: 'AI Assistant',
      skills: [],
      personality: 'Thân thiện, nhiệt tình, chuyên nghiệp',
    },
    modelConfig: {
      provider: TypeProviderEnum.OPENAI,
      modelId: 'gpt-4',
      vectorStore: 'pinecone',
      maxTokens: 1000,
      temperature: 0.7,
      topP: 0.9,
      topK: 40,
      instruction: '',
    },
    integrations: {
      integrations: []
    },
    strategy: {
      strategyId: null // Mặc định không chọn strategy nào
    },
    response: {
      media: [],
      urls: [],
      products: []
    },
    convert: {
      fields: []
    },
    multiAgent: {
      agents: []
    },
  };
};
