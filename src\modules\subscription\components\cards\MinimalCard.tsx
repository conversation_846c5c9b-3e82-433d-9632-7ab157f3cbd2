/**
 * Minimal Pricing Card - Clean and simple design inspired by Apple and Linear
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon } from '@/shared/components/common';
import { ServicePackage, SubscriptionDuration } from '../../types';
import { calculateMonthlyPrice, calculateSavingPercentage } from '../../utils';

interface MinimalCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

const MinimalCard: React.FC<MinimalCardProps> = ({
  package: pkg,
  duration,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const price = pkg.prices[duration];
  const monthlyPrice = calculateMonthlyPrice(price, duration);
  const savingPercentage = calculateSavingPercentage(
    pkg.prices[SubscriptionDuration.MONTHLY],
    price,
    duration
  );

  const isPopular = pkg.isPopular;

  return (
    <div className={`relative ${className}`}>
      <div
        className={`relative p-8 rounded-xl border transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 h-full flex flex-col ${
          isPopular
            ? 'border-black dark:border-white bg-black dark:bg-white text-white dark:text-black'
            : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 hover:border-gray-300 dark:hover:border-gray-600'
        }`}
      >
        {/* Popular Badge */}
        {isPopular && (
          <div className="absolute -top-3 left-6">
            <div className="bg-white dark:bg-black text-black dark:text-white px-3 py-1 rounded-md text-xs font-medium border border-gray-200 dark:border-gray-700">
              Popular
            </div>
          </div>
        )}

        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
              isPopular
                ? 'bg-white/10 dark:bg-black/10'
                : 'bg-gray-100 dark:bg-gray-800'
            }`}>
              <Icon
                name={pkg.icon || 'package'}
                size="sm"
                className={isPopular ? 'text-white dark:text-black' : 'text-gray-600 dark:text-gray-400'}
              />
            </div>
            <Typography variant="h5" className={`font-semibold ${
              isPopular ? 'text-white dark:text-black' : 'text-gray-900 dark:text-white'
            }`}>
              {t(`subscription:packages.${pkg.name.toLowerCase()}`, { defaultValue: pkg.name })}
            </Typography>
          </div>

          {pkg.description && (
            <Typography variant="body2" className={`${
              isPopular ? 'text-gray-300 dark:text-gray-700' : 'text-gray-600 dark:text-gray-400'
            }`}>
              {t(`subscription:packages.description.${pkg.id}`, { defaultValue: pkg.description })}
            </Typography>
          )}
        </div>

        {/* Pricing */}
        <div className="mb-8">
          <div className="flex items-baseline mb-2">
            <span className={`text-3xl font-bold ${
              isPopular ? 'text-white dark:text-black' : 'text-gray-900 dark:text-white'
            }`}>
              {Math.round(monthlyPrice / 1000)}
            </span>
            <span className={`text-sm ml-2 ${
              isPopular ? 'text-gray-300 dark:text-gray-700' : 'text-gray-500'
            }`}>
              R-Point / month
            </span>
          </div>

          {duration !== SubscriptionDuration.MONTHLY && savingPercentage > 0 && (
            <Typography variant="caption" className={`${
              isPopular ? 'text-green-300 dark:text-green-700' : 'text-green-600 dark:text-green-400'
            }`}>
              Save {savingPercentage}% annually
            </Typography>
          )}
        </div>

        {/* Features */}
        <div className="space-y-3 mb-8 flex-grow">
          {pkg.features.map((feature, index) => (
            <div key={index} className="flex items-start">
              <div className="flex-shrink-0 mt-0.5">
                {typeof feature.value === 'boolean' ? (
                  feature.value ? (
                    <Icon name="check" size="sm" className={`${
                      isPopular ? 'text-green-300 dark:text-green-700' : 'text-green-500'
                    }`} />
                  ) : (
                    <Icon name="minus" size="sm" className={`${
                      isPopular ? 'text-gray-400 dark:text-gray-600' : 'text-gray-400'
                    }`} />
                  )
                ) : (
                  <Icon name="check" size="sm" className={`${
                    isPopular ? 'text-green-300 dark:text-green-700' : 'text-green-500'
                  }`} />
                )}
              </div>
              <div className="ml-3">
                <Typography variant="body2" className={`${
                  isPopular ? 'text-gray-200 dark:text-gray-800' : 'text-gray-700 dark:text-gray-300'
                }`}>
                  {t(`subscription:packages.features.${feature.name}`, {
                    defaultValue: feature.name,
                  })}
                  {typeof feature.value !== 'boolean' && (
                    <span className={`font-medium ml-1 ${
                      isPopular ? 'text-white dark:text-black' : 'text-gray-900 dark:text-white'
                    }`}>
                      {feature.value}
                    </span>
                  )}
                </Typography>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="mt-auto">
          <Button
            variant={isPopular ? 'outline' : 'primary'}
            fullWidth
            size="lg"
            onClick={() => onSelect(pkg)}
            className={`font-medium transition-all duration-200 ${
              isPopular
                ? 'border-white dark:border-black text-white dark:text-black hover:bg-white hover:text-black dark:hover:bg-black dark:hover:text-white'
                : 'bg-black dark:bg-white text-white dark:text-black hover:bg-gray-800 dark:hover:bg-gray-100 border-black dark:border-white'
            }`}
          >
            {isPopular ? 'Get started' : 'Choose plan'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default MinimalCard;
