import { IconName } from '@/shared/components/common/Icon';

/**
 * <PERSON><PERSON> liệu cho mỗi card trong TopCard
 */
export interface TopCardItem {
  /**
   * Tiêu đề của card
   */
  title: string;

  /**
   * Gi<PERSON> trị hiển thị
   */
  value: string | number;

  /**
   * Icon hiển thị (tùy chọn)
   */
  icon?: IconName;

  /**
   * Màu sắc của icon (tùy chọn)
   */
  iconColor?: string;

  /**
   * Phần trăm thay đổi (tùy chọn)
   */
  change?: string;

  /**
   * X<PERSON>c định thay đổi là tích cực hay tiêu cực
   * @default true
   */
  isPositive?: boolean;

  /**
   * <PERSON><PERSON> tả bổ sung (tùy chọn)
   */
  description?: string;
}

/**
 * Props cho component TopCard
 */
export interface TopCardProps {
  /**
   * Danh sách các card items
   */
  items: TopCardItem[];

  /**
   * Tiêu đề của component (tùy chọn)
   */
  title?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Số lượng card trên mỗi trang
   * @default 4
   */
  itemsPerPage?: number;

  /**
   * Có hiển thị phân trang hay không
   * @default false
   */
  pagination?: boolean;

  /**
   * Biến thể của phân trang
   * @default 'default'
   */
  paginationVariant?: 'default' | 'simple' | 'minimal' | 'text' | 'compact';

  /**
   * Kích thước của phân trang
   * @default 'md'
   */
  paginationSize?: 'sm' | 'md' | 'lg';
}
