import React from 'react';
import { useTranslation } from 'react-i18next';
import { Alert } from '@/shared/components/common';
import ThemeSettings from '../components/ThemeSettings';
import TimezoneSettings from '../components/TimezoneSettings';
import ChatKeywordSettings from '../components/ChatKeywordSettings';

const SettingsPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div>
      <div className="space-y-6">
        {/* Theme Settings */}
        <ThemeSettings />

        {/* Timezone Settings */}
        <TimezoneSettings />

        {/* Chat Keywords Settings */}
        <ChatKeywordSettings />

        {/* Additional info */}
        <Alert
          type="info"
          title={t('settings.additionalInfo.title', 'Thông tin bổ sung')}
          message={
            <div className="space-y-2">
              <p>• {t('settings.additionalInfo.autoSave', 'Tất cả cài đặt được lưu tự động')}</p>
              <p>
                •{' '}
                {t(
                  'settings.additionalInfo.localStorage',
                  'Dữ liệu được lưu trữ cục bộ trên thiết bị của bạn'
                )}
              </p>
              <p>
                •{' '}
                {t(
                  'settings.additionalInfo.sync',
                  'Cài đặt sẽ được đồng bộ khi bạn đăng nhập trên thiết bị khác'
                )}
              </p>
              <p>
                •{' '}
                {t(
                  'settings.additionalInfo.reset',
                  'Bạn có thể đặt lại về cài đặt mặc định bất kỳ lúc nào'
                )}
              </p>
            </div>
          }
          showIcon={true}
          className="mb-6"
        />
      </div>
    </div>
  );
};

export default SettingsPage;
