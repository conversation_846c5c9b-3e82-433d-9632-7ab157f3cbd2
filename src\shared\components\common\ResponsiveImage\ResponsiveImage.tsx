import React, { ImgHTMLAttributes } from 'react';
import { Breakpoint } from '@/shared/constants/breakpoints';

interface SourceSet {
  /**
   * URL của ảnh
   */
  src: string;

  /**
   * Breakpoint áp dụng
   */
  breakpoint: Breakpoint;
}

export interface ResponsiveImageProps extends Omit<ImgHTMLAttributes<HTMLImageElement>, 'srcSet'> {
  /**
   * URL ảnh mặc định (fallback)
   */
  src: string;

  /**
   * Alt text cho ảnh
   */
  alt: string;

  /**
   * Danh sách các ảnh cho từng breakpoint
   */
  sources?: SourceSet[];

  /**
   * Tỷ lệ khung hình (aspect ratio)
   */
  aspectRatio?: string;

  /**
   * Lazy loading
   */
  lazy?: boolean;

  /**
   * Hiệu ứng blur khi loading
   */
  blur?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * ResponsiveImage component hiển thị ảnh khác nhau ở các breakpoint khác nhau
 *
 * @example
 * // Ảnh cơ bản
 * <ResponsiveImage
 *   src="/images/default.jpg"
 *   alt="Responsive image"
 *   aspectRatio="16/9"
 * />
 *
 * @example
 * // Ảnh với nhiều nguồn cho các breakpoint khác nhau
 * <ResponsiveImage
 *   src="/images/mobile.jpg"
 *   alt="Responsive image"
 *   sources={[
 *     { src: '/images/tablet.jpg', breakpoint: 'md' },
 *     { src: '/images/desktop.jpg', breakpoint: 'lg' },
 *   ]}
 * />
 */
const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  sources = [],
  aspectRatio,
  lazy = true,
  blur = false,
  className = '',
  ...rest
}) => {
  // Generate srcSet and sizes attributes
  const generateSrcSet = () => {
    if (sources.length === 0) {
      return undefined;
    }

    // Map breakpoints to min-width in pixels
    const breakpointWidths = {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536,
    };

    // Sort sources by breakpoint size (smallest to largest)
    const sortedSources = [...sources].sort(
      (a, b) => breakpointWidths[a.breakpoint] - breakpointWidths[b.breakpoint]
    );

    // Generate srcSet string
    return sortedSources.map(source => `${source.src}`).join(', ');
  };

  // Generate sizes attribute
  const generateSizes = () => {
    if (sources.length === 0) {
      return undefined;
    }

    // Map breakpoints to min-width in pixels
    const breakpointWidths = {
      xs: 0,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      '2xl': 1536,
    };

    // Sort sources by breakpoint size (largest to smallest)
    const sortedSources = [...sources].sort(
      (a, b) => breakpointWidths[b.breakpoint] - breakpointWidths[a.breakpoint]
    );

    // Generate sizes string
    return sortedSources
      .map((source, index) => {
        // For the smallest breakpoint, don't include a media query
        if (index === sortedSources.length - 1) {
          return '100vw';
        }

        return `(min-width: ${breakpointWidths[source.breakpoint]}px) 100vw`;
      })
      .join(', ');
  };

  // Aspect ratio style
  const aspectRatioStyle = aspectRatio ? { aspectRatio, objectFit: 'cover' as const } : {};

  // Blur class
  const blurClass = blur ? 'blur-up' : '';

  // Combine all classes
  const imageClasses = ['w-full', blurClass, className].join(' ');

  return (
    <img
      src={src}
      alt={alt}
      srcSet={generateSrcSet()}
      sizes={generateSizes()}
      loading={lazy ? 'lazy' : undefined}
      className={imageClasses}
      style={aspectRatioStyle}
      {...rest}
    />
  );
};

export default ResponsiveImage;
