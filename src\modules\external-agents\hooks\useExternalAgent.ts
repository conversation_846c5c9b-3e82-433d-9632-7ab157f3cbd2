import { useQuery } from '@tanstack/react-query';
import { externalAgentService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';

// Get single external agent
export const useExternalAgent = (id: string, enabled = true) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.DETAIL(id),
    queryFn: () => externalAgentService.getExternalAgent(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get agent performance metrics
export const useAgentPerformance = (id: string, enabled = true) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.PERFORMANCE(id),
    queryFn: () => externalAgentService.getPerformanceMetrics(id),
    enabled: enabled && !!id,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 60 * 1000, // Refetch every minute
  });
};

// Custom hook for agent with performance data
export const useAgentWithPerformance = (id: string, enabled = true) => {
  const agentQuery = useExternalAgent(id, enabled);
  const performanceQuery = useAgentPerformance(id, enabled && !!agentQuery.data);

  return {
    agent: agentQuery.data,
    performance: performanceQuery.data,
    isLoading: agentQuery.isLoading || performanceQuery.isLoading,
    isError: agentQuery.isError || performanceQuery.isError,
    error: agentQuery.error || performanceQuery.error,
    refetch: () => {
      agentQuery.refetch();
      performanceQuery.refetch();
    },
  };
};
