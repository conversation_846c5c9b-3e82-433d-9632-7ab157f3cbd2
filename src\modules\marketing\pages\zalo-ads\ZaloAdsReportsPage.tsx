import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BarChart3,
  TrendingUp,
  Download,

  Eye,
  MousePointer,
  Target,
  DollarSign
} from 'lucide-react';
import {
  Card,
  Button,
  Select,
} from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAdsMetrics } from '../../hooks/zalo-ads/useZaloAdsMetrics';
import type { ZaloAdsDateRange } from '../../types/zalo-ads.types';

/**
 * Trang báo cáo Zalo Ads
 */
export function ZaloAdsReportsPage() {
  const { t } = useTranslation('marketing');
  const [dateRange, setDateRange] = useState<ZaloAdsDateRange>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0], // today
  });
  const [selectedPeriod, setSelectedPeriod] = useState<'day' | 'week' | 'month'>('day');

  // API calls
  const { data: metrics } = useZaloAdsMetrics(dateRange);
  // Note: trends data will be used for chart implementation later
  // const { data: trends } = useZaloAdsPerformanceTrends(selectedPeriod, 30);

  // Mock data for demonstration
  const mockMetrics = {
    totalCampaigns: 12,
    activeCampaigns: 8,
    totalSpend: 15420000,
    totalImpressions: 2450000,
    totalClicks: 18500,
    totalConversions: 1250,
    averageCpc: 833,
    averageCtr: 0.75,
    averageRoas: 3.2
  };

  const currentMetrics = metrics || mockMetrics;

  const handleExportReport = () => {
    // TODO: Implement export functionality
    console.log('Export report for date range:', dateRange);
  };

  const handleDateRangeChange = (value: string | string[] | number | number[]) => {
    const range = value as string;
    const today = new Date();
    let startDate: Date;

    switch (range) {
      case 'today':
        startDate = new Date(today);
        break;
      case 'yesterday':
        startDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'last7days':
        startDate = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'last30days':
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'last90days':
        startDate = new Date(today.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    setDateRange({
      startDate: startDate.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0],
    });
  };

  const dateRangeOptions = [
    { value: 'today', label: t('marketing:zaloAds.reports.dateRange.today', 'Hôm nay') },
    { value: 'yesterday', label: t('marketing:zaloAds.reports.dateRange.yesterday', 'Hôm qua') },
    { value: 'last7days', label: t('marketing:zaloAds.reports.dateRange.last7days', '7 ngày qua') },
    { value: 'last30days', label: t('marketing:zaloAds.reports.dateRange.last30days', '30 ngày qua') },
    { value: 'last90days', label: t('marketing:zaloAds.reports.dateRange.last90days', '90 ngày qua') },
  ];

  const performanceData = useMemo(() => {
    // Mock performance data
    return [
      { date: '2024-01-20', impressions: 45000, clicks: 850, conversions: 42, spend: 680000 },
      { date: '2024-01-21', impressions: 52000, clicks: 920, conversions: 38, spend: 750000 },
      { date: '2024-01-22', impressions: 48000, clicks: 780, conversions: 45, spend: 620000 },
      { date: '2024-01-23', impressions: 55000, clicks: 1100, conversions: 52, spend: 890000 },
      { date: '2024-01-24', impressions: 61000, clicks: 1250, conversions: 58, spend: 980000 },
      { date: '2024-01-25', impressions: 58000, clicks: 1180, conversions: 61, spend: 920000 },
      { date: '2024-01-26', impressions: 63000, clicks: 1320, conversions: 65, spend: 1050000 },
    ];
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zaloAds.reports.title', 'Zalo Ads Reports')}
        description={t('marketing:zaloAds.reports.description', 'Phân tích hiệu suất quảng cáo Zalo')}
        actions={
          <div className="flex gap-3">
            <Select
              onChange={handleDateRangeChange}
              options={dateRangeOptions}
              placeholder={t('marketing:zaloAds.reports.selectDateRange', 'Chọn khoảng thời gian')}
              className="w-48"
            />
            <Button variant="outline" onClick={handleExportReport} className="gap-2">
              <Download className="h-4 w-4" />
              {t('marketing:zaloAds.reports.export', 'Xuất báo cáo')}
            </Button>
          </div>
        }
      />

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.reports.metrics.impressions', 'Lượt hiển thị')}
            </span>
            <Eye className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalImpressions)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            +12.5% {t('marketing:zaloAds.reports.metrics.vsLastPeriod', 'so với kỳ trước')}
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.reports.metrics.clicks', 'Lượt nhấp')}
            </span>
            <MousePointer className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalClicks)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            CTR: {currentMetrics.averageCtr}%
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.reports.metrics.conversions', 'Chuyển đổi')}
            </span>
            <Target className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">
            {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalConversions)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.reports.metrics.conversionRate', 'Tỷ lệ chuyển đổi')}: {((currentMetrics.totalConversions / currentMetrics.totalClicks) * 100).toFixed(2)}%
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.reports.metrics.spend', 'Chi phí')}
            </span>
            <DollarSign className="h-4 w-4 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-600">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(currentMetrics.totalSpend)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            CPC: {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(currentMetrics.averageCpc)}
          </p>
        </Card>
      </div>

      {/* Performance Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zaloAds.reports.performance.title', 'Hiệu suất theo thời gian')}
            </h3>
            <p className="text-sm text-muted-foreground">
              {t('marketing:zaloAds.reports.performance.description', 'Theo dõi các chỉ số quan trọng')}
            </p>
          </div>
          <Select
            value={selectedPeriod}
            onChange={(value: string | string[] | number | number[]) => setSelectedPeriod(value as 'day' | 'week' | 'month')}
            options={[
              { value: 'day', label: t('marketing:zaloAds.reports.period.day', 'Ngày') },
              { value: 'week', label: t('marketing:zaloAds.reports.period.week', 'Tuần') },
              { value: 'month', label: t('marketing:zaloAds.reports.period.month', 'Tháng') }
            ]}
            className="w-32"
          >
          </Select>
        </div>

        {/* Simple chart representation */}
        <div className="h-64 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">
              {t('marketing:zaloAds.reports.chart.placeholder', 'Biểu đồ hiệu suất sẽ được hiển thị ở đây')}
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zaloAds.reports.chart.note', 'Tích hợp với thư viện chart sẽ được thêm vào sau')}
            </p>
          </div>
        </div>
      </Card>

      {/* Performance Table */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            {t('marketing:zaloAds.reports.table.title', 'Chi tiết hiệu suất')}
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="text-left py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.date', 'Ngày')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.impressions', 'Hiển thị')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.clicks', 'Nhấp')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.ctr', 'CTR')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.conversions', 'Chuyển đổi')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.spend', 'Chi phí')}
                </th>
                <th className="text-right py-3 px-4 font-medium">
                  {t('marketing:zaloAds.reports.table.cpc', 'CPC')}
                </th>
              </tr>
            </thead>
            <tbody>
              {performanceData.map((row, index) => (
                <tr key={index} className="border-b hover:bg-muted/50">
                  <td className="py-3 px-4">
                    {new Date(row.date).toLocaleDateString('vi-VN')}
                  </td>
                  <td className="text-right py-3 px-4">
                    {new Intl.NumberFormat('vi-VN').format(row.impressions)}
                  </td>
                  <td className="text-right py-3 px-4">
                    {new Intl.NumberFormat('vi-VN').format(row.clicks)}
                  </td>
                  <td className="text-right py-3 px-4">
                    {((row.clicks / row.impressions) * 100).toFixed(2)}%
                  </td>
                  <td className="text-right py-3 px-4">
                    {new Intl.NumberFormat('vi-VN').format(row.conversions)}
                  </td>
                  <td className="text-right py-3 px-4">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      maximumFractionDigits: 0
                    }).format(row.spend)}
                  </td>
                  <td className="text-right py-3 px-4">
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND',
                      maximumFractionDigits: 0
                    }).format(row.spend / row.clicks)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Summary Insights */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">
          {t('marketing:zaloAds.reports.insights.title', 'Thông tin chi tiết')}
        </h3>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <h4 className="font-medium text-blue-900">
                {t('marketing:zaloAds.reports.insights.bestPerforming', 'Hiệu suất tốt nhất')}
              </h4>
            </div>
            <p className="text-sm text-blue-800">
              {t('marketing:zaloAds.reports.insights.bestPerformingDesc', 'Ngày 26/01 có hiệu suất tốt nhất với 1,320 clicks và 65 conversions.')}
            </p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-green-600" />
              <h4 className="font-medium text-green-900">
                {t('marketing:zaloAds.reports.insights.recommendation', 'Khuyến nghị')}
              </h4>
            </div>
            <p className="text-sm text-green-800">
              {t('marketing:zaloAds.reports.insights.recommendationDesc', 'Tăng ngân sách cho các chiến dịch có ROAS > 3.0 để tối ưu hóa hiệu quả.')}
            </p>
          </div>
        </div>
      </Card>
    </div>
  );
}

export default ZaloAdsReportsPage;
