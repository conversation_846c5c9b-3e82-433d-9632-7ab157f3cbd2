/**
 * Component hiển thị tiến trình các bước
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { ContractStep } from '../types';

interface StepIndicatorProps {
  steps: ContractStep[];
  currentStep: ContractStep;
  className?: string;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({ steps, currentStep, className = '' }) => {
  const { t } = useTranslation();

  const getStepTitle = (step: ContractStep): string => {
    switch (step) {
      case ContractStep.TYPE_SELECTION:
        return t('contract.principle.steps.typeSelection');
      case ContractStep.TERMS_ACCEPTANCE:
        return t('contract.principle.steps.termsAcceptance');
      case ContractStep.INFO_FORM:
        return t('contract.principle.steps.infoForm');
      case ContractStep.CONTRACT_DISPLAY:
        return t('contract.principle.steps.contractDisplay');
      case ContractStep.HAND_SIGNATURE:
        return t('contract.principle.steps.handSignature');
      case ContractStep.OTP_VERIFICATION:
        return t('contract.principle.steps.otpVerification');
      case ContractStep.COMPLETED:
        return t('contract.principle.steps.completed');
      default:
        return '';
    }
  };

  const getStepIcon = (step: ContractStep): string => {
    switch (step) {
      case ContractStep.TYPE_SELECTION:
        return 'user-check';
      case ContractStep.TERMS_ACCEPTANCE:
        return 'file-text';
      case ContractStep.INFO_FORM:
        return 'edit';
      case ContractStep.CONTRACT_DISPLAY:
        return 'eye';
      case ContractStep.HAND_SIGNATURE:
        return 'pen-tool';
      case ContractStep.OTP_VERIFICATION:
        return 'shield-check';
      case ContractStep.COMPLETED:
        return 'check-circle';
      default:
        return 'circle';
    }
  };

  const currentStepIndex = steps.indexOf(currentStep);
  const visibleSteps = steps.slice(0, -1); // Exclude COMPLETED step from indicator

  return (
    <div className={`w-full ${className}`}>
      {/* Desktop view */}
      <div className="hidden md:flex items-center justify-center space-x-4 overflow-x-auto pb-4">
        {visibleSteps.map((step, index) => (
          <div key={step} className="flex items-center">
            <div className="flex flex-col items-center min-w-0">
              {/* Step circle */}
              <div
                className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                  index <= currentStepIndex
                    ? 'bg-primary border-primary text-white'
                    : 'bg-background border-border text-muted-foreground'
                }`}
              >
                {index < currentStepIndex ? (
                  <Icon name="check" size="sm" />
                ) : (
                  <Icon name={getStepIcon(step)} size="sm" />
                )}
              </div>
              
              {/* Step title */}
              <div className="mt-2 text-center">
                <Typography
                  variant="body2"
                  className={`font-medium transition-colors duration-300 ${
                    index <= currentStepIndex ? 'text-primary' : 'text-muted-foreground'
                  }`}
                >
                  {getStepTitle(step)}
                </Typography>
              </div>
            </div>

            {/* Connector line */}
            {index < visibleSteps.length - 1 && (
              <div
                className={`w-16 h-0.5 mx-4 transition-colors duration-300 ${
                  index < currentStepIndex ? 'bg-primary' : 'bg-border'
                }`}
              />
            )}
          </div>
        ))}
      </div>

      {/* Mobile view */}
      <div className="md:hidden">
        <div className="flex items-center justify-between mb-4">
          <Typography variant="body2" className="text-muted-foreground">
            Bước {currentStepIndex + 1} / {visibleSteps.length}
          </Typography>
          <Typography variant="body2" className="font-medium">
            {getStepTitle(currentStep)}
          </Typography>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-border rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentStepIndex + 1) / visibleSteps.length) * 100}%` }}
          />
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
