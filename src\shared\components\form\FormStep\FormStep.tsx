import { useMemo } from 'react';
import { useFormContext, FieldValues } from 'react-hook-form';
import { FormStepProps } from './FormStep.types';
import { useFormWizard } from '../FormWizard';

/**
 * FormStep component cho từng bước trong form wizard
 *
 * @example
 * ```tsx
 * <FormWizard steps={[...]}>
 *   <FormStep
 *     id="personal"
 *     title="Thông tin cá nhân"
 *     description="Nhập thông tin cá nhân của bạn"
 *     validationSchema={personalInfoSchema}
 *   >
 *     <FormItem name="firstName" label="Họ">
 *       <Input />
 *     </FormItem>
 *     <FormItem name="lastName" label="Tên">
 *       <Input />
 *     </FormItem>
 *   </FormStep>
 * </FormWizard>
 * ```
 */
const FormStep = <TFormValues extends FieldValues = FieldValues>({
  id,

  render,
  children,
}: FormStepProps<TFormValues>) => {
  const { currentStep } = useFormWizard();
  const formMethods = useFormContext<TFormValues>();

  // Kiểm tra xem bước này có phải là bước hiện tại không
  const isActive = useMemo(() => {
    return currentStep?.id === id;
  }, [currentStep, id]);

  // Nếu không phải bước hiện tại, không render gì cả
  if (!isActive) {
    return null;
  }

  // Render nội dung của bước
  return <div className="form-step">{render ? render(formMethods) : children}</div>;
};

export default FormStep;
