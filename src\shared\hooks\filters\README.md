# Hướng dẫn sử dụng Filter Hooks

Bộ hooks này được thiết kế để đơn giản hóa việc quản lý các bộ lọc trong ứng dụng.

## Cài đặt

Các hooks được đặt trong thư mục `src/shared/hooks/filters` và có thể được import trực tiếp:

```typescript
import { useActiveFilters } from '@/shared/hooks/filters';
```

## useActiveFilters

Hook `useActiveFilters` quản lý các hàm xử lý cho component `ActiveFilters`, giúp đơn giản hóa việc xóa các bộ lọc và lấy nhãn hiển thị.

### Tham số

| Tham số               | Kiểu dữ liệu                                                                          | Mô tả                                |
| --------------------- | ------------------------------------------------------------------------------------- | ------------------------------------ |
| `handleSearch`        | `(term: string) => void`                                                              | Hàm xử lý tìm kiếm                   |
| `setSelectedFilterId` | `(id: string) => void`                                                                | Hàm xử lý thay đổi filter            |
| `setDateRange`        | `(range: [Date \| null, Date \| null]) => void`                                       | Hàm xử lý thay đổi khoảng thời gian  |
| `handleSortChange`    | `(sortBy: string \| null, sortDirection: SortOrder \| SortDirection \| null) => void` | Hàm xử lý thay đổi sắp xếp           |
| `selectedFilterValue` | `T`                                                                                   | Giá trị filter hiện tại              |
| `filterValueLabelMap` | `Record<string, string>`                                                              | Map giá trị filter với nhãn hiển thị |
| `t`                   | `TFunction`                                                                           | Hàm dịch từ i18next                  |

### Giá trị trả về

| Tên                    | Kiểu dữ liệu   | Mô tả                            |
| ---------------------- | -------------- | -------------------------------- |
| `handleClearSearch`    | `() => void`   | Hàm xóa tìm kiếm                 |
| `handleClearFilter`    | `() => void`   | Hàm xóa filter                   |
| `handleClearDateRange` | `() => void`   | Hàm xóa khoảng thời gian         |
| `handleClearSort`      | `() => void`   | Hàm xóa sắp xếp                  |
| `handleClearAll`       | `() => void`   | Hàm xóa tất cả                   |
| `getFilterLabel`       | `() => string` | Hàm lấy nhãn hiển thị cho filter |

### Ví dụ sử dụng

```typescript
import { useActiveFilters } from '@/shared/hooks/filters';
import { ActiveFilters } from '@/modules/components/filters';

const MyComponent = () => {
  const { t } = useTranslation(['common']);

  // Sử dụng hook useDataTable
  const dataTable = useDataTable({...});

  // Sử dụng hook useActiveFilters
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      active: t('common:active'),
      inactive: t('common:inactive')
    },
    t
  });

  return (
    <div>
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Các component khác */}
    </div>
  );
};
```

## Lợi ích

1. **Tách biệt logic**: Tách biệt logic xử lý bộ lọc khỏi component, giúp code dễ đọc và bảo trì hơn.
2. **Tái sử dụng**: Có thể tái sử dụng hook trong nhiều component khác nhau.
3. **Nhất quán**: Đảm bảo xử lý bộ lọc nhất quán trong toàn bộ ứng dụng.
4. **Dễ mở rộng**: Dễ dàng mở rộng hook để hỗ trợ các loại bộ lọc mới.

## Tích hợp với useDataTable

Hook `useActiveFilters` được thiết kế để hoạt động liền mạch với hook `useDataTable` từ `shared/hooks/table`:

```typescript
// Sử dụng hook useDataTable
const dataTable = useDataTable({...});

// Sử dụng hook useActiveFilters với dataTable
const activeFilters = useActiveFilters({
  handleSearch: dataTable.tableData.handleSearch,
  setSelectedFilterId: dataTable.filter.setSelectedId,
  setDateRange: dataTable.dateRange.setDateRange,
  handleSortChange: dataTable.tableData.handleSortChange,
  selectedFilterValue: dataTable.filter.selectedValue,
  filterValueLabelMap: {...},
  t
});
```

## Mở rộng

Bạn có thể mở rộng hook `useActiveFilters` để hỗ trợ các loại bộ lọc mới bằng cách thêm các tham số và hàm xử lý tương ứng.
