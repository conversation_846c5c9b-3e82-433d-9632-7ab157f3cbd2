import React, { ReactElement } from 'react';
import { useFieldCondition, Condition } from '@/shared/hooks/useFieldCondition';

export interface ConditionalFieldProps {
  /**
   * Điều kiện để hiển thị field
   */
  condition: Condition;

  /**
   * Nội dung của ConditionalField (thường là FormItem)
   */
  children: ReactElement | ReactElement[];

  /**
   * Có xóa giá trị khi field bị ẩn hay không
   * @default true
   */
  clearWhenHidden?: boolean;

  /**
   * Tên field sẽ bị ảnh hưởng bởi điều kiện
   * Nếu không cung cấp, sẽ tự động lấy từ children nếu là FormItem
   */
  targetField?: string;

  /**
   * C<PERSON> render children khi điều kiện không thỏa mãn hay không
   * @default false
   */
  keepMounted?: boolean;

  /**
   * Nội dung hiển thị khi điều kiện không thỏa mãn
   * Chỉ có tác dụng khi keepMounted = false
   */
  fallback?: React.ReactNode;
}

/**
 * Component để hiển thị field có điều kiện
 *
 * @example
 * // Hiển thị field khi field khác có giá trị cụ thể
 * <ConditionalField
 *   condition={{
 *     field: 'userType',
 *     type: ConditionType.EQUALS,
 *     value: 'business'
 *   }}
 * >
 *   <FormItem name="businessName" label="Tên doanh nghiệp">
 *     <Input />
 *   </FormItem>
 * </ConditionalField>
 *
 * // Điều kiện phức tạp với AND
 * <ConditionalField
 *   condition={{
 *     and: [
 *       { field: 'userType', type: ConditionType.EQUALS, value: 'business' },
 *       { field: 'hasPlan', type: ConditionType.IS_TRUE }
 *     ]
 *   }}
 * >
 *   <FormItem name="planDetails" label="Chi tiết gói">
 *     <Input />
 *   </FormItem>
 * </ConditionalField>
 */
const ConditionalField: React.FC<ConditionalFieldProps> = ({
  condition,
  children,
  clearWhenHidden = true,
  targetField,
  keepMounted = false,
  fallback = null,
}) => {
  // Tự động lấy targetField từ children nếu là FormItem
  let resolvedTargetField = targetField;
  if (!resolvedTargetField && React.isValidElement(children)) {
    const childProps = children.props as Record<string, unknown>;
    if (childProps && 'name' in childProps) {
      resolvedTargetField = childProps['name'] as string;
    }
  }

  // Sử dụng hook để kiểm tra điều kiện
  const isVisible = useFieldCondition({
    condition,
    clearWhenHidden,
    targetField: resolvedTargetField || '',
  });

  // Nếu điều kiện không thỏa mãn và không cần giữ mounted
  if (!isVisible && !keepMounted) {
    return <>{fallback}</>;
  }

  // Nếu điều kiện không thỏa mãn nhưng cần giữ mounted
  if (!isVisible && keepMounted) {
    return <div style={{ display: 'none' }}>{children}</div>;
  }

  // Nếu điều kiện thỏa mãn
  return <>{children}</>;
};

export default ConditionalField;
