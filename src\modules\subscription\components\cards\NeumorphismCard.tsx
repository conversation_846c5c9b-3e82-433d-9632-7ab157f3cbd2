/**
 * Neumorphism Pricing Card - Soft UI design with subtle shadows
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { ServicePackage, SubscriptionDuration } from '../../types';
import { calculateMonthlyPrice, calculateSavingPercentage } from '../../utils';

interface NeumorphismCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

const NeumorphismCard: React.FC<NeumorphismCardProps> = ({
  package: pkg,
  duration,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const price = pkg.prices[duration];
  const monthlyPrice = calculateMonthlyPrice(price, duration);
  const savingPercentage = calculateSavingPercentage(
    pkg.prices[SubscriptionDuration.MONTHLY],
    price,
    duration
  );

  const isPopular = pkg.isPopular;

  return (
    <div className={`relative ${className}`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gradient-to-r from-orange-400 to-pink-500 text-white px-4 py-2 rounded-2xl text-sm font-semibold shadow-lg">
            🏆 Best Value
          </div>
        </div>
      )}

      <div
        className={`relative p-8 rounded-3xl transition-all duration-300 hover:-translate-y-1 h-full flex flex-col ${
          isPopular
            ? 'bg-gradient-to-br from-orange-50 to-pink-50 dark:from-orange-900/20 dark:to-pink-900/20 shadow-[20px_20px_60px_#d1d5db,-20px_-20px_60px_#ffffff] dark:shadow-[20px_20px_60px_#1f2937,-20px_-20px_60px_#374151]'
            : 'bg-gray-100 dark:bg-gray-800 shadow-[20px_20px_60px_#d1d5db,-20px_-20px_60px_#ffffff] dark:shadow-[20px_20px_60px_#1f2937,-20px_-20px_60px_#374151] hover:shadow-[25px_25px_70px_#d1d5db,-25px_-25px_70px_#ffffff]'
        }`}
      >
        {/* Header */}
        <div className="text-center mb-6">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 ${
            isPopular
              ? 'bg-gradient-to-br from-orange-100 to-pink-100 dark:from-orange-800/30 dark:to-pink-800/30 shadow-[inset_8px_8px_16px_#d1d5db,inset_-8px_-8px_16px_#ffffff] dark:shadow-[inset_8px_8px_16px_#1f2937,inset_-8px_-8px_16px_#374151]'
              : 'bg-gray-100 dark:bg-gray-800 shadow-[inset_8px_8px_16px_#d1d5db,inset_-8px_-8px_16px_#ffffff] dark:shadow-[inset_8px_8px_16px_#1f2937,inset_-8px_-8px_16px_#374151]'
          }`}>
            <Icon
              name={pkg.icon || 'package'}
              size="lg"
              className={isPopular ? 'text-orange-500' : 'text-gray-600 dark:text-gray-300'}
            />
          </div>

          <Typography variant="h4" className="font-bold mb-2 text-gray-800 dark:text-gray-200">
            {t(`subscription:packages.${pkg.name.toLowerCase()}`, { defaultValue: pkg.name })}
          </Typography>

          {pkg.description && (
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {t(`subscription:packages.description.${pkg.id}`, { defaultValue: pkg.description })}
            </Typography>
          )}
        </div>

        {/* Pricing */}
        <div className="text-center mb-6">
          <div className={`inline-block p-6 rounded-2xl mb-4 ${
            isPopular
              ? 'bg-gradient-to-br from-orange-50 to-pink-50 dark:from-orange-900/20 dark:to-pink-900/20 shadow-[inset_12px_12px_24px_#d1d5db,inset_-12px_-12px_24px_#ffffff] dark:shadow-[inset_12px_12px_24px_#1f2937,inset_-12px_-12px_24px_#374151]'
              : 'bg-gray-100 dark:bg-gray-800 shadow-[inset_12px_12px_24px_#d1d5db,inset_-12px_-12px_24px_#ffffff] dark:shadow-[inset_12px_12px_24px_#1f2937,inset_-12px_-12px_24px_#374151]'
          }`}>
            <div className="flex items-baseline justify-center">
              <span className={`text-4xl font-bold ${
                isPopular ? 'text-orange-600 dark:text-orange-400' : 'text-gray-800 dark:text-gray-200'
              }`}>
                {Math.round(monthlyPrice / 1000)}
              </span>
              <span className="text-lg text-gray-600 dark:text-gray-400 ml-2">R-Point</span>
              <span className="text-gray-500 ml-1">/mo</span>
            </div>
          </div>

          {duration !== SubscriptionDuration.MONTHLY && savingPercentage > 0 && (
            <div className="inline-flex items-center px-4 py-2 rounded-xl bg-green-100 dark:bg-green-900/30 shadow-[inset_6px_6px_12px_#d1d5db,inset_-6px_-6px_12px_#ffffff] dark:shadow-[inset_6px_6px_12px_#1f2937,inset_-6px_-6px_12px_#374151] text-green-700 dark:text-green-300 text-sm font-medium">
              <Icon name="trending-down" size="sm" className="mr-1" />
              Save {savingPercentage}%
            </div>
          )}
        </div>

        {/* Features */}
        <div className="space-y-4 mb-8 flex-grow">
          {pkg.features.slice(0, 5).map((feature, index) => (
            <div key={index} className="flex items-center">
              <div className="flex-shrink-0">
                {typeof feature.value === 'boolean' ? (
                  feature.value ? (
                    <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/30 shadow-[inset_4px_4px_8px_#d1d5db,inset_-4px_-4px_8px_#ffffff] dark:shadow-[inset_4px_4px_8px_#1f2937,inset_-4px_-4px_8px_#374151] flex items-center justify-center">
                      <Icon name="check" size="xs" className="text-green-600 dark:text-green-400" />
                    </div>
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 shadow-[inset_4px_4px_8px_#d1d5db,inset_-4px_-4px_8px_#ffffff] dark:shadow-[inset_4px_4px_8px_#1f2937,inset_-4px_-4px_8px_#374151] flex items-center justify-center">
                      <Icon name="x" size="xs" className="text-gray-500" />
                    </div>
                  )
                ) : (
                  <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900/30 shadow-[inset_4px_4px_8px_#d1d5db,inset_-4px_-4px_8px_#ffffff] dark:shadow-[inset_4px_4px_8px_#1f2937,inset_-4px_-4px_8px_#374151] flex items-center justify-center">
                    <Icon name="check" size="xs" className="text-blue-600 dark:text-blue-400" />
                  </div>
                )}
              </div>
              <div className="ml-4">
                <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                  {t(`subscription:packages.features.${feature.name}`, {
                    defaultValue: feature.name,
                  })}
                  {typeof feature.value !== 'boolean' && (
                    <span className="font-semibold text-gray-900 dark:text-white ml-1">
                      {feature.value}
                    </span>
                  )}
                </Typography>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="mt-auto">
          <button
            onClick={() => onSelect(pkg)}
            className={`w-full py-4 px-6 rounded-2xl font-semibold transition-all duration-300 ${
              isPopular
                ? 'bg-gradient-to-r from-orange-400 to-pink-500 text-white shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff] dark:shadow-[8px_8px_16px_#1f2937,-8px_-8px_16px_#374151] hover:shadow-[12px_12px_24px_#d1d5db,-12px_-12px_24px_#ffffff] active:shadow-[inset_8px_8px_16px_#d1d5db,inset_-8px_-8px_16px_#ffffff]'
                : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 shadow-[8px_8px_16px_#d1d5db,-8px_-8px_16px_#ffffff] dark:shadow-[8px_8px_16px_#1f2937,-8px_-8px_16px_#374151] hover:shadow-[12px_12px_24px_#d1d5db,-12px_-12px_24px_#ffffff] active:shadow-[inset_8px_8px_16px_#d1d5db,inset_-8px_-8px_16px_#ffffff]'
            }`}
          >
            {isPopular ? '🚀 Get Started' : 'Choose Plan'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default NeumorphismCard;
