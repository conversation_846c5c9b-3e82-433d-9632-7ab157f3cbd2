import React, { HTMLAttributes } from 'react';
import { Breakpoint } from '@/shared/constants/breakpoints';

type GridColumns = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
type GridGap = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

interface ResponsiveColumns {
  xs?: GridColumns;
  sm?: GridColumns;
  md?: GridColumns;
  lg?: GridColumns;
  xl?: GridColumns;
  '2xl'?: GridColumns;
}

export interface GridProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Nội dung của grid
   */
  children: React.ReactNode;

  /**
   * Số cột ở mỗi breakpoint
   */
  columns?: GridColumns | ResponsiveColumns;

  /**
   * Khoảng cách giữa các cột
   */
  columnGap?: GridGap;

  /**
   * Khoảng cách giữa các hàng
   */
  rowGap?: GridGap;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Grid component tạo layout grid responsive
 *
 * @example
 * // Grid cơ bản với 3 cột
 * <Grid columns={3}>
 *   <div>Item 1</div>
 *   <div>Item 2</div>
 *   <div>Item 3</div>
 * </Grid>
 *
 * @example
 * // Grid với số cột khác nhau ở mỗi breakpoint
 * <Grid columns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
 *   <div>Item 1</div>
 *   <div>Item 2</div>
 *   <div>Item 3</div>
 *   <div>Item 4</div>
 * </Grid>
 */
const Grid: React.FC<GridProps> = ({
  children,
  columns = 1,
  columnGap = 'md',
  rowGap = 'md',
  className = '',
  ...rest
}) => {
  // Column gap classes
  const columnGapClasses = {
    none: 'gap-x-0',
    xs: 'gap-x-2',
    sm: 'gap-x-4',
    md: 'gap-x-6',
    lg: 'gap-x-8',
    xl: 'gap-x-10',
  };

  // Row gap classes
  const rowGapClasses = {
    none: 'gap-y-0',
    xs: 'gap-y-2',
    sm: 'gap-y-4',
    md: 'gap-y-6',
    lg: 'gap-y-8',
    xl: 'gap-y-10',
  };

  // Generate grid template columns classes
  const getGridColumnsClass = (cols: GridColumns, breakpoint?: Breakpoint) => {
    const prefix = breakpoint ? `${breakpoint}:` : '';
    return `${prefix}grid-cols-${cols}`;
  };

  // Generate responsive grid columns classes
  const getResponsiveGridColumnsClasses = () => {
    if (typeof columns === 'number') {
      return getGridColumnsClass(columns);
    }

    const classes = [];

    // Default to 1 column on mobile if not specified
    if (!columns.xs) {
      classes.push(getGridColumnsClass(1));
    }

    // Add classes for each specified breakpoint
    Object.entries(columns).forEach(([breakpoint, cols]) => {
      if (cols) {
        classes.push(getGridColumnsClass(cols, breakpoint as Breakpoint));
      }
    });

    return classes.join(' ');
  };

  // Combine all classes
  const gridClasses = [
    'grid',
    getResponsiveGridColumnsClasses(),
    columnGapClasses[columnGap],
    rowGapClasses[rowGap],
    className,
  ].join(' ');

  return (
    <div className={gridClasses} {...rest}>
      {children}
    </div>
  );
};

export default Grid;
