import { useState, useCallback, useRef, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  FormValidationConfig,
  ValidationResult,
  ValidationContext,
  ValidationState,
  CrossFieldValidationRule,
  UseValidationOptions,
  ValidationError,
  ValidationWarning,
  ValidationInfo
} from '../types';
import { useValidation } from './useValidation';
import { useFieldValidation } from './useFieldValidation';

/**
 * Form-level validation hook that manages validation for all fields
 */
export const useFormValidation = (
  config: FormValidationConfig,
  options: UseValidationOptions = {}
) => {
  const formContext = useFormContext();
  const {
    fields,
    globalRules = [],
    validateOnSubmit = true,
    validateOnChange = true,
    validateOnBlur = true,
    stopOnFirstError = false,
  } = config;

  // Global validation for cross-field rules
  const globalValidation = useValidation(globalRules, {
    validateOnMount: false,
    validateOnChange,
    validateOnBlur,
    stopOnFirstError,
    ...options,
  });

  // Field validations
  const fieldValidations = useRef<Map<string, ReturnType<typeof useFieldValidation>>>(new Map());

  // Form-level state
  const [formState, setFormState] = useState<ValidationState>({
    isValidating: false,
    isValid: true,
    errors: {},
    warnings: {},
    infos: {},
    touchedFields: new Set(),
    validatedFields: new Set(),
  });

  // Cross-field validation rules
  const [crossFieldRules] = useState<CrossFieldValidationRule[]>([]);

  /**
   * Get or create field validation
   */
  // Note: Simplified field validation approach to comply with React Hooks rules
  // In a real implementation, you might need a different pattern for dynamic field validation

  const getFieldValidation = useCallback((fieldName: string) => {
    return fieldValidations.current.get(fieldName);
  }, []);

  /**
   * Get all form values
   */
  const getAllFormValues = useCallback(() => {
    if (formContext) {
      return formContext.getValues();
    }
    return {};
  }, [formContext]);

  /**
   * Run cross-field validation
   */
  const runCrossFieldValidation = useCallback(async (
    formValues: Record<string, unknown>
  ): Promise<ValidationResult> => {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    const infos: ValidationInfo[] = [];

    for (const rule of crossFieldRules) {
      try {
        const context: ValidationContext = {
          field: rule.fields.join(','),
          formValues,
          meta: { crossField: true },
        };

        const result = rule.validator(formValues, context);

        errors.push(...result.errors);
        warnings.push(...result.warnings);
        infos.push(...result.infos);

        if (stopOnFirstError && result.errors.length > 0) {
          break;
        }
      } catch (error) {
        errors.push({
          field: rule.fields.join(','),
          message: `Cross-field validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
          code: 'CROSS_FIELD_ERROR',
          severity: 'error',
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      infos,
    };
  }, [crossFieldRules, stopOnFirstError]);

  /**
   * Validate entire form
   */
  const validateForm = useCallback(async (
    immediate = true
  ): Promise<ValidationResult> => {
    setFormState(prev => ({ ...prev, isValidating: true }));

    try {
      const formValues = getAllFormValues();
      const fieldNames = Object.keys(fields);

      // Validate all fields in parallel
      const fieldValidationPromises = fieldNames.map(async (fieldName) => {
        const fieldValidation = getFieldValidation(fieldName);
        if (fieldValidation) {
          return {
            fieldName,
            result: await fieldValidation.validateField(formValues[fieldName], immediate),
          };
        }
        return null;
      });

      const fieldResults = (await Promise.all(fieldValidationPromises))
        .filter(Boolean) as Array<{ fieldName: string; result: ValidationResult }>;

      // Run global validation
      const globalResult = await globalValidation.validateForm(formValues, immediate);

      // Run cross-field validation
      const crossFieldResult = await runCrossFieldValidation(formValues);

      // Combine all results
      const allErrors: ValidationError[] = [
        ...globalResult.errors,
        ...crossFieldResult.errors,
        ...fieldResults.flatMap(fr => fr.result.errors),
      ];

      const allWarnings: ValidationWarning[] = [
        ...globalResult.warnings,
        ...crossFieldResult.warnings,
        ...fieldResults.flatMap(fr => fr.result.warnings),
      ];

      const allInfos: ValidationInfo[] = [
        ...globalResult.infos,
        ...crossFieldResult.infos,
        ...fieldResults.flatMap(fr => fr.result.infos),
      ];

      // Group errors by field
      const errorsByField: Record<string, ValidationError[]> = {};
      const warningsByField: Record<string, ValidationWarning[]> = {};
      const infosByField: Record<string, ValidationInfo[]> = {};

      allErrors.forEach(error => {
        if (!errorsByField[error.field]) {
          errorsByField[error.field] = [];
        }
        errorsByField[error.field]?.push(error);
      });

      allWarnings.forEach(warning => {
        if (!warningsByField[warning.field]) {
          warningsByField[warning.field] = [];
        }
        warningsByField[warning.field]?.push(warning);
      });

      allInfos.forEach(info => {
        if (!infosByField[info.field]) {
          infosByField[info.field] = [];
        }
        infosByField[info.field]?.push(info);
      });

      // Update form state
      const touchedFields = new Set(fieldNames.filter(name => {
        const fieldValidation = getFieldValidation(name);
        return fieldValidation?.isTouched;
      }));

      const validatedFields = new Set(fieldNames.filter(name => {
        const fieldValidation = getFieldValidation(name);
        return fieldValidation && (fieldValidation.errors.length > 0 || fieldValidation.isValid);
      }));

      setFormState({
        isValidating: false,
        isValid: allErrors.length === 0,
        errors: errorsByField,
        warnings: warningsByField,
        infos: infosByField,
        touchedFields,
        validatedFields,
      });

      return {
        isValid: allErrors.length === 0,
        errors: allErrors,
        warnings: allWarnings,
        infos: allInfos,
      };
    } catch (error) {
      setFormState(prev => ({
        ...prev,
        isValidating: false,
        isValid: false,
      }));

      throw error;
    }
  }, [
    getAllFormValues,
    fields,
    getFieldValidation,
    globalValidation,
    runCrossFieldValidation,
  ]);

  /**
   * Validate specific fields
   */
  const validateFields = useCallback(async (
    fieldNames: string[],
    immediate = true
  ): Promise<Record<string, ValidationResult>> => {
    const results: Record<string, ValidationResult> = {};
    const formValues = getAllFormValues();

    const validationPromises = fieldNames.map(async (fieldName) => {
      const fieldValidation = getFieldValidation(fieldName);
      if (fieldValidation) {
        const result = await fieldValidation.validateField(formValues[fieldName], immediate);
        results[fieldName] = result;
      }
    });

    await Promise.all(validationPromises);
    return results;
  }, [getAllFormValues, getFieldValidation]);

  /**
   * Clear all validation
   */
  const clearValidation = useCallback(() => {
    // Clear field validations
    fieldValidations.current.forEach(fieldValidation => {
      fieldValidation.clearValidation();
    });

    // Clear global validation
    globalValidation.clearValidation();

    // Reset form state
    setFormState({
      isValidating: false,
      isValid: true,
      errors: {},
      warnings: {},
      infos: {},
      touchedFields: new Set(),
      validatedFields: new Set(),
    });
  }, [globalValidation]);

  /**
   * Clear validation for specific field
   */
  const clearFieldValidation = useCallback((fieldName: string) => {
    const fieldValidation = getFieldValidation(fieldName);
    if (fieldValidation) {
      fieldValidation.clearValidation();
    }

    setFormState(prev => {
      const newErrors = { ...prev.errors };
      const newWarnings = { ...prev.warnings };
      const newInfos = { ...prev.infos };
      const newValidatedFields = new Set(prev.validatedFields);

      delete newErrors[fieldName];
      delete newWarnings[fieldName];
      delete newInfos[fieldName];
      newValidatedFields.delete(fieldName);

      return {
        ...prev,
        errors: newErrors,
        warnings: newWarnings,
        infos: newInfos,
        validatedFields: newValidatedFields,
        isValid: Object.keys(newErrors).length === 0,
      };
    });
  }, [getFieldValidation]);

  /**
   * Get validation state for a specific field
   */
  const getFieldState = useCallback((fieldName: string) => {
    const fieldValidation = getFieldValidation(fieldName);
    return {
      isValidating: fieldValidation?.isValidating || false,
      isValid: fieldValidation?.isValid || true,
      isTouched: fieldValidation?.isTouched || false,
      isDirty: fieldValidation?.isDirty || false,
      errors: formState.errors[fieldName] || [],
      warnings: formState.warnings[fieldName] || [],
      infos: formState.infos[fieldName] || [],
    };
  }, [getFieldValidation, formState]);

  /**
   * Check if form has any errors
   */
  const hasErrors = useCallback(() => {
    return Object.keys(formState.errors).length > 0;
  }, [formState.errors]);

  /**
   * Check if form has any warnings
   */
  const hasWarnings = useCallback(() => {
    return Object.keys(formState.warnings).length > 0;
  }, [formState.warnings]);

  /**
   * Get all errors as flat array
   */
  const getAllErrors = useCallback(() => {
    return Object.values(formState.errors).flat();
  }, [formState.errors]);

  /**
   * Get all warnings as flat array
   */
  const getAllWarnings = useCallback(() => {
    return Object.values(formState.warnings).flat();
  }, [formState.warnings]);

  /**
   * Check if form is ready for submission
   */
  const isReadyForSubmit = useCallback(() => {
    return !formState.isValidating && formState.isValid && !hasErrors();
  }, [formState.isValidating, formState.isValid, hasErrors]);

  // Handle form submission validation
  useEffect(() => {
    if (formContext && validateOnSubmit) {
      const originalHandleSubmit = formContext.handleSubmit;

      formContext.handleSubmit = (onValid, onInvalid) => {
        return originalHandleSubmit(async (data) => {
          const result = await validateForm(true);
          if (result.isValid) {
            return onValid(data);
          } else if (onInvalid) {
            // Convert ValidationError[] to FieldErrors format
            const fieldErrors = Object.fromEntries(
              Object.entries(formState.errors).map(([field, errors]) => [
                field,
                { message: errors[0]?.message || 'Validation error', type: 'validation' }
              ])
            );
            return onInvalid(fieldErrors);
          }
          return undefined;
        }, onInvalid);
      };
    }
  }, [formContext, validateOnSubmit, validateForm, formState.errors]);

  return {
    // State
    formState,
    isValidating: formState.isValidating,
    isValid: formState.isValid,

    // Validation methods
    validateForm,
    validateFields,

    // Field management
    clearValidation,
    clearFieldValidation,
    getFieldState,
    getFieldValidation,

    // Utility methods
    hasErrors,
    hasWarnings,
    getAllErrors,
    getAllWarnings,
    isReadyForSubmit,
  };
};
