import React, { useState, useRef, useEffect, useCallback } from 'react';
import { cn } from '@/shared/utils';

export interface OTPInputProps {
  /**
   * Số lượng ô input OTP
   */
  length?: number;

  /**
   * Callback khi OTP thay đổi
   */
  onChange?: (otp: string) => void;

  /**
   * Callback khi OTP được nhập đầy đủ
   */
  onComplete?: (otp: string) => void;

  /**
   * Callback khi người dùng nhấn phím Enter và OTP đã đủ độ dài
   */
  onEnterPress?: (otp: string) => void;

  /**
   * Giá trị OTP ban đầu
   */
  value?: string;

  /**
   * Tự động focus vào ô đầu tiên khi component được render
   */
  autoFocus?: boolean;

  /**
   * Disabled state
   */
  disabled?: boolean;

  /**
   * Placeholder cho mỗi ô input
   */
  placeholder?: string;

  /**
   * Class name cho container
   */
  className?: string;

  /**
   * Class name cho mỗi ô input
   */
  inputClassName?: string;

  /**
   * Hiển thị dưới dạng password
   */
  isPassword?: boolean;
}

/**
 * Component OTPInput cho phép nhập mã OTP
 */
const OTPInput: React.FC<OTPInputProps> = ({
  length = 6,
  onChange,
  onComplete,
  onEnterPress,
  value = '',
  autoFocus = false,
  disabled = false,
  placeholder = '•',
  className,
  inputClassName,
  isPassword = false,
}) => {
  // State để lưu trữ giá trị OTP
  const [otp, setOtp] = useState<string[]>(
    value
      .split('')
      .slice(0, length)
      .concat(Array(length - value.length).fill(''))
  );

  // Refs cho các input
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  // Cập nhật state khi prop value thay đổi
  // Sử dụng useRef để theo dõi giá trị trước đó của prop value
  const prevValueRef = useRef(value);

  useEffect(() => {
    // Chỉ cập nhật state nếu prop value thay đổi từ bên ngoài
    // và khác với giá trị hiện tại của otp
    const currentOtpValue = otp.join('');
    if (value !== currentOtpValue && value !== prevValueRef.current) {
      setOtp(
        value
          .split('')
          .slice(0, length)
          .concat(Array(length - value.length).fill(''))
      );
    }
    prevValueRef.current = value;
  }, [value, length, otp]);

  // Xử lý khi OTP thay đổi - chỉ thông báo ra bên ngoài
  // khi thay đổi đến từ người dùng (không phải từ prop)
  const notifyChange = useCallback(
    (newOtp: string[]) => {
      const otpValue = newOtp.join('');
      if (onChange && otpValue !== value) {
        onChange(otpValue);
      }

      if (otpValue.length === length && onComplete && otpValue !== value) {
        onComplete(otpValue);
      }
    },
    [length, onChange, onComplete, value]
  );

  // Xử lý khi nhập vào input
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const inputValue = e.target.value;

    // Chỉ cho phép nhập số
    if (!/^\d*$/.test(inputValue)) {
      return;
    }

    // Nếu paste nhiều số
    if (inputValue.length > 1) {
      // Lấy các ký tự số từ giá trị được paste
      const digits = inputValue.split('').filter(char => /\d/.test(char));

      // Cập nhật state với các số được paste
      const newOtp = [...otp];
      for (let i = 0; i < Math.min(digits.length, length - index); i++) {
        const digit = digits[i];
        if (digit !== undefined) {
          newOtp[index + i] = digit;
        }
      }
      setOtp(newOtp);
      notifyChange(newOtp);

      // Focus vào ô tiếp theo sau khi paste
      const nextIndex = Math.min(index + digits.length, length - 1);
      inputRefs.current[nextIndex]?.focus();
    } else {
      // Xử lý nhập một số
      const newOtp = [...otp];
      newOtp[index] = inputValue;
      setOtp(newOtp);
      notifyChange(newOtp);

      // Focus vào ô tiếp theo nếu có
      if (inputValue && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    }
  };

  // Xử lý khi nhấn phím
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    // Xử lý phím Backspace
    if (e.key === 'Backspace') {
      if (otp[index]) {
        // Nếu ô hiện tại có giá trị, xóa giá trị đó
        const newOtp = [...otp];
        newOtp[index] = '';
        setOtp(newOtp);
        notifyChange(newOtp);
      } else if (index > 0) {
        // Nếu ô hiện tại trống, focus vào ô trước đó
        inputRefs.current[index - 1]?.focus();
      }
    }

    // Xử lý phím mũi tên
    if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }

    if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    // Xử lý phím Enter
    if (e.key === 'Enter') {
      const otpValue = otp.join('');
      // Chỉ gọi callback khi OTP đã đủ độ dài
      if (otpValue.length === length && onEnterPress) {
        e.preventDefault(); // Ngăn chặn hành vi mặc định của phím Enter
        onEnterPress(otpValue);
      }
    }
  };

  // Xử lý khi paste
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');

    // Lấy các ký tự số từ dữ liệu được paste
    const digits = pastedData.split('').filter(char => /\d/.test(char));

    if (digits.length > 0) {
      // Cập nhật state với các số được paste
      const newOtp = [...otp];
      for (let i = 0; i < Math.min(digits.length, length - index); i++) {
        const digit = digits[i];
        if (digit !== undefined) {
          newOtp[index + i] = digit;
        }
      }
      setOtp(newOtp);
      notifyChange(newOtp);

      // Focus vào ô tiếp theo sau khi paste
      const nextIndex = Math.min(index + digits.length, length - 1);
      inputRefs.current[nextIndex]?.focus();
    }
  };

  return (
    <div className={cn('flex gap-2 justify-center', className)}>
      {Array.from({ length }).map((_, index) => (
        <input
          key={index}
          ref={el => (inputRefs.current[index] = el)}
          type={isPassword ? 'password' : 'text'}
          inputMode="numeric"
          pattern="\d*"
          maxLength={1}
          value={otp[index] || ''}
          onChange={e => handleChange(e, index)}
          onKeyDown={e => handleKeyDown(e, index)}
          onPaste={e => handlePaste(e, index)}
          disabled={disabled}
          autoFocus={autoFocus && index === 0}
          placeholder={placeholder}
          className={cn(
            'w-12 h-12 text-center text-lg font-medium border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent',
            'bg-background text-foreground border-border',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            inputClassName
          )}
          aria-label={`OTP digit ${index + 1}`}
        />
      ))}
    </div>
  );
};

export default OTPInput;
