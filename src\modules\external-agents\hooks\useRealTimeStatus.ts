import { useEffect, useState, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { websocketService, websocketServiceMethods } from '../services/websocketService';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import { RealTimeMessage, ExternalAgent } from '../types';

// Hook for real-time agent status updates
export const useRealTimeStatus = (agentId?: string) => {
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const queryClient = useQueryClient();

  useEffect(() => {
    // Connect to WebSocket if not already connected
    if (!websocketService.isConnected()) {
      websocketService.connect().catch(console.error);
    }

    // Monitor connection status
    const checkConnection = () => {
      setIsConnected(websocketService.isConnected());
    };

    const interval = setInterval(checkConnection, 1000);
    checkConnection(); // Initial check

    return () => {
      clearInterval(interval);
    };
  }, []);

  useEffect(() => {
    // Subscribe to agent status updates
    const unsubscribe = websocketServiceMethods.subscribeToAgentStatus((data: RealTimeMessage) => {
      // Update specific agent or all agents
      if (!agentId || data.agentId === agentId) {
        setLastUpdate(new Date());

        // Update agent in cache
        queryClient.setQueryData(
          EXTERNAL_AGENT_QUERY_KEYS.DETAIL(data.agentId),
          (oldData: ExternalAgent | undefined) => oldData ? {
            ...oldData,
            status: data.message.content.status,
            lastConnectedAt: data.timestamp,
          } : oldData
        );

        // Invalidate agents list to reflect changes
        queryClient.invalidateQueries({
          queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
        });
      }
    });

    return unsubscribe;
  }, [agentId, queryClient]);

  return {
    isConnected,
    lastUpdate,
  };
};

// Hook for real-time message updates
export const useRealTimeMessages = (agentId?: string) => {
  const [messageCount, setMessageCount] = useState(0);
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribe = websocketServiceMethods.subscribeToAgentMessages((data: RealTimeMessage) => {
      if (!agentId || data.agentId === agentId) {
        setMessageCount(prev => prev + 1);

        // Invalidate message queries
        queryClient.invalidateQueries({
          queryKey: EXTERNAL_AGENT_QUERY_KEYS.MESSAGES({ agentId: data.agentId }),
        });
      }
    });

    return unsubscribe;
  }, [agentId, queryClient]);

  return {
    messageCount,
    resetCount: () => setMessageCount(0),
  };
};

// Hook for real-time connection test updates
export const useRealTimeConnectionTests = () => {
  const [activeTests, setActiveTests] = useState<Set<string>>(new Set());
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribe = websocketServiceMethods.subscribeToConnectionTests((data: unknown) => {
      const typedData = data as { agentId: string; status: string; result?: unknown };
      const { agentId, status, result } = typedData;

      if (status === 'started') {
        setActiveTests(prev => new Set(prev).add(agentId));
      } else if (status === 'completed') {
        setActiveTests(prev => {
          const newSet = new Set(prev);
          newSet.delete(agentId);
          return newSet;
        });

        // Update test result in cache
        queryClient.setQueryData(
          EXTERNAL_AGENT_QUERY_KEYS.TEST_CONNECTION(agentId),
          result
        );
      }
    });

    return unsubscribe;
  }, [queryClient]);

  return {
    activeTests: Array.from(activeTests),
    isTestActive: (agentId: string) => activeTests.has(agentId),
  };
};

// Hook for real-time performance updates
export const useRealTimePerformance = (agentId?: string) => {
  const [performanceUpdates, setPerformanceUpdates] = useState<Record<string, unknown>>({});
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribe = websocketServiceMethods.subscribeToPerformanceUpdates((data: unknown) => {
      const typedData = data as { agentId: string; metrics: unknown };
      if (!agentId || typedData.agentId === agentId) {
        setPerformanceUpdates(prev => ({
          ...prev,
          [typedData.agentId]: typedData.metrics,
        }));

        // Update performance data in cache
        queryClient.setQueryData(
          EXTERNAL_AGENT_QUERY_KEYS.PERFORMANCE(typedData.agentId),
          typedData.metrics
        );
      }
    });

    return unsubscribe;
  }, [agentId, queryClient]);

  return {
    performanceUpdates,
    getPerformanceUpdate: (id: string) => performanceUpdates[id],
  };
};

// Comprehensive real-time hook
export const useRealTimeUpdates = (agentId?: string) => {
  const status = useRealTimeStatus(agentId);
  const messages = useRealTimeMessages(agentId);
  const connectionTests = useRealTimeConnectionTests();
  const performance = useRealTimePerformance(agentId);

  const sendCommand = useCallback((command: string, payload: Record<string, unknown> = {}) => {
    if (agentId) {
      websocketServiceMethods.sendAgentCommand(agentId, command, payload);
    }
  }, [agentId]);

  return {
    ...status,
    ...messages,
    ...connectionTests,
    ...performance,
    sendCommand,
  };
};
