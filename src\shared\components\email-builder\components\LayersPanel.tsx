import * as React from 'react';
import { useState } from 'react';
import { Typography } from '@/shared/components/common';
import { EmailElement } from '../types';
import { Layers, ChevronDown, ChevronRight, Eye, EyeOff, Trash2, Copy } from 'lucide-react';

interface LayersPanelProps {
  emailElements: EmailElement[];
  selectedElement: EmailElement | null;
  selectedIndex: number | null;
  onSelectElement: (element: EmailElement, index: number) => void;
  onDeleteElement: (index: number) => void;
  onDuplicateElement: (index: number) => void;
  onToggleVisibility: (index: number) => void;
}

const LayersPanel: React.FC<LayersPanelProps> = ({
  emailElements,
  selectedElement,
  onSelectElement,
  onDeleteElement,
  onDuplicateElement,
  onToggleVisibility
}) => {
  const [expandedLayers, setExpandedLayers] = useState<Record<string, boolean>>({});

  // <PERSON><PERSON>ch thước icon
  const iconSize = 16;

  // Xử lý khi toggle expand/collapse layer
  const toggleLayerExpand = (id: string) => {
    setExpandedLayers({
      ...expandedLayers,
      [id]: !expandedLayers[id]
    });
  };

  // Render một layer
  const renderLayer = (element: EmailElement, index: number, depth = 0) => {
    const isSelected = selectedElement?.id === element.id;
    const isExpanded = expandedLayers[element.id] || false;
    const hasChildren = element.children && element.children.length > 0;
    const isVisible = element.style?.display !== 'none';

    return (
      <div key={element.id}>
        <div
          className={`flex items-center p-2 hover:bg-accent/10 transition-all duration-200 ${isSelected ? 'bg-accent/20 border-l-2 border-accent shadow-sm' : ''}`}
          style={{ paddingLeft: `${depth * 12 + 8}px` }}
        >
          <div className="mr-2 w-4">
            {hasChildren && (
              <button
                className="text-muted-foreground hover:text-foreground transition-colors"
                onClick={() => toggleLayerExpand(element.id)}
              >
                {isExpanded ? <ChevronDown size={iconSize} /> : <ChevronRight size={iconSize} />}
              </button>
            )}
          </div>

          <button
            className="text-muted-foreground hover:text-foreground transition-colors mr-2"
            onClick={() => onToggleVisibility(index)}
            title={isVisible ? "Ẩn phần tử" : "Hiện phần tử"}
          >
            {isVisible ? <Eye size={iconSize} /> : <EyeOff size={iconSize} />}
          </button>

          <div
            className="flex-1 cursor-pointer truncate"
            onClick={() => onSelectElement(element, index)}
          >
            <Typography variant="body2" className="truncate">
              {element.type === 'text' ? 'Văn bản' :
               element.type === 'heading' ? 'Tiêu đề' :
               element.type === 'image' ? 'Hình ảnh' :
               element.type === 'button' ? 'Nút nhấn' :
               element.type}
              {element.content && element.content.length < 20 ? `: ${element.content}` : ''}
            </Typography>
          </div>

          <div className="flex space-x-1">
            <button
              className="text-muted-foreground hover:text-foreground transition-colors"
              onClick={() => onDuplicateElement(index)}
              title="Nhân bản"
            >
              <Copy size={iconSize} />
            </button>
            <button
              className="text-muted-foreground hover:text-destructive transition-colors"
              onClick={() => onDeleteElement(index)}
              title="Xóa"
            >
              <Trash2 size={iconSize} />
            </button>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {element.children?.map((child, childIndex) =>
              renderLayer(child, childIndex, depth + 1)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="mb-4 flex items-center">
        <Layers size={20} className="mr-2 text-muted-foreground" />
        <Typography variant="h3" className="text-lg font-medium">Layers</Typography>
      </div>

      {emailElements.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Typography variant="body2">Chưa có phần tử nào</Typography>
          <Typography variant="body2">Thêm phần tử từ tab Elements</Typography>
        </div>
      ) : (
        <div className="border border-border rounded-md bg-card">
          {emailElements.map((element, index) => renderLayer(element, index))}
        </div>
      )}
    </div>
  );
};

export default LayersPanel;
