import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useImageGallery } from './useImageGallery';
import { GalleryImage } from './types';
import { Icon, Loading } from '@/shared/components/common';

interface ImageGalleryCarouselProps {
  className?: string;
}

/**
 * Component hiển thị layout dạng carousel cho Image Gallery
 */
const ImageGalleryCarousel: React.FC<ImageGalleryCarouselProps> = ({ className = '' }) => {
  const { images, lightboxEnabled, openLightbox, onImageClick, lazyLoadEnabled } =
    useImageGallery();

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Khai báo startAutoplay trước
  const startAutoplay = useCallback(() => {
    autoplayTimerRef.current = setTimeout(() => {
      if (!isTransitioning && images.length > 1) {
        setIsTransitioning(true);
        setCurrentIndex(prevIndex => (prevIndex + 1) % images.length);
      }
    }, 5000); // 5 seconds
  }, [isTransitioning, images.length]);

  // Xử lý chuyển đến slide trước
  const goToPrev = useCallback(() => {
    if (isTransitioning || images.length <= 1) return;

    setIsTransitioning(true);
    setCurrentIndex(prevIndex => (prevIndex - 1 + images.length) % images.length);

    // Reset autoplay timer
    if (autoplayTimerRef.current) {
      clearTimeout(autoplayTimerRef.current);
      startAutoplay();
    }
  }, [isTransitioning, images.length, startAutoplay]);

  // Xử lý chuyển đến slide tiếp theo
  const goToNext = useCallback(() => {
    if (isTransitioning || images.length <= 1) return;

    setIsTransitioning(true);
    setCurrentIndex(prevIndex => (prevIndex + 1) % images.length);

    // Reset autoplay timer
    if (autoplayTimerRef.current) {
      clearTimeout(autoplayTimerRef.current);
      startAutoplay();
    }
  }, [isTransitioning, images.length, startAutoplay]);

  // Xử lý chuyển đến slide cụ thể
  const goToSlide = useCallback(
    (index: number) => {
      if (isTransitioning || index === currentIndex || index < 0 || index >= images.length) return;

      setIsTransitioning(true);
      setCurrentIndex(index);

      // Reset autoplay timer
      if (autoplayTimerRef.current) {
        clearTimeout(autoplayTimerRef.current);
        startAutoplay();
      }
    },
    [isTransitioning, currentIndex, images.length, startAutoplay]
  );

  // Xử lý click vào hình ảnh
  const handleImageClick = (image: GalleryImage, index: number) => {
    if (lightboxEnabled) {
      openLightbox(index);
    }

    if (onImageClick) {
      onImageClick(image, index);
    }
  };

  // Xử lý kết thúc transition
  const handleTransitionEnd = () => {
    setIsTransitioning(false);
  };

  // Autoplay đã được khai báo ở trên

  // Bắt đầu autoplay khi component mount
  useEffect(() => {
    startAutoplay();

    return () => {
      if (autoplayTimerRef.current) {
        clearTimeout(autoplayTimerRef.current);
      }
    };
  }, [startAutoplay]);

  // Xử lý keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        goToPrev();
      } else if (e.key === 'ArrowRight') {
        goToNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [goToPrev, goToNext]);

  return (
    <div className={`relative overflow-hidden ${className}`} data-testid="image-gallery-carousel">
      {/* Carousel container */}
      <div
        ref={carouselRef}
        className="flex transition-transform duration-500 ease-in-out"
        style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        onTransitionEnd={handleTransitionEnd}
      >
        {images.map((image: GalleryImage, index: number) => (
          <CarouselItem
            key={`carousel-image-${index}`}
            image={image}
            index={index}
            onClick={handleImageClick}
            useLazyLoad={lazyLoadEnabled}
            isActive={index === currentIndex}
          />
        ))}
      </div>

      {/* Navigation arrows */}
      {images.length > 1 && (
        <>
          <button
            className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-gray-800 rounded-full p-2 shadow-md opacity-70 hover:opacity-100 transition-opacity focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={goToPrev}
            aria-label="Previous image"
            disabled={isTransitioning}
          >
            <Icon name="chevron-left" size="md" />
          </button>

          <button
            className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white dark:bg-gray-800 rounded-full p-2 shadow-md opacity-70 hover:opacity-100 transition-opacity focus:outline-none focus:ring-2 focus:ring-primary"
            onClick={goToNext}
            aria-label="Next image"
            disabled={isTransitioning}
          >
            <Icon name="chevron-right" size="md" />
          </button>
        </>
      )}

      {/* Indicators */}
      {images.length > 1 && (
        <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
          {images.map((_: GalleryImage, index: number) => (
            <button
              key={`carousel-indicator-${index}`}
              className={`w-3 h-3 rounded-full transition-all focus:outline-none focus:ring-2 focus:ring-primary ${
                index === currentIndex
                  ? 'bg-primary scale-110'
                  : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
              }`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              aria-current={index === currentIndex ? 'true' : 'false'}
            />
          ))}
        </div>
      )}
    </div>
  );
};

interface CarouselItemProps {
  image: GalleryImage;
  index: number;
  onClick: (image: GalleryImage, index: number) => void;
  useLazyLoad: boolean;
  isActive: boolean;
}

const CarouselItem: React.FC<CarouselItemProps> = ({ image, index, onClick, useLazyLoad }) => {
  // Sử dụng lazy load nếu được bật
  const { ref, inView, isLoaded, imageSrc, onLoad } = useLazyLoad
    ? {
        ref: React.createRef<HTMLDivElement>(),
        inView: true,
        isLoaded: false,
        imageSrc: image.src,
        onLoad: () => {},
      }
    : {
        ref: React.createRef<HTMLDivElement>(),
        inView: true,
        isLoaded: true,
        imageSrc: image.src,
        onLoad: () => {},
      };

  // Xử lý click vào hình ảnh
  const handleClick = () => {
    onClick(image, index);
  };

  return (
    <div
      ref={useLazyLoad ? (ref as React.RefObject<HTMLDivElement>) : undefined}
      className="min-w-full relative"
      role="group"
      aria-roledescription="slide"
      aria-label={`Slide ${index + 1} of ${image.alt || ''}`}
    >
      <figure className="relative h-full">
        <div className="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-800">
          <img
            src={useLazyLoad ? imageSrc : image.src}
            alt={image.alt || `Image ${index + 1}`}
            className={`w-full h-full object-contain transition-opacity duration-300 ${
              useLazyLoad && !isLoaded ? 'opacity-0' : 'opacity-100'
            }`}
            onLoad={useLazyLoad ? onLoad : undefined}
            onClick={handleClick}
            style={{ cursor: 'pointer' }}
          />

          {/* Loading indicator */}
          {useLazyLoad && inView && !isLoaded && (
            <div className="absolute inset-0 flex items-center justify-center">
              <Loading />
            </div>
          )}
        </div>

        {/* Caption */}
        {image.caption && (
          <figcaption className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-3 text-center">
            {image.caption}
          </figcaption>
        )}
      </figure>
    </div>
  );
};

export default ImageGalleryCarousel;
