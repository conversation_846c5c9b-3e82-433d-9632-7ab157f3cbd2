# Contract Module - <PERSON>ợ<PERSON> đồng nguyên tắc

Module hợp đồng nguyên tắc cho phép người dùng ký kết hợp đồng trực tuyến với hai luồng khác nhau: <PERSON><PERSON><PERSON> nghiệ<PERSON> và Cá nhân.

## Tính năng

### 🏢 Luồng Doanh nghiệp
1. **Chọn loại hợp đồng** - <PERSON><PERSON><PERSON> nghiệp
2. **Chấp nhận điều khoản** - Đ<PERSON><PERSON> và đồng ý với các điều khoản
3. **Thông tin doanh nghiệp** - Nhập thông tin công ty, người đại diện
4. **Hiển thị hợp đồng** - Xem nội dung hợp đồng PDF
5. **Hoàn thành** - Kết thúc quy trình

### 👤 Luồng Cá nhân
1. **Chọn loại hợp đồng** - <PERSON><PERSON> nhân
2. **Chấp nhận điề<PERSON> k<PERSON>** - <PERSON><PERSON><PERSON> và đồng ý với các điề<PERSON> kho<PERSON>
3. **Thông tin cá nhân** - Nhập thông tin CCCD, địa chỉ
4. **Hiển thị hợp đồng** - Xem nội dung hợp đồng PDF
5. **Chữ ký tay** - Ký tên trực tiếp trên màn hình
6. **Xác thực OTP** - Nhập mã OTP 6 số
7. **Hoàn thành** - Kết thúc quy trình

## Cấu trúc thư mục

```
src/modules/contract/
├── components/           # Các component UI
│   ├── ContractTypeSelector.tsx    # Chọn loại hợp đồng
│   ├── TermsAcceptance.tsx        # Chấp nhận điều khoản
│   ├── BusinessInfoForm.tsx       # Form thông tin doanh nghiệp
│   ├── PersonalInfoForm.tsx       # Form thông tin cá nhân
│   ├── ContractDisplay.tsx        # Hiển thị hợp đồng
│   ├── PDFViewer.tsx             # Component xem PDF
│   ├── HandSignature.tsx         # Chữ ký tay
│   ├── OTPVerification.tsx       # Xác thực OTP
│   ├── ContractSuccess.tsx       # Trang thành công
│   ├── ContractStepper.tsx       # Thanh tiến trình
│   └── index.ts
├── pages/               # Các trang
│   ├── ContractPrinciplePage.tsx  # Trang chính
│   └── index.ts
├── types/               # Type definitions
│   ├── contract.types.ts
│   └── index.ts
├── locales/             # Đa ngôn ngữ
│   ├── vi.json          # Tiếng Việt
│   ├── en.json          # Tiếng Anh
│   ├── zh.json          # Tiếng Trung
│   └── index.ts
├── routers/             # Routing
│   └── contractRoutes.tsx
└── index.ts             # Entry point
```

## Các component chính

### ContractTypeSelector
- Cho phép chọn loại hợp đồng (Doanh nghiệp/Cá nhân)
- Responsive design với card layout
- Hover effects và animations

### PDFViewer
- Hiển thị PDF từ URL hoặc base64
- Hỗ trợ download
- Loading states và error handling

### HandSignature
- Canvas-based signature pad
- Hỗ trợ mouse và touch events
- High DPI support
- Clear và save functionality

### OTPVerification
- 6-digit OTP input với auto-focus
- Paste support
- Countdown timer cho resend
- Error states với animations

### ContractStepper
- Hiển thị tiến trình các bước
- Dynamic steps dựa trên loại hợp đồng
- Progress bar animation
- Icon indicators

## Routing

- `/contract/principle` - Trang hợp đồng nguyên tắc chính

## Đa ngôn ngữ

Module hỗ trợ 3 ngôn ngữ:
- Tiếng Việt (vi) - Mặc định
- Tiếng Anh (en)
- Tiếng Trung (zh)

## Responsive Design

- Mobile-first approach
- Breakpoints: sm, md, lg, xl
- Touch-friendly interfaces
- Optimized for tablets và smartphones

## Accessibility

- Keyboard navigation
- Screen reader support
- ARIA labels
- Focus management
- Color contrast compliance

## Sử dụng

```tsx
import { ContractPrinciplePage } from '@/modules/contract';

// Hoặc import các component riêng lẻ
import { 
  PDFViewer, 
  HandSignature, 
  OTPVerification 
} from '@/modules/contract/components';
```

## Demo

Truy cập `/contract/principle` để xem demo đầy đủ của module.

## Tính năng nâng cao

- **Form validation** với Zod schema
- **TypeScript** strict mode
- **Error handling** với custom error boundaries
- **Loading states** cho tất cả async operations
- **Animation transitions** giữa các bước
- **Auto-save** draft data
- **Progress persistence** khi reload trang
