import React, { ReactNode } from 'react';

export type GridColumns = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;
export type GridGap = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';

export interface FormGridProps {
  /**
   * Nội dung của FormGrid (thường là các FormItem)
   */
  children: ReactNode;

  /**
   * Số cột trên màn hình lớn (lg và lớn hơn)
   * @default 2
   */
  columns?: GridColumns;

  /**
   * Số cột trên màn hình trung bình (md)
   * @default 2
   */
  columnsMd?: GridColumns;

  /**
   * Số cột trên màn hình nhỏ (sm)
   * @default 1
   */
  columnsSm?: GridColumns;

  /**
   * Số cột trên màn hình extra small (xs)
   * @default 1
   */
  columnsXs?: GridColumns;

  /**
   * K<PERSON>ảng cách gi<PERSON>a các cột
   * @default 'md'
   */
  gap?: GridGap;

  /**
   * Khoảng cách giữa các hàng
   * @default 'md'
   */
  rowGap?: GridGap;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Tự động điều chỉnh số cột theo kích thước màn hình
   * @default false
   */
  responsive?: boolean;

  /**
   * Số cột tối thiểu khi responsive
   * @default 1
   */
  minColumns?: GridColumns;

  /**
   * Số cột tối đa khi responsive
   * @default 12
   */
  maxColumns?: GridColumns;

  /**
   * Sử dụng dense packing algorithm
   * @default false
   */
  dense?: boolean;

  /**
   * Tự động điều chỉnh kích thước hàng
   * @default false
   */
  autoRows?: boolean;

  /**
   * Tự động điều chỉnh kích thước cột
   * @default false
   */
  autoColumns?: boolean;

  /**
   * Định nghĩa grid areas (CSS Grid Template Areas)
   */
  areas?: string[];

  /**
   * Alignment của items trong grid
   * @default 'stretch'
   */
  alignItems?: 'start' | 'end' | 'center' | 'stretch';

  /**
   * Justification của items trong grid
   * @default 'stretch'
   */
  justifyItems?: 'start' | 'end' | 'center' | 'stretch';

  /**
   * Alignment của grid content
   * @default 'start'
   */
  alignContent?: 'start' | 'end' | 'center' | 'stretch' | 'space-around' | 'space-between' | 'space-evenly';

  /**
   * Justification của grid content
   * @default 'start'
   */
  justifyContent?: 'start' | 'end' | 'center' | 'stretch' | 'space-around' | 'space-between' | 'space-evenly';
}

/**
 * Component tạo grid layout cho form
 *
 * @example
 * <FormGrid columns={2} gap="md">
 *   <FormItem name="firstName" label="Họ">
 *     <Input />
 *   </FormItem>
 *   <FormItem name="lastName" label="Tên">
 *     <Input />
 *   </FormItem>
 *   <FormItem name="email" label="Email" className="col-span-2">
 *     <Input type="email" />
 *   </FormItem>
 * </FormGrid>
 */
const FormGrid: React.FC<FormGridProps> = ({
  children,
  columns = 2,
  columnsMd = 2,
  columnsSm = 1,
  columnsXs = 1,
  gap = 'md',
  rowGap,
  className = '',
  responsive = false,
  minColumns = 1,
  maxColumns = 12,
  dense = false,
  autoRows = false,
  autoColumns = false,
  areas,
  alignItems = 'stretch',
  justifyItems = 'stretch',
  alignContent = 'start',
  justifyContent = 'start',
}) => {
  // Responsive logic
  const finalColumns = responsive ? Math.min(Math.max(columns, minColumns), maxColumns) : columns;
  const finalColumnsMd = responsive ? Math.min(Math.max(columnsMd, minColumns), maxColumns) : columnsMd;
  const finalColumnsSm = responsive ? Math.min(Math.max(columnsSm, minColumns), maxColumns) : columnsSm;
  const finalColumnsXs = responsive ? Math.min(Math.max(columnsXs, minColumns), maxColumns) : columnsXs;

  // Map gap size to Tailwind class
  const gapMap: Record<GridGap, string> = {
    none: 'gap-0',
    xs: 'gap-1',
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8',
  };

  // Map row gap size to Tailwind class
  const rowGapMap: Record<GridGap, string> = {
    none: 'gap-y-0',
    xs: 'gap-y-1',
    sm: 'gap-y-2',
    md: 'gap-y-4',
    lg: 'gap-y-6',
    xl: 'gap-y-8',
  };

  // Alignment classes
  const alignItemsMap = {
    start: 'items-start',
    end: 'items-end',
    center: 'items-center',
    stretch: 'items-stretch',
  };

  const justifyItemsMap = {
    start: 'justify-items-start',
    end: 'justify-items-end',
    center: 'justify-items-center',
    stretch: 'justify-items-stretch',
  };

  const alignContentMap = {
    start: 'content-start',
    end: 'content-end',
    center: 'content-center',
    stretch: 'content-stretch',
    'space-around': 'content-around',
    'space-between': 'content-between',
    'space-evenly': 'content-evenly',
  };

  const justifyContentMap = {
    start: 'justify-start',
    end: 'justify-end',
    center: 'justify-center',
    stretch: 'justify-stretch',
    'space-around': 'justify-around',
    'space-between': 'justify-between',
    'space-evenly': 'justify-evenly',
  };

  // Map columns to Tailwind grid-cols class for different breakpoints
  // Không cần colsMap nữa vì chúng ta sử dụng trực tiếp grid-cols-1

  // Map sm columns to Tailwind sm:grid-cols class
  const colsSmMap: Record<GridColumns, string> = {
    1: 'sm:grid-cols-1',
    2: 'sm:grid-cols-2',
    3: 'sm:grid-cols-3',
    4: 'sm:grid-cols-4',
    5: 'sm:grid-cols-5',
    6: 'sm:grid-cols-6',
    7: 'sm:grid-cols-7',
    8: 'sm:grid-cols-8',
    9: 'sm:grid-cols-9',
    10: 'sm:grid-cols-10',
    11: 'sm:grid-cols-11',
    12: 'sm:grid-cols-12',
  };

  // Map md columns to Tailwind md:grid-cols class
  const colsMdMap: Record<GridColumns, string> = {
    1: 'md:grid-cols-1',
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-3',
    4: 'md:grid-cols-4',
    5: 'md:grid-cols-5',
    6: 'md:grid-cols-6',
    7: 'md:grid-cols-7',
    8: 'md:grid-cols-8',
    9: 'md:grid-cols-9',
    10: 'md:grid-cols-10',
    11: 'md:grid-cols-11',
    12: 'md:grid-cols-12',
  };

  // Map lg columns to Tailwind lg:grid-cols class
  const colsLgMap: Record<GridColumns, string> = {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4',
    5: 'lg:grid-cols-5',
    6: 'lg:grid-cols-6',
    7: 'lg:grid-cols-7',
    8: 'lg:grid-cols-8',
    9: 'lg:grid-cols-9',
    10: 'lg:grid-cols-10',
    11: 'lg:grid-cols-11',
    12: 'lg:grid-cols-12',
  };

  // Build class string - Enhanced with new features
  const gridClass = [
    'grid',
    // Base columns (xs)
    `grid-cols-${finalColumnsXs}`,
    // Responsive columns
    finalColumnsSm > finalColumnsXs ? colsSmMap[finalColumnsSm as GridColumns] : '',
    finalColumnsMd > finalColumnsSm ? colsMdMap[finalColumnsMd as GridColumns] : '',
    finalColumns > finalColumnsMd ? colsLgMap[finalColumns as GridColumns] : '',
    // Gap
    gapMap[gap],
    rowGap ? rowGapMap[rowGap] : '',
    // Dense packing
    dense ? 'grid-flow-dense' : '',
    // Auto sizing
    autoRows ? 'auto-rows-auto' : '',
    autoColumns ? 'auto-cols-auto' : '',
    // Alignment
    alignItemsMap[alignItems],
    justifyItemsMap[justifyItems],
    alignContentMap[alignContent],
    justifyContentMap[justifyContent],
    className,
  ]
    .filter(Boolean)
    .join(' ');

  // Grid style object for areas
  const gridStyle: React.CSSProperties = areas
    ? {
        gridTemplateAreas: areas.map(area => `"${area}"`).join(' '),
      }
    : {};

  return (
    <div className={gridClass} style={gridStyle}>
      {children}
    </div>
  );
};

export default FormGrid;
