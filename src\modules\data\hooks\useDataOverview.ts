import { useQuery } from '@tanstack/react-query';
import { useApiQuery } from '@/shared/api/hooks';
import type { DataOverviewStats, StorageInfo, StoragePlan, StorageStatistics } from '../types';

/**
 * Query keys cho data overview
 */
export const DATA_OVERVIEW_QUERY_KEYS = {
  all: ['data', 'overview'] as const,
  stats: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'stats'] as const,
  storage: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'storage'] as const,
  plans: () => [...DATA_OVERVIEW_QUERY_KEYS.all, 'plans'] as const,
};

/**
 * Hook để lấy thống kê tổng quan data
 */
export const useDataOverview = () => {
  return useApiQuery<DataOverviewStats>(
    DATA_OVERVIEW_QUERY_KEYS.stats(),
    '/user/statistics',
    {},
    {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    }
  );
};

/**
 * Transform storage statistics từ API thành StorageInfo cho UI
 */
const transformStorageStatistics = (stats: StorageStatistics): StorageInfo => {
  // Convert bytes to GB
  const usedGB = parseFloat(stats.currentUsage) / (1024 * 1024 * 1024);
  const totalGB = parseFloat(stats.usageLimit) / (1024 * 1024 * 1024);

  return {
    used: usedGB,
    total: totalGB,
    percentage: stats.usagePercentage,
    usedFormatted: stats.currentUsageFormatted,
    totalFormatted: stats.usageLimitFormatted,
    remainingFormatted: stats.remainingValueFormatted,
  };
};

/**
 * Mock service để lấy storage plans
 */
const getStoragePlans = async (): Promise<StoragePlan[]> => {
  await new Promise(resolve => setTimeout(resolve, 300));

  return [
    {
      id: 'basic',
      name: 'Gói Cơ Bản',
      storage: 10,
      price: 99000,
      priceFormatted: '99,000 ₫',
      features: ['10 GB lưu trữ', 'Hỗ trợ cơ bản', 'Upload không giới hạn'],
      isCurrentPlan: true,
    },
    {
      id: 'pro',
      name: 'Gói Pro',
      storage: 50,
      price: 299000,
      priceFormatted: '299,000 ₫',
      features: ['50 GB lưu trữ', 'Hỗ trợ ưu tiên', 'Backup tự động', 'API access'],
      isPopular: true,
    },
    {
      id: 'enterprise',
      name: 'Gói Enterprise',
      storage: 200,
      price: 999000,
      priceFormatted: '999,000 ₫',
      features: ['200 GB lưu trữ', 'Hỗ trợ 24/7', 'Backup tự động', 'API access', 'Custom integrations'],
    },
  ];
};

/**
 * Hook để lấy storage information từ API
 */
export const useStorageInfo = () => {
  const { data, isLoading, error, ...rest } = useApiQuery<StorageStatistics>(
    DATA_OVERVIEW_QUERY_KEYS.storage(),
    '/user/statistics/storage',
    {},
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
      retry: 2,
      refetchOnWindowFocus: false,
    }
  );

  // Transform data nếu có
  const transformedData = data ? transformStorageStatistics(data) : undefined;

  return {
    data: transformedData,
    isLoading,
    error,
    ...rest,
  };
};

/**
 * Hook để lấy storage plans
 */
export const useStoragePlans = () => {
  return useQuery({
    queryKey: DATA_OVERVIEW_QUERY_KEYS.plans(),
    queryFn: getStoragePlans,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook để lấy data counts cho từng module
 */
export const useDataModuleCounts = () => {
  return useQuery({
    queryKey: [...DATA_OVERVIEW_QUERY_KEYS.all, 'module-counts'],
    queryFn: async () => {
      // TODO: Implement actual API calls
      return {
        media: 1250,
        knowledgeFiles: 340,
        urls: 89,
        vectorStores: 12,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 2,
  });
};
