import React from 'react';
import { Typography, Icon, Button, Grid } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts';
import './stepper.css';

// Utility function for combining class names
const cn = (...classes: (string | boolean | undefined)[]) => {
  return classes.filter(Boolean).join(' ');
};

export interface StepItem {
  id: number | string;
  title: string;
  description?: string;
  completed?: boolean;
  active?: boolean;
  icon?: IconName;
  status?: 'completed' | 'processing' | 'waiting' | 'error';
}

export type StepperVariant = 'default' | 'outlined' | 'filled' | 'dot' | 'arrow' | 'card';
export type StepperSize = 'sm' | 'md' | 'lg';
export type StepperDirection = 'horizontal' | 'vertical';

export interface CustomStepperProps {
  steps: StepItem[];
  currentStep?: number;
  className?: string;
  lineClassName?: string;
  stepClassName?: string;
  activeStepClassName?: string;
  completedStepClassName?: string;
  inactiveStepClassName?: string;
  titleClassName?: string;
  activeTitleClassName?: string;
  completedTitleClassName?: string;
  inactiveTitleClassName?: string;
  showStepNumbers?: boolean;
  onClick?: (stepId: number | string) => void;
  onChange?: (step: number) => void;
  variant?: StepperVariant;
  size?: StepperSize;
  direction?: StepperDirection;
  showNavigationButtons?: boolean;
  showContinueButton?: boolean;
  onContinue?: () => void;
  onPrevious?: () => void;
  showConnector?: boolean;
}

function CustomStepper({
  steps,
  currentStep = 1,
  className,
  lineClassName,
  stepClassName,
  activeStepClassName,
  completedStepClassName,
  inactiveStepClassName,
  titleClassName,
  activeTitleClassName,
  completedTitleClassName,
  inactiveTitleClassName,
  showStepNumbers = true,
  onClick,
  onChange,
  variant = 'default',
  size = 'md',
  direction = 'horizontal',
  showNavigationButtons = false,
  showContinueButton = false,
  onContinue,
  onPrevious,
  showConnector = true,
}: CustomStepperProps) {
  const { t } = useTranslation();
  useTheme(); // Ensure theme context is used

  // Xác định trạng thái của mỗi step
  const getStepStatus = (step: StepItem) => {
    const stepNumber = typeof step.id === 'string' ? parseInt(step.id, 10) : step.id;

    // Nếu step đã có trạng thái explicit, sử dụng nó
    if (step.status) {
      return {
        isCompleted: step.status === 'completed',
        isActive: step.status === 'processing',
        isError: step.status === 'error',
        isWaiting: step.status === 'waiting',
      };
    }

    if (step.completed !== undefined || step.active !== undefined) {
      return {
        isCompleted: step.completed || false,
        isActive: step.active || false,
        isError: false,
        isWaiting: !step.completed && !step.active,
      };
    }

    // Ngược lại, tính toán trạng thái dựa trên currentStep
    return {
      isCompleted: stepNumber < currentStep,
      isActive: stepNumber === currentStep,
      isError: false,
      isWaiting: stepNumber > currentStep,
    };
  };

  // Handle step change
  const handleStepChange = (stepId: number | string) => {
    if (onClick) {
      onClick(stepId);
    }

    if (onChange) {
      const stepNumber = typeof stepId === 'string' ? parseInt(stepId, 10) : stepId;
      onChange(stepNumber);
    }
  };

  // Handle continue button click
  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    } else if (onChange && currentStep < steps.length) {
      onChange(currentStep + 1);
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    if (onPrevious) {
      onPrevious();
    } else if (onChange && currentStep > 1) {
      onChange(currentStep - 1);
    }
  };

  // Get size-specific classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          step: 'w-5 h-5 text-xs',
          dot: 'w-3 h-3',
          title: 'text-xs',
          description: 'text-xs',
          arrow: 'h-8 px-4',
          card: 'px-3 py-1 text-xs',
        };
      case 'lg':
        return {
          step: 'w-8 h-8 text-base',
          dot: 'w-4 h-4',
          title: 'text-base',
          description: 'text-sm',
          arrow: 'h-12 px-6',
          card: 'px-5 py-2 text-base',
        };
      default: // md
        return {
          step: 'w-6 h-6 text-sm',
          dot: 'w-3.5 h-3.5',
          title: 'text-sm',
          description: 'text-xs',
          arrow: 'h-10 px-5',
          card: 'px-4 py-1.5 text-sm',
        };
    }
  };

  // Get variant-specific classes
  const getVariantClasses = (isActive: boolean, isCompleted: boolean, isError: boolean) => {
    const sizeClasses = getSizeClasses();

    switch (variant) {
      case 'outlined':
        return isActive
          ? cn(`border-red-500 bg-white text-red-500 ${sizeClasses.step}`, activeStepClassName)
          : isCompleted
            ? cn(`border-red-500 bg-white text-red-500 ${sizeClasses.step}`, completedStepClassName)
            : isError
              ? cn(
                  `border-red-500 bg-white text-red-500 ${sizeClasses.step}`,
                  inactiveStepClassName
                )
              : cn(
                  `border-gray-300 bg-white text-gray-500 ${sizeClasses.step}`,
                  inactiveStepClassName
                );

      case 'filled':
        return isActive
          ? cn(`border-red-500 bg-red-500 text-white ${sizeClasses.step}`, activeStepClassName)
          : isCompleted
            ? cn(`border-red-500 bg-red-500 text-white ${sizeClasses.step}`, completedStepClassName)
            : isError
              ? cn(
                  `border-red-500 bg-red-500 text-white ${sizeClasses.step}`,
                  inactiveStepClassName
                )
              : cn(
                  `border-gray-300 bg-gray-200 text-gray-500 ${sizeClasses.step}`,
                  inactiveStepClassName
                );

      case 'dot':
        return isActive
          ? cn(`bg-red-500 ${sizeClasses.dot} animate-pulse`, activeStepClassName)
          : isCompleted
            ? cn(`bg-red-500 ${sizeClasses.dot}`, completedStepClassName)
            : isError
              ? cn(`bg-red-500 ${sizeClasses.dot}`, inactiveStepClassName)
              : cn(`bg-gray-300 ${sizeClasses.dot}`, inactiveStepClassName);

      case 'arrow':
        return isActive
          ? cn(`bg-red-500 text-white clip-path-arrow ${sizeClasses.arrow}`, activeStepClassName)
          : isCompleted
            ? cn(
                `bg-red-500 text-white clip-path-arrow ${sizeClasses.arrow}`,
                completedStepClassName
              )
            : isError
              ? cn(
                  `bg-red-300 text-white clip-path-arrow ${sizeClasses.arrow}`,
                  inactiveStepClassName
                )
              : cn(
                  `bg-gray-200 text-gray-500 clip-path-arrow ${sizeClasses.arrow}`,
                  inactiveStepClassName
                );

      case 'card':
        return isActive
          ? cn(
              `bg-red-500 text-white border-red-500 border step-card ${sizeClasses.card}`,
              activeStepClassName
            )
          : isCompleted
            ? cn(
                `bg-red-500 text-white border-red-500 border step-card ${sizeClasses.card}`,
                completedStepClassName
              )
            : isError
              ? cn(
                  `bg-red-100 text-red-500 border-red-300 border step-card ${sizeClasses.card}`,
                  inactiveStepClassName
                )
              : cn(
                  `bg-gray-100 text-gray-500 border-gray-300 border step-card ${sizeClasses.card}`,
                  inactiveStepClassName
                );

      default: // default
        return isActive
          ? cn(`border-red-500 bg-red-500 text-white ${sizeClasses.step}`, activeStepClassName)
          : isCompleted
            ? cn(`border-red-500 bg-red-500 text-white ${sizeClasses.step}`, completedStepClassName)
            : isError
              ? cn(
                  `border-red-300 bg-red-300 text-white ${sizeClasses.step}`,
                  inactiveStepClassName
                )
              : cn(
                  `border-gray-300 bg-white text-gray-500 ${sizeClasses.step}`,
                  inactiveStepClassName
                );
    }
  };

  // Render step icon based on variant
  const renderStepIcon = (
    step: StepItem,
    index: number,
    status: ReturnType<typeof getStepStatus>
  ) => {
    const { isCompleted, isActive, isError, isWaiting } = status;
    const stepNumber = typeof step.id === 'string' ? parseInt(step.id, 10) : step.id;

    // If custom icon is provided
    if (step.icon) {
      return <Icon name={step.icon} size={size === 'lg' ? 'md' : 'sm'} />;
    }

    // For dot variant
    if (variant === 'dot') {
      return null; // Dot variant doesn't show numbers or icons inside
    }

    // For arrow and card variants
    if (variant === 'arrow' || variant === 'card') {
      if (isCompleted) {
        return <Icon name="check" size={size === 'lg' ? 'md' : 'sm'} />;
      }
      if (isError) {
        return <Icon name="alert-circle" size={size === 'lg' ? 'md' : 'sm'} />;
      }
      if (isActive) {
        // Nếu step đang active, hiển thị icon phù hợp với trạng thái
        if (step.status === 'processing') {
          return (
            <Icon name="loading" size={size === 'lg' ? 'md' : 'sm'} className="animate-spin" />
          );
        }
      }
      if (isWaiting) {
        if (step.status === 'waiting') {
          return <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />;
        }
      }
      return showStepNumbers ? index + 1 : null;
    }

    // Default, outlined, filled variants
    if (showStepNumbers) {
      if (isCompleted) {
        return <Icon name="check" size={size === 'lg' ? 'md' : 'sm'} />;
      }
      if (isError) {
        return <Icon name="alert-circle" size={size === 'lg' ? 'md' : 'sm'} />;
      }
      if (isActive && step.status === 'processing') {
        return <Icon name="loading" size={size === 'lg' ? 'md' : 'sm'} className="animate-spin" />;
      }
      if (isWaiting && step.status === 'waiting') {
        return <Icon name="calendar" size={size === 'lg' ? 'md' : 'sm'} />;
      }
      return stepNumber;
    }

    return null;
  };

  // Render horizontal stepper (for desktop)
  const renderHorizontalStepper = () => {
    return (
      <Grid className="relative hidden sm:flex items-center">
        {steps.map((step: StepItem, index: number) => {
          const status = getStepStatus(step);
          const { isCompleted, isActive, isError } = status;
          const isLastStep = index === steps.length - 1;

          // Get connector color
          const getConnectorColor = () => {
            if (isLastStep) return '';
            const nextStep = steps[index + 1];
            if (!nextStep) return 'bg-gray-300';

            const nextStepStatus = getStepStatus(nextStep);
            return nextStepStatus.isActive || isCompleted ? 'bg-red-500' : 'bg-gray-300';
          };

          // For arrow and card variants
          if (variant === 'arrow' || variant === 'card') {
            return (
              <div
                key={step.id}
                className={cn(
                  'flex items-center justify-center text-center cursor-pointer',
                  variant === 'arrow' ? 'flex-1' : 'flex-1'
                )}
                onClick={() => handleStepChange(step.id)}
              >
                <div
                  className={cn(
                    'flex items-center justify-center',
                    getVariantClasses(isActive, isCompleted, isError),
                    onClick && 'cursor-pointer'
                  )}
                >
                  {renderStepIcon(step, index, status)}
                  <span className="ml-2">{step.title}</span>
                </div>
              </div>
            );
          }

          // For dot variant
          if (variant === 'dot') {
            return (
              <React.Fragment key={step.id}>
                <div className="flex flex-col items-center z-10">
                  <div
                    className={cn(
                      'rounded-full',
                      getVariantClasses(isActive, isCompleted, isError),
                      onClick && 'cursor-pointer'
                    )}
                    onClick={() => handleStepChange(step.id)}
                  />

                  <Typography
                    variant="caption"
                    className={cn(
                      'mt-2 text-center',
                      titleClassName,
                      isActive && cn('text-red-500', activeTitleClassName),
                      isCompleted && cn('text-red-500', completedTitleClassName),
                      isError && cn('text-red-500', inactiveTitleClassName),
                      !isActive &&
                        !isCompleted &&
                        !isError &&
                        cn('text-gray-500', inactiveTitleClassName)
                    )}
                  >
                    {step.title}
                  </Typography>
                </div>

                {!isLastStep && showConnector && (
                  <div className={cn('h-0.5 flex-grow mx-4', getConnectorColor(), lineClassName)} />
                )}
              </React.Fragment>
            );
          }

          // Default, outlined, filled variants
          return (
            <React.Fragment key={step.id}>
              <div
                className="flex items-center z-10 cursor-pointer"
                onClick={() => handleStepChange(step.id)}
              >
                <div
                  className={cn(
                    'flex items-center justify-center rounded-full border-2 transition-colors',
                    stepClassName,
                    getVariantClasses(isActive, isCompleted, isError)
                  )}
                >
                  {renderStepIcon(step, index, status)}
                </div>

                <div className="ml-2">
                  <Typography
                    variant="caption"
                    className={cn(
                      'whitespace-nowrap',
                      titleClassName,
                      isActive && cn('text-red-500', activeTitleClassName),
                      isCompleted && cn('text-red-500', completedTitleClassName),
                      isError && cn('text-red-500', inactiveTitleClassName),
                      !isActive &&
                        !isCompleted &&
                        !isError &&
                        cn('text-gray-500', inactiveTitleClassName)
                    )}
                  >
                    {step.title}
                  </Typography>

                  {step.description && (
                    <Typography variant="caption" className="text-gray-500 text-xs block mt-0.5">
                      {step.description}
                    </Typography>
                  )}
                </div>
              </div>

              {!isLastStep && showConnector && (
                <div className={cn('h-0.5 flex-grow mx-4', getConnectorColor(), lineClassName)} />
              )}
            </React.Fragment>
          );
        })}
      </Grid>
    );
  };

  // Render vertical stepper (for mobile)
  const renderVerticalStepper = () => {
    return (
      <div className="flex flex-col sm:hidden">
        {steps.map((step: StepItem, index: number) => {
          const status = getStepStatus(step);
          const { isCompleted, isActive, isError } = status;
          const isLastStep = index === steps.length - 1;

          // For dot variant
          if (variant === 'dot') {
            return (
              <div key={step.id} className="flex mb-4">
                <div className="flex flex-col items-center">
                  <div
                    className={cn(
                      'rounded-full',
                      getVariantClasses(isActive, isCompleted, isError),
                      onClick && 'cursor-pointer'
                    )}
                    onClick={() => handleStepChange(step.id)}
                  />

                  {!isLastStep && showConnector && (
                    <div
                      className={cn(
                        'w-0.5 h-8',
                        isCompleted ? 'bg-red-500' : 'bg-gray-300',
                        lineClassName
                      )}
                    />
                  )}
                </div>

                <div className="flex flex-col ml-4">
                  <Typography
                    variant="caption"
                    className={cn(
                      'flex items-center',
                      titleClassName,
                      isActive && cn('text-red-500', activeTitleClassName),
                      isCompleted && cn('text-red-500', completedTitleClassName),
                      isError && cn('text-red-500', inactiveTitleClassName),
                      !isActive &&
                        !isCompleted &&
                        !isError &&
                        cn('text-gray-500', inactiveTitleClassName)
                    )}
                    onClick={() => handleStepChange(step.id)}
                  >
                    {step.title}
                  </Typography>

                  {step.description && (
                    <Typography variant="caption" className="text-gray-500 text-xs block mt-0.5">
                      {step.description}
                    </Typography>
                  )}

                  {!isLastStep && <div className="h-8" />}
                </div>
              </div>
            );
          }

          // Default, outlined, filled, arrow, card variants
          return (
            <div key={step.id} className="flex mb-4">
              <div className="flex flex-col items-center">
                <div
                  className={cn(
                    'flex items-center justify-center rounded-full border-2 transition-colors z-10',
                    stepClassName,
                    getVariantClasses(isActive, isCompleted, isError)
                  )}
                  onClick={() => handleStepChange(step.id)}
                >
                  {renderStepIcon(step, index, status)}
                </div>

                {!isLastStep && showConnector && (
                  <div
                    className={cn(
                      'w-0.5 h-8',
                      isCompleted ? 'bg-red-500' : 'bg-gray-300',
                      lineClassName
                    )}
                  />
                )}
              </div>

              <div className="flex flex-col ml-4">
                <Typography
                  variant="caption"
                  className={cn(
                    'flex items-center',
                    titleClassName,
                    isActive && cn('text-red-500', activeTitleClassName),
                    isCompleted && cn('text-red-500', completedTitleClassName),
                    isError && cn('text-red-500', inactiveTitleClassName),
                    !isActive &&
                      !isCompleted &&
                      !isError &&
                      cn('text-gray-500', inactiveTitleClassName)
                  )}
                  onClick={() => handleStepChange(step.id)}
                >
                  {step.title}
                </Typography>

                {step.description && (
                  <Typography variant="caption" className="text-gray-500 text-xs block mt-0.5">
                    {step.description}
                  </Typography>
                )}

                {!isLastStep && <div className="h-8" />}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={cn('w-full', className)}>
      {/* Render horizontal or vertical stepper based on direction */}
      {direction === 'horizontal' ? (
        <>
          {renderHorizontalStepper()}
          {renderVerticalStepper()}
        </>
      ) : (
        <div className="flex flex-col">{renderVerticalStepper()}</div>
      )}

      {/* Navigation buttons */}
      {(showNavigationButtons || showContinueButton) && (
        <div className="flex justify-between mt-6">
          {showNavigationButtons && (
            <Button
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep <= 1}
              size="sm"
              className="px-6 py-2 bg-gray-100 hover:bg-gray-200"
              leftIcon={<Icon name="chevron-left" size="sm" />}
            >
              {t('common.previous', 'Quay lại')}
            </Button>
          )}

          {(showNavigationButtons || showContinueButton) && (
            <Button
              variant="primary"
              onClick={handleContinue}
              disabled={currentStep >= steps.length}
              size="sm"
              className="px-6 py-2 bg-red-500 hover:bg-red-600 text-white"
              rightIcon={
                <Icon name={currentStep >= steps.length ? 'check' : 'chevron-right'} size="sm" />
              }
            >
              {currentStep >= steps.length
                ? t('common.finish', 'Hoàn thành')
                : t('common.continue', 'Tiếp tục')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export default CustomStepper;
