import React from 'react';
import { NotificationType } from '@/shared/components/common/Notification/Notification';
import NotificationMessage from './NotificationMessage';

export interface NotificationItem {
  id: string;
  type: NotificationType;
  message: string;
  duration?: number;
}

interface NotificationContainerProps {
  notifications: NotificationItem[];
  onRemove: (id: string) => void;
}

const NotificationContainer: React.FC<NotificationContainerProps> = ({
  notifications,
  onRemove,
}) => {
  console.log('[NotificationContainer] Rendering with notifications:', notifications);

  return (
    <div className="w-full space-y-4">
      {notifications.length === 0 ? (
        <div className="text-gray-500 text-sm">
          {/* Không hiển thị gì khi không có thông báo */}
        </div>
      ) : (
        notifications.map(notification => {
          console.log('[NotificationContainer] Rendering notification:', notification);
          return (
            <NotificationMessage
              key={notification.id}
              id={notification.id}
              type={notification.type}
              message={notification.message}
              duration={notification.duration || 5000}
              onRemove={onRemove}
              className="mb-4"
            />
          );
        })
      )}
    </div>
  );
};

export default NotificationContainer;
