import React from 'react';
import Icon, { IconName } from '@/shared/components/common/Icon';

export interface IconButtonProps {
  /**
   * Tên icon
   */
  icon: IconName;

  /**
   * Hàm xử lý khi click
   */
  onClick?: (event?: React.MouseEvent<HTMLButtonElement>) => void;

  /**
   * Kích thước của button
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant của button
   */
  variant?: 'default' | 'primary' | 'outline' | 'ghost';

  /**
   * Tooltip hiển thị khi hover
   */
  title?: string;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Sử dụng fill thay vì stroke cho icon
   */
  fill?: boolean;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Component button với icon
 */
const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onClick,
  size = 'md',
  variant = 'default',
  title,
  disabled = false,
  className = '',
  fill = false,
  isLoading = false,
}) => {
  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
  }[size];

  // Xác định icon size dựa trên button size
  const iconSize = {
    sm: 'sm',
    md: 'md',
    lg: 'lg',
  }[size] as 'sm' | 'md' | 'lg';

  // Xác định variant classes
  const variantClasses = {
    default: 'hover:bg-gray-100 dark:hover:bg-dark-lighter',
    primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline:
      'border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-dark-lighter',
    ghost: 'hover:bg-gray-100 dark:hover:bg-dark-lighter bg-opacity-0',
  }[variant];

  // Xác định disabled classes
  const disabledClasses =
    disabled || isLoading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  // Xác định kích thước spinner dựa trên kích thước button
  const spinnerSize = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5',
  }[size];

  // Xác định màu spinner dựa trên variant
  const spinnerColor = variant === 'primary' ? 'border-white' : 'border-primary';

  return (
    <button
      className={`rounded ${sizeClasses} ${variantClasses} ${disabledClasses} ${className} relative`}
      onClick={(e) => onClick?.(e)}
      disabled={disabled || isLoading}
      title={title}
      type="button"
    >
      {isLoading ? (
        <span
          className={`inline-block animate-spin rounded-full ${spinnerSize} border-2 ${spinnerColor} border-t-transparent`}
        ></span>
      ) : (
        <Icon name={icon} size={iconSize} fill={fill} />
      )}
    </button>
  );
};

export default IconButton;
