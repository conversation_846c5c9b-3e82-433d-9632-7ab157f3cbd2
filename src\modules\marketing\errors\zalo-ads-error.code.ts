// Error codes for Zalo Ads module
export enum HttpStatus {
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  REQUEST_TIMEOUT = 408,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
}

export class ErrorCode {
  constructor(
    public code: number,
    public message: string,
    public httpStatus: HttpStatus
  ) {}
}

/**
 * Mã lỗi cho Zalo Ads module
 * Phạm vi: 13000-13099
 */
export const ZALO_ADS_ERROR_CODES = {
  // Authentication errors (13000-13009)
  UNAUTHORIZED: new ErrorCode(
    13000,
    'Không có quyền truy cập Zalo Ads API',
    HttpStatus.UNAUTHORIZED
  ),

  ACCESS_TOKEN_EXPIRED: new ErrorCode(
    13001,
    'Access token Zalo Ads đã hết hạn',
    HttpStatus.UNAUTHORIZED
  ),

  REFRESH_TOKEN_INVALID: new ErrorCode(
    13002,
    'Refresh token Zalo Ads không hợp lệ',
    HttpStatus.UNAUTHORIZED
  ),

  OAUTH_CALLBACK_ERROR: new ErrorCode(
    13003,
    'Lỗi trong quá trình OAuth callback',
    HttpStatus.BAD_REQUEST
  ),

  // Account errors (13010-13019)
  ACCOUNT_NOT_FOUND: new ErrorCode(
    13010,
    'Không tìm thấy tài khoản Zalo Ads',
    HttpStatus.NOT_FOUND
  ),

  ACCOUNT_SUSPENDED: new ErrorCode(
    13011,
    'Tài khoản Zalo Ads đã bị tạm ngưng',
    HttpStatus.FORBIDDEN
  ),

  ACCOUNT_INSUFFICIENT_BALANCE: new ErrorCode(
    13012,
    'Tài khoản Zalo Ads không đủ số dư',
    HttpStatus.BAD_REQUEST
  ),

  ACCOUNT_ALREADY_EXISTS: new ErrorCode(
    13013,
    'Tài khoản Zalo Ads đã tồn tại',
    HttpStatus.CONFLICT
  ),

  // Campaign errors (13020-13039)
  CAMPAIGN_NOT_FOUND: new ErrorCode(
    13020,
    'Không tìm thấy chiến dịch quảng cáo',
    HttpStatus.NOT_FOUND
  ),

  CAMPAIGN_INVALID_BUDGET: new ErrorCode(
    13021,
    'Ngân sách chiến dịch không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  CAMPAIGN_INVALID_TARGETING: new ErrorCode(
    13022,
    'Targeting chiến dịch không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  CAMPAIGN_INVALID_SCHEDULE: new ErrorCode(
    13023,
    'Lịch trình chiến dịch không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  CAMPAIGN_CANNOT_DELETE: new ErrorCode(
    13024,
    'Không thể xóa chiến dịch đang chạy',
    HttpStatus.BAD_REQUEST
  ),

  CAMPAIGN_LIMIT_EXCEEDED: new ErrorCode(
    13025,
    'Đã vượt quá giới hạn số lượng chiến dịch',
    HttpStatus.BAD_REQUEST
  ),

  // Creative errors (13040-13049)
  CREATIVE_NOT_FOUND: new ErrorCode(
    13040,
    'Không tìm thấy creative quảng cáo',
    HttpStatus.NOT_FOUND
  ),

  CREATIVE_INVALID_FORMAT: new ErrorCode(
    13041,
    'Định dạng creative không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  CREATIVE_IMAGE_TOO_LARGE: new ErrorCode(
    13042,
    'Kích thước hình ảnh quá lớn',
    HttpStatus.BAD_REQUEST
  ),

  CREATIVE_VIDEO_TOO_LONG: new ErrorCode(
    13043,
    'Video quá dài so với quy định',
    HttpStatus.BAD_REQUEST
  ),

  CREATIVE_CONTENT_REJECTED: new ErrorCode(
    13044,
    'Nội dung creative bị từ chối',
    HttpStatus.BAD_REQUEST
  ),

  // API errors (13050-13069)
  API_ERROR: new ErrorCode(
    13050,
    'Lỗi khi gọi Zalo Ads API',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  API_RATE_LIMIT_EXCEEDED: new ErrorCode(
    13051,
    'Đã vượt quá giới hạn tần suất gọi API',
    HttpStatus.TOO_MANY_REQUESTS
  ),

  API_TIMEOUT: new ErrorCode(
    13052,
    'Timeout khi gọi Zalo Ads API',
    HttpStatus.REQUEST_TIMEOUT
  ),

  API_INVALID_RESPONSE: new ErrorCode(
    13053,
    'Phản hồi từ Zalo Ads API không hợp lệ',
    HttpStatus.BAD_GATEWAY
  ),

  API_SERVICE_UNAVAILABLE: new ErrorCode(
    13054,
    'Dịch vụ Zalo Ads API không khả dụng',
    HttpStatus.SERVICE_UNAVAILABLE
  ),

  // Report errors (13070-13079)
  REPORT_GENERATION_FAILED: new ErrorCode(
    13070,
    'Không thể tạo báo cáo',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  REPORT_DATA_NOT_AVAILABLE: new ErrorCode(
    13071,
    'Dữ liệu báo cáo chưa sẵn sàng',
    HttpStatus.NOT_FOUND
  ),

  REPORT_DATE_RANGE_INVALID: new ErrorCode(
    13072,
    'Khoảng thời gian báo cáo không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  // Validation errors (13080-13089)
  INVALID_OBJECTIVE: new ErrorCode(
    13080,
    'Mục tiêu quảng cáo không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  INVALID_BID_STRATEGY: new ErrorCode(
    13081,
    'Chiến lược đấu giá không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  INVALID_PLACEMENT: new ErrorCode(
    13082,
    'Vị trí hiển thị không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  INVALID_AUDIENCE_SIZE: new ErrorCode(
    13083,
    'Kích thước đối tượng mục tiêu quá nhỏ',
    HttpStatus.BAD_REQUEST
  ),

  // General errors (13090-13099)
  FEATURE_NOT_AVAILABLE: new ErrorCode(
    13090,
    'Tính năng chưa khả dụng',
    HttpStatus.NOT_IMPLEMENTED
  ),

  PERMISSION_DENIED: new ErrorCode(
    13091,
    'Không có quyền thực hiện thao tác này',
    HttpStatus.FORBIDDEN
  ),

  RESOURCE_CONFLICT: new ErrorCode(
    13092,
    'Xung đột tài nguyên',
    HttpStatus.CONFLICT
  ),

  OPERATION_NOT_ALLOWED: new ErrorCode(
    13093,
    'Thao tác không được phép',
    HttpStatus.BAD_REQUEST
  ),

  UNKNOWN_ERROR: new ErrorCode(
    13099,
    'Lỗi không xác định trong Zalo Ads',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
} as const;

/**
 * Helper function để lấy error code từ Zalo Ads API response
 */
export function getZaloAdsErrorCode(apiErrorCode: number): ErrorCode {
  switch (apiErrorCode) {
    case 401:
      return ZALO_ADS_ERROR_CODES.UNAUTHORIZED;
    case 403:
      return ZALO_ADS_ERROR_CODES.PERMISSION_DENIED;
    case 404:
      return ZALO_ADS_ERROR_CODES.ACCOUNT_NOT_FOUND;
    case 429:
      return ZALO_ADS_ERROR_CODES.API_RATE_LIMIT_EXCEEDED;
    case 500:
      return ZALO_ADS_ERROR_CODES.API_ERROR;
    case 503:
      return ZALO_ADS_ERROR_CODES.API_SERVICE_UNAVAILABLE;
    default:
      return ZALO_ADS_ERROR_CODES.UNKNOWN_ERROR;
  }
}
