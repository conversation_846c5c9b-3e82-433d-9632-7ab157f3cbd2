import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Icon, ScrollArea } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';

export interface ModernMenuItem {
  id: string;
  label: string;
  path: string;
  icon: IconName;
  keywords?: string[]; // Danh sách từ khóa để tìm kiếm và điều hướng
}

interface ModernMenuProps {
  items: ModernMenuItem[];
  isOpen: boolean;
  onClose: () => void;
  chatInputWidth?: number | undefined; // Chiều rộng của ô chat input để đồng bộ
  filterText?: string; // Văn bản để lọc các menu item
}

const ModernMenu: React.FC<ModernMenuProps> = ({
  items,
  isOpen,
  onClose,
  chatInputWidth,
  filterText = '',
}) => {
  const { t } = useTranslation(['common', 'chat', 'business', 'integrations', 'calendar', 'marketing', 'data', 'marketplace', 'subscription', 'rpoint', 'affiliate', 'admin', 'aiAgents']);
  const navigate = useNavigate();
  const menuRef = useRef<HTMLDivElement>(null);
  const [menuWidth, setMenuWidth] = useState<number>(350);

  // Lọc các menu item dựa trên filterText
  const filteredItems = filterText.trim()
    ? items.filter(item => {
        const searchText = filterText.toLowerCase();

        // Tìm trong label (cả translation key và translated text)
        const translatedLabel = t(item.label, item.label).toLowerCase();
        if (item.label.toLowerCase().includes(searchText) || translatedLabel.includes(searchText)) return true;

        // Tìm trong path
        if (item.path.toLowerCase().includes(searchText)) return true;

        // Tìm trong keywords nếu có
        if (
          item.keywords &&
          item.keywords.some(keyword => keyword.toLowerCase().includes(searchText))
        )
          return true;

        return false;
      })
    : items;

  // Tính toán width phù hợp cho menu dựa trên chatInputWidth
  const calculateMenuWidth = useCallback(() => {
    // Lấy width của cửa sổ để kiểm tra giới hạn
    const windowWidth = window.innerWidth;

    // Nếu có chatInputWidth, sử dụng nó làm cơ sở
    if (chatInputWidth) {
      // Đảm bảo menu có width bằng với chatInput
      // Không cộng thêm padding vì chúng ta muốn menu có width chính xác bằng với chatInput
      let calculatedWidth = chatInputWidth;

      // Đảm bảo menu không vượt quá width của cửa sổ
      calculatedWidth = Math.min(calculatedWidth, windowWidth - 20);

      // Đảm bảo menu có width tối thiểu
      calculatedWidth = Math.max(calculatedWidth, 300);

      setMenuWidth(calculatedWidth);
    } else {
      // Fallback nếu không có chatInputWidth
      // Tính toán width dựa trên kích thước màn hình
      let fallbackWidth;

      if (windowWidth < 768) {
        // Mobile: Sử dụng gần như toàn bộ width
        fallbackWidth = windowWidth - 40;
      } else if (windowWidth < 1024) {
        // Tablet: Sử dụng 40% viewport width
        fallbackWidth = windowWidth * 0.4;
      } else {
        // Desktop: Sử dụng 30% viewport width
        fallbackWidth = windowWidth * 0.3;
      }

      setMenuWidth(fallbackWidth);
    }
  }, [chatInputWidth]);

  // Cập nhật menuWidth khi chatInputWidth thay đổi
  useEffect(() => {
    calculateMenuWidth();
  }, [chatInputWidth, calculateMenuWidth]);

  // Lắng nghe sự kiện resize của cửa sổ
  useEffect(() => {
    const handleResize = () => {
      calculateMenuWidth();
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('layout-resized', handleResize);
    window.addEventListener('layout-resizing', handleResize);

    // Gọi ngay lập tức khi menu mở
    if (isOpen) {
      handleResize();
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('layout-resized', handleResize);
      window.removeEventListener('layout-resizing', handleResize);
    };
  }, [chatInputWidth, isOpen, calculateMenuWidth]);

  // Kiểm tra vị trí của menu khi nó được hiển thị
  useEffect(() => {
    if (isOpen && menuRef.current) {
      // Đảm bảo menu không bị cắt ở bên phải
      setTimeout(() => {
        if (menuRef.current) {
          const menuRect = menuRef.current.getBoundingClientRect();
          const windowWidth = window.innerWidth;

          // Tìm ChatInputBox container
          const chatInputBox = document.querySelector('.chat-input-box-container');
          const chatInputRect = chatInputBox?.getBoundingClientRect();

          // Kiểm tra nếu menu bị cắt ở bên phải
          if (menuRect.right > windowWidth) {
            // Nếu menu bị cắt ở bên phải, điều chỉnh width
            const newWidth = windowWidth - menuRect.left - 10; // Trừ 10px margin
            if (newWidth >= 300 && Math.abs(newWidth - menuWidth) > 5) {
              setMenuWidth(newWidth);
            }
          }

          // Đảm bảo menu không rộng hơn chatInput container
          if (chatInputRect && menuRect.width !== chatInputRect.width) {
            console.log('Adjusting menu width to match chat input box:', chatInputRect.width);
            setMenuWidth(chatInputRect.width);
          }
        }
      }, 0);
    }
  }, [isOpen, menuWidth]);

  // Xử lý click bên ngoài menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Xử lý khi click vào menu item
  const handleMenuItemClick = (path: string) => {
    navigate(path);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-40" onClick={onClose} />
      <div
        ref={menuRef}
        style={{
          width: `${menuWidth}px`,
          maxHeight: '400px',
          maxWidth: 'calc(100vw - 20px)', // Đảm bảo menu không vượt quá width của cửa sổ
          minWidth: '300px', // Đảm bảo menu có width tối thiểu
        }}
        className="absolute bottom-full left-0 mb-2 z-50 bg-white dark:bg-dark-light rounded-lg shadow-xl overflow-hidden animate-slide-in"
      >
        {/* Menu header with gradient */}
        <div className="bg-menu-gradient p-3 text-white">
          <h3 className="font-medium">{t('common:menu', 'Menu')}</h3>
          <p className="text-xs opacity-80">{t('chat.chooseFeature', 'Chọn tính năng')}</p>
        </div>
        <ScrollArea
          height="auto"
          maxHeight="350px"
          className="p-3"
          autoHide={true}
          invisible={false}
          direction="vertical"
        >
          {filteredItems.length > 0 ? (
            filteredItems.map(item => (
              <div
                key={item.id}
                className="group hover:bg-gray-50 dark:hover:bg-dark-lighter transition-all duration-200 rounded-lg mb-1 last:mb-0 overflow-hidden"
                onClick={() => handleMenuItemClick(item.path)}
              >
                <div className="flex items-center p-3 cursor-pointer relative">
                  {/* Gradient hover effect */}
                  <div className="absolute inset-0 bg-menu-gradient opacity-0 group-hover:opacity-10 transition-all duration-300"></div>

                  {/* Icon container */}
                  <div className="flex-shrink-0 w-10 h-10 rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center mr-3 group-hover:bg-primary/20 dark:group-hover:bg-primary/30 transition-colors relative z-10">
                    <Icon
                      name={item.icon}
                      size="md"
                      className="text-primary transition-transform duration-300 group-hover:scale-110"
                    />
                  </div>

                  {/* Content */}
                  <div className="flex-grow relative z-10">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-800 dark:text-white group-hover:text-primary transition-colors">
                        {t(item.label, item.label)}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400 group-hover:text-primary/70 transition-colors ml-2">
                        {item.path}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              {t('common:noResults', 'Không tìm thấy kết quả')}
            </div>
          )}
        </ScrollArea>
      </div>
    </>
  );
};

export default ModernMenu;
