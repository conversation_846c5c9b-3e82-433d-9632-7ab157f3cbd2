/**
 * Component hiển thị hợp đồng
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/shared/components/common';
import { ContractStepProps } from '../types';
import PDFViewer from './PDFViewer';

const ContractDisplay: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');

  // Demo contract URL - sử dụng URL hợp đồng thực tế
  const contractUrl = data.contractUrl || 'https://cdn.redai.vn/contract/HDRULEBusiness';

  const handleNext = () => {
    onNext({});
  };

  return (
    <div className="w-full">
      {/* PDF Viewer */}
      <div className="mb-8 w-full">
        <PDFViewer
          url={contractUrl}
          base64={data.contractBase64 || ''}
          height="700px"
          showDownload={true}
          className="w-full rounded-lg overflow-hidden"
        />
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract:actions.previous')}
        </Button>

        <Button
          variant="primary"
          onClick={handleNext}
          isLoading={isLoading || false}
        >
          {t('contract:actions.next')}
        </Button>
      </div>
    </div>
  );
};

export default ContractDisplay;
