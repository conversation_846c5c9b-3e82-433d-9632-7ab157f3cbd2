import { useEffect, useCallback } from 'react';

interface UseKeyboardNavigationOptions {
  /**
   * Whether the component is active/visible
   */
  isActive: boolean;

  /**
   * Callback for next action
   */
  onNext?: (() => void) | undefined;

  /**
   * Callback for previous action
   */
  onPrev?: (() => void) | undefined;

  /**
   * Callback for close action
   */
  onClose?: (() => void) | undefined;

  /**
   * Callback for zoom in action
   */
  onZoomIn?: (() => void) | undefined;

  /**
   * Callback for zoom out action
   */
  onZoomOut?: (() => void) | undefined;

  /**
   * Callback for reset zoom action
   */
  onResetZoom?: (() => void) | undefined;
}

/**
 * Hook for handling keyboard navigation
 */
const useKeyboardNavigation = ({
  isActive,
  onNext,
  onPrev,
  onClose,
  onZoomIn,
  onZoomOut,
  onResetZoom,
}: UseKeyboardNavigationOptions): void => {
  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (!isActive) return;

      switch (e.key) {
        case 'ArrowRight':
          if (onNext) {
            e.preventDefault();
            onNext();
          }
          break;
        case 'ArrowLeft':
          if (onPrev) {
            e.preventDefault();
            onPrev();
          }
          break;
        case 'Escape':
          if (onClose) {
            e.preventDefault();
            onClose();
          }
          break;
        case '+':
        case '=':
          if (onZoomIn) {
            e.preventDefault();
            onZoomIn();
          }
          break;
        case '-':
          if (onZoomOut) {
            e.preventDefault();
            onZoomOut();
          }
          break;
        case '0':
          if (onResetZoom) {
            e.preventDefault();
            onResetZoom();
          }
          break;
        default:
          break;
      }
    },
    [isActive, onNext, onPrev, onClose, onZoomIn, onZoomOut, onResetZoom]
  );

  useEffect(() => {
    if (isActive) {
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive, handleKeyDown]);
};

export default useKeyboardNavigation;
