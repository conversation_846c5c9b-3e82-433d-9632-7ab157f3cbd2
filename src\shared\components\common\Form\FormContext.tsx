import { createContext, useContext } from 'react';
import { UseFormReturn } from 'react-hook-form';

// Context để truyền form context xuống các component con
export const FormContext = createContext<UseFormReturn<Record<string, unknown>> | null>(null);

// Hook để sử dụng form context
export const useFormContext = () => {
  const context = useContext(FormContext);
  if (!context) {
    throw new Error('useFormContext must be used within a Form component');
  }
  return context;
};
