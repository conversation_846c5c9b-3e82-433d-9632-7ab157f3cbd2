# API Tạo Sản Phẩm Mới

## Endpoint
```
POST /api/v1/products
```

## Request Body

### Interface: CreateProductDto

```typescript
interface CreateProductDto {
  name: string;                           // Tên sản phẩm (bắt buộc)
  typePrice: PriceTypeEnum;              // Loại giá (bắt buộc)
  price: HasPriceDto | StringPriceDto | null;  // Thông tin giá
  description?: string;                   // Mô tả sản phẩm (tùy chọn)
  tags?: string[];                       // Danh sách tag (tùy chọn)
  shipmentConfig?: ShipmentConfigDto;    // Cấu hình vận chuyển (tùy chọn)
  imagesMediaTypes?: string[];           // Danh sách MIME types của ảnh (tùy chọn)
  classifications?: ProductVariantDto[]; // Danh sách phân loại/biến thể (tùy chọn)
}
```

### Enum: PriceTypeEnum
```typescript
enum PriceTypeEnum {
  HAS_PRICE = 'HAS_PRICE',        // Có giá cố định
  STRING_PRICE = 'STRING_PRICE',  // Giá dạng mô tả
  NO_PRICE = 'NO_PRICE'           // Không có giá
}
```

### Interface: HasPriceDto (khi typePrice = 'HAS_PRICE')
```typescript
interface HasPriceDto {
  listPrice: number;    // Giá niêm yết (bắt buộc, phải > salePrice)
  salePrice: number;    // Giá bán (bắt buộc, phải < listPrice)
  currency: string;     // Đơn vị tiền tệ (bắt buộc, VD: 'VND', 'USD', 'EUR')
}
```

### Interface: StringPriceDto (khi typePrice = 'STRING_PRICE')
```typescript
interface StringPriceDto {
  priceDescription: string;  // Mô tả giá (bắt buộc, VD: "Liên hệ", "Theo thỏa thuận")
}
```

### Interface: ShipmentConfigDto
```typescript
interface ShipmentConfigDto {
  lengthCm?: number;    // Chiều dài (cm)
  widthCm?: number;     // Chiều rộng (cm)
  heightCm?: number;    // Chiều cao (cm)
  weightGram?: number;  // Khối lượng (gram)
}
```

### Interface: ProductVariantDto (cho classifications)
```typescript
interface ProductVariantDto {
  id?: number;                    // ID biến thể (tùy chọn, cho update)
  name: string;                   // Tên biến thể (bắt buộc)
  listPrice: number;              // Giá niêm yết biến thể
  salePrice: number;              // Giá bán biến thể
  currency: string;               // Đơn vị tiền tệ biến thể
  customFields: CustomFieldDto[]; // Trường tùy chỉnh của biến thể
}
```

### Interface: CustomFieldDto
```typescript
interface CustomFieldDto {
  fieldId: number;                    // ID của trường tùy chỉnh
  value: Record<string, unknown>;     // Giá trị trường tùy chỉnh
}
```

## Ví dụ Request Body

### 1. Sản phẩm có giá cố định với ảnh và biến thể
```json
{
  "name": "iPhone 15 Pro",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 30000000,
    "salePrice": 28000000,
    "currency": "VND"
  },
  "description": "iPhone 15 Pro với chip A17 Pro mạnh mẽ",
  "tags": ["smartphone", "apple", "premium"],
  "shipmentConfig": {
    "lengthCm": 15,
    "widthCm": 7.5,
    "heightCm": 0.8,
    "weightGram": 187
  },
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "classifications": [
    {
      "name": "128GB - Titan Tự Nhiên",
      "listPrice": 30000000,
      "salePrice": 28000000,
      "currency": "VND",
      "customFields": [
        {
          "fieldId": 1,
          "value": { "value": "128GB" }
        },
        {
          "fieldId": 2,
          "value": { "value": "Titan Tự Nhiên" }
        }
      ]
    },
    {
      "name": "256GB - Titan Xanh",
      "listPrice": 35000000,
      "salePrice": 33000000,
      "currency": "VND",
      "customFields": [
        {
          "fieldId": 1,
          "value": { "value": "256GB" }
        },
        {
          "fieldId": 2,
          "value": { "value": "Titan Xanh" }
        }
      ]
    }
  ]
}
```

### 2. Sản phẩm có giá dạng mô tả
```json
{
  "name": "Dịch vụ tư vấn IT",
  "typePrice": "STRING_PRICE",
  "price": {
    "priceDescription": "Liên hệ để được báo giá"
  },
  "description": "Dịch vụ tư vấn và triển khai hệ thống IT cho doanh nghiệp",
  "tags": ["consulting", "IT", "enterprise"]
}
```

### 3. Sản phẩm không có giá
```json
{
  "name": "Mẫu sản phẩm miễn phí",
  "typePrice": "NO_PRICE",
  "price": null,
  "description": "Sản phẩm mẫu để khách hàng trải nghiệm",
  "tags": ["sample", "free"]
}
```

## Response

### Interface: CreateProductResponse
```typescript
interface CreateProductResponse {
  id: string;                           // ID sản phẩm đã tạo
  name: string;                         // Tên sản phẩm
  price: HasPriceDto | StringPriceDto | null;  // Thông tin giá
  typePrice: string;                    // Loại giá
  description?: string;                 // Mô tả sản phẩm
  images: Array<{                       // Danh sách ảnh với upload URLs
    key: string;                        // Key của ảnh trên S3
    position: number;                   // Vị trí ảnh
    url: string;                        // URL để upload ảnh lên cloud
  }>;
}
```

### Ví dụ Response
```json
{
  "id": "prod_123456789",
  "name": "iPhone 15 Pro",
  "price": {
    "listPrice": 30000000,
    "salePrice": 28000000,
    "currency": "VND"
  },
  "typePrice": "HAS_PRICE",
  "description": "iPhone 15 Pro với chip A17 Pro mạnh mẽ",
  "images": [
    {
      "key": "products/prod_123456789/image_1.jpg",
      "position": 0,
      "url": "https://s3.amazonaws.com/bucket/upload-url-1"
    },
    {
      "key": "products/prod_123456789/image_2.jpg", 
      "position": 1,
      "url": "https://s3.amazonaws.com/bucket/upload-url-2"
    }
  ]
}
```

## Validation Rules

### Bắt buộc
- `name`: Không được để trống
- `typePrice`: Phải là một trong các giá trị của PriceTypeEnum

### Khi typePrice = 'HAS_PRICE'
- `price.listPrice`: Bắt buộc, phải là số >= 0
- `price.salePrice`: Bắt buộc, phải là số >= 0
- `price.currency`: Bắt buộc, không được để trống
- `price.listPrice` phải lớn hơn `price.salePrice`

### Khi typePrice = 'STRING_PRICE'
- `price.priceDescription`: Bắt buộc, không được để trống

### Khi typePrice = 'NO_PRICE'
- `price`: Phải là null

## Error Responses

### 400 Bad Request - Validation Error
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "name",
      "message": "Tên sản phẩm không được để trống"
    },
    {
      "field": "price.listPrice",
      "message": "Giá niêm yết phải lớn hơn giá bán"
    }
  ]
}
```

### 401 Unauthorized
```json
{
  "statusCode": 401,
  "message": "Unauthorized"
}
```

### 500 Internal Server Error
```json
{
  "statusCode": 500,
  "message": "Internal server error"
}
```

## Notes

1. **Upload Ảnh**: Nếu có `imagesMediaTypes`, backend sẽ tạo pre-signed URLs trong response để frontend upload ảnh trực tiếp lên S3.

2. **Biến thể sản phẩm**: Mỗi biến thể có thể có giá riêng và trường tùy chỉnh riêng.

3. **Trường tùy chỉnh**: Cần tạo trước trong hệ thống, sau đó sử dụng `fieldId` để tham chiếu.

4. **Cấu hình vận chuyển**: Tất cả các trường đều tùy chọn, chỉ gửi những trường có giá trị.

5. **Tags**: Mảng string đơn giản, không có cấu trúc phức tạp.
