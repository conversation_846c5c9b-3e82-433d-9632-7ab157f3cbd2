import React from 'react';
import { Link } from 'react-router-dom';
import { Card, Button } from '@/shared/components/common';

/**
 * Demo Index Page for Generic Data Page System
 */
const GenericDataPageDemoIndex: React.FC = () => {
  const demos = [
    {
      title: 'Tổng quan hệ thống',
      description: 'Xem tổng quan về Generic Data Page System với hướng dẫn sử dụng',
      path: '/demo/generic-data-page',
      color: 'bg-blue-500',
      icon: '📋',
    },
    {
      title: 'Demo: Quản lý người dùng',
      description: 'CRUD đầy đủ với form validation, custom renderers, bulk operations',
      path: '/demo/generic-data-page/users',
      color: 'bg-green-500',
      icon: '👥',
    },
    {
      title: 'Demo: Quản lý sản phẩm',
      description: 'Custom renderers phức tạp, price formatting, stock management',
      path: '/demo/generic-data-page/products',
      color: 'bg-purple-500',
      icon: '📦',
    },
    {
      title: 'Demo: Quản lý đơn hàng',
      description: 'Read-only page với complex data display và custom actions',
      path: '/demo/generic-data-page/orders',
      color: 'bg-orange-500',
      icon: '📋',
    },
    {
      title: 'Demo: Quản lý Media',
      description: 'API thật với file upload, image gallery, media management',
      path: '/demo/generic-data-page/media',
      color: 'bg-red-500',
      icon: '🎬',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            🚀 Generic Data Page System
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
            Hệ thống tạo trang quản lý dữ liệu từ JSON configuration.
            Giảm thiểu code duplication và tăng tốc độ phát triển.
          </p>
        </div>

        {/* Demo Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {demos.map((demo, index) => (
            <Card key={index} className="p-6 hover:shadow-lg transition-all duration-300 hover:scale-105">
              <div className="flex items-start space-x-4">
                <div className={`w-12 h-12 ${demo.color} rounded-lg flex items-center justify-center text-white text-2xl`}>
                  {demo.icon}
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    {demo.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {demo.description}
                  </p>
                  <Link to={demo.path}>
                    <Button variant="primary" size="sm">
                      Xem Demo →
                    </Button>
                  </Link>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Features */}
        <Card className="p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            ✨ Tính năng chính
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              'CRUD Operations đầy đủ',
              'Form validation tự động',
              'Custom renderers',
              'Bulk operations',
              'Search & Filter',
              'Sorting & Pagination',
              'Custom handlers',
              'Type-safe TypeScript',
              'Responsive design',
            ].map((feature, index) => (
              <div key={index} className="flex items-center space-x-2">
                <span className="text-green-500">✅</span>
                <span className="text-gray-700 dark:text-gray-300">{feature}</span>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Start */}
        <Card className="p-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            🚀 Quick Start
          </h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                1. Import component
              </h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <code className="text-sm text-gray-800 dark:text-gray-200">
                  import {`{ GenericDataPage }`} from '@/shared/components/generic';
                </code>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                2. Tạo configuration
              </h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <pre className="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
{`const config: GenericPageConfig = {
  title: 'Quản lý dữ liệu',
  api: { endpoint: '/api/data', ... },
  table: { columns: [...], rowKey: 'id' },
  form: { fields: [...] }
};`}
                </pre>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                3. Sử dụng component
              </h3>
              <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
                <code className="text-sm text-gray-800 dark:text-gray-200">
                  {`<GenericDataPage config={config} />`}
                </code>
              </div>
            </div>
          </div>
        </Card>

        {/* Navigation */}
        <div className="text-center mt-8">
          <Link to="/">
            <Button variant="secondary">
              ← Về trang chủ
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default GenericDataPageDemoIndex;
