/**
 * Component xác thực OTP
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon, OTPInput, Alert } from '@/shared/components/common';
import { ContractStepProps } from '../types';

const OTPVerification: React.FC<ContractStepProps> = ({ data, onNext, onPrevious, isLoading }) => {
  const { t } = useTranslation('contract');
  const [otpValue, setOtpValue] = useState('');
  const [error, setError] = useState('');
  const [countdown, setCountdown] = useState(0);

  // Countdown timer for resend
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [countdown]);

  const handleOtpChange = (otp: string) => {
    setOtpValue(otp);
    setError('');
  };

  const handleOtpComplete = (otp: string) => {
    setOtpValue(otp);
    setError('');
    // Auto verify when OTP is complete
    handleVerify(otp);
  };

  const handleVerify = (otp?: string) => {
    const otpCode = otp || otpValue;

    if (otpCode.length !== 6) {
      setError(t('contract:validation.otpLength'));
      return;
    }

    // Mock verification - in real app, call API
    if (otpCode === '123456') {
      onNext({ otpCode, isCompleted: true });
    } else {
      setError(t('contract:validation.otpIncorrect'));
    }
  };

  const handleResend = () => {
    // Mock resend - in real app, call API
    setCountdown(60);
    setOtpValue('');
    setError('');
  };

  const isOtpComplete = otpValue.length === 6;
  const phoneNumber = data.personalInfo?.phone || data.businessInfo?.companyPhone || '';

  return (
    <div className="w-full bg-background text-foreground">
      <div className="text-center mb-8">
        <Typography variant="h3" className="mb-4">
          {t('contract:otp.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground mb-2">
          {t('contract:otp.instruction')}
        </Typography>
        {phoneNumber && (
          <Typography variant="body2" className="text-primary font-medium">
            {phoneNumber.replace(/(\d{3})(\d{3})(\d{4})/, '$1 *** $3')}
          </Typography>
        )}
      </div>

      <div className="mb-6">
        {/* OTP Input */}
        <div className="mb-6">
          <OTPInput
            length={6}
            value={otpValue}
            onChange={handleOtpChange}
            onComplete={handleOtpComplete}
            onEnterPress={handleVerify}
            autoFocus
            disabled={isLoading}
            className="mb-4"
          />
        </div>

        {/* Error message */}
        {error && (
          <Alert type="error" message={error} className="mb-4" />
        )}

        {/* Resend button */}
        <div className="text-center mb-6">
          {countdown > 0 ? (
            <Typography variant="body2" className="text-muted-foreground">
              {t('contract:otp.resendCountdown', 'Gửi lại mã sau {{countdown}}s', { countdown })}
            </Typography>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResend}
              leftIcon={<Icon name="refresh-cw" />}
              disabled={isLoading}
            >
              {t('contract:otp.resend')}
            </Button>
          )}
        </div>

        {/* Verify button */}
        <Button
          variant="primary"
          fullWidth
          onClick={() => handleVerify()}
          disabled={!isOtpComplete}
          isLoading={isLoading}
        >
          {t('contract:otp.verify')}
        </Button>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isLoading}
        >
          {t('contract:actions.previous')}
        </Button>
      </div>
    </div>
  );
};

export default OTPVerification;
