import React, { useState, useRef, useEffect, useMemo, useCallback, useId } from 'react';
import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import { createPortal } from 'react-dom';

export interface ItemsPerPageSelectProps {
  /**
   * Gi<PERSON> trị hiện tại
   */
  value: number;

  /**
   * Các tùy chọn có sẵn
   */
  options: number[];

  /**
   * Sự kiện khi giá trị thay đổi
   */
  onChange: (value: number) => void;

  /**
   * Vô hiệu hóa bộ chọn
   */
  disabled?: boolean;

  /**
   * Kích thước của bộ chọn
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Vị trí hiển thị dropdown
   * - 'auto': Tự động xác định vị trí dựa trên không gian có sẵn
   * - 'top': <PERSON><PERSON>n hiển thị phía trên
   * - 'bottom': <PERSON><PERSON>n hiển thị phía dưới
   * @default 'auto'
   */
  dropdownPosition?: 'auto' | 'top' | 'bottom';
}

// ID duy nhất cho dropdown
const DROPDOWN_LISTBOX_ID = 'items-per-page-select-listbox';

/**
 * Component bộ chọn số lượng mục trên mỗi trang
 */
const ItemsPerPageSelect: React.FC<ItemsPerPageSelectProps> = ({
  value,
  options,
  onChange,
  disabled = false,
  size = 'md',
  className = '',
  dropdownPosition: propDropdownPosition = 'auto',
}) => {
  // Sử dụng hook theme mới
  useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'bottom' | 'top'>(
    propDropdownPosition === 'top' ? 'top' : 'bottom'
  );
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Xác định các lớp kích thước
  const sizeClasses = useMemo(() => {
    return {
      sm: 'h-7 text-xs',
      md: 'h-9 text-sm',
      lg: 'h-11 text-base',
    }[size];
  }, [size]);

  // Xác định kích thước icon
  const iconSize = useMemo(() => {
    return {
      sm: 'xs',
      md: 'sm',
      lg: 'md',
    }[size] as 'xs' | 'sm' | 'md';
  }, [size]);

  // Xử lý sự kiện nhấp chuột bên ngoài để đóng dropdown
  const handleClickOutside = useCallback((event: MouseEvent) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  }, []);

  // Xử lý sự kiện cuộn trang để đóng dropdown
  const handleScroll = useCallback(
    (event: Event) => {
      // Chỉ đóng dropdown khi scroll xảy ra bên ngoài dropdown
      if (isOpen && event.target instanceof Node) {
        // Kiểm tra xem sự kiện scroll có xảy ra trong dropdown không
        const dropdownElement = document.getElementById(DROPDOWN_LISTBOX_ID);
        if (
          dropdownElement &&
          !dropdownElement.contains(event.target) &&
          event.target !== dropdownElement
        ) {
          setIsOpen(false);
        }
      }
    },
    [isOpen]
  );

  useEffect(() => {
    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('scroll', handleScroll, true);

    // Thêm sự kiện resize để cập nhật vị trí dropdown
    window.addEventListener('resize', () => {
      if (isOpen) {
        // Cập nhật vị trí hoặc đóng dropdown khi resize
        setIsOpen(false);
      }
    });

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', () => {});
    };
  }, [handleClickOutside, handleScroll, isOpen]);

  // Xử lý sự kiện thay đổi giá trị
  const handleChange = useCallback(
    (option: number) => {
      console.log(`ItemsPerPageSelect handleChange: option=${option}`);
      // Đảm bảo sự kiện được gọi với giá trị mới
      if (option !== value) {
        onChange(option);
      }
      setIsOpen(false);
    },
    [onChange, value]
  );

  // Kiểm tra vị trí của dropdown
  const checkDropdownPosition = useCallback(() => {
    // Nếu đã chỉ định vị trí cố định, sử dụng vị trí đó
    if (propDropdownPosition === 'top') {
      setDropdownPosition('top');
      return;
    } else if (propDropdownPosition === 'bottom') {
      setDropdownPosition('bottom');
      return;
    }

    // Nếu là 'auto', tự động xác định vị trí dựa trên không gian có sẵn
    if (buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      const spaceBelow = windowHeight - buttonRect.bottom;
      const optionsHeight = options.length * 40; // Ước tính chiều cao của dropdown

      // Nếu không đủ không gian bên dưới, hiển thị dropdown bên trên
      if (spaceBelow < optionsHeight && buttonRect.top > optionsHeight) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [options.length, propDropdownPosition]);

  // Tính toán vị trí chính xác cho dropdown khi sử dụng Portal
  const getDropdownPosition = useCallback(() => {
    if (!buttonRef.current) return {};

    const buttonRect = buttonRef.current.getBoundingClientRect();

    // Vị trí cơ bản
    const position = {
      width: buttonRect.width,
      left: buttonRect.left,
    };

    // Xác định vị trí theo chiều dọc
    if (dropdownPosition === 'top') {
      return {
        ...position,
        bottom: window.innerHeight - buttonRect.top,
      };
    } else {
      return {
        ...position,
        top: buttonRect.bottom,
      };
    }
  }, [dropdownPosition]);

  // Xử lý sự kiện mở/đóng dropdown
  const handleToggle = useCallback(() => {
    if (!disabled) {
      if (!isOpen) {
        // Kiểm tra vị trí trước khi mở dropdown
        checkDropdownPosition();
      }
      setIsOpen(!isOpen);
    }
  }, [disabled, isOpen, checkDropdownPosition]);

  // Tạo lớp cho nút dropdown
  const buttonClasses = useMemo(() => {
    return [
      'flex items-center justify-between px-3',
      'border-none rounded-md',
      'bg-card',
      'text-foreground',
      disabled
        ? 'opacity-50 cursor-not-allowed'
        : 'cursor-pointer hover:bg-card-muted hover:shadow-sm',
      'transition-all duration-200',
      sizeClasses,
    ].join(' ');
  }, [disabled, sizeClasses]);

  // Tạo lớp cho dropdown
  const dropdownClasses = useMemo(() => {
    // Không cần position classes vì sẽ sử dụng style inline với position: fixed
    return [
      'bg-card',
      'rounded-md',
      'shadow-lg',
      'max-h-60 overflow-y-auto', // Giới hạn chiều cao và cho phép cuộn
      'transition-all duration-200 ease-in-out', // Thêm hiệu ứng chuyển động
      'animate-fadeIn', // Thêm hiệu ứng xuất hiện
      'mt-1', // Margin top nhỏ
    ].join(' ');
  }, []);

  // Tạo ID duy nhất cho dropdown
  const dropdownId = useId();
  const labelId = `${dropdownId}-label`;

  return (
    <div className={`relative inline-block ${className}`} ref={dropdownRef}>
      <button
        ref={buttonRef}
        className={buttonClasses}
        onClick={handleToggle}
        disabled={disabled}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-labelledby={labelId}
        aria-controls={isOpen ? DROPDOWN_LISTBOX_ID : undefined}
        id={dropdownId}
        type="button"
      >
        <span className="mr-2">{value}</span>
        <Icon name="chevron-down" size={iconSize} />
      </button>

      {isOpen &&
        createPortal(
          <div
            className={dropdownClasses}
            role="listbox"
            id={DROPDOWN_LISTBOX_ID}
            aria-labelledby={labelId}
            tabIndex={-1}
            style={{
              position: 'fixed',
              zIndex: 9999,
              ...getDropdownPosition(),
            }}
          >
            {options.map(option => {
              const isSelected = option === value;
              const optionId = `${dropdownId}-option-${option}`;
              const optionClasses = [
                'block w-full border-none text-left px-3 py-2',
                isSelected ? 'bg-card-muted font-medium' : '',
                'hover:bg-card-muted',
                'text-foreground',
                'transition-colors duration-200',
                'focus:outline-none focus:bg-card-muted',
                sizeClasses,
              ].join(' ');

              return (
                <button
                  key={option}
                  id={optionId}
                  className={optionClasses}
                  onClick={() => handleChange(option)}
                  role="option"
                  aria-selected={isSelected}
                  aria-label={`${option} mục trên mỗi trang`}
                  type="button"
                >
                  {option}
                </button>
              );
            })}
          </div>,
          document.body
        )}
      <span id={labelId} className="sr-only">
        Số mục trên mỗi trang
      </span>
    </div>
  );
};

export default ItemsPerPageSelect;
