/**
 * Hooks React Query cho quyền (permissions)
 */
import { useApiQuery, useApiMutation, useApiDeleteMutation } from '@/shared/api/hooks';
import { useQueryClient } from '@tanstack/react-query';
import { PermissionDto } from '../types/employee.types';

// Định nghĩa các query key
export const PERMISSION_QUERY_KEYS = {
  all: ['employee', 'permissions'] as const,
  lists: () => [...PERMISSION_QUERY_KEYS.all, 'list'] as const,
  details: () => [...PERMISSION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...PERMISSION_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách tất cả quyền
 * @returns Query object
 */
export const useAllPermissions = () => {
  return useApiQuery<PermissionDto[]>(PERMISSION_QUERY_KEYS.lists(), '/employee/permissions');
};

/**
 * Hook để lấy thông tin quyền theo ID
 * @param id ID quyền
 * @returns Query object
 */
export const usePermission = (id: number) => {
  return useApiQuery<PermissionDto>(PERMISSION_QUERY_KEYS.detail(id), `/employee/permissions/${id}`);
};

/**
 * DTO cho tạo quyền mới
 */
export interface CreatePermissionDto {
  action: string;
  description: string;
  module: string;
}

/**
 * Hook để tạo quyền mới
 * @returns Mutation object
 */
export const useCreatePermission = () => {
  const queryClient = useQueryClient();

  return useApiMutation<PermissionDto, CreatePermissionDto>('/employee/permissions', {
    onSuccess: () => {
      // Invalidate để load lại danh sách quyền
      queryClient.invalidateQueries({
        queryKey: PERMISSION_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa quyền
 * @param id ID quyền
 * @returns Mutation object
 */
export const useDeletePermission = (id: number) => {
  const queryClient = useQueryClient();

  return useApiDeleteMutation<{ success: boolean }, number>('/employee/permissions', {
    onSuccess: () => {
      // Invalidate để load lại danh sách quyền
      queryClient.invalidateQueries({
        queryKey: PERMISSION_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: PERMISSION_QUERY_KEYS.detail(id),
      });
    },
  });
};
