import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import MainLayout from '@/shared/layouts/MainLayout';
import { Loading } from '@/shared/components/common';

// Lazy load pages
const ContractPrinciplePage = lazy(() => import('../pages/ContractPrinciplePage'));

/**
 * Contract module routes
 */
const contractRoutes: RouteObject[] = [
  {
    path: '/contract/principle',
    element: (
      <MainLayout title="Hợp đồng nguyên tắc">
        <Suspense fallback={<Loading />}>
          <ContractPrinciplePage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default contractRoutes;
