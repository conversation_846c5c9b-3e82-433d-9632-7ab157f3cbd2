/**
 * Settings Module Types
 */

export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
}

export interface ChatKeyword {
  id: string;
  keyword: string;
  path: string;
  description?: string;
  enabled: boolean;
}

export interface SettingsState {
  // Theme settings (sử dụng ThemeContext, không cần lưu trong Redux)
  
  // Timezone settings
  timezone: string;
  
  // Chat keywords settings
  chatKeywords: ChatKeyword[];
  customKeywords: ChatKeyword[];
  
  // General settings
  isLoading: boolean;
  error: string | null;
}

export interface SettingsFormData {
  timezone: string;
  chatKeywords: ChatKeyword[];
}

// Predefined timezones
export const TIMEZONE_OPTIONS: TimezoneOption[] = [
  {
    value: 'Asia/Ho_Chi_Minh',
    label: 'Việt Nam (GMT+7)',
    offset: '+07:00'
  },
  {
    value: 'Asia/Shanghai',
    label: 'Trung Quốc (GMT+8)',
    offset: '+08:00'
  },
  {
    value: 'Asia/Tokyo',
    label: '<PERSON><PERSON><PERSON><PERSON> (GMT+9)',
    offset: '+09:00'
  },
  {
    value: 'Europe/London',
    label: 'London (GMT+0)',
    offset: '+00:00'
  },
  {
    value: 'Europe/Paris',
    label: 'Paris (GMT+1)',
    offset: '+01:00'
  },
  {
    value: 'America/New_York',
    label: 'New York (GMT-5)',
    offset: '-05:00'
  },
  {
    value: 'America/Los_Angeles',
    label: 'Los Angeles (GMT-8)',
    offset: '-08:00'
  },
  {
    value: 'Australia/Sydney',
    label: 'Sydney (GMT+11)',
    offset: '+11:00'
  }
];

// Default chat keywords (từ menu-items.ts)
export const DEFAULT_CHAT_KEYWORDS: ChatKeyword[] = [
  {
    id: 'home',
    keyword: 'trang chủ',
    path: '/',
    description: 'Điều hướng về trang chủ',
    enabled: true
  },
  {
    id: 'ai-agents',
    keyword: 'ai agents',
    path: '/ai-agents',
    description: 'Điều hướng đến trang AI Agents',
    enabled: true
  },
  {
    id: 'workflows',
    keyword: 'workflows',
    path: '/workflows',
    description: 'Điều hướng đến trang Workflows',
    enabled: true
  },
  {
    id: 'components',
    keyword: 'components',
    path: '/components',
    description: 'Điều hướng đến trang Components',
    enabled: true
  },
  {
    id: 'settings',
    keyword: 'settings',
    path: '/settings',
    description: 'Điều hướng đến trang Settings',
    enabled: true
  }
];
