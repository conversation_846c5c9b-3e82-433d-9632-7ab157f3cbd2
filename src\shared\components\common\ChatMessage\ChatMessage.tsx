import React from 'react';

export interface ChatMessageProps {
  /**
   * Nội dung tin nhắn
   */
  content: React.ReactNode;

  /**
   * Người gửi tin nhắn ('user' hoặc 'ai')
   */
  sender: 'user' | 'ai';

  /**
   * Thời gian gửi tin nhắn
   */
  timestamp: Date;

  /**
   * URL avatar (chỉ hiển thị cho tin nhắn từ AI)
   */
  avatar?: string;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị tin nhắn trong chat
 */
const ChatMessage: React.FC<ChatMessageProps> = props => {
  const { content, sender, timestamp, className = '' } = props;
  // Format timestamp
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div
      className={`flex flex-col ${sender === 'user' ? 'items-end' : 'items-start'} ${className}`}
    >
      {/* Avatar chỉ hiển thị cho AI */}
      {sender === 'ai' && (
        <div className="mb-1 self-start">
          <div className="w-6 h-6">
            <img
              src="/assets/images/ai-agents/assistant-robot.svg"
              alt="AI Assistant"
              className="w-full h-full rounded-full"
            />
          </div>
        </div>
      )}

      {/* Nội dung tin nhắn */}
      <div className={`max-w-[90%] min-w-0`}>
        <div
          className={`p-3 rounded-lg break-words overflow-hidden ${
            sender === 'user'
              ? 'bg-primary text-white rounded-tr-none'
              : 'bg-gray-100 dark:bg-dark-light rounded-tl-none'
          }`}
          style={{
            wordBreak: 'break-word',
            overflowWrap: 'break-word',
            hyphens: 'auto'
          }}
        >
          <div className="whitespace-pre-wrap break-words overflow-hidden">
            {content}
          </div>
        </div>

        <div
          className={`text-xs text-gray-500 mt-1 ${sender === 'user' ? 'text-right' : 'text-left'}`}
        >
          {formatTime(timestamp)}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;
