import React, { useState, useCallback } from 'react';

export interface SliderProps {
  /**
   * Gi<PERSON> trị hiện tại của slider
   */
  value: number;

  /**
   * Gi<PERSON> trị tối thiểu
   */
  min: number;

  /**
   * Gi<PERSON> trị tối đa
   */
  max: number;

  /**
   * B<PERSON>ớc nhả<PERSON>
   */
  step?: number;

  /**
   * Callback khi giá trị thay đổi
   */
  onValueChange: (value: number) => void;

  /**
   * Hiển thị giá trị
   */
  showValue?: boolean;

  /**
   * Hậu tố cho giá trị hiển thị
   */
  valueSuffix?: string;

  /**
   * Tiền tố cho giá trị hiển thị
   */
  valuePrefix?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * ID
   */
  id?: string;

  /**
   * Name
   */
  name?: string;
}

/**
 * Component Slider cho phép người dùng chọn giá trị trong một khoảng
 */
const Slider: React.FC<SliderProps> = ({
  value,
  min,
  max,
  step = 1,
  onValueChange,
  showValue = true,
  valueSuffix = '',
  valuePrefix = '',
  disabled = false,
  className = '',
  id,
  name,
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // Tính toán phần trăm vị trí của slider
  const percentage = ((value - min) / (max - min)) * 100;

  // Xử lý thay đổi giá trị
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = parseFloat(e.target.value);
      onValueChange(newValue);
    },
    [onValueChange]
  );

  // Xử lý khi bắt đầu kéo
  const handleMouseDown = useCallback(() => {
    setIsDragging(true);
  }, []);

  // Xử lý khi kết thúc kéo
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div className={`slider-container ${className}`}>
      <div className="relative">
        {/* Track */}
        <div className="slider-track h-2 bg-gray-200 dark:bg-gray-700 rounded-full relative">
          {/* Progress */}
          <div
            className="slider-progress h-full bg-primary rounded-full transition-all duration-150"
            style={{ width: `${percentage}%` }}
          />
          
          {/* Thumb */}
          <div
            className={`slider-thumb absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-4 h-4 bg-white border-2 border-primary rounded-full shadow-md cursor-pointer transition-all duration-150 ${
              isDragging ? 'scale-110' : 'hover:scale-105'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            style={{ left: `${percentage}%` }}
          />
        </div>

        {/* Hidden input */}
        <input
          type="range"
          id={id}
          name={name}
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={handleChange}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          disabled={disabled}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
        />
      </div>

      {/* Value display */}
      {showValue && (
        <div className="flex justify-between items-center mt-2 text-sm text-gray-600 dark:text-gray-400">
          <span>{min}{valueSuffix}</span>
          <span className="font-medium text-gray-900 dark:text-gray-100">
            {valuePrefix}{value}{valueSuffix}
          </span>
          <span>{max}{valueSuffix}</span>
        </div>
      )}
    </div>
  );
};

export default Slider;
