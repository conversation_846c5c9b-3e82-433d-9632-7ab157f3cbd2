import { useState, useEffect, useRef, useCallback } from 'react';

interface UseLazyLoadOptions {
  /**
   * Root margin for Intersection Observer
   */
  rootMargin?: string;

  /**
   * Threshold for Intersection Observer
   */
  threshold?: number | number[];

  /**
   * Placeholder image to show while loading
   */
  placeholderSrc?: string;
}

interface UseLazyLoadResult {
  /**
   * Ref to attach to the image container
   */
  ref: React.RefObject<HTMLElement>;

  /**
   * Whether the image is in view
   */
  inView: boolean;

  /**
   * Whether the image has been loaded
   */
  isLoaded: boolean;

  /**
   * Source to use for the image
   */
  imageSrc: string;

  /**
   * Handle image load event
   */
  onLoad: () => void;
}

/**
 * Hook for lazy loading images
 */
const useLazyLoad = (src: string, options: UseLazyLoadOptions = {}): UseLazyLoadResult => {
  const {
    rootMargin = '200px 0px',
    threshold = 0.1,
    placeholderSrc = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9IiNlMmUyZTIiLz48L3N2Zz4=',
  } = options;

  const [inView, setInView] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const ref = useRef<HTMLElement>(null);

  // Set up intersection observer to detect when image is in viewport
  useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            setInView(true);
            observer.unobserve(element);
          }
        });
      },
      { rootMargin, threshold }
    );

    observer.observe(element);

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, [rootMargin, threshold]);

  // Handle image load event
  const onLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  // Determine which source to use
  const imageSrc = inView ? src : placeholderSrc;

  return { ref, inView, isLoaded, imageSrc, onLoad };
};

export default useLazyLoad;
