/**
 * Provider Validation Service
 */

import { apiClient } from '@/shared/api/axios';

export interface ValidationResult {
  isValid: boolean;
  message: string;
  details?: Record<string, unknown>;
}

export interface ApiKeyValidationRequest {
  providerId: string;
  apiKey: string;
  additionalData?: Record<string, unknown>;
}

export interface DomainValidationRequest {
  providerId: string;
  domain: string;
}

export interface ConnectionHealthCheck {
  providerId: string;
  configurationId: string;
}

export interface ProviderStatus {
  providerId: string;
  isHealthy: boolean;
  lastChecked: string;
  responseTime?: number;
  errorMessage?: string;
}

/**
 * Real-time API Key Validation Service
 */
export class ApiKeyValidationService {
  /**
   * Validate SendGrid API Key
   */
  static async validateSendGridApiKey(apiKey: string): Promise<ValidationResult> {
    try {
      const response = await apiClient.post<ValidationResult>('/integration/email/validate/sendgrid-api-key', {
        apiKey
      });
      return response.result;
    } catch (error) {
      return {
        isValid: false,
        message: 'Failed to validate SendGrid API Key',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Validate Mailgun API Key and Domain
   */
  static async validateMailgunCredentials(apiKey: string, domain: string): Promise<ValidationResult> {
    try {
      const response = await apiClient.post<ValidationResult>('/integration/email/validate/mailgun-credentials', {
        apiKey,
        domain
      });
      return response.result;
    } catch (error) {
      return {
        isValid: false,
        message: 'Failed to validate Mailgun credentials',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Validate Amazon SES Credentials
   */
  static async validateAmazonSESCredentials(
    accessKeyId: string,
    secretAccessKey: string,
    region: string
  ): Promise<ValidationResult> {
    try {
      const response = await apiClient.post<ValidationResult>('/integration/email/validate/amazon-ses-credentials', {
        accessKeyId,
        secretAccessKey,
        region
      });
      return response.result;
    } catch (error) {
      return {
        isValid: false,
        message: 'Failed to validate Amazon SES credentials',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Generic API Key Validation
   */
  static async validateApiKey(request: ApiKeyValidationRequest): Promise<ValidationResult> {
    switch (request.providerId) {
      case 'sendgrid':
        return this.validateSendGridApiKey(request.apiKey);
      
      case 'mailgun':
        if (!request.additionalData?.domain) {
          return {
            isValid: false,
            message: 'Domain is required for Mailgun validation'
          };
        }
        return this.validateMailgunCredentials(request.apiKey, request.additionalData.domain as string);
      
      case 'amazon-ses':
        if (!request.additionalData?.secretAccessKey || !request.additionalData?.region) {
          return {
            isValid: false,
            message: 'Secret Access Key and Region are required for Amazon SES validation'
          };
        }
        return this.validateAmazonSESCredentials(
          request.apiKey, // Access Key ID
          request.additionalData.secretAccessKey as string,
          request.additionalData.region as string
        );
      
      default:
        return {
          isValid: false,
          message: `API Key validation not supported for provider: ${request.providerId}`
        };
    }
  }
}

/**
 * Domain Verification Service
 */
export class DomainValidationService {
  /**
   * Check domain verification status
   */
  static async checkDomainVerification(request: DomainValidationRequest): Promise<ValidationResult> {
    try {
      const response = await apiClient.post<ValidationResult>('/integration/email/validate/domain-verification', {
        providerId: request.providerId,
        domain: request.domain
      });
      return response.result;
    } catch (error) {
      return {
        isValid: false,
        message: 'Failed to check domain verification',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      };
    }
  }

  /**
   * Get domain verification instructions
   */
  static async getDomainVerificationInstructions(
    providerId: string,
    domain: string
  ): Promise<{ instructions: string[]; dnsRecords?: Record<string, string> }> {
    try {
      const response = await apiClient.get<{ instructions: string[]; dnsRecords?: Record<string, string> }>('/integration/email/domain-verification-instructions', {
        params: { providerId, domain }
      });
      return response.result;
    } catch {
      throw new Error('Failed to get domain verification instructions');
    }
  }
}

/**
 * Connection Health Check Service
 */
export class ConnectionHealthService {
  /**
   * Perform health check on email provider connection
   */
  static async checkConnectionHealth(request: ConnectionHealthCheck): Promise<ProviderStatus> {
    try {
      const startTime = Date.now();
      const response = await apiClient.post<{ isHealthy: boolean; errorMessage?: string }>('/integration/email/health-check', {
        providerId: request.providerId,
        configurationId: request.configurationId
      });
      const responseTime = Date.now() - startTime;

      return {
        providerId: request.providerId,
        isHealthy: response.result.isHealthy,
        lastChecked: new Date().toISOString(),
        responseTime,
        errorMessage: response.result.errorMessage
      };
    } catch (error) {
      return {
        providerId: request.providerId,
        isHealthy: false,
        lastChecked: new Date().toISOString(),
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get health status for multiple providers
   */
  static async getMultipleProviderStatus(configurationIds: string[]): Promise<ProviderStatus[]> {
    try {
      const response = await apiClient.post<ProviderStatus[]>('/integration/email/bulk-health-check', {
        configurationIds
      });
      return response.result;
    } catch {
      throw new Error('Failed to get provider status');
    }
  }
}

/**
 * Provider-specific Error Messages
 */
export class ProviderErrorService {
  private static readonly ERROR_MESSAGES: Record<string, Record<string, string>> = {
    sendgrid: {
      'invalid_api_key': 'API Key không hợp lệ. Vui lòng kiểm tra lại API Key từ SendGrid Dashboard.',
      'insufficient_permissions': 'API Key không có quyền gửi email. Vui lòng tạo API Key với quyền "Mail Send".',
      'rate_limit_exceeded': 'Đã vượt quá giới hạn rate limit của SendGrid.',
      'account_suspended': 'Tài khoản SendGrid đã bị tạm ngưng.'
    },
    mailgun: {
      'invalid_api_key': 'API Key không hợp lệ. Vui lòng kiểm tra Private API Key từ Mailgun Dashboard.',
      'domain_not_verified': 'Domain chưa được verify trong Mailgun. Vui lòng verify domain trước.',
      'invalid_domain': 'Domain không hợp lệ hoặc không tồn tại trong tài khoản Mailgun.',
      'insufficient_permissions': 'API Key không có quyền truy cập domain này.'
    },
    'amazon-ses': {
      'invalid_credentials': 'AWS credentials không hợp lệ. Vui lòng kiểm tra Access Key và Secret Key.',
      'region_mismatch': 'Region không khớp với cấu hình SES.',
      'email_not_verified': 'Email address chưa được verify trong Amazon SES.',
      'sandbox_mode': 'Tài khoản SES đang ở sandbox mode. Vui lòng request production access.',
      'insufficient_permissions': 'IAM User không có quyền SES cần thiết.'
    },
    gmail: {
      'invalid_app_password': 'App Password không hợp lệ. Vui lòng tạo App Password mới.',
      'two_factor_required': 'Cần bật 2-Step Verification để sử dụng App Password.',
      'oauth_token_expired': 'OAuth token đã hết hạn. Vui lòng xác thực lại.',
      'insufficient_scope': 'OAuth token không có quyền gửi email.'
    },
    outlook: {
      'invalid_password': 'Mật khẩu không hợp lệ. Vui lòng kiểm tra lại.',
      'oauth_token_expired': 'OAuth token đã hết hạn. Vui lòng xác thực lại.',
      'account_locked': 'Tài khoản Microsoft đã bị khóa.',
      'two_factor_required': 'Cần xác thực 2 bước để truy cập.'
    }
  };

  /**
   * Get user-friendly error message
   */
  static getErrorMessage(providerId: string, errorCode: string): string {
    const providerMessages = this.ERROR_MESSAGES[providerId];
    if (providerMessages && providerMessages[errorCode]) {
      return providerMessages[errorCode];
    }
    
    return `Lỗi không xác định: ${errorCode}`;
  }

  /**
   * Get troubleshooting suggestions
   */
  static getTroubleshootingSuggestions(providerId: string, errorCode: string): string[] {
    const suggestions: Record<string, Record<string, string[]>> = {
      sendgrid: {
        'invalid_api_key': [
          'Kiểm tra API Key đã copy đúng chưa',
          'Đảm bảo API Key bắt đầu với "SG."',
          'Tạo API Key mới nếu cần thiết'
        ]
      },
      mailgun: {
        'domain_not_verified': [
          'Vào Mailgun Dashboard → Domains',
          'Thêm DNS records theo hướng dẫn',
          'Đợi DNS propagation (có thể mất vài giờ)'
        ]
      }
    };

    const providerSuggestions = suggestions[providerId];
    if (providerSuggestions && providerSuggestions[errorCode]) {
      return providerSuggestions[errorCode];
    }

    return ['Vui lòng kiểm tra lại cấu hình và thử lại'];
  }
}
