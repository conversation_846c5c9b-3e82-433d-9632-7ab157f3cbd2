import { useState, useRef, useEffect, ReactNode, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import { Icon } from '@/shared/components/common';
import { SelectOption as SelectOptionType, SelectGroup as SelectGroupType } from './Select';

// Định nghĩa keyframes animation đơn giản
const dropdownAnimationStyle = `
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
`;

export interface SearchableSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | number;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | number) => void;

  /**
   * Options
   */
  options: (SelectOptionType | SelectGroupType)[];

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Loading
   */
  loading?: boolean;

  /**
   * Custom rendering cho option
   */
  renderOption?: (option: SelectOptionType) => ReactNode;

  /**
   * Custom rendering cho giá trị đã chọn
   */
  renderValue?: (value: string | number, options: SelectOptionType[]) => ReactNode;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Placeholder cho ô tìm kiếm
   */
  searchPlaceholder?: string;

  /**
   * Thông báo khi không có kết quả
   */
  noResultsMessage?: string;

  /**
   * Cho phép tìm kiếm trực tiếp trên thanh select
   */
  inlineSearch?: boolean;
}

/**
 * Component SearchableSelect với tính năng tìm kiếm nâng cao
 */
const SearchableSelect = forwardRef<HTMLInputElement, SearchableSelectProps>(
  (
    {
      value,
      onChange,
      options = [],
      placeholder = '',
      label,
      disabled = false,
      loading = false,
      renderOption,
      renderValue,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      searchPlaceholder,
      noResultsMessage,
      inlineSearch = false,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme();
    const [isOpen, setIsOpen] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [selectedValue, setSelectedValue] = useState<string | number | undefined>(value);
    const [isSearchMode, setIsSearchMode] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValue when value prop changes
    useEffect(() => {
      setSelectedValue(value);
    }, [value]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
          setIsSearchMode(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Focus search input when dropdown opens
    useEffect(() => {
      if (isOpen && isSearchMode && searchInputRef.current) {
        searchInputRef.current.focus();
      }

      // Reset search mode when dropdown closes
      if (!isOpen) {
        setIsSearchMode(false);
        setSearchText('');
      }
    }, [isOpen, isSearchMode]);

    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      // Tìm option đã chọn để log thông tin
      const selectedOption = flattenedOptions.find(opt => opt.value === optionValue);
      console.log('Option clicked:', optionValue);
      console.log('Selected option data:', selectedOption);

      // Lưu trữ URL hình ảnh vào localStorage
      if (selectedOption) {
        let imageUrl = selectedOption.imageUrl;

        // Nếu không có imageUrl trực tiếp, kiểm tra trong data
        if (!imageUrl && selectedOption.data && typeof selectedOption.data === 'object') {
          if ('imageUrl' in selectedOption.data) {
            imageUrl = String(selectedOption.data['imageUrl']);
          } else if ('image' in selectedOption.data) {
            imageUrl = String(selectedOption.data['image']);
          }
        }

        if (imageUrl) {
          try {
            localStorage.setItem(`select-option-image-${optionValue}`, imageUrl);
            console.log('Saved image URL to localStorage:', imageUrl);
          } catch (e) {
            console.error('Failed to save image URL to localStorage:', e);
          }
        }
      }

      setSelectedValue(optionValue);
      setIsOpen(false);
      setSearchText('');
      setIsSearchMode(false);

      // Call onChange with the new value
      if (onChange) {
        onChange(optionValue);
      }

      // Force re-render để đảm bảo hiển thị logo
      setTimeout(() => {
        const forceUpdate = Date.now();
        console.log('Force update:', forceUpdate);
      }, 0);
    };

    // Handle clear selection
    const handleClearSelection = (e: React.MouseEvent) => {
      e.stopPropagation();
      setSelectedValue(undefined);
      setSearchText('');
      setIsSearchMode(true);

      // Call onChange with empty string value
      if (onChange) {
        onChange('' as unknown as string | number);
      }

      // Focus the search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    };

    // Flatten options for easier access
    const flattenedOptions = options.flatMap(opt => ('options' in opt ? opt.options : [opt]));

    // Log flattened options for debugging
    useEffect(() => {
      if (selectedValue !== undefined) {
        const selectedOption = flattenedOptions.find(opt => opt.value === selectedValue);
        console.log('Selected value:', selectedValue);
        console.log('Flattened options:', flattenedOptions);
        console.log('Found selected option:', selectedOption);
      }
    }, [selectedValue, flattenedOptions]);

    // Filter options based on search text
    const filterOptions = (options: (SelectOptionType | SelectGroupType)[]) => {
      if (!searchText) return options;

      return options
        .map(option => {
          if ('options' in option) {
            // Filter group options
            const filteredOptions = option.options.filter(opt =>
              opt.label.toLowerCase().includes(searchText.toLowerCase())
            );

            return filteredOptions.length > 0 ? { ...option, options: filteredOptions } : null;
          } else {
            // Filter single option
            return option.label.toLowerCase().includes(searchText.toLowerCase()) ? option : null;
          }
        })
        .filter(Boolean) as (SelectOptionType | SelectGroupType)[];
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedValue === undefined) return placeholder;

      // Không sử dụng renderValue ở đây để tránh xung đột với phần render bên dưới
      const selectedOption = flattenedOptions.find(opt => opt.value === selectedValue);
      return selectedOption ? selectedOption.label : selectedValue.toString();
    };

    // Render selected value with logo if available
    const renderSelectedValue = () => {
      if (selectedValue === undefined) return placeholder;

      const selectedOption = flattenedOptions.find(opt => opt.value === selectedValue);
      if (!selectedOption) return selectedValue.toString();

      // Check if option has direct imageUrl or image in data
      let imageUrl = selectedOption.imageUrl;

      // Nếu không có imageUrl trực tiếp, kiểm tra trong data
      if (!imageUrl && selectedOption.data && typeof selectedOption.data === 'object') {
        if ('imageUrl' in selectedOption.data) {
          imageUrl = String(selectedOption.data['imageUrl']);
        } else if ('image' in selectedOption.data) {
          imageUrl = String(selectedOption.data['image']);
        }
      }

      console.log('Rendering selected value:', selectedValue);
      console.log('Selected option:', selectedOption);
      console.log('Image URL:', imageUrl);

      // Tạo một bản sao của selectedOption để tránh bị thay đổi
      const optionCopy = { ...selectedOption };

      // Lưu trữ URL hình ảnh vào localStorage để đảm bảo nó luôn có sẵn
      if (imageUrl) {
        try {
          localStorage.setItem(`select-option-image-${selectedValue}`, imageUrl);
        } catch (e) {
          console.error('Failed to save image URL to localStorage:', e);
        }
      }

      // Lấy URL hình ảnh từ localStorage nếu không có trong data
      const cachedImageUrl = !imageUrl
        ? localStorage.getItem(`select-option-image-${selectedValue}`)
        : undefined;

      // Sử dụng URL hình ảnh từ data hoặc từ localStorage
      const finalImageUrl = imageUrl || cachedImageUrl;

      // Always return a div with flex layout to ensure consistent rendering
      return (
        <div className="flex items-center">
          {finalImageUrl ? (
            <div className="w-6 h-6 mr-2.5 flex-shrink-0 border border-border/30 rounded-sm overflow-hidden bg-white flex items-center justify-center">
              <img
                src={finalImageUrl}
                alt={optionCopy.label}
                className="w-5 h-5 object-contain"
                loading="eager"
                onError={e => {
                  console.log('Image load error:', e);
                  // Fallback if image fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                  // Hiển thị fallback khi lỗi
                  const parent = (e.target as HTMLImageElement).parentElement;
                  if (parent) {
                    parent.innerHTML = `<span class="text-xs font-bold text-primary">${optionCopy.label.charAt(0)}</span>`;
                    parent.classList.add('bg-primary/10');
                  }
                }}
              />
            </div>
          ) : (
            // Fallback icon if no image
            <div className="w-6 h-6 mr-2.5 flex-shrink-0 bg-primary/10 rounded-sm flex items-center justify-center">
              <span className="text-xs font-bold text-primary">{optionCopy.label.charAt(0)}</span>
            </div>
          )}
          <span className="truncate font-medium">{optionCopy.label}</span>
        </div>
      );
    };

    // Render options
    const renderOptions = () => {
      const filteredOptions = filterOptions(options);

      if (filteredOptions.length === 0) {
        return (
          <div className="px-4 py-6 text-sm text-muted flex flex-col items-center justify-center space-y-2">
            <Icon name="search" size="lg" className="text-muted/50" />
            <div>{noResultsMessage || t('common.noResults', 'No results found')}</div>
          </div>
        );
      }

      return filteredOptions.map((option, index) => {
        if ('options' in option) {
          // Render group
          return (
            <div key={`group-${index}`} className="mb-1 last:mb-0">
              <div className="px-3 py-1.5 text-xs font-medium text-muted uppercase tracking-wider bg-card-muted/50">
                {option.label}
              </div>
              <div className="py-1">
                {option.options.map(groupOption => renderSingleOption(groupOption))}
              </div>
            </div>
          );
        } else {
          // Render single option
          return renderSingleOption(option);
        }
      });
    };

    // Render single option
    const renderSingleOption = (option: SelectOptionType) => {
      const isSelected = option.value === selectedValue;

      if (renderOption) {
        return (
          <div
            key={`option-${option.value}`}
            onClick={() => !option.disabled && handleOptionClick(option.value)}
            className={`
              px-3 py-2 mx-1 my-0.5 rounded-md
              relative
              ${
                isSelected
                  ? 'bg-primary/10 text-primary font-medium'
                  : 'hover:bg-card-muted hover:text-primary'
              }
              ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
            `}
            role="option"
            aria-selected={isSelected}
          >
            {renderOption(option)}
            {isSelected && !option.disabled && (
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                <Icon name="check" size="sm" className="text-primary" />
              </div>
            )}
          </div>
        );
      }

      // Custom rendering for standard options
      // Lấy URL hình ảnh từ option
      let imageUrl = option.imageUrl;

      // Nếu không có imageUrl trực tiếp, kiểm tra trong data
      if (!imageUrl && option.data && typeof option.data === 'object') {
        if ('imageUrl' in option.data) {
          imageUrl = String(option.data['imageUrl']);
        } else if ('image' in option.data) {
          imageUrl = String(option.data['image']);
        }
      }

      return (
        <div
          key={`option-${option.value}`}
          onClick={() => !option.disabled && handleOptionClick(option.value)}
          className={`
            px-3 py-2 mx-1 my-0.5 rounded-md
            flex items-center relative
            ${
              isSelected
                ? 'bg-primary/10 text-primary font-medium'
                : 'hover:bg-card-muted hover:text-primary'
            }
            ${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          `}
          role="option"
          aria-selected={isSelected}
        >
          {option.icon && <span className="mr-2 flex-shrink-0">{option.icon}</span>}

          {/* Hiển thị hình ảnh nếu có */}
          {imageUrl && !option.icon && (
            <div className="w-6 h-6 mr-2.5 flex-shrink-0 border border-border/30 rounded-sm overflow-hidden bg-white flex items-center justify-center">
              <img
                src={imageUrl}
                alt={option.label}
                className="w-5 h-5 object-contain"
                loading="eager"
                onError={e => {
                  console.log('Option image load error:', e);
                  // Fallback if image fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                  // Hiển thị fallback khi lỗi
                  const parent = (e.target as HTMLImageElement).parentElement;
                  if (parent) {
                    parent.innerHTML = `<span class="text-xs font-bold text-primary">${option.label.charAt(0)}</span>`;
                    parent.classList.add('bg-primary/10');
                  }
                }}
              />
            </div>
          )}

          <span className="flex-grow truncate">{option.label}</span>
          {isSelected && (
            <Icon name="check" size="sm" className="ml-2 text-primary flex-shrink-0" />
          )}
        </div>
      );
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Thêm style animation */}
        <style>{dropdownAnimationStyle}</style>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={selectedValue !== undefined ? selectedValue.toString() : ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium text-foreground mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
            flex items-center justify-between px-3
            border rounded-md bg-card-muted text-foreground
            ${sizeClasses}
            ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
            ${error ? 'border-error' : 'border-border'}
            ${isOpen ? 'ring-2 ring-primary/30' : ''}
            ${fullWidth ? 'w-full' : ''}
            ${isSearchMode ? 'pl-3' : ''}
          `}
          onClick={() => {
            if (!disabled && !loading) {
              setIsOpen(!isOpen);
            }
          }}
          onDoubleClick={() => {
            if (!disabled && !loading) {
              setIsSearchMode(true);
              setIsOpen(true);
              // Focus vào input sau khi double click
              setTimeout(() => {
                searchInputRef.current?.focus();
              }, 0);
            }
          }}
        >
          {/* Không hiển thị biểu tượng tìm kiếm nữa */}

          {(inlineSearch && !isOpen) || isSearchMode ? (
            <div className="flex items-center flex-grow">
              {/* Hiển thị hình ảnh bên cạnh ô input khi có selectedValue */}
              {selectedValue !== undefined && !isSearchMode && (
                <>
                  {(() => {
                    // Tìm option đã chọn
                    const selectedOption = flattenedOptions.find(
                      opt => opt.value === selectedValue
                    );
                    if (!selectedOption) return null;

                    // Lấy URL hình ảnh
                    let imageUrl = selectedOption.imageUrl;

                    // Nếu không có imageUrl trực tiếp, kiểm tra trong data
                    if (
                      !imageUrl &&
                      selectedOption.data &&
                      typeof selectedOption.data === 'object'
                    ) {
                      if ('imageUrl' in selectedOption.data) {
                        imageUrl = String(selectedOption.data['imageUrl']);
                      } else if ('image' in selectedOption.data) {
                        imageUrl = String(selectedOption.data['image']);
                      }
                    }

                    // Lấy URL hình ảnh từ localStorage nếu không có trong data
                    const cachedImageUrl = !imageUrl
                      ? localStorage.getItem(`select-option-image-${selectedValue}`)
                      : undefined;

                    // Sử dụng URL hình ảnh từ data hoặc từ localStorage
                    const finalImageUrl = imageUrl || cachedImageUrl;

                    if (finalImageUrl) {
                      return (
                        <div className="w-6 h-6 mr-2.5 flex-shrink-0 border border-border/30 rounded-sm overflow-hidden bg-white flex items-center justify-center">
                          <img
                            src={finalImageUrl}
                            alt={selectedOption.label}
                            className="w-5 h-5 object-contain"
                            loading="eager"
                            onError={e => {
                              console.log('Image load error:', e);
                              // Fallback if image fails to load
                              (e.target as HTMLImageElement).style.display = 'none';
                              // Hiển thị fallback khi lỗi
                              const parent = (e.target as HTMLImageElement).parentElement;
                              if (parent) {
                                parent.innerHTML = `<span class="text-xs font-bold text-primary">${selectedOption.label.charAt(0)}</span>`;
                                parent.classList.add('bg-primary/10');
                              }
                            }}
                          />
                        </div>
                      );
                    }

                    return (
                      <div className="w-6 h-6 mr-2.5 flex-shrink-0 bg-primary/10 rounded-sm flex items-center justify-center">
                        <span className="text-xs font-bold text-primary">
                          {selectedOption.label.charAt(0)}
                        </span>
                      </div>
                    );
                  })()}
                </>
              )}

              <input
                ref={searchInputRef}
                type="text"
                value={searchText}
                onChange={e => {
                  e.stopPropagation();
                  setSearchText(e.target.value);
                  if (!isOpen) setIsOpen(true);
                }}
                placeholder={
                  selectedValue && !isSearchMode
                    ? typeof getDisplayValue() === 'string'
                      ? (getDisplayValue() as string)
                      : ''
                    : searchPlaceholder || t('common.search', 'Search...')
                }
                className="w-full bg-transparent border-none outline-none focus:ring-0 cursor-pointer"
                onClick={e => {
                  e.stopPropagation();
                  if (!isOpen) setIsOpen(true);
                }}
                disabled={disabled}
              />
            </div>
          ) : (
            <div className="flex-grow truncate">
              {renderValue && selectedValue !== undefined
                ? renderValue(selectedValue, flattenedOptions)
                : renderSelectedValue()}
            </div>
          )}

          <div className="flex items-center">
            {/* Nút xóa khi đang tìm kiếm */}
            {searchText && ((inlineSearch && !isOpen) || isSearchMode) && (
              <button
                type="button"
                className="mr-2 text-muted hover:text-foreground"
                onClick={e => {
                  e.stopPropagation();
                  setSearchText('');
                  searchInputRef.current?.focus();
                }}
              >
                <Icon name="x" size="sm" />
              </button>
            )}

            {/* Nút xóa giá trị đã chọn */}
            {selectedValue !== undefined && !isSearchMode && !disabled && (
              <button
                type="button"
                className="mr-2 text-muted hover:text-foreground"
                onClick={handleClearSelection}
              >
                <Icon name="x" size="sm" />
              </button>
            )}

            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-error">{error}</p>}

        {/* Helper text */}
        {helperText && !error && <p className="mt-1 text-sm text-muted">{helperText}</p>}

        {/* Dropdown */}
        {isOpen && (
          <div
            className="absolute z-50 w-full mt-1.5 bg-card rounded-lg shadow-lg max-h-60 overflow-auto border border-border/30"
            style={{
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.08)',
              animation: 'dropdownFadeIn 0.15s ease-out forwards',
            }}
          >
            {/* Search input - always visible if not using inline search */}
            {!inlineSearch && (
              <div className="sticky top-0 p-2.5 bg-card border-b border-border/50 backdrop-blur-sm bg-opacity-95 z-10">
                <div className="relative">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchText}
                    onChange={e => setSearchText(e.target.value)}
                    placeholder={searchPlaceholder || t('common.search', 'Search...')}
                    className="w-full px-3 py-2 text-sm border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary bg-card-muted text-foreground"
                    onClick={e => e.stopPropagation()}
                  />
                  {/* Không hiển thị icon search */}
                  {searchText && (
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted hover:text-foreground transition-colors duration-200"
                      onClick={e => {
                        e.stopPropagation();
                        setSearchText('');
                        searchInputRef.current?.focus();
                      }}
                    >
                      <Icon name="x" size="sm" />
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Options */}
            <div role="listbox" className="py-1">
              {renderOptions()}
            </div>

            {/* Footer - có thể thêm nếu cần */}
            {options.length > 10 && (
              <div className="text-xs text-muted text-center py-1.5 border-t border-border/30 bg-card-muted">
                {t('common.totalOptions', 'Tổng {{count}} tùy chọn', { count: options.length })}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

SearchableSelect.displayName = 'SearchableSelect';

export default SearchableSelect;
