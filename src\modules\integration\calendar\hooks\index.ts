import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { GoogleCalendarService } from '../services';
import {
  GoogleCalendarQueryParams,
  CreateGoogleCalendarDto,
  UpdateGoogleCalendarDto,
  TestGoogleCalendarDto,
  TestGoogleCalendarWithConfigDto,
} from '../types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Query Keys
 */
export const googleCalendarQueryKeys = {
  all: ['user', 'integration', 'google-calendar'] as const,
  lists: () => [...googleCalendarQueryKeys.all, 'list'] as const,
  list: (params?: GoogleCalendarQueryParams) => [...googleCalendarQueryKeys.lists(), params] as const,
  details: () => [...googleCalendarQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...googleCalendarQueryKeys.details(), id] as const,
  events: (id: number) => [...googleCalendarQueryKeys.all, 'events', id] as const,
  syncStatus: (id: number) => [...googleCalendarQueryKeys.all, 'sync-status', id] as const,
};

/**
 * Hook to get Google Calendar configurations
 */
export function useGoogleCalendarConfigurations(params?: GoogleCalendarQueryParams) {
  return useQuery({
    queryKey: googleCalendarQueryKeys.list(params),
    queryFn: () => GoogleCalendarService.getConfigurations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get Google Calendar configuration by ID
 */
export function useGoogleCalendarConfiguration(id: number) {
  return useQuery({
    queryKey: googleCalendarQueryKeys.detail(id),
    queryFn: () => GoogleCalendarService.getConfiguration(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook to create Google Calendar configuration
 */
export function useCreateGoogleCalendarConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateGoogleCalendarDto) => GoogleCalendarService.createConfiguration(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.lists() });
      NotificationUtil.success({
        message: 'Tạo cấu hình Google Calendar thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo cấu hình Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to update Google Calendar configuration
 */
export function useUpdateGoogleCalendarConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateGoogleCalendarDto }) =>
      GoogleCalendarService.updateConfiguration(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.detail(id) });
      NotificationUtil.success({
        message: 'Cập nhật cấu hình Google Calendar thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật cấu hình Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to delete Google Calendar configuration
 */
export function useDeleteGoogleCalendarConfiguration() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => GoogleCalendarService.deleteConfiguration(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.lists() });
      NotificationUtil.success({
        message: 'Xóa cấu hình Google Calendar thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa cấu hình Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to test Google Calendar configuration
 */
export function useTestGoogleCalendarConfiguration() {
  return useMutation({
    mutationFn: ({ id, testData }: { id: number; testData?: TestGoogleCalendarDto }) =>
      GoogleCalendarService.testConfiguration(id, testData),
    onSuccess: (result) => {
      if (result.success) {
        NotificationUtil.success({
          message: 'Kiểm tra kết nối Google Calendar thành công!',
          title: result.message,
        });
      } else {
        NotificationUtil.warning({
          message: 'Kiểm tra kết nối Google Calendar thất bại',
          title: result.message,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Kiểm tra kết nối Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to test Google Calendar with configuration
 */
export function useTestGoogleCalendarWithConfiguration() {
  return useMutation({
    mutationFn: (data: TestGoogleCalendarWithConfigDto) =>
      GoogleCalendarService.testWithConfiguration(data),
    onSuccess: (result) => {
      if (result.success) {
        NotificationUtil.success({
          message: 'Kiểm tra cấu hình Google Calendar thành công!',
          title: result.message,
        });
      } else {
        NotificationUtil.warning({
          message: 'Kiểm tra cấu hình Google Calendar thất bại',
          title: result.message,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Kiểm tra cấu hình Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to sync calendar events
 */
export function useSyncGoogleCalendarEvents() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => GoogleCalendarService.syncEvents(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.events(id) });
      queryClient.invalidateQueries({ queryKey: googleCalendarQueryKeys.syncStatus(id) });
      NotificationUtil.success({
        message: 'Đồng bộ sự kiện Google Calendar thành công!',
        title: 'Thành công',
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Đồng bộ sự kiện Google Calendar thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook to get calendar events
 */
export function useGoogleCalendarEvents(
  id: number,
  params?: {
    startDate?: string;
    endDate?: string;
    maxResults?: number;
  }
) {
  return useQuery({
    queryKey: googleCalendarQueryKeys.events(id),
    queryFn: () => GoogleCalendarService.getEvents(id, params),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get sync status
 */
export function useGoogleCalendarSyncStatus(id: number) {
  return useQuery({
    queryKey: googleCalendarQueryKeys.syncStatus(id),
    queryFn: () => GoogleCalendarService.getSyncStatus(id),
    enabled: !!id,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });
}
