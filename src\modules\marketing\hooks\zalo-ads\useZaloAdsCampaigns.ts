import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient } from '@/shared/api';
import type {
  ZaloAdsCampaignDto,
  ZaloAdsCampaignQueryDto,
  CreateZaloAdsCampaignDto,
  UpdateZaloAdsCampaignDto
} from '../../types/zalo-ads.types';
import type { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Hook để lấy danh sách chiến dịch Zalo Ads
 */
export function useZaloAdsCampaigns(query: ZaloAdsCampaignQueryDto) {
  return useQuery({
    queryKey: ['zalo-ads-campaigns', query],
    queryFn: async (): Promise<PaginatedResult<ZaloAdsCampaignDto>> => {
      const params = new URLSearchParams();

      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.search) params.append('search', query.search);
      if (query.accountId) params.append('accountId', query.accountId);
      if (query.status) params.append('status', query.status);
      if (query.objective) params.append('objective', query.objective);
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortDirection) params.append('sortDirection', query.sortDirection);

      const response = await apiClient.get<PaginatedResult<ZaloAdsCampaignDto>>(
        `/marketing/zalo-ads/campaigns?${params.toString()}`
      );

      return response.result;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để lấy chi tiết chiến dịch Zalo Ads
 */
export function useZaloAdsCampaign(campaignId: string) {
  return useQuery({
    queryKey: ['zalo-ads-campaign', campaignId],
    queryFn: async (): Promise<ZaloAdsCampaignDto> => {
      const response = await apiClient.get<ZaloAdsCampaignDto>(
        `/marketing/zalo-ads/campaigns/${campaignId}`
      );

      return response.result;
    },
    enabled: !!campaignId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để tạo chiến dịch Zalo Ads mới
 */
export function useCreateZaloAdsCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateZaloAdsCampaignDto): Promise<ZaloAdsCampaignDto> => {
      const response = await apiClient.post<ZaloAdsCampaignDto>(
        '/marketing/zalo-ads/campaigns',
        data
      );

      return response.result;
    },
    onSuccess: () => {
      // Invalidate và refetch campaigns list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để cập nhật chiến dịch Zalo Ads
 */
export function useUpdateZaloAdsCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      campaignId,
      data
    }: {
      campaignId: string;
      data: UpdateZaloAdsCampaignDto
    }): Promise<ZaloAdsCampaignDto> => {
      const response = await apiClient.put<ZaloAdsCampaignDto>(
        `/marketing/zalo-ads/campaigns/${campaignId}`,
        data
      );

      return response.result;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(['zalo-ads-campaign', data.campaignId], data);
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để xóa chiến dịch Zalo Ads
 */
export function useDeleteZaloAdsCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (campaignId: string): Promise<void> => {
      await apiClient.delete(`/marketing/zalo-ads/campaigns/${campaignId}`);
    },
    onSuccess: () => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để pause/resume chiến dịch
 */
export function useToggleZaloAdsCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      campaignId,
      action
    }: {
      campaignId: string;
      action: 'pause' | 'resume'
    }): Promise<ZaloAdsCampaignDto> => {
      const response = await apiClient.post<ZaloAdsCampaignDto>(
        `/marketing/zalo-ads/campaigns/${campaignId}/${action}`
      );

      return response.result;
    },
    onSuccess: (data) => {
      // Update cache
      queryClient.setQueryData(['zalo-ads-campaign', data.campaignId], data);
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}

/**
 * Hook để duplicate chiến dịch
 */
export function useDuplicateZaloAdsCampaign() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      campaignId,
      newName
    }: {
      campaignId: string;
      newName: string
    }): Promise<ZaloAdsCampaignDto> => {
      const response = await apiClient.post<ZaloAdsCampaignDto>(
        `/marketing/zalo-ads/campaigns/${campaignId}/duplicate`,
        { name: newName }
      );

      return response.result;
    },
    onSuccess: () => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
    },
  });
}

/**
 * Hook để lấy campaigns theo account
 */
export function useZaloAdsCampaignsByAccount(accountId: string) {
  return useQuery({
    queryKey: ['zalo-ads-campaigns-by-account', accountId],
    queryFn: async (): Promise<ZaloAdsCampaignDto[]> => {
      const response = await apiClient.get<ZaloAdsCampaignDto[]>(
        `/marketing/zalo-ads/accounts/${accountId}/campaigns`
      );

      return response.result;  
    },
    enabled: !!accountId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
}

/**
 * Hook để bulk update campaigns
 */
export function useBulkUpdateZaloAdsCampaigns() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      campaignIds,
      action,
      data
    }: {
      campaignIds: string[];
      action: 'pause' | 'resume' | 'delete' | 'update';
      data?: Partial<UpdateZaloAdsCampaignDto>;
    }): Promise<{ success: string[]; failed: string[] }> => {
      const response = await apiClient.post<{ success: string[]; failed: string[] }>(
        '/marketing/zalo-ads/campaigns/bulk',
        { campaignIds, action, data }
      );

      return response.result;
    },
    onSuccess: () => {
      // Invalidate campaigns list
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-campaigns'] });
      queryClient.invalidateQueries({ queryKey: ['zalo-ads-metrics'] });
    },
  });
}
