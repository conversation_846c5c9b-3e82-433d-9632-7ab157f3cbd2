import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Interface cho thông tin sản phẩm trong kho
 */
export interface InventoryItemDto {
  /**
   * ID bản ghi tồn kho
   */
  id: number;

  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * ID kho chứa sản phẩm
   */
  warehouseId: number;

  /**
   * Số lượng hiện tại trong kho
   */
  currentQuantity: number;

  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * Tổng số lượng
   */
  totalQuantity?: number;

  /**
   * Thông tin sản phẩm
   */
  product?: {
    id: number;
    name: string;
    code?: string;
    description?: string;
    images?: Array<{
      key: string;
      url: string;
      position: number;
    }>;
  };

  /**
   * Thông tin kho
   */
  warehouse?: {
    warehouseId: number;
    name: string;
    description?: string;
    type?: string;
  };

  /**
   * Thời gian tạo
   */
  createdAt?: string;

  /**
   * Thời gian cập nhật
   */
  updatedAt?: string;
}

/**
 * Interface cho tham số truy vấn inventory
 */
export interface InventoryQueryParams extends QueryDto {
  /**
   * ID sản phẩm để lọc
   */
  productId?: number;

  /**
   * ID kho để lọc
   */
  warehouseId?: number;

  /**
   * ID người dùng
   */
  userId?: number;
}

/**
 * Interface cho việc cập nhật số lượng sản phẩm trong kho
 */
export interface UpdateInventoryQuantityDto {
  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * ID kho (nếu muốn chuyển kho)
   */
  warehouseId?: number;

  /**
   * Mã SKU
   */
  sku?: string;

  /**
   * Mã vạch
   */
  barcode?: string;
}

/**
 * Interface cho việc tạo mới inventory
 */
export interface CreateInventoryDto {
  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * ID kho
   */
  warehouseId: number;

  /**
   * Số lượng có sẵn
   */
  availableQuantity?: number;

  /**
   * Số lượng đã đặt trước
   */
  reservedQuantity?: number;

  /**
   * Số lượng hỏng
   */
  defectiveQuantity?: number;

  /**
   * ID người dùng
   */
  userId?: number;
}

/**
 * Interface cho thông tin sản phẩm kết hợp với inventory
 */
export interface ProductInventoryResponseDto {
  /**
   * ID bản ghi tồn kho
   */
  inventoryId: number;

  /**
   * ID sản phẩm
   */
  productId: number;

  /**
   * Tên sản phẩm
   */
  productName: string;

  /**
   * Mô tả sản phẩm
   */
  productDescription?: string;

  /**
   * Hình ảnh sản phẩm
   */
  productImages?: Array<{
    key: string;
    url: string;
    position: number;
  }>;

  /**
   * Giá sản phẩm
   */
  productPrice?: {
    price: number;
    currency: string;
  };

  /**
   * ID kho chứa sản phẩm
   */
  warehouseId?: number;

  /**
   * Số lượng hiện tại trong kho
   */
  currentQuantity: number;

  /**
   * Tổng số lượng đã nhập vào kho
   */
  totalQuantity: number;

  /**
   * Số lượng sẵn sàng để bán
   */
  availableQuantity: number;

  /**
   * Số lượng bị giữ chỗ
   */
  reservedQuantity: number;

  /**
   * Số lượng sản phẩm lỗi
   */
  defectiveQuantity: number;

  /**
   * Mã SKU của sản phẩm trong kho
   */
  sku?: string;

  /**
   * Mã vạch của sản phẩm trong kho
   */
  barcode?: string;

  /**
   * Thời gian cập nhật tồn kho gần nhất
   */
  lastUpdated: number;
}

/**
 * Interface cho query params của API warehouse products
 */
export interface WarehouseProductsQueryParams extends QueryDto {
  /**
   * ID kho
   */
  warehouseId: number;
}

/**
 * Enum cho trạng thái inventory
 */
export enum InventoryStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}
