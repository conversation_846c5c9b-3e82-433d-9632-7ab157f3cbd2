import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import {
  Button,
  Input,
  Alert,
  Select,
  FormItem
} from '@/shared/components/common';
import { useCreateZaloAdsCampaign, useUpdateZaloAdsCampaign } from '../../hooks/zalo-ads/useZaloAdsCampaigns';
import { useZaloAdsAccounts } from '../../hooks/zalo-ads/useZaloAdsAccounts';
import type { CreateZaloAdsCampaignDto, ZaloAdsCampaignDto, ZaloAdsObjective, ZaloAdsBidStrategy, ZaloAdsPlacement } from '../../types/zalo-ads.types';

// Validation schema
const campaignSchema = z.object({
  accountId: z.string().min(1, '<PERSON><PERSON><PERSON> khoản là bắt buộc'),
  name: z.string().min(1, 'Tên chiến dịch là bắt buộc'),
  objective: z.nativeEnum({
    REACH: 'REACH',
    TRAFFIC: 'TRAFFIC',
    ENGAGEMENT: 'ENGAGEMENT',
    LEAD_GENERATION: 'LEAD_GENERATION',
    CONVERSIONS: 'CONVERSIONS',
    BRAND_AWARENESS: 'BRAND_AWARENESS',
    APP_INSTALLS: 'APP_INSTALLS',
    VIDEO_VIEWS: 'VIDEO_VIEWS'
  } as const),
  budgetType: z.enum(['daily', 'lifetime']),
  budgetAmount: z.number().min(50000, 'Ngân sách tối thiểu 50,000 VND'),
  bidStrategy: z.nativeEnum({
    LOWEST_COST: 'LOWEST_COST',
    TARGET_COST: 'TARGET_COST',
    COST_CAP: 'COST_CAP',
    BID_CAP: 'BID_CAP'
  } as const),
  bidAmount: z.number().optional(),
  placements: z.array(z.string()).min(1, 'Chọn ít nhất một vị trí hiển thị'),
  startDate: z.string().min(1, 'Ngày bắt đầu là bắt buộc'),
  endDate: z.string().optional(),
  targeting: z.object({
    locations: z.array(z.string()),
    ageMin: z.number().min(13).max(65),
    ageMax: z.number().min(13).max(65),
    genders: z.array(z.enum(['MALE', 'FEMALE'])),
  }).optional(),
});

type CampaignFormData = z.infer<typeof campaignSchema>;

interface CreateZaloAdsCampaignFormProps {
  campaign?: ZaloAdsCampaignDto;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo/chỉnh sửa chiến dịch Zalo Ads
 */
export function CreateZaloAdsCampaignForm({ campaign, onSuccess, onCancel }: CreateZaloAdsCampaignFormProps) {
  const { t } = useTranslation('marketing');
  const [step, setStep] = useState<'basic' | 'targeting' | 'budget'>('basic');

  const createCampaign = useCreateZaloAdsCampaign();
  const updateCampaign = useUpdateZaloAdsCampaign();
  const { data: accountsData } = useZaloAdsAccounts({ page: 1, limit: 100 });

  const isEditing = !!campaign;

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    watch,
    setValue,
  } = useForm<CampaignFormData>({
    resolver: zodResolver(campaignSchema),
    mode: 'onChange',
    defaultValues: campaign ? {
      accountId: campaign.accountId,
      name: campaign.name,
      objective: campaign.objective,
      budgetType: campaign.dailyBudget ? 'daily' : 'lifetime',
      budgetAmount: campaign.dailyBudget || campaign.lifetimeBudget || 50000,
      bidStrategy: campaign.bidStrategy,
      bidAmount: campaign.bidAmount,
      placements: campaign.placements,
      startDate: new Date(campaign.schedule.startTime).toISOString().split('T')[0],
      endDate: campaign.schedule.endTime ? new Date(campaign.schedule.endTime).toISOString().split('T')[0] : '',
      targeting: {
        locations: campaign.targeting.locations,
        ageMin: campaign.targeting.ageMin,
        ageMax: campaign.targeting.ageMax,
        genders: campaign.targeting.genders,
      }
    } : {
      budgetType: 'daily',
      budgetAmount: 50000,
      targeting: {
        locations: ['VN'],
        ageMin: 18,
        ageMax: 65,
        genders: ['MALE', 'FEMALE'],
      }
    }
  });

  const watchedBudgetType = watch('budgetType');

  const onSubmit = async (data: CampaignFormData) => {
    try {
      const payload: CreateZaloAdsCampaignDto = {
        accountId: data.accountId,
        name: data.name,
        objective: data.objective as ZaloAdsObjective,
        dailyBudget: data.budgetType === 'daily' ? data.budgetAmount : undefined,
        lifetimeBudget: data.budgetType === 'lifetime' ? data.budgetAmount : undefined,
        bidStrategy: data.bidStrategy as ZaloAdsBidStrategy,
        bidAmount: data.bidAmount,
        targeting: {
          locations: data.targeting?.locations || ['VN'],
          ageMin: data.targeting?.ageMin || 18,
          ageMax: data.targeting?.ageMax || 65,
          genders: data.targeting?.genders || ['MALE', 'FEMALE'],
          interests: [],
          behaviors: [],
        },
        schedule: {
          startTime: new Date(data.startDate).getTime(),
          endTime: data.endDate ? new Date(data.endDate).getTime() : undefined,
        },
        placements: data.placements as ZaloAdsPlacement[],
      };

      if (isEditing && campaign) {
        await updateCampaign.mutateAsync({ campaignId: campaign.campaignId, data: payload });
      } else {
        await createCampaign.mutateAsync(payload);
      }

      reset();
      onSuccess?.();
    } catch (error) {
      console.error('Error saving campaign:', error);
    }
  };

  const objectiveOptions = [
    { value: 'REACH', label: 'Tiếp cận' },
    { value: 'TRAFFIC', label: 'Lưu lượng truy cập' },
    { value: 'ENGAGEMENT', label: 'Tương tác' },
    { value: 'LEAD_GENERATION', label: 'Thu thập lead' },
    { value: 'CONVERSIONS', label: 'Chuyển đổi' },
    { value: 'BRAND_AWARENESS', label: 'Nhận diện thương hiệu' },
    { value: 'APP_INSTALLS', label: 'Cài đặt ứng dụng' },
    { value: 'VIDEO_VIEWS', label: 'Xem video' },
  ];

  const bidStrategyOptions = [
    { value: 'LOWEST_COST', label: 'Chi phí thấp nhất' },
    { value: 'TARGET_COST', label: 'Chi phí mục tiêu' },
    { value: 'COST_CAP', label: 'Giới hạn chi phí' },
    { value: 'BID_CAP', label: 'Giới hạn đấu giá' },
  ];

  const placementOptions = [
    { value: 'ZALO_NEWSFEED', label: 'Zalo Newsfeed' },
    { value: 'ZALO_MEDIA_BOX', label: 'Zalo Media Box' },
    { value: 'ZALO_VIDEO', label: 'Zalo Video' },
    { value: 'ZALO_STORY', label: 'Zalo Story' },
    { value: 'ZING_MP3', label: 'Zing MP3' },
    { value: 'ZING_NEWS', label: 'Zing News' },
    { value: 'BAO_MOI', label: 'Báo Mới' },
  ];

  const renderBasicStep = () => (
    <div className="space-y-4">
      <FormItem
        name="accountId"
        label={t('marketing:zaloAds.campaigns.form.account.label', 'Tài khoản Zalo Ads')}
        required
      >
        <Select
          value={watch('accountId')}
          onChange={(value) => setValue('accountId', value as string)}
          options={accountsData?.items.map((account) => ({
            value: account.accountId,
            label: account.accountName
          })) || []}
          placeholder={t('marketing:zaloAds.campaigns.form.account.placeholder', 'Chọn tài khoản')}
          error={errors.accountId?.message}
        />
      </FormItem>

      <FormItem
        name="name"
        label={t('marketing:zaloAds.campaigns.form.name.label', 'Tên chiến dịch')}
        required
      >
        <Input
          id="name"
          {...register('name')}
          placeholder={t('marketing:zaloAds.campaigns.form.name.placeholder', 'Nhập tên chiến dịch')}
          error={errors.name?.message}
        />
      </FormItem>

      <FormItem
        name="objective"
        label={t('marketing:zaloAds.campaigns.form.objective.label', 'Mục tiêu chiến dịch')}
        required
      >
        <Select
          value={watch('objective')}
          onChange={(value) => setValue('objective', value as ZaloAdsObjective)}
          options={objectiveOptions}
          placeholder={t('marketing:zaloAds.campaigns.form.objective.placeholder', 'Chọn mục tiêu')}
          error={errors.objective?.message}
        />
      </FormItem>

      <div>
        <div className="text-sm font-medium mb-2">
          {t('marketing:zaloAds.campaigns.form.placements.label', 'Vị trí hiển thị')} *
        </div>
        <div className="grid grid-cols-2 gap-2 mt-2">
          {placementOptions.map((placement) => (
            <label key={placement.value} className="flex items-center space-x-2">
              <input
                type="checkbox"
                value={placement.value}
                {...register('placements')}
                className="rounded border-gray-300"
              />
              <span className="text-sm">{placement.label}</span>
            </label>
          ))}
        </div>
        {errors.placements && (
          <p className="text-sm text-red-600 mt-1">{errors.placements.message}</p>
        )}
      </div>
    </div>
  );

  const renderBudgetStep = () => (
    <div className="space-y-4">
      <div>
        <div className="text-sm font-medium mb-2">
          {t('marketing:zaloAds.campaigns.form.budgetType.label', 'Loại ngân sách')} *
        </div>
        <div className="flex space-x-4 mt-2">
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              value="daily"
              {...register('budgetType')}
              className="text-primary"
            />
            <span>{t('marketing:zaloAds.campaigns.form.budgetType.daily', 'Ngân sách hàng ngày')}</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="radio"
              value="lifetime"
              {...register('budgetType')}
              className="text-primary"
            />
            <span>{t('marketing:zaloAds.campaigns.form.budgetType.lifetime', 'Ngân sách tổng')}</span>
          </label>
        </div>
      </div>

      <FormItem
        name="budgetAmount"
        label={watchedBudgetType === 'daily'
          ? t('marketing:zaloAds.campaigns.form.dailyBudget.label', 'Ngân sách hàng ngày')
          : t('marketing:zaloAds.campaigns.form.lifetimeBudget.label', 'Ngân sách tổng')
        }
        helpText={t('marketing:zaloAds.campaigns.form.budgetAmount.helper', 'Tối thiểu 50,000 VND')}
        required
      >
        <Input
          id="budgetAmount"
          type="number"
          min="50000"
          step="10000"
          {...register('budgetAmount', { valueAsNumber: true })}
          placeholder="50,000"
          error={errors.budgetAmount?.message}
        />
      </FormItem>

      <FormItem
        name="bidStrategy"
        label={t('marketing:zaloAds.campaigns.form.bidStrategy.label', 'Chiến lược đấu giá')}
        required
      >
        <Select
          value={watch('bidStrategy')}
          onChange={(value) => setValue('bidStrategy', value as ZaloAdsBidStrategy)}
          options={bidStrategyOptions}
          placeholder={t('marketing:zaloAds.campaigns.form.bidStrategy.placeholder', 'Chọn chiến lược')}
          error={errors.bidStrategy?.message}
        />
      </FormItem>

      <div className="grid grid-cols-2 gap-4">
        <FormItem
          name="startDate"
          label={t('marketing:zaloAds.campaigns.form.startDate.label', 'Ngày bắt đầu')}
          required
        >
          <Input
            id="startDate"
            type="date"
            {...register('startDate')}
            error={errors.startDate?.message}
          />
        </FormItem>

        <FormItem
          name="endDate"
          label={t('marketing:zaloAds.campaigns.form.endDate.label', 'Ngày kết thúc')}
          helpText={t('marketing:zaloAds.campaigns.form.endDate.helper', 'Để trống nếu chạy liên tục')}
        >
          <Input
            id="endDate"
            type="date"
            {...register('endDate')}
            error={errors.endDate?.message}
          />
        </FormItem>
      </div>
    </div>
  );

  const renderTargetingStep = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <FormItem
          name="targeting.ageMin"
          label={t('marketing:zaloAds.campaigns.form.ageMin.label', 'Tuổi từ')}
          required
        >
          <Input
            id="ageMin"
            type="number"
            min="13"
            max="65"
            {...register('targeting.ageMin', { valueAsNumber: true })}
            error={errors.targeting?.ageMin?.message}
          />
        </FormItem>

        <FormItem
          name="targeting.ageMax"
          label={t('marketing:zaloAds.campaigns.form.ageMax.label', 'Tuổi đến')}
          required
        >
          <Input
            id="ageMax"
            type="number"
            min="13"
            max="65"
            {...register('targeting.ageMax', { valueAsNumber: true })}
            error={errors.targeting?.ageMax?.message}
          />
        </FormItem>
      </div>

      <div>
        <div className="text-sm font-medium mb-2">
          {t('marketing:zaloAds.campaigns.form.genders.label', 'Giới tính')} *
        </div>
        <div className="flex space-x-4 mt-2">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              value="MALE"
              {...register('targeting.genders')}
              className="rounded border-gray-300"
            />
            <span>{t('marketing:zaloAds.campaigns.form.genders.male', 'Nam')}</span>
          </label>
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              value="FEMALE"
              {...register('targeting.genders')}
              className="rounded border-gray-300"
            />
            <span>{t('marketing:zaloAds.campaigns.form.genders.female', 'Nữ')}</span>
          </label>
        </div>
      </div>

      <FormItem
        name="targeting.locations"
        label={t('marketing:zaloAds.campaigns.form.locations.label', 'Vị trí địa lý')}
        required
      >
        <Select
          value={watch('targeting.locations')?.[0]}
          onChange={(value) => setValue('targeting.locations', [value as string])}
          options={[
            { value: 'VN', label: 'Việt Nam' },
            { value: 'HCM', label: 'TP. Hồ Chí Minh' },
            { value: 'HN', label: 'Hà Nội' },
            { value: 'DN', label: 'Đà Nẵng' }
          ]}
          placeholder={t('marketing:zaloAds.campaigns.form.locations.placeholder', 'Chọn vị trí')}
        />
      </FormItem>
    </div>
  );

  const getCurrentStepContent = () => {
    switch (step) {
      case 'basic':
        return renderBasicStep();
      case 'budget':
        return renderBudgetStep();
      case 'targeting':
        return renderTargetingStep();
      default:
        return renderBasicStep();
    }
  };

  const isCreatePending = createCampaign.isPending;
  const isUpdatePending = updateCampaign.isPending;
  const isPending = isCreatePending || isUpdatePending;
  const error = createCampaign.error || updateCampaign.error;
  const isSuccess = createCampaign.isSuccess || updateCampaign.isSuccess;

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2">
          {isEditing
            ? t('marketing:zaloAds.campaigns.form.editTitle', 'Chỉnh sửa chiến dịch')
            : t('marketing:zaloAds.campaigns.form.createTitle', 'Tạo chiến dịch mới')
          }
        </h3>
        <p className="text-sm text-muted-foreground">
          {isEditing
            ? t('marketing:zaloAds.campaigns.form.editDescription', 'Cập nhật thông tin chiến dịch')
            : t('marketing:zaloAds.campaigns.form.createDescription', 'Tạo chiến dịch quảng cáo mới trên Zalo')
          }
        </p>
      </div>

      {/* Step Navigation */}
      <div className="flex space-x-4 border-b">
        {['basic', 'budget', 'targeting'].map((stepName, index) => (
          <button
            key={stepName}
            type="button"
            onClick={() => setStep(stepName as 'basic' | 'budget' | 'targeting')}
            className={`pb-2 px-1 border-b-2 transition-colors ${
              step === stepName
                ? 'border-primary text-primary'
                : 'border-transparent text-muted-foreground hover:text-foreground'
            }`}
          >
            {index + 1}. {stepName === 'basic' ? 'Cơ bản' : stepName === 'budget' ? 'Ngân sách' : 'Targeting'}
          </button>
        ))}
      </div>

      {error && (
        <Alert
          type="error"
          message={t('marketing:zaloAds.campaigns.form.error.title', 'Lỗi tạo chiến dịch')}
          description={error?.message || t('marketing:zaloAds.campaigns.form.error.message', 'Không thể tạo chiến dịch. Vui lòng thử lại.')}
        />
      )}

      {isSuccess && (
        <Alert
          type="success"
          message={isEditing
            ? t('marketing:zaloAds.campaigns.form.updateSuccess.title', 'Cập nhật thành công')
            : t('marketing:zaloAds.campaigns.form.createSuccess.title', 'Tạo chiến dịch thành công')
          }
          description={isEditing
            ? t('marketing:zaloAds.campaigns.form.updateSuccess.message', 'Chiến dịch đã được cập nhật thành công.')
            : t('marketing:zaloAds.campaigns.form.createSuccess.message', 'Chiến dịch đã được tạo thành công.')
          }
        />
      )}

      {/* Step Content */}
      {getCurrentStepContent()}

      <div className="flex justify-end space-x-3 pt-4 border-t">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isPending}
        >
          {t('common.cancel', 'Hủy')}
        </Button>
        <Button
          type="submit"
          disabled={!isValid || isPending}
          isLoading={isPending}
        >
          {isPending
            ? (isEditing ? t('marketing:zaloAds.campaigns.form.updating', 'Đang cập nhật...') : t('marketing:zaloAds.campaigns.form.creating', 'Đang tạo...'))
            : (isEditing ? t('marketing:zaloAds.campaigns.form.update', 'Cập nhật chiến dịch') : t('marketing:zaloAds.campaigns.form.create', 'Tạo chiến dịch'))
          }
        </Button>
      </div>
    </form>
  );
}

export default CreateZaloAdsCampaignForm;
