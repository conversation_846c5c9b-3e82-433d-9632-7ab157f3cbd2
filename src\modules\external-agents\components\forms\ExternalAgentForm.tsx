import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FormItem,
  Input,
  Textarea,
  Select,
  Button,
  Typography,
  Card
} from '@/shared/components/common';
import AuthenticationForm from './AuthenticationForm';
import {
  ExternalAgent,
  ExternalAgentCreateDto,
  ExternalAgentUpdateDto,
  ProtocolType,
  AuthenticationType,
  AuthenticationConfig,
  AgentCapability
} from '../../types';
import { validateExternalAgentCreate, validateExternalAgentUpdate } from '../../utils';

interface ExternalAgentFormProps {
  agent?: ExternalAgent;
  onSubmit: (data: ExternalAgentCreateDto | ExternalAgentUpdateDto) => void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: 'create' | 'edit';
  className?: string;
}

interface FormData {
  name: string;
  description: string;
  protocol: ProtocolType;
  endpoint: string;
  authentication: {
    type: AuthenticationType;
    credentials: Record<string, string>;
    headers: Record<string, string>;
    parameters: Record<string, string>;
  };
  capabilities: AgentCapability[];
  tags: string[];
  metadata: Record<string, unknown>;
}

const ExternalAgentForm: React.FC<ExternalAgentFormProps> = ({
  agent,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = 'create',
  className,
}) => {
  const { t } = useTranslation(['common', 'external-agents']);
  const [, setFormErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<FormData>({
    name: agent?.name || '',
    description: agent?.description || '',
    protocol: agent?.protocol || ProtocolType.REST_API,
    endpoint: agent?.endpoint || '',
    authentication: {
      type: agent?.authentication?.type || AuthenticationType.NONE,
      credentials: agent?.authentication?.credentials || {},
      headers: agent?.authentication?.headers || {},
      parameters: agent?.authentication?.parameters || {},
    },
    capabilities: agent?.capabilities || [],
    tags: agent?.tags || [],
    metadata: agent?.metadata || {},
  });

  const [tagInput, setTagInput] = useState('');

  const protocolOptions = Object.values(ProtocolType).map(protocol => ({
    value: protocol,
    label: t(`external-agents:protocol.${protocol}`),
  }));

  const handleInputChange = (field: keyof FormData, value: string | ProtocolType) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAuthenticationChange = (authData: AuthenticationConfig) => {
    setFormData(prev => ({
      ...prev,
      authentication: {
        type: authData.type,
        credentials: authData.credentials || {},
        headers: authData.headers || {},
        parameters: authData.parameters || {}
      },
    }));
  };

  const handleCapabilityToggle = (capability: AgentCapability) => {
    setFormData(prev => ({
      ...prev,
      capabilities: prev.capabilities.includes(capability)
        ? prev.capabilities.filter(c => c !== capability)
        : [...prev.capabilities, capability],
    }));
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validate form data
    const validation = mode === 'create'
      ? validateExternalAgentCreate(formData)
      : validateExternalAgentUpdate(formData);

    if (!validation.success) {
      const errors: Record<string, string> = {};
      validation.error.errors.forEach(error => {
        errors[error.path[0]] = error.message;
      });
      setFormErrors(errors);
      return;
    }

    // Submit form data
    onSubmit(formData);
  };

  return (
    <div className={`w-full ${className}`}>
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:form.basicInfo')}
              </Typography>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem 
                  label={t('external-agents:agent.name')} 
                  name="name" 
                  required
                >
                  <Input
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={t('external-agents:form.placeholder.name')}
                  />
                </FormItem>

                <FormItem 
                  label={t('external-agents:agent.protocol')} 
                  name="protocol" 
                  required
                >
                  <Select
                    value={formData.protocol}
                    onChange={(value) => handleInputChange('protocol', value as ProtocolType)}
                    options={protocolOptions}
                  />
                </FormItem>
              </div>

              <FormItem 
                label={t('external-agents:agent.description')} 
                name="description"
                className="mt-4"
              >
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('external-agents:form.placeholder.description')}
                  rows={3}
                />
              </FormItem>

              <FormItem 
                label={t('external-agents:agent.endpoint')} 
                name="endpoint" 
                required
                className="mt-4"
              >
                <Input
                  value={formData.endpoint}
                  onChange={(e) => handleInputChange('endpoint', e.target.value)}
                  placeholder={t('external-agents:form.placeholder.endpoint')}
                />
              </FormItem>
            </div>
          </Card>

          {/* Authentication */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:form.authentication')}
              </Typography>
              <AuthenticationForm
                value={formData.authentication}
                onChange={handleAuthenticationChange}
              />
            </div>
          </Card>

          {/* Capabilities */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:agent.capabilities')}
              </Typography>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {Object.values(AgentCapability).map((capability) => (
                  <div
                    key={capability}
                    className={`
                      flex items-center gap-2 p-3 rounded-md border cursor-pointer transition-colors
                      ${formData.capabilities.includes(capability)
                        ? 'bg-primary/10 border-primary text-primary'
                        : 'bg-background border-border hover:bg-muted'
                      }
                    `}
                    onClick={() => handleCapabilityToggle(capability)}
                  >
                    <input
                      type="checkbox"
                      checked={formData.capabilities.includes(capability)}
                      onChange={() => handleCapabilityToggle(capability)}
                      className="rounded"
                    />
                    <Typography variant="body2">
                      {t(`external-agents:capabilities.${capability}`)}
                    </Typography>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Tags */}
          <Card>
            <div className="p-6">
              <Typography variant="h3" className="mb-4">
                {t('external-agents:agent.tags')}
              </Typography>
              
              <div className="flex gap-2 mb-3">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  placeholder={t('external-agents:form.placeholder.tag')}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button 
                  type="button" 
                  onClick={handleAddTag}
                  variant="outline"
                >
                  {t('external-agents:actions.add')}
                </Button>
              </div>

              {formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <div 
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-secondary text-secondary-foreground rounded-md text-sm"
                    >
                      <span>{tag}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveTag(tag)}
                        className="h-auto p-0 w-4 h-4"
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-3">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onCancel}
              disabled={isLoading}
            >
              {t('external-agents:actions.cancel')}
            </Button>
            <Button 
              type="submit" 
              variant="primary"
              disabled={isLoading}
            >
              {isLoading 
                ? t('external-agents:loading.saving')
                : mode === 'create' 
                  ? t('external-agents:actions.create')
                  : t('external-agents:actions.save')
              }
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ExternalAgentForm;
