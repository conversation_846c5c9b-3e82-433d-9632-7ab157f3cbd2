import React from 'react';
import { CalendarGridProps } from './types';
import {
  getCalendarDays,
  getWeekDayNames,
  isDateDisabled,
  isSameMonthFn,
  isTodayFn,
  isDateInRange,
} from './utils';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import CalendarCell from './CalendarCell';

/**
 * Component hiển thị lưới các ngày trong tháng
 */
const CalendarGrid: React.FC<CalendarGridProps> = ({
  month,
  selectedDate,
  onSelectDate,
  disabledDates,
  minDate,
  maxDate,
  showToday = true,
  showWeekNumbers = false,
  firstDayOfWeek = 1,
  weekDayNames: propWeekDayNames,
  className = '',
  rangeMode = false,
  startDate,
  endDate,
  focusedDate,
}) => {
  const { i18n } = useTranslation();
  useTheme();

  // L<PERSON>y danh sách các ngày trong tháng
  const days = getCalendarDays(month, firstDayOfWeek);

  // Lấy tên các ngày trong tuần
  const weekDayNames = propWeekDayNames || getWeekDayNames(i18n.language, firstDayOfWeek);

  // Base classes
  const baseClasses = '';

  // Combine all classes
  const gridClasses = [baseClasses, className].join(' ');

  return (
    <div className={gridClasses}>
      {/* Header hiển thị tên các ngày trong tuần */}
      <div className="grid grid-cols-7 mb-1">
        {showWeekNumbers && (
          <div className="w-8 h-8 flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
            #
          </div>
        )}

        {weekDayNames.map((name, index) => (
          <div
            key={index}
            className="w-8 h-8 flex items-center justify-center text-xs font-medium text-gray-500 dark:text-gray-400"
            aria-label={name}
          >
            {name}
          </div>
        ))}
      </div>

      {/* Grid các ngày */}
      <div className="grid grid-cols-7 gap-y-1">
        {days.map((day, index) => {
          // Kiểm tra ngày có thuộc tháng hiện tại không
          const isCurrentMonth = isSameMonthFn(day, month);

          // Kiểm tra ngày có phải là hôm nay không
          const isToday = showToday && isTodayFn(day);

          // Kiểm tra ngày có bị disabled không
          const disabled = isDateDisabled(day, disabledDates, minDate, maxDate);

          // Kiểm tra ngày có nằm trong range không
          const inRange =
            rangeMode && startDate && endDate ? isDateInRange(day, startDate, endDate) : false;

          // Hiển thị số tuần nếu cần
          if (showWeekNumbers && index % 7 === 0) {
            const weekNumber = Math.ceil((index + 1) / 7);
            return (
              <React.Fragment key={`week-${weekNumber}`}>
                <div className="w-8 h-8 flex items-center justify-center text-xs text-gray-500 dark:text-gray-400">
                  {weekNumber}
                </div>
                <CalendarCell
                  date={day}
                  selectedDate={selectedDate || null}
                  onSelectDate={onSelectDate}
                  disabled={disabled}
                  isToday={isToday}
                  isCurrentMonth={isCurrentMonth}
                  rangeMode={rangeMode}
                  startDate={startDate || null}
                  endDate={endDate || null}
                  inRange={inRange}
                  focusedDate={focusedDate || null}
                />
              </React.Fragment>
            );
          }

          return (
            <CalendarCell
              key={day.toISOString()}
              date={day}
              selectedDate={selectedDate || null}
              onSelectDate={onSelectDate}
              disabled={disabled}
              isToday={isToday}
              isCurrentMonth={isCurrentMonth}
              rangeMode={rangeMode}
              startDate={startDate || null}
              endDate={endDate || null}
              inRange={inRange}
              focusedDate={focusedDate || null}
            />
          );
        })}
      </div>
    </div>
  );
};

export default CalendarGrid;
