import { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  // ValidationRule,
  ValidationResult,
  ValidationContext,
  FieldValidationConfig,
  UseValidationOptions,
  AsyncValidator
} from '../types';
import { useValidation } from './useValidation';
import { useAsyncValidation } from './useAsyncValidation';

/**
 * Field-specific validation hook that integrates with React Hook Form
 */
export const useFieldValidation = (
  fieldName: string,
  config: FieldValidationConfig,
  options: UseValidationOptions = {}
) => {
  const formContext = useFormContext();
  const {
    rules,
    debounceMs = 300,
    validateOnChange = true,
    validateOnBlur = true,
    validateOnMount = false,
    stopOnFirstError = false,
  } = config;

  // Separate sync and async rules
  const syncRules = rules.filter(rule =>
    rule.validator.constructor.name !== 'AsyncFunction'
  );
  const asyncRules = rules.filter(rule =>
    rule.validator.constructor.name === 'AsyncFunction'
  );

  // Use validation hooks
  const syncValidation = useValidation(syncRules, {
    debounceMs: 0, // No debounce for sync validation
    validateOnMount,
    validateOnChange,
    validateOnBlur,
    stopOnFirstError,
    ...options,
  });

  // Create async validations at top level (React Hooks rules compliance)
  const asyncValidations = useMemo(() => {
    return asyncRules.map(rule => ({
      rule,
      validator: rule.validator as AsyncValidator,
    }));
  }, [asyncRules]);

  // Use async validation for the first rule only (simplified approach)
  const primaryAsyncValidation = useAsyncValidation(
    asyncValidations[0]?.validator || (async () => ({ isValid: true, errors: [], warnings: [], infos: [] })),
    {
      debounceMs,
      validateOnMount,
      validateOnChange,
      validateOnBlur,
      ...options,
    }
  );

  // Combined state
  const [fieldState, setFieldState] = useState({
    isValidating: false,
    isValid: true,
    isTouched: false,
    isDirty: false,
    errors: [] as string[],
    warnings: [] as string[],
    infos: [] as string[],
  });

  // Refs
  const lastValue = useRef<unknown>(undefined);
  const validationCount = useRef(0);

  /**
   * Get current field value from form context
   */
  const getCurrentValue = useCallback(() => {
    if (formContext) {
      return formContext.getValues(fieldName);
    }
    return undefined;
  }, [formContext, fieldName]);

  /**
   * Get all form values
   */
  const getAllFormValues = useCallback(() => {
    if (formContext) {
      return formContext.getValues();
    }
    return {};
  }, [formContext]);

  /**
   * Create validation context
   */
  const createContext = useCallback((): ValidationContext => {
    return {
      field: fieldName,
      formValues: getAllFormValues(),
      meta: {
        validationCount: validationCount.current,
        lastValue: lastValue.current,
      },
    };
  }, [fieldName, getAllFormValues]);

  /**
   * Validate field with all rules
   */
  const validateField = useCallback(async (
    value: unknown = getCurrentValue(),
    immediate = false
  ): Promise<ValidationResult> => {
    validationCount.current++;
    const context = createContext();

    setFieldState(prev => ({
      ...prev,
      isValidating: true,
    }));

    try {
      // Run sync validation first
      const syncResult = await syncValidation.validateField(
        fieldName,
        value,
        context.formValues,
        immediate
      );

      // If sync validation fails and stopOnFirstError is true, skip async
      if (stopOnFirstError && !syncResult.isValid) {
        setFieldState(prev => ({
          ...prev,
          isValidating: false,
          isValid: false,
          errors: syncResult.errors.map(e => e.message),
          warnings: syncResult.warnings.map(w => w.message),
          infos: syncResult.infos.map(i => i.message),
        }));
        return syncResult;
      }

      // Run async validations (simplified to use primary validation)
      const asyncResults = [];
      if (asyncValidations.length > 0) {
        try {
          const primaryResult = await primaryAsyncValidation.validate(value, context, immediate);
          asyncResults.push(primaryResult);
        } catch (error) {
          asyncResults.push({
            isValid: false,
            errors: [{
              field: fieldName,
              message: error instanceof Error ? error.message : 'Async validation failed',
              code: 'ASYNC_ERROR',
              severity: 'error' as const,
            }],
            warnings: [],
            infos: [],
          });
        }
      }

      // Combine all results
      const allErrors = [
        ...syncResult.errors,
        ...asyncResults.flatMap(r => r.errors),
      ];
      const allWarnings = [
        ...syncResult.warnings,
        ...asyncResults.flatMap(r => r.warnings),
      ];
      const allInfos = [
        ...syncResult.infos,
        ...asyncResults.flatMap(r => r.infos),
      ];

      const combinedResult: ValidationResult = {
        isValid: allErrors.length === 0,
        errors: allErrors,
        warnings: allWarnings,
        infos: allInfos,
      };

      // Update field state
      setFieldState(prev => ({
        ...prev,
        isValidating: false,
        isValid: combinedResult.isValid,
        errors: allErrors.map(e => e.message),
        warnings: allWarnings.map(w => w.message),
        infos: allInfos.map(i => i.message),
      }));

      // Update form context if available
      if (formContext && formContext.setError && formContext.clearErrors) {
        if (allErrors.length > 0) {
          formContext.setError(fieldName, {
            type: 'validation',
            message: allErrors[0]?.message || 'Validation error',
          });
        } else {
          formContext.clearErrors(fieldName);
        }
      }

      lastValue.current = value;
      return combinedResult;
    } catch (error) {
      const errorResult: ValidationResult = {
        isValid: false,
        errors: [{
          field: fieldName,
          message: error instanceof Error ? error.message : 'Validation failed',
          code: 'VALIDATION_ERROR',
          severity: 'error',
        }],
        warnings: [],
        infos: [],
      };

      setFieldState(prev => ({
        ...prev,
        isValidating: false,
        isValid: false,
        errors: [errorResult.errors[0]?.message || 'Validation error'],
        warnings: [],
        infos: [],
      }));

      return errorResult;
    }
  }, [
    getCurrentValue,
    createContext,
    fieldName,
    syncValidation,
    primaryAsyncValidation,
    asyncValidations,
    stopOnFirstError,
    formContext,
  ]);

  /**
   * Handle field change
   */
  const handleChange = useCallback(async (value: unknown) => {
    setFieldState(prev => ({
      ...prev,
      isDirty: true,
    }));

    if (validateOnChange) {
      await validateField(value, false);
    }
  }, [validateOnChange, validateField]);

  /**
   * Handle field blur
   */
  const handleBlur = useCallback(async () => {
    setFieldState(prev => ({
      ...prev,
      isTouched: true,
    }));

    if (validateOnBlur) {
      await validateField(getCurrentValue(), true);
    }
  }, [validateOnBlur, validateField, getCurrentValue]);

  /**
   * Clear field validation
   */
  const clearValidation = useCallback(() => {
    syncValidation.clearFieldValidation(fieldName);
    primaryAsyncValidation.clear();

    setFieldState({
      isValidating: false,
      isValid: true,
      isTouched: false,
      isDirty: false,
      errors: [],
      warnings: [],
      infos: [],
    });

    if (formContext && formContext.clearErrors) {
      formContext.clearErrors(fieldName);
    }
  }, [syncValidation, primaryAsyncValidation, fieldName, formContext]);

  /**
   * Reset field validation state
   */
  const reset = useCallback(() => {
    clearValidation();
    lastValue.current = undefined;
    validationCount.current = 0;
  }, [clearValidation]);

  /**
   * Force validation
   */
  const forceValidate = useCallback(async () => {
    return validateField(getCurrentValue(), true);
  }, [validateField, getCurrentValue]);

  /**
   * Check if field has specific error
   */
  const hasError = useCallback((errorCode?: string) => {
    if (!errorCode) {
      return fieldState.errors.length > 0;
    }

    const syncErrors = syncValidation.getFieldValidation(fieldName).errors;
    const asyncErrors = primaryAsyncValidation.errors;

    return [...syncErrors, ...asyncErrors].some(error => error.code === errorCode);
  }, [fieldState.errors, syncValidation, fieldName, primaryAsyncValidation]);

  /**
   * Get first error message
   */
  const getFirstError = useCallback(() => {
    return fieldState.errors[0] || null;
  }, [fieldState.errors]);

  /**
   * Get all error messages
   */
  const getAllErrors = useCallback(() => {
    return fieldState.errors;
  }, [fieldState.errors]);

  /**
   * Check if field is currently being validated
   */
  const isValidating = fieldState.isValidating ||
    syncValidation.isValidating ||
    primaryAsyncValidation.isValidating;

  // Validate on mount if configured
  useEffect(() => {
    if (validateOnMount) {
      validateField(getCurrentValue(), true);
    }
  }, [validateOnMount, validateField, getCurrentValue]);

  // Watch for form context changes
  useEffect(() => {
    if (formContext && formContext.watch) {
      const subscription = formContext.watch((value, { name }) => {
        if (name === fieldName && validateOnChange) {
          handleChange(value[fieldName]);
        }
      });

      return () => subscription.unsubscribe();
    }
    return undefined;
  }, [formContext, fieldName, validateOnChange, handleChange]);

  return {
    // State
    isValidating,
    isValid: fieldState.isValid,
    isTouched: fieldState.isTouched,
    isDirty: fieldState.isDirty,
    errors: fieldState.errors,
    warnings: fieldState.warnings,
    infos: fieldState.infos,

    // Methods
    validateField,
    handleChange,
    handleBlur,
    clearValidation,
    reset,
    forceValidate,

    // Utility methods
    hasError,
    getFirstError,
    getAllErrors,

    // Field props for easy integration
    fieldProps: {
      onChange: handleChange,
      onBlur: handleBlur,
      error: fieldState.errors[0] || undefined,
      isInvalid: !fieldState.isValid,
      isValidating,
    },
  };
};
