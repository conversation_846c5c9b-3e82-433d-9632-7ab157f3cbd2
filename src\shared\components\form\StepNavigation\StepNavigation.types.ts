import { ReactNode } from 'react';

/**
 * Props cho StepNavigation component
 */
export interface StepNavigationProps {
  /**
   * Bước hiện tại
   */
  currentStep: number;

  /**
   * Tổng số bước
   */
  totalSteps: number;

  /**
   * Callback khi next
   */
  onNext?: () => void;

  /**
   * Callback khi previous
   */
  onPrevious?: () => void;

  /**
   * Callback khi submit
   */
  onSubmit?: () => void;

  /**
   * <PERSON><PERSON> disable nút next không
   */
  isNextDisabled?: boolean;

  /**
   * <PERSON><PERSON> disable nút previous không
   */
  isPreviousDisabled?: boolean;

  /**
   * <PERSON><PERSON> disable nút submit không
   */
  isSubmitDisabled?: boolean;

  /**
   * Có hiển thị nút previous không
   */
  showPrevious?: boolean;

  /**
   * Có hiển thị nút next không
   */
  showNext?: boolean;

  /**
   * <PERSON><PERSON> hiển thị nút submit không
   */
  showSubmit?: boolean;

  /**
   * Label cho nút next
   */
  nextLabel?: string;

  /**
   * Label cho nút previous
   */
  previousLabel?: string;

  /**
   * Label cho nút submit
   */
  submitLabel?: string;

  /**
   * Icon cho nút next
   */
  nextIcon?: ReactNode;

  /**
   * Icon cho nút previous
   */
  previousIcon?: ReactNode;

  /**
   * Icon cho nút submit
   */
  submitIcon?: ReactNode;

  /**
   * Vị trí của navigation
   */
  position?: 'top' | 'bottom' | 'left' | 'right';

  /**
   * Căn chỉnh của navigation
   */
  align?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';

  /**
   * Class bổ sung
   */
  className?: string;
}
