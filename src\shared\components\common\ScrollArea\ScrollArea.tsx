import { ReactNode, forwardRef, HTMLAttributes } from 'react';
import '@/shared/styles/scrollbar.css';

export interface ScrollAreaProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Nội dung bên trong ScrollArea
   */
  children: ReactNode;

  /**
   * Chiều cao của ScrollArea
   * Có thể là giá trị cụ thể (ví dụ: '300px') hoặc 'auto' hoặc '100%'
   */
  height?: string;

  /**
   * Chiều cao tối đa của ScrollArea
   * Có thể là giá trị cụ thể (ví dụ: '300px')
   */
  maxHeight?: string;

  /**
   * Chiều rộng của ScrollArea
   * Có thể là giá trị cụ thể (ví dụ: '300px') hoặc 'auto' hoặc '100%'
   */
  width?: string;

  /**
   * Tự động ẩn thanh cuộn khi không sử dụng
   */
  autoHide?: boolean;

  /**
   * Ẩn hoàn toàn thanh cuộn nhưng vẫn giữ chức năng cuộn
   */
  invisible?: boolean;

  /**
   * Sử dụng style đặc biệt cho chat panel (thêm padding-right)
   */
  isChatPanel?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hướng cuộn
   */
  direction?: 'vertical' | 'horizontal' | 'both';
}

/**
 * Component ScrollArea cung cấp thanh cuộn tùy chỉnh
 * Thanh cuộn mỏng, chìm mờ và hiện đại
 */
const ScrollArea = forwardRef<HTMLDivElement, ScrollAreaProps>(
  (
    {
      children,
      height = 'auto',
      maxHeight,
      width = '100%',
      autoHide = true,
      invisible = false,
      isChatPanel = false,
      className = '',
      direction = 'vertical',
      ...rest
    },
    ref
  ) => {
    // Xác định overflow dựa trên hướng cuộn
    const overflowClass = {
      vertical: 'overflow-y-auto overflow-x-hidden',
      horizontal: 'overflow-x-auto overflow-y-hidden',
      both: 'overflow-auto',
    }[direction];

    // Xác định class cho scrollbar
    const scrollbarClass = invisible ? 'invisible-scrollbar' : autoHide ? 'auto-hide' : '';

    // Xác định class cho chat panel
    const chatPanelClass = isChatPanel ? 'chat-panel-scroll' : '';

    return (
      <div
        ref={ref}
        className={`custom-scrollbar ${scrollbarClass} ${overflowClass} ${chatPanelClass} ${className}`}
        style={{
          height,
          maxHeight,
          width,
          // Đảm bảo scrollbar không chiếm không gian bên trong container
          scrollbarGutter: 'stable',
        }}
        {...rest}
      >
        {children}
      </div>
    );
  }
);

ScrollArea.displayName = 'ScrollArea';

export default ScrollArea;
