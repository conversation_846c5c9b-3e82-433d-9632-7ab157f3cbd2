import React, { useRef } from 'react';
import { z } from 'zod';
import { FieldValues } from 'react-hook-form';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Icon,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
// import { useTranslation } from 'react-i18next';

// Schema validation với Zod
const enhancedFormSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username must be at most 20 characters'),
  email: z.string().min(1, 'Email is required').email('Invalid email format'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  confirmPassword: z.string().min(1, 'Please confirm your password'),
  phone: z.string().optional(),
  website: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
});

// type EnhancedFormValues = z.infer<typeof enhancedFormSchema>;

/**
 * Demo page cho Enhanced Form Components
 */
const EnhancedFormDemo: React.FC = () => {
  // const { t } = useTranslation();
  const formRef = useRef<FormRef<FieldValues>>(null);

  const handleSubmit = async (values: FieldValues) => {
    console.log('Enhanced Form values:', values);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
  };

  const handleReset = () => {
    formRef.current?.reset();
  };

  const handleSetSuccess = () => {
    formRef.current?.setValues({
      username: 'johndoe',
      email: '<EMAIL>',
    });
  };

  return (
    <div className="container mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Enhanced Form Components Demo
        </h1>
        <p className="text-muted">
          Demonstration of enhanced FormItem features with various layouts and validations
        </p>
      </div>

      {/* Enhanced Form Demo */}
      <Card title="Enhanced Form Features" className="mb-8">
        <Form
          ref={formRef}
          schema={enhancedFormSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            username: '',
            email: '',
            password: '',
            confirmPassword: '',
            phone: '',
            website: '',
          }}
          validateOnChange={true}
          resetOnSubmitSuccess={false}
          confirmOnDirty={true}
          scrollToError={true}
          focusOnError={true}
          submitOnEnter={true}
          successMessage="Form submitted successfully!"
          errorMessage="Please fix the errors below"
          className="space-y-6"
        >
          {/* Username with tooltip and prefix */}
          <FormItem
            name="username"
            label="Username"
            required
            tooltip="Choose a unique username between 3-20 characters"
            description="This will be your public display name"
            prefix={<Icon name="user" className="text-muted" />}
            labelPosition="top"
            errorAnimation="fadeIn"
            size="md"
            colon={false}
            asterisk={true}
          >
            <Input placeholder="Enter your username" />
          </FormItem>

          {/* Email with horizontal layout */}
          <FormItem
            name="email"
            label="Email Address"
            required
            tooltip="We'll use this to send you important updates"
            prefix={<Icon name="mail" className="text-muted" />}
            suffix={<Icon name="check" className="text-success" />}
            labelPosition="left"
            errorAnimation="slideDown"
            size="md"
            colon={true}
            asterisk={true}
          >
            <Input type="email" placeholder="Enter your email" />
          </FormItem>

          {/* Password with validation status */}
          <FormItem
            name="password"
            label="Password"
            required
            tooltip="Must contain uppercase, lowercase, and numbers"
            description="Choose a strong password with at least 8 characters"
            prefix={<Icon name="lock" className="text-muted" />}
            labelPosition="top"
            errorAnimation="fadeIn"
            size="md"
            asterisk={true}
          >
            <Input type="password" placeholder="Enter your password" />
          </FormItem>

          {/* Confirm Password */}
          <FormItem
            name="confirmPassword"
            label="Confirm Password"
            required
            prefix={<Icon name="lock" className="text-muted" />}
            labelPosition="top"
            errorAnimation="fadeIn"
            size="md"
            asterisk={true}
          >
            <Input type="password" placeholder="Confirm your password" />
          </FormItem>

          {/* Phone with right label position */}
          <FormItem
            name="phone"
            label="Phone"
            tooltip="Optional: We'll only use this for account recovery"
            prefix={<Icon name="phone" className="text-muted" />}
            labelPosition="right"
            errorAnimation="slideDown"
            size="md"
          >
            <Input type="tel" placeholder="Enter your phone number" />
          </FormItem>

          {/* Website with success message */}
          <FormItem
            name="website"
            label="Website"
            tooltip="Share your personal or professional website"
            prefix={<span className="text-muted">https://</span>}
            successMessage="Valid URL format"
            validateStatus="success"
            labelPosition="top"
            errorAnimation="fadeIn"
            size="md"
          >
            <Input placeholder="www.example.com" />
          </FormItem>

          {/* Form Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" variant="primary" size="md">
              Submit Form
            </Button>
            <Button type="button" variant="outline" size="md" onClick={handleReset}>
              Reset
            </Button>
            <Button type="button" variant="ghost" size="md" onClick={handleSetSuccess}>
              Set Sample Data
            </Button>
          </div>
        </Form>
      </Card>

      {/* Size Variations */}
      <Card title="Size Variations" className="mb-8">
        <div className="space-y-6">
          <FormItem
            label="Small Size"
            size="sm"
            tooltip="This is a small form field"
            description="Small size is good for compact layouts"
          >
            <Input placeholder="Small input" />
          </FormItem>

          <FormItem
            label="Medium Size (Default)"
            size="md"
            tooltip="This is a medium form field"
            description="Medium size is the default and most commonly used"
          >
            <Input placeholder="Medium input" />
          </FormItem>

          <FormItem
            label="Large Size"
            size="lg"
            tooltip="This is a large form field"
            description="Large size is good for important or prominent fields"
          >
            <Input placeholder="Large input" />
          </FormItem>
        </div>
      </Card>

      {/* Label Positions */}
      <Card title="Label Positions" className="mb-8">
        <div className="space-y-6">
          <FormItem
            label="Top Label"
            labelPosition="top"
            tooltip="Label positioned above the input"
            colon={true}
          >
            <Input placeholder="Input with top label" />
          </FormItem>

          <FormItem
            label="Left Label"
            labelPosition="left"
            tooltip="Label positioned to the left of input"
            colon={true}
          >
            <Input placeholder="Input with left label" />
          </FormItem>

          <FormItem
            label="Right Label"
            labelPosition="right"
            tooltip="Label positioned to the right of input"
            colon={true}
          >
            <Input placeholder="Input with right label" />
          </FormItem>
        </div>
      </Card>

      {/* Validation States */}
      <Card title="Validation States" className="mb-8">
        <div className="space-y-6">
          <FormItem
            label="Success State"
            validateStatus="success"
            successMessage="This field is valid!"
            prefix={<Icon name="check" className="text-success" />}
          >
            <Input placeholder="Valid input" defaultValue="<EMAIL>" />
          </FormItem>

          <FormItem
            label="Warning State"
            validateStatus="warning"
            helpText="This field has a warning"
            prefix={<Icon name="alert-triangle" className="text-warning" />}
          >
            <Input placeholder="Warning input" />
          </FormItem>

          <FormItem
            label="Error State"
            validateStatus="error"
            helpText="This field has an error"
            prefix={<Icon name="x" className="text-error" />}
            errorAnimation="fadeIn"
          >
            <Input placeholder="Error input" />
          </FormItem>

          <FormItem
            label="Validating State"
            validateStatus="validating"
            prefix={<Icon name="loader" className="text-muted animate-spin" />}
          >
            <Input placeholder="Validating input" />
          </FormItem>
        </div>
      </Card>

      {/* Usage Guide */}
      <Card title="Usage Guide" className="mb-8">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-lg mb-2">Enhanced FormItem Features</h3>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted">
              <li><strong>Tooltip:</strong> Hover over labels to see helpful tooltips</li>
              <li><strong>Prefix/Suffix:</strong> Add icons or text before/after inputs</li>
              <li><strong>Label Positions:</strong> Position labels top, left, or right</li>
              <li><strong>Animations:</strong> Smooth error message animations</li>
              <li><strong>Validation States:</strong> Visual feedback for different states</li>
              <li><strong>Size Variants:</strong> Small, medium, and large sizes</li>
              <li><strong>Accessibility:</strong> Proper ARIA attributes and keyboard navigation</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default EnhancedFormDemo;
