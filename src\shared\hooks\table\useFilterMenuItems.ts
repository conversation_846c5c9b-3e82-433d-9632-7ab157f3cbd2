/**
 * Hook tạo menuItems từ filterOptions
 * @module useFilterMenuItems
 */
import { useMemo } from 'react';
import { ModernMenuItem } from '@/shared/components/common/ModernMenu';
import { FilterOption } from './useFilterOptions';

/**
 * Tạo menuItems từ filterOptions
 * @param options Danh sách tùy chọn lọc
 * @param selectedId ID của tùy chọn đang được chọn
 * @param onFilterChange Callback khi thay đổi filter
 * @returns Danh sách menuItems
 */
export function useFilterMenuItems(
  options: FilterOption[],
  selectedId: string,
  onFilterChange: (id: string) => void
): ModernMenuItem[] {
  return useMemo(() => {
    return options.map(option => ({
      id: option.id,
      label: option.label,
      icon: option.icon,
      onClick: () => onFilterChange(option.id),
      active: selectedId === option.id,
    }));
  }, [options, selectedId, onFilterChange]);
}
