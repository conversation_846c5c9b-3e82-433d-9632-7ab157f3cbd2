import React from 'react';
import { Z_INDEX } from '@/shared/constants/breakpoints';
import symbolImage from '@/shared/assets/images/symbol.png';

interface ChatButtonProps {
  /**
   * Callback khi click vào nút chat
   */
  onClick: () => void;

  /**
   * Vị trí của nút chat
   */
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';

  /**
   * Khoảng cách từ cạnh màn hình
   */
  offset?: number;

  /**
   * Kích thước của nút
   */
  size?: number;

  /**
   * Hiệu ứng khi hover
   */
  hoverEffect?: 'scale' | 'rotate' | 'pulse' | 'none';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị nút chat ở góc màn hình mobile
 */
const ChatButton: React.FC<ChatButtonProps> = ({
  onClick,
  position = 'bottom-right',
  offset = 16,
  size = 50,
  hoverEffect = 'scale',
  className = '',
}) => {
  // Position classes
  const positionClasses = {
    'bottom-right': 'bottom-0 right-0',
    'bottom-left': 'bottom-0 left-0',
    'top-right': 'top-0 right-0',
    'top-left': 'top-0 left-0',
  };

  // Hover effect classes
  const hoverEffectClasses = {
    scale: 'hover:scale-110',
    rotate: 'hover:rotate-12',
    pulse: 'hover:animate-pulse',
    none: '',
  };

  return (
    <button
      className={`fixed ${positionClasses[position]} rounded-full bg-menu-gradient text-white shadow-lg hover:shadow-xl transition-all duration-300 ${hoverEffectClasses[hoverEffect]} ${className}`}
      style={{
        margin: offset,
        zIndex: Z_INDEX.fixed,
        width: `${size}px`,
        height: `${size}px`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={onClick}
      aria-label="Open chat"
    >
      <img src={symbolImage} alt="Chat Symbol" className="w-[92%] h-[92%] animate-gentle-spin" />
    </button>
  );
};

export default ChatButton;
