import { useState, useCallback, useMemo, useEffect } from 'react';
import { get } from 'lodash';

interface UseTableSelectionOptions<T> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * Khóa duy nhất cho mỗi hàng
   */
  rowKey?: string | ((record: T) => string);

  /**
   * Các khóa đã chọn mặc định
   */
  defaultSelectedRowKeys?: React.Key[];

  /**
   * Callback khi thay đổi lựa chọn
   */
  onChange?: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;

  /**
   * Hàm lấy thuộc tính checkbox
   */
  getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
}

interface UseTableSelectionResult<T> {
  /**
   * Các khóa đã chọn
   */
  selectedRowKeys: React.Key[];

  /**
   * <PERSON><PERSON><PERSON> hàng đã chọn
   */
  selectedRows: T[];

  /**
   * Hàm chọn tất cả
   */
  selectAll: () => void;

  /**
   * Hàm bỏ chọn tất cả
   */
  deselectAll: () => void;

  /**
   * Hàm chọn một hàng
   */
  selectRow: (key: React.Key) => void;

  /**
   * Hàm bỏ chọn một hàng
   */
  deselectRow: (key: React.Key) => void;

  /**
   * Hàm kiểm tra xem một hàng có được chọn không
   */
  isSelected: (key: React.Key) => boolean;

  /**
   * Hàm kiểm tra xem tất cả các hàng có được chọn không
   */
  isAllSelected: boolean;

  /**
   * Hàm kiểm tra xem một hàng có bị vô hiệu hóa không
   */
  isDisabled: (record: T) => boolean;
}

/**
 * Hook xử lý chọn hàng trong bảng
 */
export function useTableSelection<T>({
  data,
  rowKey = 'id',
  defaultSelectedRowKeys = [],
  onChange,
  getCheckboxProps,
}: UseTableSelectionOptions<T>): UseTableSelectionResult<T> {
  // Đảm bảo data luôn là array
  const safeData = useMemo(() => Array.isArray(data) ? data : [], [data]);

  // State lưu trữ các khóa đã chọn
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(defaultSelectedRowKeys);

  // Sync với external state khi defaultSelectedRowKeys thay đổi
  useEffect(() => {
    // Chỉ cập nhật nếu thực sự khác nhau để tránh infinite loop
    const currentKeys = selectedRowKeys.map(String).sort();
    const newKeys = defaultSelectedRowKeys.map(String).sort();

    if (JSON.stringify(currentKeys) !== JSON.stringify(newKeys)) {
      setSelectedRowKeys(defaultSelectedRowKeys);
    }
  }, [defaultSelectedRowKeys, selectedRowKeys]);

  // Lấy khóa duy nhất cho mỗi hàng
  const getRowKeyValue = useCallback(
    (record: T): React.Key => {
      if (typeof rowKey === 'function') {
        return rowKey(record);
      }
      const value = get(record, rowKey);
      // Xử lý trường hợp value là undefined, null hoặc không có toString method
      if (value == null) {
        console.warn(`Row key "${rowKey}" is null or undefined for record:`, record);
        return '';
      }
      return String(value);
    },
    [rowKey]
  );

  // Lấy các hàng đã chọn
  const selectedRows = useMemo(() => {
    return safeData.filter(record => {
      const key = getRowKeyValue(record);
      return selectedRowKeys.includes(key);
    });
  }, [safeData, selectedRowKeys, getRowKeyValue]);

  // Kiểm tra xem một hàng có bị vô hiệu hóa không
  const isDisabled = useCallback(
    (record: T): boolean => {
      if (!getCheckboxProps) {
        return false;
      }
      const props = getCheckboxProps(record);
      return !!props.disabled;
    },
    [getCheckboxProps]
  );

  // Lấy các hàng có thể chọn
  const selectableRows = useMemo(() => {
    return safeData.filter(record => !isDisabled(record));
  }, [safeData, isDisabled]);

  // Lấy các khóa có thể chọn
  const selectableKeys = useMemo(() => {
    return selectableRows.map(getRowKeyValue);
  }, [selectableRows, getRowKeyValue]);

  // Kiểm tra xem tất cả các hàng có được chọn không
  const isAllSelected = useMemo(() => {
    // Nếu không có hàng nào có thể chọn hoặc không có hàng nào được chọn, trả về false
    if (selectableKeys.length === 0 || selectedRowKeys.length === 0) {
      return false;
    }
    // Kiểm tra xem tất cả các hàng có thể chọn có được chọn không
    return selectableKeys.every(key => selectedRowKeys.includes(key));
  }, [selectableKeys, selectedRowKeys]);

  // Hàm chọn tất cả
  const selectAll = useCallback(() => {
    // Nếu không có dữ liệu hoặc không có hàng nào có thể chọn, không làm gì cả
    if (safeData.length === 0 || selectableKeys.length === 0) {
      return;
    }

    const newSelectedRowKeys = [...selectedRowKeys];

    // Thêm các khóa chưa được chọn
    selectableKeys.forEach(key => {
      if (!newSelectedRowKeys.includes(key)) {
        newSelectedRowKeys.push(key);
      }
    });

    setSelectedRowKeys(newSelectedRowKeys);
    onChange?.(
      newSelectedRowKeys,
      safeData.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
    );
  }, [selectedRowKeys, selectableKeys, onChange, safeData, getRowKeyValue]);

  // Hàm bỏ chọn tất cả
  const deselectAll = useCallback(() => {
    const newSelectedRowKeys = selectedRowKeys.filter(key => !selectableKeys.includes(key));

    setSelectedRowKeys(newSelectedRowKeys);
    onChange?.(
      newSelectedRowKeys,
      safeData.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
    );
  }, [selectedRowKeys, selectableKeys, onChange, safeData, getRowKeyValue]);

  // Hàm chọn một hàng
  const selectRow = useCallback(
    (key: React.Key) => {
      if (selectedRowKeys.includes(key)) {
        return;
      }

      const newSelectedRowKeys = [...selectedRowKeys, key];
      setSelectedRowKeys(newSelectedRowKeys);
      onChange?.(
        newSelectedRowKeys,
        safeData.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
      );
    },
    [selectedRowKeys, onChange, safeData, getRowKeyValue]
  );

  // Hàm bỏ chọn một hàng
  const deselectRow = useCallback(
    (key: React.Key) => {
      if (!selectedRowKeys.includes(key)) {
        return;
      }

      const newSelectedRowKeys = selectedRowKeys.filter(k => k !== key);
      setSelectedRowKeys(newSelectedRowKeys);
      onChange?.(
        newSelectedRowKeys,
        safeData.filter(record => newSelectedRowKeys.includes(getRowKeyValue(record)))
      );
    },
    [selectedRowKeys, onChange, safeData, getRowKeyValue]
  );

  // Hàm kiểm tra xem một hàng có được chọn không
  const isSelected = useCallback(
    (key: React.Key) => {
      return selectedRowKeys.includes(key);
    },
    [selectedRowKeys]
  );

  return {
    selectedRowKeys,
    selectedRows,
    selectAll,
    deselectAll,
    selectRow,
    deselectRow,
    isSelected,
    isAllSelected,
    isDisabled,
  };
}
