import React from 'react';
import { useTheme } from '@/shared/contexts/theme';

export type ThemeType = 'light' | 'dark' | 'custom';

export interface ThemeToggleProps {
  /**
   * Theme hiện tại
   */
  theme?: ThemeType;

  /**
   * Hàm xử lý khi thay đổi theme
   */
  onToggle?: () => void;

  /**
   * Kích thước của toggle
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Text cho light mode
   */
  lightText?: string;

  /**
   * Text cho dark mode
   */
  darkText?: string;
}

/**
 * Component toggle chuyển đổi giữa light mode và dark mode
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  theme: propTheme,
  onToggle: propOnToggle,
  size = 'md',
  className = '',
  lightText = 'Light',
  darkText = 'Dark',
}) => {
  // Sử dụng useTheme hook nếu không có theme và onToggle được truyền vào
  const { themeMode, toggleTheme } = useTheme();

  // Sử dụng prop nếu có, nếu không thì sử dụng giá trị từ context
  const theme = propTheme || (themeMode === 'custom' ? 'light' : themeMode);
  const onToggle = propOnToggle || toggleTheme;
  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    sm: 'h-5 p-0.5',
    md: 'h-6 p-0.5',
    lg: 'h-7 p-1',
  }[size];

  const buttonSizeClasses = {
    sm: 'w-6 h-4',
    md: 'w-7 h-5',
    lg: 'w-8 h-5',
  }[size];

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-3.5 h-3.5',
    lg: 'w-4 h-4',
  }[size];

  return (
    <div
      className={`flex items-center bg-gray-200 dark:bg-dark-lighter rounded-full ${sizeClasses} ${className}`}
    >
      <button
        className={`flex items-center justify-center rounded-full ${buttonSizeClasses} transition-all ${
          theme === 'light' ? 'bg-white text-yellow-500 shadow' : 'text-gray-500'
        }`}
        onClick={e => {
          e.stopPropagation();
          if (theme !== 'light') onToggle();
        }}
        title={lightText}
      >
        <svg
          className={iconSizeClasses}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
          />
        </svg>
      </button>
      <button
        className={`flex items-center justify-center rounded-full ${buttonSizeClasses} transition-all ${
          theme === 'dark' ? 'bg-dark-light text-blue-300 shadow' : 'text-gray-500'
        }`}
        onClick={e => {
          e.stopPropagation();
          if (theme !== 'dark') onToggle();
        }}
        title={darkText}
      >
        <svg
          className={iconSizeClasses}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
          />
        </svg>
      </button>
    </div>
  );
};

export default ThemeToggle;
