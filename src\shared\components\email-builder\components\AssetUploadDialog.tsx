import React, { useState, useRef } from 'react';
import { Modal, Button, Input, Typography, Tabs } from '@/shared/components/common';
import { Upload, Link, Video, Image } from 'lucide-react';
import { Asset } from '../types';

interface AssetUploadDialogProps {
  open: boolean;
  onClose: () => void;
  onUpload: (asset: Asset) => void;
}

const AssetUploadDialog: React.FC<AssetUploadDialogProps> = ({
  open,
  onClose,
  onUpload
}) => {
  const [activeTab, setActiveTab] = useState('image');
  const [imageUrl, setImageUrl] = useState('');
  const [imageName, setImageName] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState('');
  const [videoName, setVideoName] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setImageFile(file);
      setImageName(file.name);

      // Tạo URL tạm thời cho file
      const fileUrl = URL.createObjectURL(file);
      setImageUrl(fileUrl);
    }
  };

  // Xử lý khi nhấn nút tải lên
  const handleUpload = () => {
    if (activeTab === 'image') {
      if (imageUrl) {
        const newAsset: Asset = {
          id: `asset-${Date.now()}`,
          type: 'image',
          src: imageUrl,
          alt: imageName || 'Uploaded Image',
          name: imageName || 'Uploaded Image',
          category: 'uploaded'
        };
        onUpload(newAsset);
        resetForm();
        onClose();
      }
    } else if (activeTab === 'url') {
      if (imageUrl) {
        const newAsset: Asset = {
          id: `asset-${Date.now()}`,
          type: 'image',
          src: imageUrl,
          alt: imageName || 'Image from URL',
          name: imageName || 'Image from URL',
          category: 'urls'
        };
        onUpload(newAsset);
        resetForm();
        onClose();
      }
    } else if (activeTab === 'video') {
      if (videoUrl) {
        const newAsset: Asset = {
          id: `asset-${Date.now()}`,
          type: 'video',
          src: videoUrl,
          alt: videoName || 'Video',
          name: videoName || 'Video',
          category: 'videos'
        };
        onUpload(newAsset);
        resetForm();
        onClose();
      }
    }
  };

  // Reset form
  const resetForm = () => {
    setImageUrl('');
    setImageName('');
    setImageFile(null);
    setVideoUrl('');
    setVideoName('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const tabItems = [
    {
      key: 'image',
      label: 'Hình ảnh',
      icon: <Image size={16} />,
      children: (
        <div className="space-y-4">
          <div
            className="flex flex-col items-center justify-center border-2 border-dashed border-border rounded-lg p-8 cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
            onDragOver={(e) => e.preventDefault()}
            onDrop={(e) => {
              e.preventDefault();
              const files = e.dataTransfer.files;
              if (files.length > 0) {
                const file = files[0];
                if (file && file.type.startsWith('image/')) {
                  setImageFile(file);
                  setImageName(file.name);
                  const fileUrl = URL.createObjectURL(file);
                  setImageUrl(fileUrl);
                }
              }
            }}
          >
            <Upload size={32} className="mb-4 text-muted-foreground" />
            <Typography variant="body1" className="text-center text-foreground mb-2">
              Nhấp để chọn hoặc kéo thả file vào đây
            </Typography>
            <Typography variant="caption" className="text-center text-muted-foreground">
              Hỗ trợ: JPG, PNG, GIF, SVG, WebP
            </Typography>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={handleFileChange}
            />
          </div>

          {imageFile && (
            <div className="p-4 bg-muted/30 rounded-lg">
              <Typography variant="body2" className="font-medium mb-1">File đã chọn:</Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {imageFile.name} ({Math.round(imageFile.size / 1024)} KB)
              </Typography>
            </div>
          )}

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên hình ảnh</Typography>
            <Input
              value={imageName}
              onChange={(e) => setImageName(e.target.value)}
              placeholder="Nhập tên hình ảnh"
              className="w-full"
            />
          </div>
        </div>
      )
    },
    {
      key: 'url',
      label: 'URL',
      icon: <Link size={16} />,
      children: (
        <div className="space-y-4">
          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">URL hình ảnh</Typography>
            <Input
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="https://example.com/image.jpg"
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên hình ảnh</Typography>
            <Input
              value={imageName}
              onChange={(e) => setImageName(e.target.value)}
              placeholder="Nhập tên hình ảnh"
              className="w-full"
            />
          </div>
        </div>
      )
    },
    {
      key: 'video',
      label: 'Video',
      icon: <Video size={16} />,
      children: (
        <div className="space-y-4">
          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">URL video</Typography>
            <Input
              value={videoUrl}
              onChange={(e) => setVideoUrl(e.target.value)}
              placeholder="https://example.com/video.mp4"
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">Tên video</Typography>
            <Input
              value={videoName}
              onChange={(e) => setVideoName(e.target.value)}
              placeholder="Nhập tên video"
              className="w-full"
            />
          </div>
        </div>
      )
    }
  ];

  return (
    <Modal
      title="Tải lên tài nguyên"
      isOpen={open}
      onClose={() => {
        resetForm();
        onClose();
      }}
      size="lg"
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onClose}>Hủy</Button>
          <Button variant="primary" onClick={handleUpload}>Tải lên</Button>
        </div>
      }
    >
      <Tabs
        items={tabItems}
        activeKey={activeTab}
        onChange={setActiveTab}
        type="underline"
        size="md"
      />
    </Modal>
  );
};

export default AssetUploadDialog;
