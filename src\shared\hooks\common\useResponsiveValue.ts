import { useMemo } from 'react';
import { Breakpoint, BREAKPOINTS } from '@/shared/constants/breakpoints.ts';
import { useCurrentBreakpoint } from './useMediaQuery.ts';

type ResponsiveValue<T> = {
  [key in Breakpoint]?: T;
} & {
  base: T; // Default value for all breakpoints
};

/**
 * Hook to get a value based on the current breakpoint
 * Returns the value for the current breakpoint, or the value for the closest smaller breakpoint,
 * or the base value if no matching breakpoint is found
 *
 * @param values Object containing values for different breakpoints
 * @returns The value for the current breakpoint
 *
 * @example
 * const fontSize = useResponsiveValue({
 *   base: '16px',
 *   md: '18px',
 *   lg: '20px',
 * });
 * // Returns '16px' on mobile, '18px' on tablet, '20px' on desktop
 */
function useResponsiveValue<T>(values: ResponsiveValue<T>): T {
  const currentBreakpoint = useCurrentBreakpoint();

  return useMemo(() => {
    // If there's a value for the current breakpoint, return it
    if (values[currentBreakpoint] !== undefined) {
      return values[currentBreakpoint] as T;
    }

    // Otherwise, find the closest smaller breakpoint with a defined value
    const breakpointKeys = Object.keys(BREAKPOINTS) as Breakpoint[];
    const currentIndex = breakpointKeys.indexOf(currentBreakpoint);

    // Look for smaller breakpoints with defined values
    for (let i = currentIndex - 1; i >= 0; i--) {
      const breakpoint = breakpointKeys[i];
      if (breakpoint && values[breakpoint] !== undefined) {
        return values[breakpoint] as T;
      }
    }

    // If no matching breakpoint is found, return the base value
    return values.base;
  }, [currentBreakpoint, values]);
}

export default useResponsiveValue;
