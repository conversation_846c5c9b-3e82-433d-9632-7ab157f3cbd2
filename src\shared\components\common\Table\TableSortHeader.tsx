import React from 'react';
import { SortOrder } from './types';

interface TableSortHeaderProps {
  /**
   * Cột đang sắp xếp
   */
  column: string;

  /**
   * Tiêu đề cột
   */
  title: React.ReactNode;

  /**
   * Thứ tự sắp xếp
   */
  sortOrder: SortOrder | null;

  /**
   * Callback khi thay đổi sắp xếp
   */
  onSort?: ((column: string, order: SortOrder) => void) | undefined;

  /**
   * Class tùy chỉnh
   */
  className?: string | undefined;
}

/**
 * Component header có thể sắp xếp
 */
const TableSortHeader: React.FC<TableSortHeaderProps> = ({
  column,
  title,
  sortOrder,
  onSort,
  className = '',
}) => {
  // Xử lý sự kiện click
  const handleClick = () => {
    if (!onSort) return;

    // Thay đổi thứ tự sắp xếp: null -> asc -> desc -> null
    let nextOrder: SortOrder = null;
    if (sortOrder === null) {
      nextOrder = 'asc';
    } else if (sortOrder === 'asc') {
      nextOrder = 'desc';
    } else {
      nextOrder = null;
    }

    onSort(column, nextOrder);
  };

  return (
    <th
      className={`${className} cursor-pointer select-none`}
      onClick={handleClick}
      aria-sort={sortOrder === 'asc' ? 'ascending' : sortOrder === 'desc' ? 'descending' : 'none'}
    >
      <div className="flex items-center">
        <span>{title}</span>
      </div>
    </th>
  );
};

export default TableSortHeader;
