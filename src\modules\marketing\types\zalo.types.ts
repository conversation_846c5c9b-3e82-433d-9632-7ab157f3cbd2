/**
 * Types cho tích hợp Zalo OA/ZNS
 */

/**
 * Trạng thái tài khoản Zalo OA
 */
export enum ZaloOAStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  ERROR = 'error',
}

/**
 * Trạng thái template ZNS
 */
export enum ZNSTemplateStatus {
  APPROVED = 'approved',
  PENDING = 'pending',
  REJECTED = 'rejected',
}

/**
 * Trạng thái gửi ZNS
 */
export enum ZNSMessageStatus {
  SENT = 'sent',
  DELIVERED = 'delivered',
  READ = 'read',
  FAILED = 'failed',
  PENDING = 'pending',
}

/**
 * Loại tin nhắn Zalo
 */
export enum ZaloMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  TEMPLATE = 'template',
  LIST = 'list',
  BUTTON = 'button',
}

/**
 * DTO tài khoản Zalo OA
 */
export interface ZaloOAAccountDto {
  id: number;
  userId: number;
  oaId: string;
  name: string;
  avatar?: string;
  accessToken: string;
  refreshToken: string;
  status: ZaloOAStatus;
  followersCount: number;
  expireTime: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO người theo dõi Zalo OA
 */
export interface ZaloFollowerDto {
  id: string;
  userId: number;
  oaId: number;
  followerId: string;
  displayName: string;
  avatar?: string;
  phone?: string;
  gender?: 'male' | 'female' | 'unknown';
  status: 'ACTIVE' | 'BLOCKED' | 'UNFOLLOWED';
  tags: string[];
  followedAt: number;
  lastInteractionAt?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tin nhắn Zalo
 */
export interface ZaloMessageDto {
  id: number;
  userId: number;
  oaId: number;
  followerId: string;
  messageId: string;
  type: ZaloMessageType;
  content: string;
  attachments?: ZaloAttachmentDto[];
  isFromUser: boolean;
  sentAt: number;
  createdAt: number;
}

/**
 * DTO đính kèm tin nhắn Zalo
 */
export interface ZaloAttachmentDto {
  type: string;
  url: string;
  name?: string;
  size?: number;
  thumbnailUrl?: string;
}

/**
 * DTO template ZNS
 */
export interface ZNSTemplateDto {
  id: number;
  userId: number;
  oaId: number;
  templateId: string;
  name: string;
  content: string;
  params: string[];
  status: ZNSTemplateStatus;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tin nhắn ZNS
 */
export interface ZNSMessageDto {
  id: number;
  userId: number;
  oaId: number;
  templateId: number;
  phone: string;
  params: Record<string, string>;
  status: ZNSMessageStatus;
  errorCode?: string;
  errorMessage?: string;
  sentAt: number;
  deliveredAt?: number;
  readAt?: number;
  createdAt: number;
}

/**
 * DTO chiến dịch ZNS
 */
export interface ZNSCampaignDto {
  id: number;
  userId: number;
  oaId: number;
  name: string;
  templateId: number;
  status: string;
  totalMessages: number;
  deliveredCount: number;
  readCount: number;
  failedCount: number;
  startedAt: number;
  completedAt?: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * DTO tham số truy vấn tài khoản Zalo OA
 */
export interface ZaloOAAccountQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: ZaloOAStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn người theo dõi Zalo
 */
export interface ZaloFollowerQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  oaId?: number;
  status?: 'ACTIVE' | 'BLOCKED' | 'UNFOLLOWED';
  tags?: string[];
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn tin nhắn Zalo
 */
export interface ZaloMessageQueryDto {
  page?: number;
  limit?: number;
  oaId: number;
  followerId: string;
  startDate?: number;
  endDate?: number;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tham số truy vấn template ZNS
 */
export interface ZNSTemplateQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  oaId?: number;
  status?: ZNSTemplateStatus;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tạo tài khoản Zalo OA
 */
export interface CreateZaloOAAccountDto {
  oaId: string;
  accessToken: string;
  refreshToken: string;
  name: string;
  avatar?: string;
}

/**
 * DTO cập nhật tài khoản Zalo OA
 */
export interface UpdateZaloOAAccountDto {
  name?: string;
  status?: ZaloOAStatus;
}

/**
 * DTO tạo template ZNS
 */
export interface CreateZNSTemplateDto {
  oaId: number;
  name: string;
  content: string;
  params: string[];
}

/**
 * DTO gửi tin nhắn ZNS
 */
export interface SendZNSMessageDto {
  oaId: number;
  templateId: number;
  phone: string;
  params: Record<string, string>;
}

/**
 * DTO tạo chiến dịch ZNS
 */
export interface CreateZNSCampaignDto {
  oaId: number;
  name: string;
  templateId: number;
  phones: string[];
  params: Record<string, string>[];
  scheduleTime?: number;
}

// PagingResponseDto đã được định nghĩa trong shared types