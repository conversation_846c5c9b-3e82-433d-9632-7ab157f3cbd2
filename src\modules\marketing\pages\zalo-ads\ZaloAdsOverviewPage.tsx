
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  TrendingUp,
  Target,
  DollarSign,
  Users,
  BarChart3,
  Settings,
  Eye
} from 'lucide-react';
import { Card, Button } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAdsMetrics } from '../../hooks/zalo-ads/useZaloAdsMetrics';

/**
 * Trang tổng quan Zalo Ads
 */
export function ZaloAdsOverviewPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  // Mock data - sẽ được thay thế bằng API thực
  const { data: metrics } = useZaloAdsMetrics();

  const mockMetrics = {
    totalCampaigns: 12,
    activeCampaigns: 8,
    totalSpend: 15420000,
    totalImpressions: 2450000,
    totalClicks: 18500,
    totalConversions: 1250,
    averageCpc: 833,
    averageCtr: 0.75,
    averageRoas: 3.2
  };

  const currentMetrics = metrics || mockMetrics;

  const handleCreateCampaign = () => {
    navigate('/marketing/zalo-ads/campaigns?action=create');
  };

  const handleViewCampaigns = () => {
    navigate('/marketing/zalo-ads/campaigns');
  };

  const handleViewReports = () => {
    navigate('/marketing/zalo-ads/reports');
  };

  const handleManageAccounts = () => {
    navigate('/marketing/zalo-ads/accounts');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zaloAds.overview.title', 'Zalo Ads Overview')}
        description={t('marketing:zaloAds.overview.description', 'Quản lý và theo dõi hiệu suất quảng cáo Zalo')}
        actions={
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleManageAccounts} className="gap-2">
              <Settings className="h-4 w-4" />
              {t('marketing:zaloAds.overview.manageAccounts', 'Quản lý tài khoản')}
            </Button>
            <Button onClick={handleCreateCampaign} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('marketing:zaloAds.overview.createCampaign', 'Tạo chiến dịch')}
            </Button>
          </div>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.overview.stats.totalCampaigns', 'Tổng chiến dịch')}
            </span>
            <Target className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {currentMetrics.totalCampaigns}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {currentMetrics.activeCampaigns} {t('marketing:zaloAds.overview.stats.active', 'đang chạy')}
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.overview.stats.totalSpend', 'Tổng chi phí')}
            </span>
            <DollarSign className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(currentMetrics.totalSpend)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.overview.stats.thisMonth', 'Tháng này')}
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.overview.stats.totalClicks', 'Tổng clicks')}
            </span>
            <Users className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">
            {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalClicks)}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            CTR: {currentMetrics.averageCtr}%
          </p>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.overview.stats.roas', 'ROAS')}
            </span>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-600">
            {currentMetrics.averageRoas}x
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.overview.stats.returnOnAdSpend', 'Return on Ad Spend')}
          </p>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={handleCreateCampaign}>
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
              <Plus className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">
                {t('marketing:zaloAds.overview.quickActions.createCampaign', 'Tạo chiến dịch mới')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('marketing:zaloAds.overview.quickActions.createCampaignDesc', 'Bắt đầu quảng cáo trên Zalo')}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={handleViewCampaigns}>
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-green-500 to-teal-600 flex items-center justify-center">
              <Eye className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">
                {t('marketing:zaloAds.overview.quickActions.viewCampaigns', 'Xem chiến dịch')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('marketing:zaloAds.overview.quickActions.viewCampaignsDesc', 'Quản lý chiến dịch hiện tại')}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={handleViewReports}>
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 rounded-lg bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold">
                {t('marketing:zaloAds.overview.quickActions.viewReports', 'Xem báo cáo')}
              </h3>
              <p className="text-sm text-muted-foreground">
                {t('marketing:zaloAds.overview.quickActions.viewReportsDesc', 'Phân tích hiệu suất quảng cáo')}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Performance */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">
            {t('marketing:zaloAds.overview.recentPerformance.title', 'Hiệu suất gần đây')}
          </h3>
          <Button variant="outline" size="sm" onClick={handleViewReports}>
            {t('marketing:zaloAds.overview.recentPerformance.viewAll', 'Xem tất cả')}
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-blue-600">
              {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalImpressions)}
            </div>
            <div className="text-sm text-muted-foreground">
              {t('marketing:zaloAds.overview.recentPerformance.impressions', 'Lượt hiển thị')}
            </div>
          </Card>

          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-green-600">
              {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalClicks)}
            </div>
            <div className="text-sm text-muted-foreground">
              {t('marketing:zaloAds.overview.recentPerformance.clicks', 'Lượt nhấp')}
            </div>
          </Card>

          <Card className="text-center p-4">
            <div className="text-2xl font-bold text-orange-600">
              {new Intl.NumberFormat('vi-VN').format(currentMetrics.totalConversions)}
            </div>
            <div className="text-sm text-muted-foreground">
              {t('marketing:zaloAds.overview.recentPerformance.conversions', 'Chuyển đổi')}
            </div>
          </Card>
        </div>
      </Card>

      {/* Getting Started */}
      {currentMetrics.totalCampaigns === 0 && (
        <Card className="p-6 text-center">
          <div className="max-w-md mx-auto">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {t('marketing:zaloAds.overview.gettingStarted.title', 'Bắt đầu với Zalo Ads')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t('marketing:zaloAds.overview.gettingStarted.description', 'Tạo chiến dịch quảng cáo đầu tiên để tiếp cận hàng triệu người dùng Zalo')}
            </p>
            <Button onClick={handleCreateCampaign} className="gap-2">
              <Plus className="h-4 w-4" />
              {t('marketing:zaloAds.overview.gettingStarted.createFirst', 'Tạo chiến dịch đầu tiên')}
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}

export default ZaloAdsOverviewPage;
