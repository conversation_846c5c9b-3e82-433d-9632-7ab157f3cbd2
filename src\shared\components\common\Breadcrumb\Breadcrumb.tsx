import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/shared/components/common';

export interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactNode;
  onClick?: () => void;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  const { t } = useTranslation();

  return (
    <nav className={`flex items-center overflow-hidden ${className}`} aria-label="Breadcrumb">
      <ol className="flex items-center space-x-2 overflow-hidden">
        {items.map((item, index) => {
          const isLast = index === items.length - 1;
          const itemContent = (
            <div className="flex items-center overflow-hidden">
              {item.icon && (
                <span className={item.label ? 'mr-1 flex-shrink-0' : 'flex-shrink-0'}>
                  {item.icon}
                </span>
              )}
              {item.label && (
                <span
                  className={`${isLast ? 'font-semibold' : 'text-gray-600 dark:text-gray-400'} font-medium truncate`}
                >
                  {t(item.label)}
                </span>
              )}
            </div>
          );

          return (
            <li key={index} className="flex items-center overflow-hidden">
              {index > 0 && (
                <span className="mx-1.5 text-gray-400 dark:text-gray-500 flex-shrink-0">
                  <Icon name="chevron-right" size="sm" />
                </span>
              )}

              {item.path && !isLast ? (
                <Link
                  to={item.path}
                  className="hover:text-primary dark:hover:text-primary-light transition-colors duration-200 flex items-center"
                  onClick={item.onClick}
                >
                  <span className="relative group">
                    {itemContent}
                    <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                  </span>
                </Link>
              ) : (
                <div
                  className={`${!isLast && item.onClick ? 'cursor-pointer hover:text-primary dark:hover:text-primary-light' : ''} transition-colors duration-200 flex items-center`}
                  onClick={item.onClick}
                >
                  <span className={`relative group ${!isLast && item.onClick ? '' : ''}`}>
                    {itemContent}
                    {!isLast && item.onClick && (
                      <span className="absolute -bottom-0.5 left-0 w-0 h-0.5 bg-primary group-hover:w-full transition-all duration-300"></span>
                    )}
                  </span>
                </div>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
