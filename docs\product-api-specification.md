# Product API Specification

## 📋 API Specification để Backend khớp với Frontend

Dựa trên phân tích từ `frontend-backend-product-api-analysis.md`.

## 🚀 API Endpoints

### 1. Create Product
```
POST /user/products
Content-Type: application/json
Authorization: Bearer {token}
```

#### Request Body
```json
{
  "name": "iPhone 15 Pro Max",
  "productType": "PHYSICAL",
  "typePrice": "HAS_PRICE",
  "price": {
    "listPrice": 29990000,
    "salePrice": 27990000,
    "currency": "VND"
  },
  "description": "iPhone 15 Pro Max với chip A17 Pro",
  "tags": ["smartphone", "apple", "premium"],
  "shipmentConfig": {
    "lengthCm": 20,
    "widthCm": 10,
    "heightCm": 2,
    "weightGram": 221
  },
  "imagesMediaTypes": [
    "image/jpeg",
    "image/png",
    "image/webp"
  ],
  "classifications": [
    {
      "name": "<PERSON><PERSON> mục",
      "value": "Đi<PERSON>n thoại"
    }
  ],
  "customFields": [
    {
      "name": "<PERSON>àn hình",
      "type": "text",
      "value": "6.7 inch Super Retina XDR",
      "required": true,
      "description": "Kích thước và loại màn hình"
    },
    {
      "name": "Bộ nhớ",
      "type": "select",
      "value": "256GB",
      "required": true,
      "options": ["128GB", "256GB", "512GB", "1TB"]
    }
  ],
  "classifications": [
    {
      "name": "iPhone 15 Pro Max - Titan Tự Nhiên - 256GB",
      "sku": "IP15PM-TN-256",
      "price": {
        "listPrice": 29990000,
        "salePrice": 27990000,
        "currency": "VND"
      },
      "stock": 50,
      "attributes": ["Color: Titan Tự Nhiên", "Storage: 256GB"],
      "description": "Phiên bản Titan Tự Nhiên 256GB",
      "isActive": true,
      "image": "https://example.com/iphone15-titan.jpg",
      "weight": 221,
      "dimensions": {
        "length": 159.9,
        "width": 76.7,
        "height": 8.25
      }
    },
    {
      "name": "iPhone 15 Pro Max - Titan Xanh - 256GB",
      "sku": "IP15PM-TX-256",
      "price": {
        "listPrice": 29990000,
        "salePrice": 27990000,
        "currency": "VND"
      },
      "stock": 30,
      "attributes": ["Color: Titan Xanh", "Storage: 256GB"],
      "description": "Phiên bản Titan Xanh 256GB",
      "isActive": true,
      "image": "https://example.com/iphone15-blue.jpg",
      "weight": 221
    }
  ]
}
```

#### Response Success (201)
```json
{
  "code": 201,
  "message": "Tạo sản phẩm thành công",
  "result": {
    "id": 123,
    "name": "iPhone 15 Pro Max",
    "productType": "PHYSICAL",
    "typePrice": "HAS_PRICE",
    "price": {
      "listPrice": 29990000,
      "salePrice": 27990000,
      "currency": "VND"
    },
    "description": "iPhone 15 Pro Max với chip A17 Pro",
    "tags": ["smartphone", "apple", "premium"],
    "shipmentConfig": {
      "lengthCm": 20,
      "widthCm": 10,
      "heightCm": 2,
      "weightGram": 221
    },
    "classifications": [
      {
        "id": 1,
        "name": "Danh mục",
        "value": "Điện thoại",
        "createdAt": 1703123456789
      }
    ],
    "customFields": [
      {
        "id": 1,
        "name": "Màn hình",
        "type": "text",
        "value": "6.7 inch Super Retina XDR",
        "required": true,
        "description": "Kích thước và loại màn hình",
        "createdAt": 1703123456789,
        "updatedAt": 1703123456789
      },
      {
        "id": 2,
        "name": "Bộ nhớ",
        "type": "select",
        "value": "256GB",
        "required": true,
        "options": ["128GB", "256GB", "512GB", "1TB"],
        "createdAt": 1703123456789,
        "updatedAt": 1703123456789
      }
    ],
    "classifications": [
      {
        "id": 1,
        "name": "iPhone 15 Pro Max - Titan Tự Nhiên - 256GB",
        "sku": "IP15PM-TN-256",
        "price": {
          "listPrice": 29990000,
          "salePrice": 27990000,
          "currency": "VND"
        },
        "stock": 50,
        "attributes": ["Color: Titan Tự Nhiên", "Storage: 256GB"],
        "description": "Phiên bản Titan Tự Nhiên 256GB",
        "isActive": true,
        "image": "https://example.com/iphone15-titan.jpg",
        "weight": 221,
        "dimensions": {
          "length": 159.9,
          "width": 76.7,
          "height": 8.25
        },
        "createdAt": 1703123456789,
        "updatedAt": 1703123456789
      },
      {
        "id": 2,
        "name": "iPhone 15 Pro Max - Titan Xanh - 256GB",
        "sku": "IP15PM-TX-256",
        "price": {
          "listPrice": 29990000,
          "salePrice": 27990000,
          "currency": "VND"
        },
        "stock": 30,
        "attributes": ["Color: Titan Xanh", "Storage: 256GB"],
        "description": "Phiên bản Titan Xanh 256GB",
        "isActive": true,
        "image": "https://example.com/iphone15-blue.jpg",
        "weight": 221,
        "dimensions": null,
        "createdAt": 1703123456789,
        "updatedAt": 1703123456789
      }
    ],
    "createdAt": 1703123456789,
    "updatedAt": 1703123456789,
    "createdBy": 456
  },
  "uploadUrls": {
    "imagesUploadUrls": [
      "https://s3.amazonaws.com/bucket/upload-url-1?signature=...",
      "https://s3.amazonaws.com/bucket/upload-url-2?signature=...",
      "https://s3.amazonaws.com/bucket/upload-url-3?signature=..."
    ]
  }
}
```

#### Response Error (400)
```json
{
  "code": 400,
  "message": "Dữ liệu không hợp lệ",
  "errors": [
    {
      "field": "name",
      "message": "Tên sản phẩm không được để trống"
    },
    {
      "field": "price",
      "message": "Giá sản phẩm là bắt buộc khi loại giá là HAS_PRICE"
    },
    {
      "field": "variants[0].sku",
      "message": "SKU đã tồn tại trong hệ thống"
    }
  ]
}
```

## 🔧 Validation Rules

### Product Level
- `name`: Required, max 255 characters
- `productType`: Optional, enum values: PHYSICAL, DIGITAL, SERVICE, SUBSCRIPTION, BUNDLE
- `typePrice`: Required, enum values: HAS_PRICE, STRING_PRICE, NO_PRICE
- `price`: Required when typePrice = HAS_PRICE or STRING_PRICE
- `description`: Optional, max 1000 characters
- `tags`: Optional array of strings
- `imagesMediaTypes`: Optional array of strings, valid MIME types for images

### Custom Fields
- `name`: Required, max 255 characters
- `type`: Required, values: text, number, select, date, textarea, checkbox
- `value`: Optional string
- `required`: Optional boolean, default false
- `options`: Required for select type
- `description`: Optional, max 500 characters

### Custom Group Form
- `name`: Required, max 255 characters
- `description`: Optional, max 500 characters
- `fields`: Optional array of custom fields
- `layout`: Optional, values: grid, list, tabs
- `config`: Optional object for layout configuration

### Variants
- `name`: Required, max 255 characters
- `sku`: Optional, max 100 characters, must be unique
- `price`: Optional price object
- `stock`: Optional number, min 0
- `attributes`: Optional array of strings
- `isActive`: Optional boolean, default true
- `weight`: Optional number in grams
- `dimensions`: Optional object with length, width, height

### Shipment Config
- `lengthCm`: Optional number
- `widthCm`: Optional number  
- `heightCm`: Optional number
- `weightGram`: Optional number

## 🔍 Business Logic

### Price Validation
```typescript
if (typePrice === 'HAS_PRICE') {
  // price must be HasPriceDto with listPrice and salePrice
  if (!price || !price.listPrice || !price.salePrice) {
    throw new Error('Giá niêm yết và giá bán là bắt buộc');
  }
  if (price.salePrice > price.listPrice) {
    throw new Error('Giá bán không được lớn hơn giá niêm yết');
  }
}

if (typePrice === 'STRING_PRICE') {
  // price must be StringPriceDto with priceDescription
  if (!price || !price.priceDescription) {
    throw new Error('Mô tả giá là bắt buộc');
  }
}

if (typePrice === 'NO_PRICE') {
  // price should be null
  price = null;
}
```

### Variant SKU Validation
```typescript
// SKUs must be unique across all variants
const skus = variants.map(v => v.sku).filter(Boolean);
if (skus.length !== new Set(skus).size) {
  throw new Error('SKU của các biến thể phải là duy nhất');
}

// Check SKU uniqueness in database
const existingSku = await variantRepository.findOne({ where: { sku: In(skus) } });
if (existingSku) {
  throw new Error(`SKU ${existingSku.sku} đã tồn tại trong hệ thống`);
}
```

### Custom Field Type Validation
```typescript
const validateCustomField = (field: CreateCustomFieldDto) => {
  if (field.type === 'select' && (!field.options || field.options.length === 0)) {
    throw new Error(`Trường ${field.name} loại select phải có danh sách options`);
  }
  
  if (field.type === 'number' && field.value && isNaN(Number(field.value))) {
    throw new Error(`Trường ${field.name} phải là số hợp lệ`);
  }
  
  if (field.required && !field.value) {
    throw new Error(`Trường ${field.name} là bắt buộc`);
  }
};
```

## 📊 Database Schema

### Products Table Updates
```sql
ALTER TABLE products ADD COLUMN product_type VARCHAR(50) DEFAULT 'PHYSICAL';
```

### New Tables
```sql
-- Product Custom Fields
CREATE TABLE product_custom_fields (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  type VARCHAR(50) NOT NULL,
  value TEXT,
  required BOOLEAN DEFAULT FALSE,
  options JSON,
  description VARCHAR(500),
  placeholder VARCHAR(100),
  default_value TEXT,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);

-- Product Custom Group Forms  
CREATE TABLE product_custom_group_forms (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description VARCHAR(500),
  fields JSON,
  config JSON,
  layout VARCHAR(50),
  is_active BOOLEAN DEFAULT TRUE,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);

-- Product Variants
CREATE TABLE product_variants (
  id SERIAL PRIMARY KEY,
  product_id INTEGER REFERENCES products(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  sku VARCHAR(100) UNIQUE,
  price JSON,
  stock INTEGER DEFAULT 0,
  attributes JSON,
  description VARCHAR(500),
  is_active BOOLEAN DEFAULT TRUE,
  image VARCHAR(255),
  weight INTEGER,
  dimensions JSON,
  created_at BIGINT NOT NULL,
  updated_at BIGINT NOT NULL
);
```

## 🚀 Additional APIs (Future)

### Get Product with Relations
```
GET /user/products/{id}?include=customFields,variants,customGroupForms
```

### Update Product Variant
```
PUT /user/products/{productId}/variants/{variantId}
```

### Delete Product Variant
```
DELETE /user/products/{productId}/variants/{variantId}
```

### Bulk Update Variant Stock
```
PATCH /user/products/{productId}/variants/stock
```

## ✅ Implementation Priority

### Phase 1 (High Priority)
1. ✅ Update DTOs and validation
2. ✅ Create database entities
3. ✅ Update service layer
4. ✅ Test basic product creation

### Phase 2 (Medium Priority)  
5. Add variant management APIs
6. Add custom field management APIs
7. Add inventory tracking
8. Add search and filtering

### Phase 3 (Low Priority)
9. Add bulk operations
10. Add product import/export
11. Add product analytics
12. Add product recommendations
