import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { messageService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import { MessageQueryDto } from '../types';

// Get messages for all agents
export const useMessages = (params?: MessageQueryDto) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.MESSAGES(params || {}),
    queryFn: () => messageService.getMessages(params),
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get message history for specific agent
export const useMessageHistory = (agentId: string, params?: MessageQueryDto, enabled = true) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.MESSAGES({ agentId, ...params }),
    queryFn: () => messageService.getMessageHistory(agentId, params),
    enabled: enabled && !!agentId,
    staleTime: 30 * 1000,
    gcTime: 2 * 60 * 1000,
  });
};

// Get message statistics
export const useMessageStats = (
  agentId: string,
  period?: { start: string; end: string },
  enabled = true
) => {
  return useQuery({
    queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'stats', agentId, period],
    queryFn: () => messageService.getMessageStats(agentId, period),
    enabled: enabled && !!agentId,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Send message mutation
export const useSendMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ agentId, content }: { agentId: string; content: Record<string, unknown> }) =>
      messageService.sendMessage(agentId, content),
    onSuccess: (_, { agentId }) => {
      // Invalidate message queries for this agent
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.MESSAGES({ agentId }),
      });

      // Invalidate stats
      queryClient.invalidateQueries({
        queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.ALL, 'stats', agentId],
      });
    },
  });
};

// Delete message mutation
export const useDeleteMessage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (messageId: string) => messageService.deleteMessage(messageId),
    onSuccess: () => {
      // Invalidate all message queries
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Bulk delete messages mutation
export const useBulkDeleteMessages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (messageIds: string[]) => messageService.bulkDeleteMessages(messageIds),
    onSuccess: () => {
      // Invalidate all message queries
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.ALL,
      });
    },
  });
};

// Custom hook for message management
export const useMessageManager = (agentId: string) => {
  const sendMutation = useSendMessage();
  const deleteMutation = useDeleteMessage();
  const bulkDeleteMutation = useBulkDeleteMessages();

  const sendMessage = (content: Record<string, unknown>) => {
    return sendMutation.mutate({ agentId, content });
  };

  const deleteMessage = (messageId: string) => {
    return deleteMutation.mutate(messageId);
  };

  const bulkDeleteMessages = (messageIds: string[]) => {
    return bulkDeleteMutation.mutate(messageIds);
  };

  return {
    sendMessage,
    deleteMessage,
    bulkDeleteMessages,
    isSending: sendMutation.isPending,
    isDeleting: deleteMutation.isPending || bulkDeleteMutation.isPending,
    error: sendMutation.error || deleteMutation.error || bulkDeleteMutation.error,
  };
};

// Custom hook for real-time message updates
export const useRealtimeMessages = (agentId: string) => {
  const queryClient = useQueryClient();

  // This would be used with WebSocket service
  const invalidateMessages = () => {
    queryClient.invalidateQueries({
      queryKey: EXTERNAL_AGENT_QUERY_KEYS.MESSAGES({ agentId }),
    });
  };

  return {
    invalidateMessages,
  };
};
