import { createContext, useState, useEffect, ReactNode, useMemo } from 'react';
import {
  ThemeMode,
  Theme,
  themes,
  lightTheme,
  darkTheme,
  applyTheme,
  createCustomTheme,
} from '@/shared/styles/theme';

export interface ThemeContextType {
  /**
   * Theme hiện tại (light, dark, custom)
   */
  themeMode: ThemeMode;

  /**
   * Đối tượng Theme đầy đủ hiện tại
   */
  currentTheme: Theme;

  /**
   * Danh sách các theme có sẵn
   */
  availableThemes: Record<string, Theme>;

  /**
   * Chuyển đổi giữa light và dark mode
   */
  toggleTheme: () => void;

  /**
   * Thiết lập theme theo mode
   */
  setThemeMode: (mode: ThemeMode) => void;

  /**
   * Áp dụng một theme cụ thể
   */
  setTheme: (theme: Theme) => void;

  /**
   * Tạo và áp dụng theme tùy chỉnh
   */
  setCustomTheme: (customizations: Partial<Theme>) => void;

  /**
   * <PERSON><PERSON>u theme hiện tại
   */
  saveCurrentTheme: () => void;

  /**
   * Đặt lại về theme mặc định
   */
  resetToDefaultTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

// Khóa lưu trữ theme trong localStorage
const THEME_MODE_KEY = 'theme-mode';
const CUSTOM_THEME_KEY = 'custom-theme';

export const ThemeProvider = ({ children }: ThemeProviderProps) => {
  // State cho theme mode (light, dark, custom)
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    // Kiểm tra theme đã lưu trong localStorage
    const savedThemeMode = localStorage.getItem(THEME_MODE_KEY) as ThemeMode | null;

    // Kiểm tra nếu người dùng ưa thích dark mode
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

    return savedThemeMode || (prefersDark ? 'dark' : 'light');
  });

  // State cho theme tùy chỉnh
  const [customTheme, setCustomTheme] = useState<Theme | null>(() => {
    const savedCustomTheme = localStorage.getItem(CUSTOM_THEME_KEY);
    return savedCustomTheme ? JSON.parse(savedCustomTheme) : null;
  });

  // Danh sách các theme có sẵn
  const availableThemes = useMemo(() => {
    const allThemes = { ...themes };
    if (customTheme) {
      allThemes['custom'] = customTheme;
    }
    return allThemes;
  }, [customTheme]);

  // Theme hiện tại
  const currentTheme = useMemo(() => {
    if (themeMode === 'custom' && customTheme) {
      return customTheme;
    }
    return themeMode === 'dark' ? darkTheme : lightTheme;
  }, [themeMode, customTheme]);

  // Áp dụng theme khi thay đổi
  useEffect(() => {
    // Lưu theme mode vào localStorage
    localStorage.setItem(THEME_MODE_KEY, themeMode);

    // Áp dụng theme
    applyTheme(currentTheme);
  }, [themeMode, currentTheme]);

  // Chuyển đổi giữa light và dark mode
  const toggleTheme = (): void => {
    setThemeMode(prevMode => {
      if (prevMode === 'custom') return 'light';
      return prevMode === 'light' ? 'dark' : 'light';
    });
  };

  // Thiết lập theme cụ thể
  const setTheme = (theme: Theme): void => {
    if (theme.mode === 'custom') {
      setCustomTheme(theme);
      setThemeMode('custom');
    } else {
      setThemeMode(theme.mode);
    }
  };

  // Tạo và áp dụng theme tùy chỉnh
  const setCustomThemeHandler = (customizations: Partial<Theme>): void => {
    const baseTheme = themeMode === 'dark' ? darkTheme : lightTheme;
    const newCustomTheme = createCustomTheme(baseTheme, customizations);
    setCustomTheme(newCustomTheme);
    setThemeMode('custom');
  };

  // Lưu theme hiện tại
  const saveCurrentTheme = (): void => {
    if (themeMode === 'custom' && customTheme) {
      localStorage.setItem(CUSTOM_THEME_KEY, JSON.stringify(customTheme));
    }
  };

  // Đặt lại về theme mặc định
  const resetToDefaultTheme = (): void => {
    setThemeMode('light');
    localStorage.removeItem(CUSTOM_THEME_KEY);
    setCustomTheme(null);
  };

  const value = {
    themeMode,
    currentTheme,
    availableThemes,
    toggleTheme,
    setThemeMode,
    setTheme,
    setCustomTheme: setCustomThemeHandler,
    saveCurrentTheme,
    resetToDefaultTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

export default ThemeContext;
