# PortalMenu Component

Component `PortalMenu` là một component dùng chung để tạo dropdown menu sử dụng React Portal. Component này giúp hiển thị menu bên ngoài DOM tree hiện tại, tránh các vấn đề về z-index và overflow.

## Tính năng

- ✅ Sử dụng React Portal để render menu
- ✅ Hỗ trợ click outside để đóng menu
- ✅ Tùy chỉnh vị trí hiển thị
- ✅ Tùy chỉnh kích thước và styling
- ✅ Hỗ trợ dark mode
- ✅ Animation fade-in khi hiển thị

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `menuRef` | `React.RefObject<HTMLDivElement>` | ✅ | - | Ref cho menu element để xử lý click outside |
| `position` | `{ top: number; left: number }` | ✅ | - | Vị trí hiển thị menu |
| `children` | `React.ReactNode` | ✅ | - | Nội dung hiển thị trong menu |
| `onClose` | `() => void` | ✅ | - | Callback khi đóng menu |
| `width` | `string \| number` | ❌ | `'220px'` | Chiều rộng của menu |
| `zIndex` | `number` | ❌ | `100000` | Z-index của menu |
| `className` | `string` | ❌ | `''` | Class CSS bổ sung |

## Cách sử dụng

### Sử dụng cơ bản

```tsx
import React, { useState, useRef } from 'react';
import { PortalMenu } from '@/shared/components/common';

const MyComponent: React.FC = () => {
  const [showMenu, setShowMenu] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLButtonElement>(null);

  const handleToggleMenu = () => {
    if (showMenu) {
      setShowMenu(false);
    } else {
      // Tính toán vị trí menu
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        setMenuPosition({
          top: rect.bottom + 8,
          left: rect.left
        });
        setShowMenu(true);
      }
    }
  };

  return (
    <>
      <button ref={triggerRef} onClick={handleToggleMenu}>
        Toggle Menu
      </button>
      
      {showMenu && (
        <PortalMenu
          menuRef={menuRef}
          position={menuPosition}
          onClose={() => setShowMenu(false)}
        >
          <div className="p-4">
            <div>Menu Item 1</div>
            <div>Menu Item 2</div>
            <div>Menu Item 3</div>
          </div>
        </PortalMenu>
      )}
    </>
  );
};
```

### Sử dụng với tùy chỉnh

```tsx
<PortalMenu
  menuRef={menuRef}
  position={menuPosition}
  onClose={() => setShowMenu(false)}
  width="300px"
  zIndex={999999}
  className="border-2 border-primary"
>
  <CustomMenuContent />
</PortalMenu>
```

### Tính toán vị trí menu thông minh

```tsx
const calculateMenuPosition = (triggerElement: HTMLElement) => {
  const rect = triggerElement.getBoundingClientRect();
  const menuWidth = 220;
  const menuHeight = 300;
  const margin = 8;
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  // Tính toán vị trí left, căn giữa menu với trigger
  const triggerCenter = rect.left + rect.width / 2;
  let left = triggerCenter - menuWidth / 2;

  // Điều chỉnh để tránh tràn màn hình
  if (left < margin) {
    left = margin;
  }
  if (left + menuWidth > viewportWidth - margin) {
    left = viewportWidth - menuWidth - margin;
  }

  // Tính toán vị trí top
  let top = rect.bottom + margin;
  if (top + menuHeight > viewportHeight - margin) {
    top = rect.top - menuHeight - margin;
  }
  top = Math.max(margin, top);

  return { top, left };
};
```

## Lưu ý

1. **Click Outside**: Component tự động xử lý click outside để đóng menu
2. **Z-index**: Mặc định sử dụng z-index cao (100000) để đảm bảo menu hiển thị trên các element khác
3. **Portal**: Menu được render vào `document.body` thông qua React Portal
4. **Responsive**: Cần tính toán vị trí menu cẩn thận để tránh tràn màn hình
5. **Memory Leak**: Component tự động cleanup event listener khi unmount

## Ví dụ thực tế

Component này đã được sử dụng trong:
- `MenuIconBar` component cho column visibility menu
- Các dropdown menu khác trong hệ thống

## Migration từ component cũ

Nếu bạn đang sử dụng component PortalMenu cũ, chỉ cần:

1. Import từ shared components:
```tsx
// Cũ
const PortalMenu = () => { ... }

// Mới
import { PortalMenu } from '@/shared/components/common';
```

2. Sử dụng như bình thường, props không thay đổi.
