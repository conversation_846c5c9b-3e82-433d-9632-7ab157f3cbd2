import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import {
  Users,
  MessageCircle,
  Tag as TagIcon,
  Download,
  UserPlus,
  Settings
} from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
  Button,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloFollowers, useZaloFollowerManagement } from '../../hooks/zalo/useZaloFollowers';
import { useZaloAccount } from '../../hooks/zalo/useZaloAccounts';
import type { ZaloFollowerQueryDto, ZaloFollowerDto } from '../../types/zalo.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Zalo Followers
 */
export function ZaloFollowersPage() {
  const { t } = useTranslation('marketing');
  const { oaId } = useParams<{ oaId: string }>();

  const [selectedFollowers, setSelectedFollowers] = useState<string[]>([]);

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  const oaIdNumber = parseInt(oaId || '0');
  const { data: accountData } = useZaloAccount(oaIdNumber);
  const { bulkOperation } = useZaloFollowerManagement();

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ZaloFollowerDto>[]>(
    () => [
      {
        key: 'select',
        title: '',
        width: '50px',
        render: (_: unknown, record: ZaloFollowerDto) => (
          <input
            type="checkbox"
            checked={selectedFollowers.includes(record.id)}
            onChange={(e) => {
              if (e.target.checked) {
                setSelectedFollowers(prev => [...prev, record.id]);
              } else {
                setSelectedFollowers(prev => prev.filter(id => id !== record.id));
              }
            }}
            className="rounded border-gray-300"
          />
        ),
      },
      {
        key: 'name',
        title: t('marketing:zalo.followers.table.name', 'Tên'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: ZaloFollowerDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-8 w-8 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-semibold text-sm">
              {record.displayName?.charAt(0).toUpperCase() || 'U'}
            </div>
            <span className="font-medium">{String(value || 'Unknown')}</span>
          </div>
        ),
      },
      {
        key: 'phone',
        title: t('marketing:zalo.followers.table.phone', 'Số điện thoại'),
        dataIndex: 'phone',
        sortable: true,
        render: (value: unknown) => (
          <span className="font-mono text-sm">{String(value || '')}</span>
        ),
      },
      {
        key: 'followDate',
        title: t('marketing:zalo.followers.table.followDate', 'Ngày theo dõi'),
        dataIndex: 'followDate',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'lastInteraction',
        title: t('marketing:zalo.followers.table.lastInteraction', 'Tương tác cuối'),
        dataIndex: 'lastInteraction',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : 'Chưa có'}
          </span>
        ),
      },
      {
        key: 'tags',
        title: t('marketing:zalo.followers.table.tags', 'Tags'),
        dataIndex: 'tags',
        render: (value: unknown) => {
          const tags = value as string[];
          return (
            <div className="flex flex-wrap gap-1">
              {tags && tags.length > 0 ? (
                tags.slice(0, 2).map((tag, index) => (
                  <Chip key={index} variant="info">
                    {tag}
                  </Chip>
                ))
              ) : (
                <span className="text-xs text-muted-foreground">Không có</span>
              )}
              {tags && tags.length > 2 && (
                <Chip variant="info">
                  +{tags.length - 2}
                </Chip>
              )}
            </div>
          );
        },
      },
      {
        key: 'status',
        title: t('marketing:zalo.followers.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => (
          <Chip
            variant={value === 'ACTIVE' ? 'success' : 'info'}
          >
            {value === 'ACTIVE' ? t('common.status.active', 'Hoạt động') : t('common.status.inactive', 'Không hoạt động')}
          </Chip>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:zalo.followers.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: ZaloFollowerDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:zalo.followers.actions.sendMessage', 'Gửi tin nhắn')}>
              <IconCard
                icon="message-circle"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Send message to:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('marketing:zalo.followers.actions.addTag', 'Thêm tag')}>
              <IconCard
                icon="tag"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Add tag to:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, selectedFollowers]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:zalo.followers.stats.activeFollowers', 'Hoạt động'),
        icon: 'check',
        value: 'ACTIVE',
      },
      {
        id: 'inactive',
        label: 'Không hoạt động',
        icon: 'x',
        value: 'INACTIVE',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): ZaloFollowerQueryDto => {
      const queryParams: ZaloFollowerQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as 'ACTIVE' | 'BLOCKED' | 'UNFOLLOWED';
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ZaloFollowerDto, ZaloFollowerQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Hooks để gọi API
  const { data: followersData, isLoading } = useZaloFollowers(oaIdNumber, dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng khi có dữ liệu từ API
  useEffect(() => {
    if (followersData) {
      updateTableDataRef.current(followersData, isLoading);
    }
  }, [followersData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:zalo.followers.stats.activeFollowers', 'Hoạt động'),
        INACTIVE: 'Không hoạt động',
      },
      t,
    });

  const handleBulkAddTag = async (tagName: string) => {
    await bulkOperation.mutateAsync({
      oaId: oaIdNumber,
      data: {
        followerIds: selectedFollowers,
        operation: 'ADD_TAG',
        tagName,
      },
    });
    setSelectedFollowers([]);
    hideForm();
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zalo.followers.title', 'Quản lý Followers')}
        description={
          accountData
            ? t('marketing:zalo.followers.description', 'Followers của {{name}}', { name: accountData.name })
            : t('marketing:zalo.followers.descriptionDefault', 'Quản lý danh sách followers')
        }
        actions={
          <div className="flex gap-2">
            <Button variant="outline" className="gap-2">
              <Download className="h-4 w-4" />
              {t('marketing:zalo.followers.export', 'Export')}
            </Button>
            <Button className="gap-2">
              <UserPlus className="h-4 w-4" />
              {t('marketing:zalo.followers.sync', 'Đồng bộ')}
            </Button>
          </div>
        }
      />

      {/* Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.followers.stats.totalFollowers', 'Tổng Followers')}
            </span>
            <Users className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold">
            {followersData?.meta.totalItems?.toLocaleString() || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.followers.stats.newFollowersToday', '+12 hôm nay')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.followers.stats.activeFollowers', 'Hoạt động')}
            </span>
            <Users className="h-4 w-4 text-success" />
          </div>
          <div className="text-2xl font-bold text-success">
            {followersData?.items?.filter((f: ZaloFollowerDto) => f.status === 'ACTIVE').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">{t('marketing:zalo.followers.stats.interacting', 'Đang tương tác')}</p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.followers.stats.newThisWeek', 'Mới tuần này')}
            </span>
            <UserPlus className="h-4 w-4 text-info" />
          </div>
          <div className="text-2xl font-bold text-info">+24</div>
          <p className="text-xs text-muted-foreground mt-1">{t('marketing:zalo.followers.stats.increase', 'Tăng 15%')}</p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.followers.stats.selected', 'Đã chọn')}
            </span>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold">
            {selectedFollowers.length}
          </div>
          <p className="text-xs text-muted-foreground mt-1">Followers</p>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Bulk Actions Bar */}
      {selectedFollowers.length > 0 && (
        <Card className="p-4 bg-blue-50 border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-blue-900">
                {t('marketing:zalo.followers.bulkActions.description', 'Thực hiện thao tác cho {{count}} followers đã chọn', { count: selectedFollowers.length })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button size="sm" onClick={() => showForm()} className="bg-blue-600 hover:bg-blue-700">
                <TagIcon className="h-4 w-4 mr-1" />
                {t('marketing:zalo.followers.bulkActions.addTag', 'Thêm tag')}
              </Button>
              <Button size="sm" variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                <MessageCircle className="h-4 w-4 mr-1" />
                {t('marketing:zalo.followers.bulkActions.sendMessage', 'Gửi tin nhắn')}
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setSelectedFollowers([])}
                className="text-blue-600 hover:text-blue-800"
              >
                Hủy chọn
              </Button>
            </div>
          </div>
        </Card>
      )}

      {/* Followers Table */}
      <Card className="overflow-hidden">
        <Table<ZaloFollowerDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={followersData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: followersData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: followersData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho bulk actions */}
      <SlideInForm isVisible={isVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zalo.followers.bulkActions.title', 'Thao tác hàng loạt')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zalo.followers.bulkActions.description', 'Thực hiện thao tác cho {{count}} followers đã chọn', { count: selectedFollowers.length })}
            </p>
          </div>

          <div className="space-y-3">
            <Button
              className="w-full justify-start"
              variant="outline"
              onClick={() => {
                handleBulkAddTag('VIP');
                hideForm();
              }}
            >
              <TagIcon className="h-4 w-4 mr-2" />
              {t('marketing:zalo.followers.bulkActions.addTagVip', 'Thêm tag "VIP"')}
            </Button>
            <Button
              className="w-full justify-start"
              variant="outline"
              onClick={() => {
                handleBulkAddTag('Khách hàng mới');
                hideForm();
              }}
            >
              <TagIcon className="h-4 w-4 mr-2" />
              {t('marketing:zalo.followers.bulkActions.addTagNewCustomer', 'Thêm tag "Khách hàng mới"')}
            </Button>
            <Button
              className="w-full justify-start"
              variant="outline"
              onClick={() => {
                // Handle send bulk message
                hideForm();
              }}
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              {t('marketing:zalo.followers.bulkActions.sendBulkMessage', 'Gửi tin nhắn hàng loạt')}
            </Button>
          </div>

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={hideForm}>
              {t('common.cancel', 'Hủy')}
            </Button>
          </div>
        </div>
      </SlideInForm>
    </div>
  );
}

export default ZaloFollowersPage;
