import { apiClient } from '@/shared/api';
import { EXTERNAL_AGENT_ENDPOINTS } from '../constants';
import {
  ExternalAgent,
  ExternalAgentCreateDto,
  ExternalAgentUpdateDto,
  ExternalAgentQueryDto,
  ExternalAgentListResponse,
  ConnectionTestResult,
  AgentPerformanceMetrics,
} from '../types';

export const externalAgentApi = {
  // Get all external agents
  getExternalAgents: async (params?: ExternalAgentQueryDto): Promise<ExternalAgentListResponse> => {
    const response = await apiClient.get<ExternalAgentListResponse>(
      EXTERNAL_AGENT_ENDPOINTS.AGENTS,
      { params }
    );
    return response.result;
  },

  // Get external agent by ID
  getExternalAgent: async (id: string): Promise<ExternalAgent> => {
    const response = await apiClient.get<ExternalAgent>(
      EXTERNAL_AGENT_ENDPOINTS.AGENT_DETAIL(id)
    );
    return response.result;
  },

  // Create new external agent
  createExternalAgent: async (data: ExternalAgentCreateDto): Promise<ExternalAgent> => {
    const response = await apiClient.post<ExternalAgent>(
      EXTERNAL_AGENT_ENDPOINTS.AGENTS,
      data
    );
    return response.result;
  },

  // Update external agent
  updateExternalAgent: async (id: string, data: ExternalAgentUpdateDto): Promise<ExternalAgent> => {
    const response = await apiClient.put<ExternalAgent>(
      EXTERNAL_AGENT_ENDPOINTS.AGENT_DETAIL(id),
      data
    );
    return response.result;
  },

  // Delete external agent
  deleteExternalAgent: async (id: string): Promise<void> => {
    await apiClient.delete(EXTERNAL_AGENT_ENDPOINTS.AGENT_DETAIL(id));
  },

  // Test connection to external agent
  testConnection: async (id: string): Promise<ConnectionTestResult> => {
    const response = await apiClient.post<ConnectionTestResult>(
      EXTERNAL_AGENT_ENDPOINTS.AGENT_TEST(id)
    );
    return response.result;
  },

  // Get agent performance metrics
  getPerformanceMetrics: async (id: string): Promise<AgentPerformanceMetrics> => {
    const response = await apiClient.get<AgentPerformanceMetrics>(
      EXTERNAL_AGENT_ENDPOINTS.AGENT_PERFORMANCE(id)
    );
    return response.result;
  },

  // Bulk operations
  bulkUpdateStatus: async (ids: string[], status: string): Promise<void> => {
    await apiClient.patch(EXTERNAL_AGENT_ENDPOINTS.AGENTS + '/bulk-status', {
      ids,
      status,
    });
  },

  bulkDelete: async (ids: string[]): Promise<void> => {
    await apiClient.delete(EXTERNAL_AGENT_ENDPOINTS.AGENTS + '/bulk', {
      data: { ids },
    });
  },
};
