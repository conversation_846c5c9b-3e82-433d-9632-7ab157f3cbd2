/**
 * Interface cho một item tích hợp
 */
export interface IntegrationItem {
    id: string;
    name: string;
    icon?: string;
    type: 'facebook' | 'website';
    url?: string;
    imageUrl?: string;
    category?: string;
    followers?: number;
    isConnected?: boolean;
    status?: 'active' | 'pending' | 'error';
}

/**
 * Interface cho dữ liệu tích hợp
 */
export interface IntegrationsData {
    integrations: IntegrationItem[];
}
