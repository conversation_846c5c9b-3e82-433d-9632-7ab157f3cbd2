import { ReactNode } from 'react';
import { FieldValues, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

/**
 * Props cho FormStep component
 */
export interface FormStepProps<TFormValues extends FieldValues = FieldValues> {
  /**
   * ID của bước
   */
  id: string;

  /**
   * Tiêu đề của bước
   */
  title: string;

  /**
   * Mô tả của bước
   */
  description?: string;

  /**
   * Schema validation cho bước
   */
  validationSchema?: z.ZodType<unknown>;

  /**
   * Điều kiện để hiển thị bước
   */
  condition?: (data: Partial<TFormValues>) => boolean;

  /**
   * Callback khi bước được kích hoạt
   */
  onActivate?: (data: Partial<TFormValues>) => void;

  /**
   * Callback khi bước được hoàn thành
   */
  onComplete?: (data: Partial<TFormValues>) => void;

  /**
   * Callback khi bước có lỗi
   */
  onError?: (errors: Record<string, unknown>) => void;

  /**
   * Render function cho nội dung của bước
   */
  render?: (formMethods: UseFormReturn<TFormValues>) => ReactNode;

  /**
   * Children
   */
  children?: ReactNode;
}
