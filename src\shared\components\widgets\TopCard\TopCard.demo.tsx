import React from 'react';
import TopCard from './TopCard';
import { TopCardItem } from './TopCard.types';
import { Card } from '@/shared/components/common';

const TopCardDemo: React.FC = () => {
  // Dữ liệu mẫu
  const statsItems: TopCardItem[] = [
    {
      title: 'Tổng người dùng',
      value: '1,950',
      icon: 'users',
      change: '+12%',
      isPositive: true,
    },
    {
      title: '<PERSON>anh thu',
      value: '45,678,000đ',
      icon: 'chart',
      change: '+18%',
      isPositive: true,
    },
    {
      title: 'Đơn hàng',
      value: '324',
      icon: 'shopping-cart',
      change: '+5%',
      isPositive: true,
    },
    {
      title: 'Tỷ lệ chuyển đổi',
      value: '3.2%',
      icon: 'trending-up',
      change: '-0.5%',
      isPositive: false,
      description: 'So với tháng trước',
    },
  ];

  // Dữ liệu mẫu khác
  const financialItems: TopCardItem[] = [
    {
      title: '<PERSON><PERSON><PERSON> thu tháng',
      value: '120,450,000đ',
      icon: 'chart',
      iconColor: '#4CAF50',
    },
    {
      title: 'Chi phí',
      value: '45,780,000đ',
      icon: 'credit-card',
      iconColor: '#F44336',
    },
    {
      title: 'Lợi nhuận',
      value: '74,670,000đ',
      icon: 'trending-up',
      iconColor: '#2196F3',
    },
    {
      title: 'ROI',
      value: '162%',
      icon: 'chart',
      iconColor: '#FF9800',
    },
  ];

  return (
    <div className="container mx-auto p-4 space-y-8">
      <Card title="TopCard Demo" className="p-6">
        <div className="space-y-8">
          <TopCard title="Thống kê tổng quan" items={statsItems} />

          <TopCard title="Báo cáo tài chính" items={financialItems} />
        </div>
      </Card>
    </div>
  );
};

export default TopCardDemo;
