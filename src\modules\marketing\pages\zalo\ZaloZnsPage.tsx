import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FileText,
  Send
} from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
  Button,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { CreateZnsTemplateForm } from '../../components/zalo/CreateZnsTemplateForm';
import { ZNSTemplateDto, ZNSTemplateStatus, ZNSTemplateQueryDto } from '../../types/zalo.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý ZNS Templates
 */
export function ZaloZnsPage() {
  const { t } = useTranslation('marketing');

  const [selectedTemplate, setSelectedTemplate] = useState<ZNSTemplateDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isCreateVisible, showForm: showCreateForm, hideForm: hideCreateForm } = useSlideForm();
  const { isVisible: isPreviewVisible, showForm: showPreviewForm, hideForm: hidePreviewForm } = useSlideForm();

  // Mock data cho demo
  const templatesData = useMemo(() => ({
    items: [
      {
        id: 1,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_001',
        name: 'Xác nhận đơn hàng',
        content: 'Xin chào {{customer_name}}, đơn hàng {{order_id}} của bạn đã được xác nhận với tổng tiền {{total_amount}} VNĐ.',
        params: ['customer_name', 'order_id', 'total_amount'],
        status: 'ENABLE' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 2,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_002',
        name: 'Thông báo khuyến mãi',
        content: 'Xin chào {{customer_name}}, bạn có ưu đãi {{discount_percent}}% hết hạn {{expiry_date}}.',
        params: ['customer_name', 'discount_percent', 'expiry_date'],
        status: 'PENDING_REVIEW' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
      {
        id: 3,
        userId: 1,
        oaId: 1,
        templateId: 'ZNS_003',
        name: 'Nhắc nhở thanh toán',
        content: 'Xin chào {{customer_name}}, bạn có khoản thanh toán {{amount_due}} VNĐ đến hạn {{due_date}}.',
        params: ['customer_name', 'amount_due', 'due_date'],
        status: 'REJECT' as ZNSTemplateStatus,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      },
    ],
    meta: {
      totalItems: 3,
      itemCount: 3,
      itemsPerPage: 20,
      totalPages: 1,
      currentPage: 1,
    },
  }), []);

  const handlePreviewTemplate = useCallback((template: ZNSTemplateDto) => {
    setSelectedTemplate(template);
    showPreviewForm();
  }, [showPreviewForm]);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ZNSTemplateDto>[]>(
    () => [
      {
        key: 'template',
        title: t('marketing:zalo.zns.table.template', 'Template'),
        dataIndex: 'name',
        sortable: true,
        render: (value: unknown, record: ZNSTemplateDto) => (
          <div>
            <div className="font-medium">{String(value || '')}</div>
            <div className="text-xs text-muted-foreground">
              ID: {record.templateId}
            </div>
          </div>
        ),
      },
      {
        key: 'templateId',
        title: t('marketing:zalo.zns.table.templateId', 'Template ID'),
        dataIndex: 'templateId',
        sortable: true,
        render: (value: unknown) => (
          <span className="font-mono text-sm">{String(value || '')}</span>
        ),
      },
      {
        key: 'status',
        title: t('marketing:zalo.zns.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as string;
          switch (status) {
            case 'ENABLE':
              return <Chip variant="success">{t('marketing:zalo.zns.status.approved', 'Đã duyệt')}</Chip>;
            case 'PENDING_REVIEW':
              return <Chip variant="warning">{t('marketing:zalo.zns.status.pending', 'Chờ duyệt')}</Chip>;
            case 'REJECT':
              return <Chip variant="danger">{t('marketing:zalo.zns.status.rejected', 'Từ chối')}</Chip>;
            case 'DISABLE':
              return <Chip variant="info">{t('marketing:zalo.zns.status.disabled', 'Vô hiệu hóa')}</Chip>;
            default:
              return <Chip variant="info">{status}</Chip>;
          }
        },
      },
      {
        key: 'quality',
        title: t('marketing:zalo.zns.table.quality', 'Chất lượng'),
        dataIndex: 'quality',
        sortable: true,
        render: () => (
          <Chip variant="info">{t('marketing:zalo.zns.quality.normal', 'Bình thường')}</Chip>
        ),
      },
      {
        key: 'cost',
        title: t('marketing:zalo.zns.table.cost', 'Chi phí'),
        dataIndex: 'cost',
        sortable: true,
        render: () => (
          <span className="font-medium">770 VNĐ</span>
        ),
      },
      {
        key: 'params',
        title: t('marketing:zalo.zns.table.params', 'Tham số'),
        dataIndex: 'params',
        render: (value: unknown) => {
          const params = value as string[];
          return (
            <div className="text-sm">
              {params?.length || 0} tham số
            </div>
          );
        },
      },
      {
        key: 'updatedAt',
        title: t('marketing:zalo.zns.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:zalo.zns.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: ZNSTemplateDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:zalo.zns.preview.title', 'Xem trước Template')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handlePreviewTemplate(record)}
              />
            </Tooltip>
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit template:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete template:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handlePreviewTemplate]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'approved',
        label: t('marketing:zalo.zns.stats.approved', 'Đã duyệt'),
        icon: 'check',
        value: 'ENABLE',
      },
      {
        id: 'pending',
        label: t('marketing:zalo.zns.stats.pending', 'Chờ duyệt'),
        icon: 'clock',
        value: 'PENDING_REVIEW',
      },
      {
        id: 'rejected',
        label: 'Từ chối',
        icon: 'x',
        value: 'REJECT',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): ZNSTemplateQueryDto => {
      const queryParams: ZNSTemplateQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as ZNSTemplateStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ZNSTemplateDto, ZNSTemplateQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Mock data sẽ được thay thế bằng API call
  const isLoading = false;

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng với mock data
  useEffect(() => {
    updateTableDataRef.current(templatesData, isLoading);
  }, [templatesData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ENABLE: t('marketing:zalo.zns.stats.approved', 'Đã duyệt'),
        PENDING_REVIEW: t('marketing:zalo.zns.stats.pending', 'Chờ duyệt'),
        REJECT: 'Từ chối',
      },
      t,
    });

  const handleCreateSuccess = () => {
    hideCreateForm();
    // Refresh data
  };

  // Removed unused function getQualityBadge

  return (
    <div className="w-full bg-background text-foreground space-y-4">
      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.zns.stats.totalTemplates', 'Tổng Templates')}
            </span>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold">{templatesData.meta.totalItems}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.zns.stats.newTemplates', '+2 template mới')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.zns.stats.approved', 'Đã duyệt')}
            </span>
            <FileText className="h-4 w-4 text-success" />
          </div>
          <div className="text-2xl font-bold text-success">
            {templatesData.items.filter(template => template.status === ZNSTemplateStatus.APPROVED).length}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.zns.stats.readyToUse', 'Sẵn sàng sử dụng')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.zns.stats.pending', 'Chờ duyệt')}
            </span>
            <FileText className="h-4 w-4 text-warning" />
          </div>
          <div className="text-2xl font-bold text-warning">
            {templatesData.items.filter(template => template.status === ZNSTemplateStatus.PENDING).length}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.zns.stats.underReview', 'Đang xem xét')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.zns.stats.avgCost', 'Chi phí TB')}
            </span>
            <Send className="h-4 w-4 text-info" />
          </div>
          <div className="text-2xl font-bold text-info">770 VNĐ</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.zns.stats.perMessage', 'Mỗi tin nhắn')}
          </p>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showCreateForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Templates Table */}
      <Card className="overflow-hidden">
        <Table<ZNSTemplateDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={templatesData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: templatesData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: templatesData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho tạo template */}
      <SlideInForm isVisible={isCreateVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zalo.zns.create.title', 'Tạo ZNS Template')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zalo.zns.create.description', 'Tạo template thông báo ZNS mới')}
            </p>
          </div>
          <CreateZnsTemplateForm onSuccess={handleCreateSuccess} onCancel={hideCreateForm} />
        </div>
      </SlideInForm>

      {/* SlideInForm cho preview template */}
      <SlideInForm isVisible={isPreviewVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zalo.zns.preview.title', 'Xem trước Template')}
            </h3>
            {selectedTemplate && (
              <p className="text-sm text-muted-foreground mt-1">
                Template: {selectedTemplate.name}
              </p>
            )}
          </div>

          {selectedTemplate && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">{selectedTemplate.name}</h4>
                <p className="text-sm text-muted-foreground">
                  ID: {selectedTemplate.templateId}
                </p>
              </div>

              <div className="space-y-2">
                <h5 className="text-sm font-medium">
                  {t('marketing:zalo.zns.preview.content', 'Nội dung:')}
                </h5>
                <div className="p-3 bg-muted rounded-lg">
                  <p className="text-sm">{selectedTemplate.content}</p>
                </div>
              </div>

              <div className="space-y-2">
                <h5 className="text-sm font-medium">
                  {t('marketing:zalo.zns.preview.params', 'Tham số:')}
                </h5>
                <div className="space-y-1">
                  {selectedTemplate.params.map((param: string, index: number) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="font-mono">{param}</span>
                      <div className="flex items-center gap-2">
                        <Chip variant="info">
                          {t('marketing:zalo.zns.preview.paramType', 'STRING')}
                        </Chip>
                        <Chip variant="danger">
                          {t('marketing:zalo.zns.preview.required', 'Bắt buộc')}
                        </Chip>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end pt-4 border-t">
            <Button variant="outline" onClick={hidePreviewForm}>
              {t('common.close', 'Đóng')}
            </Button>
          </div>
        </div>
      </SlideInForm>
    </div>
  );
}

export default ZaloZnsPage;
