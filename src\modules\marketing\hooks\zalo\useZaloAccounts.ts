import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { ZaloService } from '../../services/zalo.service';
import type {
  CreateZaloOAAccountDto,
  UpdateZaloOAAccountDto,
  ZaloOAAccountQueryDto,
} from '../../types/zalo.types';

/**
 * Query keys cho Zalo accounts
 */
export const ZALO_ACCOUNT_QUERY_KEYS = {
  all: ['zalo', 'accounts'] as const,
  lists: () => [...ZALO_ACCOUNT_QUERY_KEYS.all, 'list'] as const,
  list: (query: ZaloOAAccountQueryDto) => [...ZALO_ACCOUNT_QUERY_KEYS.lists(), query] as const,
  details: () => [...ZALO_ACCOUNT_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...ZALO_ACCOUNT_QUERY_KEYS.details(), id] as const,
  analytics: (id: number) => [...ZALO_ACCOUNT_QUERY_KEYS.all, 'analytics', id] as const,
};

/**
 * Hook để lấy danh sách Zalo OA accounts
 */
export function useZaloAccounts(query?: ZaloOAAccountQueryDto) {
  return useQuery({
    queryKey: ZALO_ACCOUNT_QUERY_KEYS.list(query || {}),
    queryFn: () => ZaloService.getAccounts(query),
    select: (response) => response.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook để lấy chi tiết Zalo OA account
 */
export function useZaloAccount(id: number) {
  return useQuery({
    queryKey: ZALO_ACCOUNT_QUERY_KEYS.detail(id),
    queryFn: () => ZaloService.getAccount(id),
    select: (response) => response.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook để kết nối Zalo OA account mới
 */
export function useConnectZaloAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateZaloOAAccountDto) => ZaloService.connectAccount(data),
    onSuccess: (response) => {
      // Invalidate và refetch accounts list
      queryClient.invalidateQueries({ queryKey: ZALO_ACCOUNT_QUERY_KEYS.lists() });

      NotificationUtil.success({
        message: 'Kết nối Zalo OA thành công!',
        title: `Đã kết nối thành công với ${response.result.name}`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Kết nối Zalo OA thất bại',
        title: error.message || 'Vui lòng kiểm tra lại thông tin và thử lại',
      });
    },
  });
}

/**
 * Hook để cập nhật Zalo OA account
 */
export function useUpdateZaloAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateZaloOAAccountDto }) =>
      ZaloService.updateAccount(id, data),
    onSuccess: (response, { id }) => {
      // Update cache cho account detail
      queryClient.setQueryData(
        ZALO_ACCOUNT_QUERY_KEYS.detail(id),
        response
      );

      // Invalidate accounts list
      queryClient.invalidateQueries({ queryKey: ZALO_ACCOUNT_QUERY_KEYS.lists() });

      NotificationUtil.success({ message: 'Cập nhật Zalo OA thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật Zalo OA thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để ngắt kết nối Zalo OA account
 */
export function useDisconnectZaloAccount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => ZaloService.disconnectAccount(id),
    onSuccess: (_, id) => {
      // Remove từ cache
      queryClient.removeQueries({ queryKey: ZALO_ACCOUNT_QUERY_KEYS.detail(id) });

      // Invalidate accounts list
      queryClient.invalidateQueries({ queryKey: ZALO_ACCOUNT_QUERY_KEYS.lists() });

      NotificationUtil.success({ message: 'Ngắt kết nối Zalo OA thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Ngắt kết nối Zalo OA thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để lấy analytics của Zalo OA
 */
export function useZaloAccountAnalytics(oaId: number, period: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR' = 'WEEK') {
  return useQuery({
    queryKey: ZALO_ACCOUNT_QUERY_KEYS.analytics(oaId),
    queryFn: () => ZaloService.getAnalytics(oaId, period),
    select: (response) => response.result,
    enabled: !!oaId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Auto refetch every 5 minutes
  });
}

/**
 * Hook để sync followers từ Zalo API
 */
export function useSyncZaloFollowers() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (oaId: number) => ZaloService.syncFollowers(oaId),
    onSuccess: (response, oaId) => {
      // Invalidate followers data
      queryClient.invalidateQueries({
        queryKey: ['zalo', 'followers', oaId]
      });

      // Update analytics
      queryClient.invalidateQueries({
        queryKey: ZALO_ACCOUNT_QUERY_KEYS.analytics(oaId)
      });

      NotificationUtil.success({
        message: 'Đồng bộ followers thành công!',
        title: `Đã đồng bộ ${response.result.syncedCount} followers`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Đồng bộ followers thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để test webhook connection
 */
export function useTestZaloWebhook() {
  return useMutation({
    mutationFn: (oaId: number) => ZaloService.testWebhook(oaId),
    onSuccess: (response) => {
      if (response.result.success) {
        NotificationUtil.success({
          message: 'Test webhook thành công!',
          title: response.result.message,
        });
      } else {
        NotificationUtil.warning({
          message: 'Test webhook có vấn đề',
          title: response.result.message,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Test webhook thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để export followers data
 */
export function useExportZaloFollowers() {
  return useMutation({
    mutationFn: ({ oaId, format }: { oaId: number; format: 'CSV' | 'EXCEL' }) =>
      ZaloService.exportFollowers(oaId, format),
    onSuccess: (response) => {
      // Tự động download file
      const link = document.createElement('a');
      link.href = response.result.downloadUrl;
      link.download = `zalo-followers-${Date.now()}.${response.result.downloadUrl.includes('.xlsx') ? 'xlsx' : 'csv'}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      NotificationUtil.success({ message: 'Export dữ liệu thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Export dữ liệu thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook tổng hợp cho Zalo account management
 */
export function useZaloAccountManagement() {
  const connectAccount = useConnectZaloAccount();
  const updateAccount = useUpdateZaloAccount();
  const disconnectAccount = useDisconnectZaloAccount();
  const syncFollowers = useSyncZaloFollowers();
  const testWebhook = useTestZaloWebhook();
  const exportFollowers = useExportZaloFollowers();

  return {
    connectAccount,
    updateAccount,
    disconnectAccount,
    syncFollowers,
    testWebhook,
    exportFollowers,
    isLoading: connectAccount.isPending ||
               updateAccount.isPending ||
               disconnectAccount.isPending ||
               syncFollowers.isPending ||
               testWebhook.isPending ||
               exportFollowers.isPending,
  };
}
