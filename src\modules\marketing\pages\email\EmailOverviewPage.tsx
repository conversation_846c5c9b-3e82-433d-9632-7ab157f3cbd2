import { useTranslation } from 'react-i18next';
import { Mail, FileText, Send, Eye, MousePointer } from 'lucide-react';
import { Card, StatusBadge, Button, Skeleton, ResponsiveGrid } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { useEmailTemplates } from '../../hooks/email/useEmailTemplates';
import { useMarketingOverview, useRecentTemplates } from '../../hooks/useMarketingOverview';
import { useRecentCampaigns } from '../../hooks/email/useEmailCampaigns';
import { useNavigate } from 'react-router-dom';
import type { RecentTemplateDto, RecentCampaignDto } from '../../types/statistics.types';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import { formatTimestamp } from '@/shared/utils/date';

/**
 * Trang tổng quan Email Marketing
 */
export function EmailOverviewPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  // API calls
  const { data: overviewData, isLoading: isOverviewLoading } = useMarketingOverview();
  const { data: recentTemplatesData, isLoading: isRecentTemplatesLoading } = useRecentTemplates();
  const { data: templatesData, isLoading: isTemplatesLoading } = useEmailTemplates({ limit: 5 });
  const { data: recentCampaignsData, isLoading: isRecentCampaignsLoading } = useRecentCampaigns();

  // Combined loading state
  const isLoading = isOverviewLoading || isRecentTemplatesLoading || isTemplatesLoading;
  const isCampaignsLoading = isRecentCampaignsLoading;

  const handleCreateTemplate = () => {
    navigate('/marketing/email/templates?action=create');
  };

  // Combine data from APIs with fallback values
  const stats = {
    totalTemplates: overviewData?.totalTemplates || templatesData?.meta.totalItems || 0,
    totalCampaigns: recentCampaignsData?.totalCampaigns || 0,
    emailsSentThisMonth: overviewData?.totalEmailsSent || 0,
    averageOpenRate: overviewData?.openRate || 0,
    averageClickRate: overviewData?.clickRate || 0,
    recentCampaigns: recentCampaignsData?.campaigns || [],
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SENT':
        return <StatusBadge variant="success" text="Đã gửi" />;
      case 'SENDING':
        return <StatusBadge variant="warning" text="Đang gửi" />;
      case 'SCHEDULED':
        return <StatusBadge variant="info" text="Đã lên lịch" />;
      case 'DRAFT':
        return <StatusBadge variant="warning" text="Bản nháp" />;
      case 'FAILED':
        return <StatusBadge variant="danger" text="Thất bại" />;
      case 'CANCELLED':
        return <StatusBadge variant="warning" text="Đã hủy" />;
      default:
        return <StatusBadge variant="primary" text={status} />;
    }
  };

  // Dữ liệu cho OverviewCard
  const overviewStats: OverviewCardProps[] = [
    {
      title: t('marketing:email.overview.stats.totalTemplates', 'Tổng Templates'),
      value: stats.totalTemplates,
      description: t('marketing:email.overview.stats.activeTemplates', '+3 template mới'),
      icon: FileText,
      color: 'blue',
      isLoading: isOverviewLoading,
    },
    {
      title: t('marketing:email.overview.stats.emailsSent', 'Email đã gửi'),
      value: stats.emailsSentThisMonth.toLocaleString(),
      description: t('marketing:email.overview.stats.thisMonth', 'Tháng này'),
      icon: Send,
      color: 'green',
      isLoading: isOverviewLoading,
    },
    {
      title: t('marketing:email.overview.stats.openRate', 'Tỷ lệ mở'),
      value: `${stats.averageOpenRate}%`,
      description: t('marketing:email.overview.stats.averageOpenRate', 'Trung bình 30 ngày'),
      icon: Eye,
      color: 'orange',
      isLoading: isOverviewLoading,
    },
    {
      title: t('marketing:email.overview.stats.clickRate', 'Tỷ lệ click'),
      value: `${stats.averageClickRate}%`,
      description: t('marketing:email.overview.stats.averageClickRate', 'Trung bình 30 ngày'),
      icon: MousePointer,
      color: 'purple',
      isLoading: isOverviewLoading,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Quick Stats */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isLoading}
        skeletonCount={4}
      />

      {/* Recent Templates */}
      <Card
        title={t('marketing:email.overview.recentTemplates', 'Templates gần đây')}
        subtitle={t(
          'marketing:email.overview.recentTemplatesDescription',
          'Các email template được tạo hoặc cập nhật gần đây'
        )}
        className="p-4"
      >
        {isRecentTemplatesLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>
            ))}
          </div>
        ) : !recentTemplatesData?.templates || recentTemplatesData.templates.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
              <FileText className="h-full w-full" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {t('marketing:email.overview.noTemplates', 'Chưa có template nào')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t(
                'marketing:email.overview.noTemplatesDescription',
                'Tạo template đầu tiên để bắt đầu gửi email'
              )}
            </p>
            <Button onClick={handleCreateTemplate}>
              {t('marketing:email.overview.createFirstTemplate', 'Tạo template đầu tiên')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {recentTemplatesData?.templates.map((template: RecentTemplateDto) => (
              <Card
                key={template.id}
                className="p-4 hover:shadow-md transition-shadow"
                hoverable
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded bg-gradient-to-r from-secondary/20 to-secondary/10 flex items-center justify-center text-secondary">
                      <Mail className="h-6 w-6" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">{template.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <span>{template.subject}</span>
                        <StatusBadge
                          variant={template.status === 'ACTIVE' ? 'success' : 'warning'}
                          text={template.status === 'ACTIVE' ? 'Hoạt động' : 'Bản nháp'}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="text-sm text-muted-foreground">
                    {formatTimestamp(template.updatedAt, 'vi-VN', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit',
                    })}
                  </div>
                </div>
              </Card>
            ))}

            {recentTemplatesData && recentTemplatesData.templates.length >= 5 && (
              <div className="text-center pt-4">
                <Button variant="outline" onClick={() => navigate('/marketing/email/templates')}>
                  {t('marketing:email.overview.viewAllTemplates', 'Xem tất cả templates')}
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Recent Campaigns */}
      <Card
        title={t('marketing:email.overview.recentCampaigns', 'Chiến dịch gần đây')}
        subtitle={t(
          'marketing:email.overview.recentCampaignsDescription',
          'Tình trạng các chiến dịch email gần đây'
        )}
        className="p-4"
      >
        {isCampaignsLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-12 w-12 rounded" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[150px]" />
                </div>
              </div>
            ))}
          </div>
        ) : !stats.recentCampaigns || stats.recentCampaigns.length === 0 ? (
          <div className="text-center py-8">
            <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
              <Send className="h-full w-full" />
            </div>
            <h3 className="text-lg font-medium mb-2">
              {t('marketing:email.overview.noCampaigns', 'Chưa có chiến dịch nào')}
            </h3>
            <p className="text-muted-foreground mb-4">
              {t(
                'marketing:email.overview.noCampaignsDescription',
                'Tạo chiến dịch đầu tiên để bắt đầu gửi email'
              )}
            </p>
            <Button onClick={() => navigate('/marketing/email/campaigns?action=create')}>
              {t('marketing:email.overview.createFirstCampaign', 'Tạo chiến dịch đầu tiên')}
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {stats.recentCampaigns.map((campaign: RecentCampaignDto) => (
              <Card
                key={campaign.id}
                className="p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => navigate(`/marketing/email/campaigns/${campaign.id}`)}
                hoverable
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded bg-gradient-to-r from-primary/20 to-primary/10 flex items-center justify-center text-primary">
                      <Send className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium text-foreground">{campaign.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <span>{campaign.totalRecipients.toLocaleString()} người nhận</span>
                        {getStatusBadge(campaign.status)}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {campaign.status === 'SENT' && (
                        <>
                          {campaign.sentRate && (
                            <span className="text-success">{campaign.sentRate}% gửi</span>
                          )}
                          {campaign.clickRate && (
                            <span className="text-info ml-2">{campaign.clickRate}% click</span>
                          )}
                        </>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatTimestamp(campaign.runAt * 1000, 'vi-VN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                      })}
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            {stats.recentCampaigns.length >= 5 && (
              <div className="text-center pt-4">
                <Button variant="outline" onClick={() => navigate('/marketing/email/campaigns')}>
                  {t('marketing:email.overview.viewAllCampaigns', 'Xem tất cả chiến dịch')}
                </Button>
              </div>
            )}
          </div>
        )}
      </Card>

      {/* Quick Actions */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        gap={4}
        maxColumnsWithChatPanel={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
      >
        <Card
          title={t('marketing:email.overview.quickActions.templates.title', 'Quản lý Templates')}
          subtitle={t(
            'marketing:email.overview.quickActions.templates.subtitle',
            'Tạo và chỉnh sửa email templates'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/templates')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>

        <Card
          title={t('marketing:email.overview.quickActions.automation.title', 'Email Automation')}
          subtitle={t(
            'marketing:email.overview.quickActions.automation.subtitle',
            'Thiết lập chuỗi email tự động'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/automation')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>

        <Card
          title={t('marketing:email.overview.quickActions.analytics.title', 'Analytics')}
          subtitle={t(
            'marketing:email.overview.quickActions.analytics.subtitle',
            'Báo cáo hiệu quả email marketing'
          )}
          className="cursor-pointer hover:shadow-lg transition-shadow p-4"
          onClick={() => navigate('/marketing/email/analytics')}
          hoverable
        >
          <div className="mt-4">
            <Button variant="outline" size="sm">
              {t('marketing:email.overview.quickActions.viewDetails', 'Xem chi tiết')}
            </Button>
          </div>
        </Card>
      </ResponsiveGrid>
    </div>
  );
}

export default EmailOverviewPage;
