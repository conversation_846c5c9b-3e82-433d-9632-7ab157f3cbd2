/**
 * Hook kết hợp useFileUpload với TaskQueue
 */
import { useCallback } from 'react';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import useFileUpload, { UseFileUploadOptions } from './useFileUpload';

/**
 * Tham số cho hook useFileUploadWithQueue
 */
export interface UseFileUploadWithQueueOptions extends UseFileUploadOptions {
  /**
   * Tiêu đề mặc định cho task upload
   * @default 'Upload File'
   */
  defaultTaskTitle?: string;

  /**
   * Mô tả mặc định cho task upload
   */
  defaultTaskDescription?: string;

  /**
   * Có tự động thêm task vào queue khi gọi uploadToUrl không
   * @default true
   */
  autoAddToQueue?: boolean;
}

/**
 * Tham số cho hàm uploadToUrlWithQueue
 */
export interface UploadToUrlWithQueueParams {
  /**
   * File cần upload
   */
  file: File;

  /**
   * URL tạm thời để upload file
   */
  presignedUrl: string;

  /**
   * Tiêu đề của task
   */
  taskTitle?: string;

  /**
   * Mô tả của task
   */
  taskDescription?: string;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onUploadProgress?: (progress: number) => void;

  /**
   * Có thêm task vào queue không
   */
  addToQueue?: boolean;
}

/**
 * Hook kết hợp useFileUpload với TaskQueue
 */
export function useFileUploadWithQueue({
  defaultTaskTitle = 'Upload File',
  defaultTaskDescription,
  autoAddToQueue = true,
  ...fileUploadOptions
}: UseFileUploadWithQueueOptions = {}) {
  // Sử dụng hook useFileUpload
  const fileUpload = useFileUpload(fileUploadOptions);

  // Lấy context của TaskQueue
  const taskQueue = useTaskQueueContext();

  /**
   * Upload file lên presigned URL và thêm vào queue
   */
  const uploadToUrlWithQueue = useCallback(
    async ({
      file,
      presignedUrl,
      taskTitle,
      taskDescription,
      onUploadProgress,
      addToQueue = autoAddToQueue,
    }: UploadToUrlWithQueueParams) => {
      // Nếu không thêm vào queue, sử dụng trực tiếp uploadToUrl
      if (!addToQueue) {
        const uploadParams: { file: File; presignedUrl: string; onUploadProgress?: (progress: number) => void } = {
          file,
          presignedUrl,
        };

        if (onUploadProgress) {
          uploadParams.onUploadProgress = onUploadProgress;
        }

        return fileUpload.uploadToUrl(uploadParams);
      }

      // Tạo tiêu đề và mô tả cho task
      const title = taskTitle || defaultTaskTitle;
      const description = taskDescription || defaultTaskDescription || `Uploading ${file.name}`;

      // Thêm task vào queue
      return new Promise<string>((resolve, reject) => {
        taskQueue.addFileUploadTask({
          title,
          description,
          file,
          uploadUrl: presignedUrl,
          execute: async (url, file, onProgress) => {
            try {
              // Gọi uploadToUrl từ useFileUpload
              const result = await fileUpload.uploadToUrl({
                file,
                presignedUrl: url,
                onUploadProgress: progress => {
                  // Cập nhật tiến trình
                  onProgress(progress);

                  // Gọi callback onUploadProgress nếu có
                  if (onUploadProgress) {
                    onUploadProgress(progress);
                  }
                },
              });

              return result;
            } catch (error) {
              reject(error);
              throw error;
            }
          },
          onSuccess: result => {
            resolve(result as string);
          },
          onError: error => {
            reject(error);
          },
        });
      });
    },
    [fileUpload, taskQueue, autoAddToQueue, defaultTaskTitle, defaultTaskDescription]
  );

  return {
    ...fileUpload,
    uploadToUrlWithQueue,
  };
}

export default useFileUploadWithQueue;
