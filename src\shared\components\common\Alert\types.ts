import { HTMLAttributes, ReactNode } from 'react';

/**
 * Loại Alert
 */
export type AlertType = 'info' | 'success' | 'warning' | 'error';

/**
 * Props cho Alert component
 */
export interface AlertProps extends Omit<HTMLAttributes<HTMLDivElement>, 'title'> {
  /**
   * Loại <PERSON>
   * @default 'info'
   */
  type?: AlertType;

  /**
   * Tiêu đề của Alert
   */
  title?: ReactNode;

  /**
   * Nội dung chính của Alert
   */
  message: ReactNode;

  /**
   * Mô tả chi tiết (hiển thị dưới message)
   */
  description?: ReactNode;

  /**
   * Hiển thị icon
   * @default true
   */
  showIcon?: boolean;

  /**
   * Có thể đóng Alert
   * @default false
   */
  closable?: boolean;

  /**
   * Callback khi Alert được đóng
   */
  onClose?: () => void;

  /**
   * Icon tùy chỉnh
   */
  icon?: string;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Hiển thị dưới dạng banner (full width, không có border radius)
   * @default false
   */
  banner?: boolean;

  /**
   * Action component (thường là button) hiển thị bên phải Alert
   */
  action?: ReactNode;
}
