import React, { useMemo, useCallback } from 'react';
import { Icon } from '@/shared/components/common';
import PaginationButton from './PaginationButton';
import ItemsPerPageSelectNew from './ItemsPerPageSelectNew';
import usePagination from './usePagination';
import { useTheme } from '@/shared/contexts';
import { useTranslation } from 'react-i18next';

export type PaginationVariant = 'default' | 'simple' | 'minimal' | 'text' | 'compact';

export interface PaginationProps {
  /**
   * Biến thể của component phân trang
   * - default: Phân trang đầy đủ với tất cả các tùy chọn
   * - simple: Chỉ hiển thị nút trước/sau và thông tin trang
   * - minimal: Hiển thị các nút số trang liền kề nhau
   * - text: Sử dụng các liên kết văn bản thay vì nút
   * - compact: Chỉ hiển thị bộ chọn số hàng, trang hiện tại và nút điều hướng
   * @default 'default'
   */
  variant?: PaginationVariant;

  /**
   * Tổng số mục (chỉ cần thiết cho biến thể 'default')
   */
  totalItems?: number;

  /**
   * Trang hiện tại (bắt đầu từ 1)
   */
  currentPage: number;

  /**
   * Tổng số trang (bắt buộc cho các biến thể 'simple', 'minimal', 'text')
   */
  totalPages?: number;

  /**
   * Số lượng mục trên mỗi trang (chỉ cần thiết cho biến thể 'default')
   */
  itemsPerPage?: number;

  /**
   * Callback khi trang thay đổi
   */
  onPageChange: (page: number) => void;

  /**
   * Callback khi số lượng mục trên mỗi trang thay đổi
   */
  onItemsPerPageChange?: (itemsPerPage: number) => void;

  /**
   * Các tùy chọn cho số lượng mục trên mỗi trang
   * @default [10, 20, 50, 100]
   */
  itemsPerPageOptions?: number[];

  /**
   * Số lượng tối đa các nút trang hiển thị
   * @default 5
   */
  maxPageButtons?: number;

  /**
   * Số lượng nút trang hiển thị ở mỗi bên của trang hiện tại (cho biến thể 'minimal')
   * @default 1
   */
  siblingCount?: number;

  /**
   * Hiển thị nút trang đầu và trang cuối
   * @default true
   */
  showFirstLastButtons?: boolean;

  /**
   * Hiển thị bộ chọn số lượng mục trên mỗi trang
   * @default true
   */
  showItemsPerPageSelector?: boolean;

  /**
   * Hiển thị thông tin trang
   * @default true
   */
  showPageInfo?: boolean;

  /**
   * Nhãn cho nút Trước (cho biến thể 'text')
   */
  previousLabel?: string;

  /**
   * Nhãn cho nút Tiếp theo (cho biến thể 'text')
   */
  nextLabel?: string;

  /**
   * Loại bỏ border cho các nút
   * @default false
   */
  borderless?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Kích thước của component phân trang
   * @default 'md'
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Vô hiệu hóa phân trang
   * @default false
   */
  disabled?: boolean;
}

/**
 * Component phân trang
 */
const Pagination: React.FC<PaginationProps> = ({
  variant = 'default',
  totalItems = 0,
  currentPage,
  totalPages: propsTotalPages,
  itemsPerPage = 10,
  onPageChange,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 20, 50, 100],
  maxPageButtons = 5,
  siblingCount = 1,
  showFirstLastButtons = true,
  showItemsPerPageSelector = true,
  showPageInfo = true,
  previousLabel,
  nextLabel,
  borderless = false,
  className = '',
  size = 'md',
  disabled = false,
}) => {
  // Sử dụng hook translation
  const { t } = useTranslation(['common', 'pagination']);
  
  // Sử dụng hook theme mới
  useTheme();
  // Tạo props cho usePagination, chỉ thêm overrideTotalPages nếu có giá trị
  const paginationProps = useMemo(() => {
    const baseProps = {
      totalItems: totalItems,
      itemsPerPage: itemsPerPage,
      currentPage,
      maxPageButtons: variant === 'minimal' ? siblingCount * 2 + 1 : maxPageButtons,
    };

    // Chỉ thêm overrideTotalPages nếu có giá trị
    if (propsTotalPages !== undefined) {
      return {
        ...baseProps,
        overrideTotalPages: propsTotalPages,
      };
    }

    return baseProps;
  }, [totalItems, itemsPerPage, currentPage, variant, siblingCount, maxPageButtons, propsTotalPages]);

  // Sử dụng hook phân trang
  const {
    totalPages,
    pageNumbers,
    canGoPrevious,
    canGoNext,
    goToPreviousPage,
    goToNextPage,
    goToFirstPage,
    goToLastPage,
  } = usePagination(paginationProps);

  // Xử lý sự kiện thay đổi trang
  const handlePageChange = useCallback(
    (page: number) => {
      if (!disabled) {
        onPageChange(page);
      }
    },
    [disabled, onPageChange]
  );

  // Xử lý sự kiện thay đổi số lượng mục trên mỗi trang
  const handleItemsPerPageChange = useCallback(
    (value: number) => {
      console.log(`Pagination handleItemsPerPageChange: value=${value}, current=${itemsPerPage}`);
      if (!disabled && onItemsPerPageChange) {
        // Luôn gọi callback để đảm bảo Table component nhận được sự kiện
        onItemsPerPageChange(value);
      }
    },
    [disabled, onItemsPerPageChange, itemsPerPage]
  );

  // Các hàm xử lý sự kiện cho các nút điều hướng
  const handleFirstPage = useCallback(() => {
    handlePageChange(goToFirstPage());
  }, [handlePageChange, goToFirstPage]);

  const handlePreviousPage = useCallback(() => {
    handlePageChange(goToPreviousPage());
  }, [handlePageChange, goToPreviousPage]);

  const handleNextPage = useCallback(() => {
    handlePageChange(goToNextPage());
  }, [handlePageChange, goToNextPage]);

  const handleLastPage = useCallback(() => {
    handlePageChange(goToLastPage());
  }, [handlePageChange, goToLastPage]);

  // Xác định kích thước icon
  const iconSize = useMemo(() => {
    return {
      sm: 'xs',
      md: 'sm',
      lg: 'md',
    }[size] as 'xs' | 'sm' | 'md';
  }, [size]);

  // Xác định khoảng cách giữa các phần tử
  const gapSize = useMemo(() => {
    return {
      sm: 'gap-1',
      md: 'gap-2',
      lg: 'gap-3',
    }[size];
  }, [size]);

  // Xác định các lớp kích thước cho biến thể text
  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  }[size];

  // Xác định các lớp kích thước cho nút compact
  const compactButtonSizeClasses = useMemo(() => {
    return {
      sm: 'w-6 h-6',
      md: 'w-8 h-8',
      lg: 'w-10 h-10',
    }[size];
  }, [size]);

  // Mặc định label cho nút
  const defaultPreviousLabel = t('pagination:previous');
  const defaultNextLabel = t('pagination:next');
  
  // Render biến thể đơn giản (simple)
  if (variant === 'simple') {
    return (
      <div className={`flex flex-wrap items-center justify-between ${className}`}>
        {/* Thông tin trang */}
        {showPageInfo && (
          <div
            className={`text-muted ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}
          >
            {t('pagination:pageInfo', { currentPage, totalPages })}
          </div>
        )}

        {/* Điều khiển phân trang đơn giản */}
        <div className={`flex items-center ${gapSize}`}>
          {/* Bộ chọn số lượng mục trên mỗi trang */}
          {showItemsPerPageSelector && onItemsPerPageChange && (
            <div className="flex items-center mr-4">
              <span
                className={`mr-2 text-muted ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}
              >
                {t('pagination:show')}
              </span>
              <ItemsPerPageSelectNew
                value={itemsPerPage}
                options={itemsPerPageOptions}
                onChange={handleItemsPerPageChange}
                disabled={disabled}
                size={size}
                className="min-w-[70px]"
              />
            </div>
          )}

          {/* Nút trang trước */}
          <PaginationButton
            page={<Icon name="chevron-left" size={iconSize} />}
            onClick={handlePreviousPage}
            disabled={disabled || !canGoPrevious}
            ariaLabel={t('pagination:previousPage')}
            size={size}
          />

          {/* Nút trang tiếp theo */}
          <PaginationButton
            page={<Icon name="chevron-right" size={iconSize} />}
            onClick={handleNextPage}
            disabled={disabled || !canGoNext}
            ariaLabel={t('pagination:nextPage')}
            size={size}
          />
        </div>
      </div>
    );
  }

  // Render biến thể tối giản (minimal)
  if (variant === 'minimal') {
    return (
      <div className={`flex justify-center ${className}`}>
        <div className="inline-flex rounded-md shadow-sm">
          {pageNumbers.map((page, index) => {
            const isEllipsis = page === '...';
            const isActive = page === currentPage;

            return (
              <button
                key={`${page}-${index}`}
                className={`
                  ${size === 'sm' ? 'px-2 py-1' : size === 'lg' ? 'px-4 py-2' : 'px-3 py-1'} ${textSizeClasses}
                  ${index === 0 ? 'rounded-l-md' : ''}
                  ${index === pageNumbers.length - 1 ? 'rounded-r-md' : ''}
                  ${isEllipsis ? 'cursor-default' : 'cursor-pointer'}
                  ${
                    isActive
                      ? 'bg-primary text-primary-foreground font-medium z-10 relative'
                      : 'bg-card text-foreground hover:bg-card-muted'
                  }
                  ${!borderless ? 'border border-border' : ''}
                  ${index > 0 ? '-ml-px' : ''}
                  ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
                `}
                onClick={
                  !isEllipsis && typeof page === 'number' ? () => handlePageChange(page) : undefined
                }
                disabled={disabled || isEllipsis}
                aria-current={isActive ? 'page' : undefined}
                aria-label={typeof page === 'number' ? t('pagination:goToPage', { page }) : t('pagination:morePage')}
                type="button"
              >
                {page}
              </button>
            );
          })}
        </div>
      </div>
    );
  }

  // Render biến thể compact (chỉ hiển thị bộ chọn số hàng, trang hiện tại và nút điều hướng)
  if (variant === 'compact') {
    return (
      <nav
        className={`flex flex-wrap items-center gap-4 md:gap-6 ${className}`}
        data-testid="pagination-compact"
        aria-label={t('pagination:paginationNavigation')}
      >
        {/* Bộ chọn số hàng trên trang */}
        <div className="flex items-center">
          <span
            className={`mr-2 text-gray-600 dark:text-gray-400 ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'} hidden sm:inline`}
          >
            {t('pagination:rowsPerPage')}
          </span>
          <div className="relative">
            <ItemsPerPageSelectNew
              value={itemsPerPage}
              options={itemsPerPageOptions}
              onChange={handleItemsPerPageChange}
              disabled={disabled}
              size={size}
              className="min-w-[70px]"
            />
          </div>
        </div>

        {/* Hiển thị trang hiện tại */}
        <div
          className={`flex items-center ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}
        >
          <span className="text-muted">{t('pagination:page')}</span>
          <span className="font-medium text-foreground mx-1">{currentPage}</span>
        </div>

        {/* Nút điều hướng */}
        <div className={`flex items-center ${gapSize} ml-auto sm:ml-0`}>
          {/* Nút trang trước */}
          <button
            className={`
              flex items-center justify-center rounded
              ${compactButtonSizeClasses}
              ${!borderless ? 'border-none' : ''}
              ${canGoPrevious && !disabled ? 'bg-card text-foreground hover:bg-card-muted cursor-pointer' : 'bg-card-muted text-muted opacity-50 cursor-not-allowed'}
              transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:ring-pagination-focus
            `}
            onClick={handlePreviousPage}
            disabled={!canGoPrevious || disabled}
            aria-label={t('pagination:previousPage')}
            aria-disabled={!canGoPrevious || disabled}
            type="button"
          >
            <Icon name="chevron-left" size={iconSize} />
          </button>

          {/* Nút trang tiếp theo */}
          <button
            className={`
              flex items-center justify-center rounded
              ${compactButtonSizeClasses}
              ${!borderless ? 'border-none' : ''}
              ${canGoNext && !disabled ? 'bg-card text-foreground hover:bg-card-muted cursor-pointer' : 'bg-card-muted text-muted opacity-50 cursor-not-allowed'}
              transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 focus:ring-pagination-focus
            `}
            onClick={handleNextPage}
            disabled={!canGoNext || disabled}
            aria-label={t('pagination:nextPage')}
            aria-disabled={!canGoNext || disabled}
            type="button"
          >
            <Icon name="chevron-right" size={iconSize} />
          </button>
        </div>
      </nav>
    );
  }

  // Render biến thể text
  if (variant === 'text') {
    return (
      <div className={`flex items-center justify-between ${className}`}>
        <div className={`text-muted ${textSizeClasses}`}>
          {t('pagination:pageInfo', { currentPage, totalPages })}
        </div>

        <div className="flex items-center space-x-4">
          <button
            className={`
              ${textSizeClasses} font-medium
              ${
                canGoPrevious && !disabled
                  ? 'text-primary hover:text-primary-600 cursor-pointer'
                  : 'text-muted opacity-50 cursor-not-allowed'
              }
            `}
            onClick={handlePreviousPage}
            disabled={!canGoPrevious || disabled}
            aria-label={t('pagination:previousPage')}
            type="button"
          >
            ← {previousLabel || defaultPreviousLabel}
          </button>

          <button
            className={`
              ${textSizeClasses} font-medium
              ${
                canGoNext && !disabled
                  ? 'text-primary hover:text-primary-600 cursor-pointer'
                  : 'text-muted opacity-50 cursor-not-allowed'
              }
            `}
            onClick={handleNextPage}
            disabled={!canGoNext || disabled}
            aria-label={t('pagination:nextPage')}
            type="button"
          >
            {nextLabel || defaultNextLabel} →
          </button>
        </div>
      </div>
    );
  }

  // Render biến thể mặc định (default)
  return (
    <div className={`flex flex-wrap items-center justify-between ${className}`}>
      {/* Thông tin trang */}
      {showPageInfo && (
        <div
          className={`text-muted ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}
        >
          {totalItems > 0 ? (
            <>
              {t('pagination:pageInfoWithItems', { currentPage, totalPages, totalItems })}
            </>
          ) : (
            t('pagination:noItems')
          )}
        </div>
      )}

      {/* Điều khiển phân trang */}
      <div className={`flex items-center ${gapSize}`}>
        {/* Bộ chọn số lượng mục trên mỗi trang */}
        {showItemsPerPageSelector && onItemsPerPageChange && (
          <div className="flex items-center mr-4">
            <span
              className={`mr-2 text-muted ${size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm'}`}
            >
              {t('pagination:show')}
            </span>
            <ItemsPerPageSelectNew
              value={itemsPerPage}
              options={itemsPerPageOptions}
              onChange={handleItemsPerPageChange}
              disabled={disabled}
              size={size}
              className="min-w-[70px]"
            />
          </div>
        )}

        {/* Nút trang đầu */}
        {showFirstLastButtons && (
          <PaginationButton
            page="<<"
            onClick={handleFirstPage}
            disabled={disabled || !canGoPrevious}
            ariaLabel={t('pagination:firstPage')}
            size={size}
            borderless={borderless}
          />
        )}

        {/* Nút trang trước */}
        <PaginationButton
          page={<Icon name="chevron-left" size={iconSize} />}
          onClick={handlePreviousPage}
          disabled={disabled || !canGoPrevious}
          ariaLabel={t('pagination:previousPage')}
          size={size}
          borderless={borderless}
        />

        {/* Các nút số trang */}
        {pageNumbers.map((page, index) => {
          // Tạo hàm xử lý sự kiện cho mỗi nút trang
          const handlePageClick =
            typeof page === 'number' ? () => handlePageChange(page) : undefined;

          // Tạo props cho PaginationButton, chỉ thêm onClick nếu có giá trị
          const buttonProps = {
            key: `${page}-${index}`,
            page,
            isActive: page === currentPage,
            disabled,
            ariaLabel: typeof page === 'number' ? t('pagination:goToPage', { page }) : t('pagination:morePage'),
            size,
            borderless,
            ...(handlePageClick && { onClick: handlePageClick }),
          };

          return <PaginationButton {...buttonProps} />;
        })}

        {/* Nút trang tiếp theo */}
        <PaginationButton
          page={<Icon name="chevron-right" size={iconSize} />}
          onClick={handleNextPage}
          disabled={disabled || !canGoNext}
          ariaLabel={t('pagination:nextPage')}
          size={size}
          borderless={borderless}
        />

        {/* Nút trang cuối */}
        {showFirstLastButtons && (
          <PaginationButton
            page=">>"
            onClick={handleLastPage}
            disabled={disabled || !canGoNext}
            ariaLabel={t('pagination:lastPage')}
            size={size}
            borderless={borderless}
          />
        )}
      </div>
    </div>
  );
};

export default Pagination;
