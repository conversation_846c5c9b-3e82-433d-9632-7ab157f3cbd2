# Static Fields Update - Segment Form

## Thay đổi

Cập nhật danh sách static fields trong Segment Form để chỉ giữ lại 3 fields cơ bản nhất.

### Trước:
```typescript
const AVAILABLE_FIELDS = [
  { value: 'email', label: 'Email' },
  { value: 'name', label: 'Tên' },
  { value: 'phone', label: 'Số điện thoại' },
  { value: 'age', label: 'Tuổi' },
  { value: 'city', label: 'Thành phố' },
  { value: 'country', label: 'Quốc gia' },
  { value: 'created_at', label: 'Ngày tạo' },
  { value: 'last_activity', label: 'Hoạt động cuối' },
];
```

### Sau:
```typescript
const AVAILABLE_FIELDS = [
  { value: 'email', label: 'Email' },
  { value: 'name', label: 'Tên' },
  { value: 'phone', label: '<PERSON><PERSON> điện thoại' },
];
```

## Lý do thay đổi

1. **Đơn giản hóa**: Chỉ giữ lại các fields cơ bản nhất
2. **Tr<PERSON>h trùng lặp**: Một số fields có thể trùng với custom fields
3. **UX tốt hơn**: Ít options hơn, dễ chọn hơn
4. **Focus vào custom fields**: Khuyến khích sử dụng custom fields từ API

## Kết quả

Bây giờ dropdown "Chọn Trường" sẽ hiển thị:

### Static Fields (3 fields):
- Email
- Tên  
- Số điện thoại

### Custom Fields (32+ fields từ API):
- Ngày sinh
- Giới tính
- Điểm tích lũy
- Khách VIP
- Nghề nghiệp
- Tình trạng hôn nhân
- Ngày mua gần nhất
- Mã giới thiệu
- Có con không
- Thành phố (custom)
- Quận/Huyện
- Phương thức liên hệ ưu tiên
- Mức thu nhập
- Facebook
- Số Zalo
- Ghi chú nội bộ
- Ngày liên hệ đầu tiên
- Trạng thái
- Cấp độ khách hàng
- Ngành nghề
- ... và nhiều fields khác

## Files đã cập nhật

1. **src/modules/marketing/components/forms/SegmentForm.tsx**
   - Cập nhật `AVAILABLE_FIELDS` array

2. **src/modules/marketing/CUSTOM_FIELD_INTEGRATION.md**
   - Cập nhật documentation

3. **src/modules/marketing/components/forms/TestAsyncSelect.tsx**
   - Cập nhật test component

## Testing

Để test thay đổi:

1. Mở Segment Form
2. Click dropdown "Chọn Trường"
3. Verify chỉ có 3 static fields ở đầu
4. Verify custom fields load từ API
5. Test search functionality với cả static và custom fields

## Impact

- **Positive**: UI cleaner, focus vào custom fields
- **Neutral**: Không ảnh hưởng functionality
- **Note**: Nếu cần thêm static fields, có thể dễ dàng thêm vào `AVAILABLE_FIELDS` array
