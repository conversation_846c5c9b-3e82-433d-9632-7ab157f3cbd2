# SSE (Server-Sent Events) Utilities

## 📋 Tổng quan

Bộ tiện ích SSE cung cấp một giải pháp hoàn chỉnh để làm việc với Server-Sent Events trong ứng dụng React. <PERSON><PERSON><PERSON><PERSON> thiết kế để dễ sử dụng, robust và có thể mở rộng.

## 🚀 Tính năng chính

### ✨ **Auto-Reconnection**
- Tự động kết nối lại khi bị ngắt
- Configurable retry attempts và delay
- Exponential backoff support

### 🎯 **Event Management**
- Subscribe/unsubscribe vào specific event types
- Advanced filtering với patterns
- Multiple subscriptions support

### 📊 **Connection Monitoring**
- Real-time connection state tracking
- Health checking
- Metrics collection (events, bandwidth, uptime)

### 🔗 **TaskQueue Integration**
- Tự động tạo tasks từ SSE events
- Queue management cho heavy processing
- Error handling và retry logic

### 🎨 **UI Components**
- Status indicators với multiple variants
- Toast-style notifications
- Customizable positioning và styling

### 🌐 **Context Support**
- Multiple SSE connections management
- Shared configuration
- Provider pattern implementation

## 📁 Cấu trúc Files

```
src/shared/
├── types/
│   └── sse.types.ts              # Types và interfaces
├── utils/
│   └── sse-client.ts             # SSE Client class
├── hooks/common/
│   ├── useSSE.ts                 # Hook chính
│   ├── useSSESubscription.ts     # Subscription hook
│   ├── useSSEConnection.ts       # Connection management
│   └── useSSEWithTaskQueue.ts    # TaskQueue integration
├── contexts/sse/
│   ├── SSEContext.tsx            # Context provider
│   ├── useSSEContext.ts          # Context hooks
│   └── index.ts                  # Exports
├── components/common/
│   ├── SSEStatus/                # Status component
│   └── SSENotification/          # Notification component
└── docs/
    ├── SSE_USAGE.md              # Hướng dẫn chi tiết
    └── SSE_README.md             # File này
```

## 🎯 Quick Start

### 1. Sử dụng cơ bản

```typescript
import { useSSE } from '@/shared/hooks/common';
import { SSEStatus } from '@/shared/components/common';

const MyComponent = () => {
  const sse = useSSE('/api/v1/sse/notifications', {
    autoConnect: true,
    autoReconnect: true,
  });

  React.useEffect(() => {
    const subscriptionId = sse.subscribe('user_notification', (event) => {
      console.log('Notification:', event.data);
    });

    return () => sse.unsubscribe(subscriptionId);
  }, [sse]);

  return (
    <div>
      <SSEStatus connectionInfo={sse.connectionInfo} />
      <button onClick={sse.connect}>Connect</button>
    </div>
  );
};
```

### 2. Sử dụng với Context

```typescript
import { SSEProvider, useSSEFromContext } from '@/shared/contexts/sse';

// App level
const App = () => (
  <SSEProvider defaultOptions={{ autoReconnect: true }}>
    <Dashboard />
  </SSEProvider>
);

// Component level
const Dashboard = () => {
  const sse = useSSEFromContext('notifications', '/api/v1/sse/notifications');
  
  // Use sse...
};
```

### 3. Notifications

```typescript
import { SSENotification } from '@/shared/components/common';

const [notifications, setNotifications] = useState([]);

<SSENotification
  notifications={notifications}
  onDismiss={(id) => setNotifications(prev => prev.filter(n => n.id !== id))}
  position="top-right"
  maxVisible={5}
/>
```

## 🔧 Configuration

### SSE Client Options

```typescript
interface UseSSEOptions {
  autoConnect?: boolean;           // Tự động kết nối
  autoReconnect?: boolean;         // Tự động kết nối lại
  reconnectDelay?: number;         // Delay giữa các lần retry (ms)
  maxReconnectAttempts?: number;   // Số lần retry tối đa
  timeout?: number;                // Timeout cho kết nối (ms)
  withCredentials?: boolean;       // Gửi credentials
  headers?: Record<string, string>; // Headers
  debug?: boolean;                 // Log debug
}
```

### Event Filtering

```typescript
const subscription = useSSESubscription(
  '/api/v1/sse/notifications',
  'user_notification',
  handler,
  {
    filter: {
      type: 'notification',
      data: { userId: getCurrentUserId() },
      filter: (event) => event.data.priority === 'high'
    }
  }
);
```

## 📊 Monitoring & Metrics

```typescript
const connection = useSSEConnection('/api/v1/sse/demo', {
  trackMetrics: true,
});

// Access metrics
console.log(connection.metrics);
// {
//   totalEvents: 150,
//   eventsByType: { notification: 100, update: 50 },
//   reconnectCount: 2,
//   errorCount: 1,
//   bytesReceived: 15000
// }

// Health check
const isHealthy = connection.checkHealth();
const uptime = connection.getUptime();
```

## 🔗 TaskQueue Integration

```typescript
const sseTaskQueue = useSSEWithTaskQueue('/api/v1/sse/tasks', {
  autoCreateTasks: true,
  taskFilter: (event) => event.type === 'background_task',
  defaultEventHandler: async (event) => {
    // Process task
    await processTask(event.data);
  },
});
```

## 🎨 UI Components

### SSEStatus Component

```typescript
<SSEStatus
  connectionInfo={sse.connectionInfo}
  metrics={metrics}
  variant="card"           // 'badge' | 'card' | 'inline'
  showDetails={true}
  showMetrics={true}
  onClick={() => sse.connect()}
/>
```

### SSENotification Component

```typescript
<SSENotification
  notifications={notifications}
  onDismiss={handleDismiss}
  onClearAll={handleClearAll}
  position="top-right"     // Position options
  maxVisible={5}
  showTimestamp={true}
  showClearAll={true}
/>
```

## 🌐 Demo & Testing

Truy cập `/components/sse` để xem demo đầy đủ các tính năng:

- **Tổng quan**: Giới thiệu tính năng
- **Cơ bản**: Demo useSSE hook
- **Subscription**: Demo useSSESubscription
- **Connection**: Demo connection management
- **Notifications**: Demo notification system

## 🔍 Troubleshooting

### Connection Issues
- Kiểm tra URL endpoint
- Verify authentication headers
- Check CORS settings
- Monitor network connectivity

### Performance Issues
- Limit số lượng events stored
- Use event filtering
- Implement proper cleanup
- Monitor memory usage

### Event Handling
- Validate event data structure
- Handle malformed JSON
- Implement retry logic
- Log errors for debugging

## 📚 Tài liệu chi tiết

Xem `SSE_USAGE.md` để có hướng dẫn chi tiết và ví dụ đầy đủ.

## 🤝 Contributing

Khi thêm tính năng mới:

1. Cập nhật types trong `sse.types.ts`
2. Implement logic trong utils/hooks
3. Thêm tests nếu cần
4. Cập nhật documentation
5. Thêm demo trong SSEPage

## 📄 License

Sử dụng theo license của dự án chính.
