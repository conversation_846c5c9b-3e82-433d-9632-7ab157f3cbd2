import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Input, ProgressBar } from '@/shared/components/common';
import { ConnectionTestResult, ConnectionTestStatus } from '../../types';
import { useConnectionTester } from '../../hooks';
import { formatResponseTime, formatDateTime } from '../../utils';

interface ConnectionTesterProps {
  agentId?: string;
  endpoint?: string;
  onTestComplete?: (result: ConnectionTestResult) => void;
  showHistory?: boolean;
  className?: string;
}

const ConnectionTester: React.FC<ConnectionTesterProps> = ({
  agentId,
  endpoint,
  onTestComplete,
  showHistory = true,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);
  const [timeout, setTimeout] = useState(30000);
  const [testHistory, setTestHistory] = useState<ConnectionTestResult[]>([]);

  // Use the connection tester hook - pass empty string if no agentId to avoid conditional hook call
  const connectionTester = useConnectionTester(agentId || '');
  const isTestingConnection = agentId ? connectionTester.isTestingConnection : false;
  const lastTestResult = agentId ? connectionTester.lastTestResult : undefined;

  const handleTest = () => {
    if (!agentId && !endpoint) {
      return;
    }

    if (agentId) {
      // Start the connection test - result will be available through lastTestResult
      connectionTester.testConnection(timeout);
    } else {
      // For testing endpoints without agent ID (during creation)
      const result: ConnectionTestResult = {
        success: false,
        error: 'Direct endpoint testing not implemented',
        timestamp: new Date().toISOString(),
      };

      // Add to history
      setTestHistory(prev => [result, ...prev.slice(0, 4)]); // Keep last 5 results

      // Call parent handler
      onTestComplete?.(result);
    }
  };

  const getStatusFromResult = (result: ConnectionTestResult): ConnectionTestStatus => {
    if (result.success) {
      return ConnectionTestStatus.SUCCESS;
    }
    if (result.error?.includes('timeout')) {
      return ConnectionTestStatus.TIMEOUT;
    }
    return ConnectionTestStatus.FAILED;
  };

  const getStatusColor = (status: ConnectionTestStatus) => {
    switch (status) {
      case ConnectionTestStatus.SUCCESS:
        return 'text-green-600';
      case ConnectionTestStatus.FAILED:
        return 'text-red-600';
      case ConnectionTestStatus.TIMEOUT:
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Test Configuration */}
      <Card>
        <div className="p-6">
          <Typography variant="h3" className="mb-4">
            {t('external-agents:connection.test')}
          </Typography>

          <div className="space-y-4">
            {/* Endpoint Display */}
            {endpoint && (
              <div>
                <Typography variant="caption" className="text-muted-foreground">
                  {t('external-agents:agent.endpoint')}
                </Typography>
                <Typography variant="body2" className="font-mono">
                  {endpoint}
                </Typography>
              </div>
            )}

            {/* Timeout Setting */}
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('external-agents:connection.timeout')} (ms)
                </Typography>
                <Input
                  type="number"
                  value={timeout}
                  onChange={(e) => setTimeout(parseInt(e.target.value) || 30000)}
                  min={1000}
                  max={60000}
                  step={1000}
                />
              </div>
              <Button
                onClick={handleTest}
                disabled={isTestingConnection || (!agentId && !endpoint)}
                variant="primary"
              >
                {isTestingConnection 
                  ? t('external-agents:connection.testing')
                  : t('external-agents:connection.test')
                }
              </Button>
            </div>

            {/* Progress Bar */}
            {isTestingConnection && (
              <div className="space-y-2">
                <ProgressBar value={0} className="w-full" />
                <Typography variant="caption" className="text-muted-foreground">
                  {t('external-agents:connection.testingProgress')}
                </Typography>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Latest Result */}
      {lastTestResult && (
        <Card>
          <div className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('external-agents:connection.latestResult')}
            </Typography>

            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div 
                  className={`w-3 h-3 rounded-full ${
                    lastTestResult.success ? 'bg-green-500' : 'bg-red-500'
                  }`}
                />
                <Typography 
                  variant="body1" 
                  className={`font-medium ${
                    lastTestResult.success ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {lastTestResult.success 
                    ? t('external-agents:connection.success')
                    : t('external-agents:connection.failed')
                  }
                </Typography>
              </div>

              {lastTestResult.responseTime && (
                <div className="flex items-center gap-2">
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('external-agents:connection.responseTime')}:
                  </Typography>
                  <Typography variant="body2" className="font-mono">
                    {formatResponseTime(lastTestResult.responseTime)}
                  </Typography>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Typography variant="body2" className="text-muted-foreground">
                  {t('external-agents:connection.timestamp')}:
                </Typography>
                <Typography variant="body2">
                  {formatDateTime(lastTestResult.timestamp)}
                </Typography>
              </div>

              {lastTestResult.error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                  <Typography variant="caption" className="text-red-600 font-medium">
                    {t('external-agents:connection.error')}:
                  </Typography>
                  <Typography variant="body2" className="text-red-700 mt-1">
                    {lastTestResult.error}
                  </Typography>
                </div>
              )}

              {lastTestResult.details && Object.keys(lastTestResult.details).length > 0 && (
                <div className="p-3 bg-muted rounded-md">
                  <Typography variant="caption" className="text-muted-foreground font-medium">
                    {t('external-agents:connection.details')}:
                  </Typography>
                  <pre className="text-xs mt-1 overflow-x-auto">
                    {JSON.stringify(lastTestResult.details, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Test History */}
      {showHistory && testHistory.length > 0 && (
        <Card>
          <div className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('external-agents:connection.history')}
            </Typography>

            <div className="space-y-3">
              {testHistory.map((result, index) => {
                const status = getStatusFromResult(result);
                return (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-md"
                  >
                    <div className="flex items-center gap-3">
                      <div 
                        className={`w-2 h-2 rounded-full ${
                          result.success ? 'bg-green-500' : 'bg-red-500'
                        }`}
                      />
                      <Typography variant="body2">
                        {formatDateTime(result.timestamp)}
                      </Typography>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      {result.responseTime && (
                        <Typography variant="caption" className="font-mono">
                          {formatResponseTime(result.responseTime)}
                        </Typography>
                      )}
                      <Typography 
                        variant="caption" 
                        className={getStatusColor(status)}
                      >
                        {result.success 
                          ? t('external-agents:connection.success')
                          : t('external-agents:connection.failed')
                        }
                      </Typography>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>
      )}

      {/* No Results */}
      {!lastTestResult && testHistory.length === 0 && !isTestingConnection && (
        <Card>
          <div className="p-8 text-center">
            <Typography variant="body1" className="text-muted-foreground">
              {t('external-agents:connection.noResults')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mt-1">
              {t('external-agents:connection.runFirstTest')}
            </Typography>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ConnectionTester;
