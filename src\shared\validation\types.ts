import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Validation severity levels
 */
export type ValidationSeverity = 'error' | 'warning' | 'info';

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  infos: ValidationInfo[];
}

/**
 * Validation error interface
 */
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
  severity: 'error';
  meta?: Record<string, unknown>;
}

/**
 * Validation warning interface
 */
export interface ValidationWarning {
  field: string;
  message: string;
  code?: string;
  severity: 'warning';
  meta?: Record<string, unknown>;
}

/**
 * Validation info interface
 */
export interface ValidationInfo {
  field: string;
  message: string;
  code?: string;
  severity: 'info';
  meta?: Record<string, unknown>;
}

/**
 * Async validation function type
 */
export type AsyncValidator<T = unknown> = (
  value: T,
  context?: ValidationContext
) => Promise<ValidationResult>;

/**
 * Sync validation function type
 */
export type SyncValidator<T = unknown> = (
  value: T,
  context?: ValidationContext
) => ValidationResult;

/**
 * Validation context interface
 */
export interface ValidationContext {
  field: string;
  formValues: Record<string, unknown>;
  t?: TFunction;
  meta?: Record<string, unknown>;
}

/**
 * Validation rule interface
 */
export interface ValidationRule<T = unknown> {
  name: string;
  validator: SyncValidator<T> | AsyncValidator<T>;
  message?: string;
  severity?: ValidationSeverity;
  enabled?: boolean | ((context: ValidationContext) => boolean);
  dependencies?: string[];
}

/**
 * Field validation configuration
 */
export interface FieldValidationConfig {
  rules: ValidationRule[];
  debounceMs?: number;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  validateOnMount?: boolean;
  stopOnFirstError?: boolean;
}

/**
 * Form validation configuration
 */
export interface FormValidationConfig {
  fields: Record<string, FieldValidationConfig>;
  globalRules?: ValidationRule[];
  validateOnSubmit?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  stopOnFirstError?: boolean;
}

/**
 * Validation schema builder options
 */
export interface SchemaBuilderOptions {
  t?: TFunction;
  locale?: string;
  strict?: boolean;
  allowUnknown?: boolean;
}

/**
 * Common validation patterns
 */
export interface ValidationPatterns {
  email: RegExp;
  phone: RegExp;
  url: RegExp;
  ipv4: RegExp;
  ipv6: RegExp;
  uuid: RegExp;
  creditCard: RegExp;
  postalCode: RegExp;
  strongPassword: RegExp;
  mediumPassword: RegExp;
  weakPassword: RegExp;
}

/**
 * Validation transformer function type
 */
export type ValidationTransformer<TInput = unknown, TOutput = unknown> = (
  value: TInput,
  context?: ValidationContext
) => TOutput;

/**
 * Validation message provider function type
 */
export type ValidationMessageProvider = (
  code: string,
  context: ValidationContext,
  meta?: Record<string, unknown>
) => string;

/**
 * Validation schema factory function type
 */
export type ValidationSchemaFactory<T = unknown> = (
  options?: SchemaBuilderOptions
) => z.ZodSchema<T>;

/**
 * Conditional validation rule
 */
export interface ConditionalValidationRule<T = unknown> extends ValidationRule<T> {
  condition: (context: ValidationContext) => boolean;
  whenTrue?: ValidationRule<T>[];
  whenFalse?: ValidationRule<T>[];
}

/**
 * Cross-field validation rule
 */
export interface CrossFieldValidationRule {
  name: string;
  fields: string[];
  validator: (values: Record<string, unknown>, context: ValidationContext) => ValidationResult;
  message?: string;
  severity?: ValidationSeverity;
}

/**
 * Validation state interface
 */
export interface ValidationState {
  isValidating: boolean;
  isValid: boolean;
  errors: Record<string, ValidationError[]>;
  warnings: Record<string, ValidationWarning[]>;
  infos: Record<string, ValidationInfo[]>;
  touchedFields: Set<string>;
  validatedFields: Set<string>;
}

/**
 * Validation options for hooks
 */
export interface UseValidationOptions {
  debounceMs?: number;
  validateOnMount?: boolean;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  stopOnFirstError?: boolean;
  enableAsyncValidation?: boolean;
}

/**
 * Async validation options
 */
export interface AsyncValidationOptions<T = unknown> extends UseValidationOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheKey?: (value: T, context: ValidationContext) => string;
}

/**
 * Validation cache entry
 */
export interface ValidationCacheEntry {
  result: ValidationResult;
  timestamp: number;
  ttl: number;
}

/**
 * Validation cache interface
 */
export interface ValidationCache {
  get(key: string): ValidationCacheEntry | undefined;
  set(key: string, entry: ValidationCacheEntry): void;
  delete(key: string): void;
  clear(): void;
  size(): number;
}

/**
 * Validation metrics interface
 */
export interface ValidationMetrics {
  totalValidations: number;
  successfulValidations: number;
  failedValidations: number;
  averageValidationTime: number;
  cacheHitRate: number;
  asyncValidationCount: number;
}

/**
 * Validation event types
 */
export type ValidationEventType =
  | 'validation:start'
  | 'validation:success'
  | 'validation:error'
  | 'validation:warning'
  | 'validation:info'
  | 'validation:complete'
  | 'validation:cache:hit'
  | 'validation:cache:miss'
  | 'validation:async:start'
  | 'validation:async:complete'
  | 'validation:async:timeout'
  | 'validation:async:retry';

/**
 * Validation event interface
 */
export interface ValidationEvent {
  type: ValidationEventType;
  field: string;
  value: unknown;
  result?: ValidationResult;
  error?: Error;
  timestamp: number;
  duration?: number;
  meta?: Record<string, unknown>;
}
