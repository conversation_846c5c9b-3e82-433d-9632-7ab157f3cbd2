import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Typography, Button } from '@/shared/components/common';

export interface ConfirmDeleteModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi xác nhận xóa
   */
  onConfirm: () => void;

  /**
   * Tiêu đề của modal
   * @default Sử dụng translation key 'common:confirmDeleteTitle'
   */
  title?: string;

  /**
   * Nội dung thông báo
   * @default Sử dụng translation key 'common:confirmDeleteMessage' hoặc 'common:confirmDeleteWithName' nếu có itemName
   */
  message?: string;

  /**
   * Tên của item cần xóa (nếu có)
   * Sẽ được sử dụng trong message mặc định
   */
  itemName?: string | null | undefined;

  /**
   * Trạng thái đang submit
   */
  isSubmitting?: boolean;

  /**
   * Nội dung nút xác nhận
   * @default Sử dụng translation key 'common:delete'
   */
  confirmButtonText?: string;

  /**
   * Nội dung nút hủy
   * @default Sử dụng translation key 'common:cancel'
   */
  cancelButtonText?: string;

  /**
   * Variant của nút xác nhận
   * @default 'danger'
   */
  confirmButtonVariant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'outline' | 'ghost';

  /**
   * Số lượng items cho bulk delete
   * Sẽ được sử dụng trong message mặc định cho bulk delete
   */
  itemCount?: number;
}

/**
 * Modal xác nhận xóa dùng chung
 */
const ConfirmDeleteModal: React.FC<ConfirmDeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  isSubmitting = false,
  confirmButtonText,
  cancelButtonText,
  confirmButtonVariant = 'danger',
  itemCount,
}) => {
  const { t } = useTranslation();

  // Tạo title mặc định từ translation
  const modalTitle = title || t('common:confirmDeleteTitle', 'Xác nhận xóa');

  // Tạo message mặc định từ translation
  const getDefaultMessage = () => {
    if (itemCount && itemCount > 1) {
      // Bulk delete message
      return t('common:confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} mục đã chọn?', {
        count: itemCount,
      });
    } else if (itemName) {
      // Single item with name
      return t('common:confirmDeleteWithName', 'Bạn có chắc chắn muốn xóa "{{itemName}}"?', {
        itemName,
      });
    } else {
      // Default single item message
      return t('common:confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa mục này?');
    }
  };

  const modalMessage = message || getDefaultMessage();

  // Tạo button text mặc định từ translation
  const modalCancelText = cancelButtonText || t('common:cancel', 'Hủy');
  const modalConfirmText = confirmButtonText || t('common:delete', 'Xóa');

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      size="sm"
      footer={
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {modalCancelText}
          </Button>
          <Button variant={confirmButtonVariant} onClick={onConfirm} disabled={isSubmitting}>
            {modalConfirmText}
          </Button>
        </div>
      }
    >
      <div className="py-4">
        <Typography>{modalMessage}</Typography>
        {itemName && !message && (
          <Typography variant="body2" className="mt-2 font-semibold text-gray-600 dark:text-gray-400">
            {itemName}
          </Typography>
        )}
      </div>
    </Modal>
  );
};

export default ConfirmDeleteModal;
