/**
 * SSE Context hooks - separated for Fast Refresh compatibility
 */
import React, { useContext } from 'react';
import { SSEContext } from './context';
import { UseSSEOptions, UseSSEReturn } from '@/shared/types/sse.types';

/**
 * Hook để sử dụng SSE Context
 */
export const useSSEContext = () => {
  const context = useContext(SSEContext);

  if (!context) {
    throw new Error('useSSEContext must be used within an SSEProvider');
  }

  return context;
};

/**
 * Hook để tạo và quản lý một SSE connection thông qua context
 */
export const useSSEFromContext = (
  id: string,
  url: string,
  options: UseSSEOptions = {}
): UseSSEReturn => {
  const { createConnection, getConnection, removeConnection } = useSSEContext();

  // Tạo connection nếu chưa có
  React.useEffect(() => {
    if (!getConnection(id)) {
      createConnection(id, url, options);
    }

    // Cleanup khi unmount
    return () => {
      removeConnection(id);
    };
  }, [id, url, options, createConnection, getConnection, removeConnection]);

  // Lấy connection
  const connection = getConnection(id);

  if (!connection) {
    throw new Error(`SSE connection with id "${id}" not found`);
  }

  return connection;
};
