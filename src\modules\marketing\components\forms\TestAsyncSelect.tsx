import React from 'react';
import AsyncSelectWithPagination from '@/shared/components/common/Select/AsyncSelectWithPagination';

/**
 * Test component để verify AsyncSelectWithPagination hoạt động
 */
const TestAsyncSelect: React.FC = () => {
  // Test data hardcoded
  const testLoadOptions = async (params: { search?: string; page?: number; limit?: number }) => {
    console.log('testLoadOptions called with:', params);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const allItems = [
      // Static fields (chỉ 3 fields)
      { value: 'email', label: 'Email', description: 'Trường hệ thống' },
      { value: 'name', label: 'Tên', description: 'Trường hệ thống' },
      { value: 'phone', label: 'Số điện thoại', description: 'Trường hệ thống' },
      // Custom fields
      { value: 'birth_date', label: '<PERSON><PERSON><PERSON> sinh', description: 'Trường tùy chỉnh' },
      { value: 'gender', label: 'Giớ<PERSON> tính', description: 'Trường tùy chỉnh' },
      { value: 'loyalty_points', label: 'Điểm tích lũy', description: 'Trường tùy chỉnh' },
      { value: 'is_vip', label: 'Khách VIP', description: 'Trường tùy chỉnh' },
      { value: 'occupation', label: 'Nghề nghiệp', description: 'Trường tùy chỉnh' },
    ];

    // Filter by search if provided
    const filteredItems = params.search 
      ? allItems.filter(item => 
          item.label.toLowerCase().includes(params.search!.toLowerCase()) ||
          item.value.toLowerCase().includes(params.search!.toLowerCase())
        )
      : allItems;

    // Pagination
    const page = params.page || 1;
    const limit = params.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedItems = filteredItems.slice(startIndex, endIndex);

    const result = {
      items: paginatedItems,
      totalItems: filteredItems.length,
      totalPages: Math.ceil(filteredItems.length / limit),
      currentPage: page
    };

    console.log('testLoadOptions returning:', result);
    return result;
  };

  const handleChange = (value: string | string[] | number | number[] | undefined) => {
    console.log('Selected value:', value);
  };

  return (
    <div className="p-4 max-w-md">
      <h3 className="text-lg font-semibold mb-4">Test AsyncSelectWithPagination</h3>
      
      <AsyncSelectWithPagination
        value=""
        onChange={handleChange}
        loadOptions={testLoadOptions}
        placeholder="Chọn trường..."
        searchOnEnter={false}
        autoLoadInitial={true}
        debounceTime={300}
        itemsPerPage={5} // Small number for testing
        noOptionsMessage="Không tìm thấy tùy chọn"
        loadingMessage="Đang tải..."
        fullWidth
      />
      
      <div className="mt-4 text-sm text-gray-600">
        <p>Mở console để xem logs</p>
        <p>Test cases:</p>
        <ul className="list-disc list-inside">
          <li>Component mount → should auto load</li>
          <li>Search "email" → should filter</li>
          <li>Scroll down → should load more</li>
          <li>Select item → should log value</li>
        </ul>
      </div>
    </div>
  );
};

export default TestAsyncSelect;
