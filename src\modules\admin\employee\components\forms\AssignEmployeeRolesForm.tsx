/**
 * Form gán vai trò cho nhân viên
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Card, Checkbox } from '@/shared/components/common';
import { EmployeeDto } from '../../types/employee.types';
import { RoleDto } from '../../types/role.types';
import { useAllRoles, useEmployeeRoles, useAssignEmployeeRoles } from '../../hooks/useRoleQuery';

interface AssignEmployeeRolesFormProps {
  employee: EmployeeDto;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Component form gán vai trò cho nhân viên
 */
const AssignEmployeeRolesForm: React.FC<AssignEmployeeRolesFormProps> = ({
  employee,
  onSuccess,
  onCancel,
}) => {
  const { t } = useTranslation(['employee', 'common']);

  // State
  const [selectedRoles, setSelectedRoles] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);

  // Queries
  const allRolesQuery = useAllRoles();
  const employeeRolesQuery = useEmployeeRoles(employee.id);
  const assignRolesMutation = useAssignEmployeeRoles(employee.id);

  // Effect để set roles hiện tại của employee
  useEffect(() => {
    if (employeeRolesQuery.data) {
      const currentRoleIds = employeeRolesQuery.data.map(r => r.id);
      setSelectedRoles(currentRoleIds);
    }
  }, [employeeRolesQuery.data]);

  // Xử lý thay đổi checkbox
  const handleRoleChange = (roleId: number, checked: boolean) => {
    if (checked) {
      setSelectedRoles(prev => [...prev, roleId]);
    } else {
      setSelectedRoles(prev => prev.filter(id => id !== roleId));
    }
  };

  // Xử lý submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await assignRolesMutation.mutateAsync({
        roleIds: selectedRoles,
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error assigning roles:', error);
    } finally {
      setLoading(false);
    }
  };

  if (allRolesQuery.isLoading || employeeRolesQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="text-center">{t('common:loading')}</div>
      </div>
    );
  }

  return (
    <Card className="m-6">
      <div className="p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('employee:role.assignRoles')}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('employee:role.assignRolesDescription', { employeeName: employee.fullName })}
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <Card className="p-4 border border-gray-200 dark:border-gray-700">
            <div className="space-y-3">
              {allRolesQuery.data?.map((role: RoleDto) => (
                <div key={role.id} className="flex items-start space-x-3">
                  <Checkbox
                    id={`role-${role.id}`}
                    checked={selectedRoles.includes(role.id)}
                    onChange={(checked) => handleRoleChange(role.id, checked)}
                    variant="filled"
                  />
                  <div className="flex-1">
                    <label
                      htmlFor={`role-${role.id}`}
                      className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
                    >
                      {role.name}
                    </label>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {role.description}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          (role.status || 'active') === 'active'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                        }`}
                      >
                        {(role.status || 'active') === 'active' ? t('common:active') : t('common:inactive')}
                      </span>
                      <span
                        className={`px-2 py-1 rounded-full text-xs font-medium ${
                          role.isSystem
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                            : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                        }`}
                      >
                        {role.isSystem ? t('employee:role.system') : t('employee:role.custom')}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              {t('common:cancel')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={loading}
              disabled={assignRolesMutation.isPending}
            >
              {t('employee:role.assignRoles')}
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default AssignEmployeeRolesForm;
