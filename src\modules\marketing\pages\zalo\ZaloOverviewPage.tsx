import { useTranslation } from 'react-i18next';
import { Plus, MessageCircle, Users, TrendingUp, Settings } from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { Chip } from '@/shared/components/common';
import { Skeleton } from '@/shared/components/common';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAccounts } from '../../hooks/zalo/useZaloAccounts';
import { useNavigate } from 'react-router-dom';
import type { ZaloOAAccountDto } from '../../types/zalo.types';

/**
 * Trang tổng quan Zalo Marketing
 */
export function ZaloOverviewPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  const { data: accountsData, isLoading } = useZaloAccounts({ limit: 10 });

  const handleConnectAccount = () => {
    navigate('/marketing/zalo/accounts?action=connect');
  };

  const handleViewAccount = (accountId: number) => {
    navigate(`/marketing/zalo/accounts/${accountId}`);
  };

  const handleViewFollowers = (accountId: number) => {
    navigate(`/marketing/zalo/accounts/${accountId}/followers`);
  };

  const handleViewMessages = (accountId: number) => {
    navigate(`/marketing/zalo/accounts/${accountId}/messages`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zalo.overview.title', 'Zalo Marketing')}
        description={t('marketing:zalo.overview.description', 'Quản lý Zalo Official Account và chiến dịch ZNS')}
        actions={
          <Button onClick={handleConnectAccount} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('marketing:zalo.overview.connectAccount', 'Kết nối OA')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.overview.stats.totalAccounts', 'Tổng số OA')}
            </span>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </div>
          <div className="text-2xl font-bold">
            {isLoading ? <Skeleton className="h-8 w-16" /> : accountsData?.meta.totalItems || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.overview.stats.activeAccounts', 'Đang hoạt động')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.overview.stats.totalFollowers', 'Tổng Followers')}
            </span>
            <Users className="h-4 w-4 text-info" />
          </div>
          <div className="text-2xl font-bold text-info">
            {isLoading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              accountsData?.items.reduce((total: number, account: ZaloOAAccountDto) => total + account.followersCount, 0)?.toLocaleString() || 0
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.overview.stats.newFollowersToday', '+12 hôm nay')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.overview.stats.messagesSent', 'Tin nhắn đã gửi')}
            </span>
            <MessageCircle className="h-4 w-4 text-success" />
          </div>
          <div className="text-2xl font-bold text-success">
            {isLoading ? <Skeleton className="h-8 w-16" /> : '1,234'}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.overview.stats.messagesToday', '+89 hôm nay')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zalo.overview.stats.engagementRate', 'Tỷ lệ tương tác')}
            </span>
            <TrendingUp className="h-4 w-4 text-warning" />
          </div>
          <div className="text-2xl font-bold text-warning">
            {isLoading ? <Skeleton className="h-8 w-16" /> : '24.5%'}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zalo.overview.stats.increaseFromLastWeek', '+2.1% từ tuần trước')}
          </p>
        </Card>
      </div>

      {/* Connected Accounts */}
      <Card
        title={t('marketing:zalo.overview.connectedAccounts', 'Tài khoản đã kết nối')}
        subtitle={t('marketing:zalo.overview.connectedAccountsDescription', 'Quản lý các Zalo Official Account đã kết nối')}
      >
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-[200px]" />
                    <Skeleton className="h-4 w-[150px]" />
                  </div>
                </div>
              ))}
            </div>
          ) : accountsData?.items.length === 0 ? (
            <div className="text-center py-8">
              <div className="mx-auto h-12 w-12 text-muted-foreground mb-4">
                <Settings className="h-full w-full" />
              </div>
              <h3 className="text-lg font-medium mb-2">
                {t('marketing:zalo.overview.noAccounts', 'Chưa có tài khoản nào')}
              </h3>
              <p className="text-muted-foreground mb-4">
                {t('marketing:zalo.overview.noAccountsDescription', 'Kết nối Zalo Official Account để bắt đầu')}
              </p>
              <Button onClick={handleConnectAccount}>
                {t('marketing:zalo.overview.connectFirstAccount', 'Kết nối tài khoản đầu tiên')}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {accountsData?.items.map((account: ZaloOAAccountDto) => (
                <Card
                  key={account.id}
                  className="flex items-center justify-between p-4 hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center text-white font-semibold">
                      {account.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <h4 className="font-medium">{account.name}</h4>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <span>{account.followersCount} followers</span>
                        <Chip
                          variant={account.status === 'active' ? 'success' : 'warning'}
                        >
                          {account.status === 'active' ? 'Hoạt động' : 'Không hoạt động'}
                        </Chip>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewFollowers(account.id)}
                    >
                      <Users className="h-4 w-4 mr-1" />
                      {t('marketing:common.followers', 'Followers')}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewMessages(account.id)}
                    >
                      <MessageCircle className="h-4 w-4 mr-1" />
                      {t('marketing:zalo.messages', 'Tin nhắn')}
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleViewAccount(account.id)}
                    >
                      {t('marketing:common.manage', 'Quản lý')}
                    </Button>
                  </div>
                </Card>
              ))}

              {accountsData && accountsData.meta.totalItems > accountsData.items.length && (
                <div className="text-center pt-4">
                  <Button
                    variant="outline"
                    onClick={() => navigate('/marketing/zalo/accounts')}
                  >
                    {t('marketing:zalo.overview.viewAllAccounts', 'Xem tất cả tài khoản')}
                  </Button>
                </div>
              )}
            </div>
          )}
      </Card>

      {/* Quick Actions */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="cursor-pointer hover:shadow-card-hover transition-shadow" onClick={() => navigate('/marketing/zalo/zns')}>
          <div>
            <h3 className="font-medium">{t('marketing:zalo.zns.title', 'ZNS Templates')}</h3>
            <p className="text-sm text-muted-foreground">{t('marketing:zalo.zns.description', 'Quản lý template thông báo ZNS')}</p>
          </div>
        </Card>

        <Card className="cursor-pointer hover:shadow-card-hover transition-shadow" onClick={() => navigate('/marketing/zalo/automation')}>
          <div>
            <h3 className="font-medium">{t('marketing:zalo.automation.title', 'Automation')}</h3>
            <p className="text-sm text-muted-foreground">{t('marketing:zalo.automation.description', 'Thiết lập tự động hóa tin nhắn')}</p>
          </div>
        </Card>

        <Card className="cursor-pointer hover:shadow-card-hover transition-shadow" onClick={() => navigate('/marketing/zalo/analytics')}>
          <div>
            <h3 className="font-medium">{t('marketing:zalo.analytics.title', 'Analytics')}</h3>
            <p className="text-sm text-muted-foreground">{t('marketing:zalo.analytics.description', 'Báo cáo và phân tích hiệu quả')}</p>
          </div>
        </Card>
      </div>
    </div>
  );
}

export default ZaloOverviewPage;
