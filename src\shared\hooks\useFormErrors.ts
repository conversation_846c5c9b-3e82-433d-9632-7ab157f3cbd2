import { useRef, useCallback } from 'react';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';

/**
 * Hook để xử lý lỗi form từ API một cách dễ dàng
 *
 * @example
 * const { formRef, setFormErrors, resetForm } = useFormErrors<LoginFormValues>();
 *
 * // Trong hàm submit
 * const handleSubmit = async (values) => {
 *   try {
 *     const result = await api.login(values);
 *     if (!result.success && result.errors) {
 *       setFormErrors(result.errors);
 *     }
 *   } catch (error) {
 *     // Xử lý lỗi
 *   }
 * };
 *
 * // Trong JSX
 * <Form ref={formRef} onSubmit={handleSubmit}>
 *   ...
 * </Form>
 */
export function useFormErrors<TFormValues extends FieldValues>() {
  // Tạo ref cho form
  const formRef = useRef<FormRef<TFormValues>>(null);

  /**
   * Đặt lỗi cho form từ API
   * @param errors Object chứa các lỗi từ API, dạng { [fieldName]: errorMessage }
   */
  const setFormErrors = useCallback((errors: Record<string, string>) => {
    if (formRef.current) {
      formRef.current.setErrors(errors);
    }
  }, []);

  /**
   * Reset form về giá trị mặc định
   * @param values Giá trị mặc định (optional)
   */
  const resetForm = useCallback((values?: TFormValues) => {
    if (formRef.current) {
      formRef.current.reset(values);
    }
  }, []);

  /**
   * Lấy form methods
   * @returns Form methods hoặc null nếu form chưa được khởi tạo
   */
  const getFormMethods = useCallback(() => {
    if (formRef.current) {
      return formRef.current.getFormMethods();
    }
    return null;
  }, []);

  return {
    formRef,
    setFormErrors,
    resetForm,
    getFormMethods,
  };
}

export default useFormErrors;
