import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Avatar, Dropdown, IconCard } from '@/shared/components/common';

// Mock data for AI agents
const aiAgents = [
  {
    id: '1',
    name: 'Assistant',
    avatar: '/assets/images/ai-agents/assistant-robot.svg',
  },
  {
    id: '2',
    name: 'Coder',
    avatar: '/assets/images/ai-agents/coder-robot.svg',
  },
  {
    id: '3',
    name: 'Writer',
    avatar: '/assets/images/ai-agents/writer-robot.svg',
  },
  {
    id: '4',
    name: 'Researcher',
    avatar: '/assets/images/ai-agents/researcher-robot.svg',
  },
];

interface ChatHeaderProps {
  onNewChat: () => void;
  onClose: () => void;
}

const ChatHeader = ({ onNewChat, onClose }: ChatHeaderProps) => {
  const { t } = useTranslation();
  const [selectedAgent, setSelectedAgent] = useState(aiAgents[0]);

  // Create dropdown items from AI agents
  const agentItems = aiAgents.map(agent => ({
    id: agent.id,
    label: (
      <div className="flex items-center">
        <Avatar src={agent.avatar} alt={agent.name} size="sm" className="mr-2" />
        <span>{agent.name}</span>
      </div>
    ),
    onClick: () => setSelectedAgent(agent),
  }));

  return (
    <div className="flex items-center justify-between p-3 bg-white dark:bg-dark">
      <div className="flex items-center">
        <Dropdown
          trigger={
            <div className="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-lighter p-2 rounded">
              <Avatar
                {...(selectedAgent?.avatar && { src: selectedAgent.avatar })}
                alt={selectedAgent?.name || 'Agent'}
                size="sm"
              />
              <span className="ml-2 font-medium">{selectedAgent?.name}</span>
              <svg
                className="w-4 h-4 ml-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          }
          items={agentItems}
          placement="bottom-left"
        />
      </div>

      <div className="flex items-center space-x-2">
        <IconCard icon="plus" variant="primary" onClick={onNewChat} title={t('chat.newChat')} />

        <IconCard
          icon="chevron-left"
          variant="default"
          onClick={onClose}
          title={t('common.close')}
        />
      </div>
    </div>
  );
};

export default ChatHeader;
