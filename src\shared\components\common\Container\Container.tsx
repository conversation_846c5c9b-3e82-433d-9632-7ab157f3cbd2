import React, { HTMLAttributes } from 'react';
import { useContainerWidth } from '@/shared/hooks/common';

export interface ContainerProps extends HTMLAttributes<HTMLDivElement> {
  /**
   * Nội dung của container
   */
  children: React.ReactNode;

  /**
   * Nếu true, container sẽ có width 100% ở tất cả các breakpoints
   */
  fluid?: boolean;

  /**
   * Padding bên trong container
   */
  padding?: boolean | string;

  /**
   * Căn giữa container
   */
  centered?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Container component giới hạn chiều rộng nội dung theo breakpoint
 *
 * @example
 * // Container cơ bản
 * <Container>
 *   <p>Content with max width based on breakpoint</p>
 * </Container>
 *
 * @example
 * // Container fluid
 * <Container fluid>
 *   <p>Content with 100% width on all breakpoints</p>
 * </Container>
 */
const Container: React.FC<ContainerProps> = ({
  children,
  fluid = false,
  padding = true,
  centered = true,
  className = '',
  ...rest
}) => {
  const containerWidth = useContainerWidth(fluid);

  // Padding classes
  const paddingClasses =
    typeof padding === 'boolean' ? (padding ? 'px-4 sm:px-6 md:px-8' : '') : padding;

  // Center classes
  const centerClasses = centered ? 'mx-auto' : '';

  // Combine all classes
  const containerClasses = [paddingClasses, centerClasses, className].join(' ');

  return (
    <div className={containerClasses} style={{ maxWidth: containerWidth }} {...rest}>
      {children}
    </div>
  );
};

export default Container;
