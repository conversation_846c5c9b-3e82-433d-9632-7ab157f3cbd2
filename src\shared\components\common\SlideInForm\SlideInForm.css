/* CSS cho animation form */
.slide-form-container {
  position: relative;
  overflow: hidden;
}

/* Animation khi form xuất hiện */
.slide-form-enter {
  transform: translateX(100%);
  opacity: 0;
}

.slide-form-enter-active {
  transform: translateX(0);
  opacity: 1;
  transition: all 300ms ease-in-out;
}

/* Animation khi form biến mất */
.slide-form-exit {
  transform: translateX(0);
  opacity: 1;
}

.slide-form-exit-active {
  transform: translateX(100%);
  opacity: 0;
  transition: all 300ms ease-in-out;
}
