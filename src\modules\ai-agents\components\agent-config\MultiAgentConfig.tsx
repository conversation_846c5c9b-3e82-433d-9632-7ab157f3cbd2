import { Button, EmptyState, Icon } from '@/shared/components/common';
import React, { useState, useEffect } from 'react';
import { MultiAgentConfigData, MultiAgentItem as MultiAgentItemType } from '../../types/agent';
import { TypeAgent } from '../agent-add/TypeAgentCard';
import AgentSelectForm from './AgentSelectForm';
import { ConfigComponentWrapper } from './ConfigComponentWrapper';
import MultiAgentItem from './MultiAgentItem';
import { useGetMultiAgentRelations, useBulkUpdateMultiAgentRelations } from '../../hooks/useMultiAgent';

interface MultiAgentConfigProps {
  /**
   * ID của agent cha
   */
  agentId?: string;

  /**
   * Dữ liệu cấu hình multi-agent ban đầu
   */
  initialData?: MultiAgentConfigData;

  /**
   * Callback khi lưu cấu hình
   */
  onSave?: (data: MultiAgentConfigData) => void;

  /**
   * Danh sách các type agent có thể chọn
   */
  availableAgents?: TypeAgent[];
}

/**
 * Component cấu hình multi-agent cho Agent
 */
const MultiAgentConfig: React.FC<MultiAgentConfigProps> = ({
  agentId,
  initialData,
  onSave,
  availableAgents = []
}) => {
  // Fetch multi-agent relations từ API nếu có agentId
  const { data: multiAgentResponse } = useGetMultiAgentRelations(agentId);
  const bulkUpdateMutation = useBulkUpdateMultiAgentRelations();

  const [configData, setConfigData] = useState<MultiAgentConfigData>(
    initialData || { agents: [] }
  );
  const [showAddForm, setShowAddForm] = useState(false);

  // Update local state khi có data từ API
  useEffect(() => {
    if (multiAgentResponse?.result) {
      const apiData = multiAgentResponse.result;
      // Convert API data to local format
      const convertedAgents: MultiAgentItemType[] = apiData.relations.map((relation, index) => ({
        id: relation.childAgentId,
        name: relation.childAgentName,
        avatar: relation.childAgentAvatar || '',
        agentTypeId: 1, // Default value
        description: '', // Default value
        order: index
      }));

      setConfigData({
        agents: convertedAgents
      });
    }
  }, [multiAgentResponse]);

  // Helper function để save data
  const saveMultiAgentData = (newData: MultiAgentConfigData) => {
    if (agentId) {
      // Convert local format to API format
      const apiData = {
        relations: newData.agents.map(agent => ({
          childAgentId: agent.id,
          prompt: '' // Default prompt, có thể extend MultiAgentItem để có prompt field
        }))
      };

      bulkUpdateMutation.mutate({
        agentId,
        data: apiData
      });
    }

    // Gọi callback onSave nếu có
    if (onSave) {
      onSave(newData);
    }
  };

  // Xử lý thêm agent mới
  const handleAddAgent = (newAgent: MultiAgentItemType) => {
    const updatedData = {
      ...configData,
      agents: [...configData.agents, newAgent]
    };

    setConfigData(updatedData);
    setShowAddForm(false);
    saveMultiAgentData(updatedData);
  };

  // Xử lý cập nhật agent
  const handleUpdateAgent = (updatedAgent: MultiAgentItemType) => {
    const updatedData = {
      ...configData,
      agents: configData.agents.map(agent =>
        agent.id === updatedAgent.id ? updatedAgent : agent
      )
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  // Xử lý xóa agent
  const handleRemoveAgent = (agentId: string) => {
    const updatedData = {
      ...configData,
      agents: configData.agents.filter(agent => agent.id !== agentId)
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  // Xử lý di chuyển agent lên
  const handleMoveUp = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex <= 0) return;

    const newAgents = [...configData.agents];
    [newAgents[currentIndex - 1], newAgents[currentIndex]] =
      [newAgents[currentIndex], newAgents[currentIndex - 1]];

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  // Xử lý di chuyển agent xuống
  const handleMoveDown = (agentId: string) => {
    const currentIndex = configData.agents.findIndex(agent => agent.id === agentId);
    if (currentIndex >= configData.agents.length - 1) return;

    const newAgents = [...configData.agents];
    [newAgents[currentIndex], newAgents[currentIndex + 1]] =
      [newAgents[currentIndex + 1], newAgents[currentIndex]];

    const updatedData = {
      ...configData,
      agents: newAgents
    };

    setConfigData(updatedData);
    saveMultiAgentData(updatedData);
  };

  return (
    <ConfigComponentWrapper
      componentId="multiAgent"
      title={
        <div className="flex items-center">
          <span className="mr-2">
            <Icon name="users" size="sm" />
          </span>
          <span>Cấu hình Multi-Agent</span>
        </div>
      }
    >
      <div className="p-4 space-y-4">
        {/* Mô tả */}
        <div className="text-sm text-gray-600 dark:text-gray-300 mb-4">
          Cấu hình nhiều agent để làm việc cùng nhau. Mỗi agent sẽ có vai trò và chức năng riêng biệt.
        </div>

        {/* Form thêm agent mới */}
        <AgentSelectForm
          availableAgents={availableAgents}
          onAddAgent={handleAddAgent}
          onCancel={() => setShowAddForm(false)}
          isVisible={showAddForm}
        />

        {/* Nút thêm agent */}
        {!showAddForm && (
          <Button
            variant="outline"
            onClick={() => setShowAddForm(true)}
            leftIcon={<Icon name="plus" size="sm" />}
            className="w-full"
          >
            Thêm Agent
          </Button>
        )}

        {/* Danh sách agents */}
        {configData.agents.length > 0 ? (
          <div className="space-y-3">
            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Danh sách Agents ({configData.agents.length})
            </div>
            {configData.agents.map((agent, index) => (
              <MultiAgentItem
                key={agent.id}
                agent={agent}
                onUpdate={handleUpdateAgent}
                onRemove={handleRemoveAgent}
                canMoveUp={index > 0}
                canMoveDown={index < configData.agents.length - 1}
                onMoveUp={handleMoveUp}
                onMoveDown={handleMoveDown}
              />
            ))}
          </div>
        ) : (
          !showAddForm && (
            <EmptyState
              icon="users"
              title="Chưa có agent nào"
              description="Thêm agent đầu tiên để bắt đầu cấu hình multi-agent"
              className="py-8"
            />
          )
        )}
      </div>
    </ConfigComponentWrapper>
  );
};

export default MultiAgentConfig;
