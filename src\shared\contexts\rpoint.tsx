import React, { useState, useEffect, ReactNode } from 'react';
import { RPointContext } from './RPointContext';

// Props cho RPointProvider
interface RPointProviderProps {
  children: ReactNode;
}

/**
 * Provider cho RPoint context
 */
export const RPointProvider: React.FC<RPointProviderProps> = ({ children }) => {
  // State để lưu trữ số RPoint của người dùng
  const [userRPoints, setUserRPoints] = useState<number>(70000000);

  // Hàm để trừ điểm
  const deductPoints = (amount: number): boolean => {
    if (userRPoints >= amount) {
      setUserRPoints(prev => prev - amount);
      return true;
    }
    return false;
  };

  // Hàm để thêm điểm
  const addPoints = (amount: number): void => {
    setUserRPoints(prev => prev + amount);
  };

  // Lưu trữ số RPoint vào localStorage khi thay đổi
  useEffect(() => {
    localStorage.setItem('userRPoints', userRPoints.toString());
  }, [userRPoints]);

  // L<PERSON>y số RPoint từ localStorage khi khởi tạo
  useEffect(() => {
    const storedPoints = localStorage.getItem('userRPoints');
    if (storedPoints) {
      setUserRPoints(parseInt(storedPoints, 10));
    }
  }, []);

  // Giá trị của context
  const value = {
    userRPoints,
    setUserRPoints,
    deductPoints,
    addPoints,
  };

  return <RPointContext.Provider value={value}>{children}</RPointContext.Provider>;
};

// Hook useRPoint đã được chuyển sang file useRPoint.ts
