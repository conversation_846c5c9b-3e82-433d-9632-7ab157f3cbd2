import React, { ReactNode } from 'react';
import { useTheme } from '@/shared/contexts/theme';
import { Button, Typography } from '@/shared/components/common';

export type BannerVariant = 'primary' | 'secondary' | 'gradient' | 'custom';
export type BannerSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';
export type BannerAlignment = 'left' | 'center' | 'right';

export interface BannerProps {
  /**
   * Tiêu đề của banner
   */
  title?: ReactNode;

  /**
   * Mô tả của banner
   */
  description?: ReactNode;

  /**
   * Nội dung của banner (sẽ thay thế title và description nếu được cung cấp)
   */
  children?: ReactNode;

  /**
   * URL hình ảnh nền
   */
  backgroundImage?: string;

  /**
   * Màu nền (sẽ được bỏ qua nếu backgroundImage được cung cấp)
   */
  backgroundColor?: string;

  /**
   * Đ<PERSON> mờ của overlay (0-100)
   */
  overlayOpacity?: number;

  /**
   * Màu của overlay
   */
  overlayColor?: string;

  /**
   * Variant của banner
   * @default 'primary'
   */
  variant?: BannerVariant;

  /**
   * Kích thước của banner
   * @default 'md'
   */
  size?: BannerSize;

  /**
   * Căn chỉnh nội dung
   * @default 'center'
   */
  alignment?: BannerAlignment;

  /**
   * Nút hành động chính
   */
  primaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };

  /**
   * Nút hành động phụ
   */
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'outline';
  };

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiệu ứng sóng (wave) ở dưới cùng
   */
  showWave?: boolean;

  /**
   * Màu của hiệu ứng sóng
   */
  waveColor?: string;

  /**
   * Border radius cho banner
   * @example 'rounded-t-xl' - Bo tròn 2 góc trên
   * @example 'rounded-xl' - Bo tròn tất cả các góc
   * @example 'rounded-b-xl' - Bo tròn 2 góc dưới
   */
  borderRadius?: string;
}

/**
 * Component Banner hiển thị nội dung nổi bật với nhiều tùy chọn
 *
 * @example
 * // Banner cơ bản
 * <Banner
 *   title="Chào mừng đến với RedAI"
 *   description="Nền tảng AI hiện đại cho doanh nghiệp của bạn"
 * />
 *
 * @example
 * // Banner với hình nền và hành động
 * <Banner
 *   title="Khám phá tính năng mới"
 *   description="Trải nghiệm sức mạnh của AI trong việc tạo nội dung"
 *   backgroundImage="/images/banner-bg.jpg"
 *   overlayOpacity={50}
 *   primaryAction={{
 *     label: "Bắt đầu ngay",
 *     onClick: () => navigate('/get-started')
 *   }}
 *   secondaryAction={{
 *     label: "Tìm hiểu thêm",
 *     onClick: () => navigate('/learn-more'),
 *     variant: "outline"
 *   }}
 * />
 */
const Banner: React.FC<BannerProps> = ({
  title,
  description,
  children,
  backgroundImage,
  backgroundColor,
  overlayOpacity = 50,
  overlayColor = '#000000',
  variant = 'primary',
  size = 'md',
  alignment = 'center',
  primaryAction,
  secondaryAction,
  className = '',
  showWave = false,
  waveColor,
  borderRadius,
}) => {
  // Sử dụng hook theme
  const { currentTheme } = useTheme();

  // Xác định chiều cao dựa trên kích thước
  const heightClasses = {
    sm: 'min-h-[150px]',
    md: 'min-h-[250px]',
    lg: 'min-h-[350px]',
    xl: 'min-h-[450px]',
    full: 'min-h-screen',
  };

  // Xác định padding dựa trên kích thước
  const paddingClasses = {
    sm: 'py-4 px-4',
    md: 'py-8 px-6',
    lg: 'py-12 px-8',
    xl: 'py-16 px-10',
    full: 'py-20 px-10',
  };

  // Xác định căn chỉnh nội dung
  const alignmentClasses = {
    left: 'text-left items-start',
    center: 'text-center items-center',
    right: 'text-right items-end',
  };

  // Xác định màu nền dựa trên variant
  const getBackgroundColor = () => {
    if (backgroundColor) return backgroundColor;

    if (variant === 'primary') return 'var(--color-primary)';
    if (variant === 'secondary') return 'var(--color-secondary)';
    if (variant === 'gradient') return 'transparent';

    return 'var(--color-background)';
  };

  // Xác định màu chữ dựa trên variant
  const getTextColor = () => {
    if (backgroundImage) return 'text-white';

    if (variant === 'primary') return 'text-primary-foreground';
    if (variant === 'secondary') return 'text-secondary-foreground';
    if (variant === 'gradient') return 'text-white';

    return 'text-foreground';
  };

  // Xác định gradient nếu variant là gradient
  const getGradientStyle = () => {
    if (variant !== 'gradient') return {};

    return {
      backgroundImage: `linear-gradient(to right, var(--color-primary), var(--color-secondary))`,
    };
  };

  // Xác định màu của wave
  const getWaveColor = () => {
    if (waveColor) return waveColor;

    if (currentTheme.mode === 'dark') {
      return 'var(--color-background)';
    }

    return 'var(--color-background)';
  };

  // Kết hợp tất cả các class
  const bannerClasses = [
    'relative overflow-hidden',
    heightClasses[size],
    paddingClasses[size],
    borderRadius || '',
    className,
  ].join(' ');

  // Kết hợp các class cho container nội dung
  const contentClasses = [
    'relative z-10 flex flex-col justify-center h-full',
    alignmentClasses[alignment],
    getTextColor(),
  ].join(' ');

  return (
    <div
      className={bannerClasses}
      style={{
        backgroundColor: getBackgroundColor(),
        ...getGradientStyle(),
      }}
    >
      {/* Background image */}
      {backgroundImage && (
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${backgroundImage})` }}
        />
      )}

      {/* Overlay */}
      {(backgroundImage || variant === 'gradient') && (
        <div
          className="absolute inset-0"
          style={{
            backgroundColor: overlayColor,
            opacity: overlayOpacity / 100,
          }}
        />
      )}

      {/* Content */}
      <div className={`container mx-auto ${contentClasses}`}>
        {children || (
          <>
            {title && (
              <Typography
                variant={size === 'sm' ? 'h3' : size === 'md' ? 'h2' : 'h1'}
                className="mb-4"
              >
                {title}
              </Typography>
            )}

            {description && (
              <Typography variant="body1" className="mb-6 max-w-2xl mx-auto">
                {description}
              </Typography>
            )}

            {(primaryAction || secondaryAction) && (
              <div className="flex flex-wrap gap-4 mt-2 justify-center">
                {primaryAction && (
                  <Button
                    variant={primaryAction.variant || 'primary'}
                    onClick={primaryAction.onClick}
                  >
                    {primaryAction.label}
                  </Button>
                )}

                {secondaryAction && (
                  <Button
                    variant={secondaryAction.variant || 'outline'}
                    onClick={secondaryAction.onClick}
                  >
                    {secondaryAction.label}
                  </Button>
                )}
              </div>
            )}
          </>
        )}
      </div>

      {/* Wave effect */}
      {showWave && (
        <div className="absolute bottom-0 left-0 w-full overflow-hidden">
          <svg
            className="relative block w-full h-[50px] md:h-[70px]"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <path
              d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
              fill={getWaveColor()}
              opacity=".8"
            />
            <path
              d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
              fill={getWaveColor()}
              opacity=".5"
            />
            <path
              d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
              fill={getWaveColor()}
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default Banner;
