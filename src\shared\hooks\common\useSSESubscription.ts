/**
 * Hook để subscribe vào specific SSE events
 */
import { useEffect, useCallback, useRef, useState } from 'react';
import { useSSE } from './useSSE';
import { SSEEvent, SSEEventPattern, UseSSEOptions } from '@/shared/types/sse.types';

/**
 * Options cho useSSESubscription
 */
export interface UseSSESubscriptionOptions extends UseSSEOptions {
  /**
   * Có tự động subscribe khi mount không
   */
  autoSubscribe?: boolean;

  /**
   * Số lượng events tối đa lưu trữ
   */
  maxEvents?: number;

  /**
   * Filter pattern cho events
   */
  filter?: SSEEventPattern;

  /**
   * Có log events không
   */
  logEvents?: boolean;
}

/**
 * Return type của useSSESubscription
 */
export interface UseSSESubscriptionReturn {
  /**
   * Danh sách events đã nhận cho event type này
   */
  events: SSEEvent[];

  /**
   * Event cuối cùng
   */
  lastEvent: SSEEvent | null;

  /**
   * <PERSON>ố lượng events đã nhận
   */
  eventCount: number;

  /**
   * C<PERSON> đang subscribe không
   */
  isSubscribed: boolean;

  /**
   * Subscribe vào event type
   */
  subscribe: () => void;

  /**
   * Unsubscribe khỏi event type
   */
  unsubscribe: () => void;

  /**
   * Clear events
   */
  clearEvents: () => void;

  /**
   * Thông tin kết nối SSE
   */
  connectionInfo: ReturnType<typeof useSSE>['connectionInfo'];

  /**
   * Kết nối SSE
   */
  connect: () => void;

  /**
   * Ngắt kết nối SSE
   */
  disconnect: () => void;
}

/**
 * Hook để subscribe vào specific SSE events
 */
export function useSSESubscription(
  url: string,
  eventType: string,
  handler?: (event: SSEEvent) => void,
  options: UseSSESubscriptionOptions = {}
): UseSSESubscriptionReturn {
  const {
    autoSubscribe = true,
    maxEvents = 100,
    filter,
    logEvents = false,
    ...sseOptions
  } = options;

  // State
  const [events, setEvents] = useState<SSEEvent[]>([]);
  const [lastEvent, setLastEvent] = useState<SSEEvent | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);

  // Refs
  const subscriptionIdRef = useRef<string | null>(null);
  const handlerRef = useRef(handler);

  // Cập nhật handler ref khi handler thay đổi
  useEffect(() => {
    handlerRef.current = handler;
  }, [handler]);

  // SSE hook
  const sse = useSSE(url, sseOptions);

  /**
   * Kiểm tra event có match với filter không
   */
  const matchesFilter = useCallback((event: SSEEvent): boolean => {
    if (!filter) return true;

    // Kiểm tra type pattern
    if (filter.type) {
      if (typeof filter.type === 'string') {
        if (event.type !== filter.type) return false;
      } else if (filter.type instanceof RegExp) {
        if (!filter.type.test(event.type)) return false;
      }
    }

    // Kiểm tra data pattern
    if (filter.data !== undefined) {
      if (typeof filter.data === 'object' && filter.data !== null) {
        // Deep comparison cho objects
        if (JSON.stringify(event.data) !== JSON.stringify(filter.data)) {
          return false;
        }
      } else {
        if (event.data !== filter.data) return false;
      }
    }

    // Kiểm tra custom filter
    if (filter.filter && !filter.filter(event)) {
      return false;
    }

    return true;
  }, [filter]);

  /**
   * Xử lý event từ SSE
   */
  const handleEvent = useCallback((event: SSEEvent) => {
    // Kiểm tra filter
    if (!matchesFilter(event)) {
      return;
    }

    // Log event nếu cần
    if (logEvents) {
      console.log(`SSE Event [${eventType}]:`, event);
    }

    // Cập nhật state
    setEvents(prev => {
      const newEvents = [...prev, event];
      // Giới hạn số lượng events
      if (newEvents.length > maxEvents) {
        return newEvents.slice(-maxEvents);
      }
      return newEvents;
    });

    setLastEvent(event);

    // Gọi handler nếu có
    if (handlerRef.current) {
      try {
        handlerRef.current(event);
      } catch (error) {
        console.error(`Error in SSE event handler for ${eventType}:`, error);
      }
    }
  }, [eventType, matchesFilter, logEvents, maxEvents]);

  /**
   * Subscribe vào event type
   */
  const subscribe = useCallback(() => {
    if (subscriptionIdRef.current) {
      // Đã subscribe rồi
      return;
    }

    subscriptionIdRef.current = sse.subscribe(eventType, handleEvent);
    setIsSubscribed(true);
  }, [sse, eventType, handleEvent]);

  /**
   * Unsubscribe khỏi event type
   */
  const unsubscribe = useCallback(() => {
    if (subscriptionIdRef.current) {
      sse.unsubscribe(subscriptionIdRef.current);
      subscriptionIdRef.current = null;
      setIsSubscribed(false);
    }
  }, [sse]);

  /**
   * Clear events
   */
  const clearEvents = useCallback(() => {
    setEvents([]);
    setLastEvent(null);
  }, []);

  // Effect để tự động subscribe
  useEffect(() => {
    if (autoSubscribe) {
      subscribe();
    }

    // Cleanup khi unmount
    return () => {
      unsubscribe();
    };
  }, [autoSubscribe, subscribe, unsubscribe]);

  // Effect để cleanup khi eventType thay đổi
  useEffect(() => {
    return () => {
      unsubscribe();
    };
  }, [eventType, unsubscribe]);

  return {
    events,
    lastEvent,
    eventCount: events.length,
    isSubscribed,
    subscribe,
    unsubscribe,
    clearEvents,
    connectionInfo: sse.connectionInfo,
    connect: sse.connect,
    disconnect: sse.disconnect,
  };
}

/**
 * Hook để subscribe vào multiple event types
 */
export function useSSEMultipleSubscriptions(
  url: string,
  eventTypes: string[],
  handler?: (eventType: string, event: SSEEvent) => void,
  options: UseSSESubscriptionOptions = {}
) {
  const [subscriptions, setSubscriptions] = useState<Map<string, UseSSESubscriptionReturn>>(new Map());

  // SSE hook
  const sse = useSSE(url, options);

  useEffect(() => {
    const newSubscriptions = new Map<string, UseSSESubscriptionReturn>();
    const subscriptionIds: string[] = [];

    eventTypes.forEach(eventType => {
      const subscriptionId = sse.subscribe(eventType, (event) => {
        if (handler) {
          handler(eventType, event);
        }
      });

      subscriptionIds.push(subscriptionId);

      // Tạo subscription object tương tự như useSSESubscription
      const subscription: UseSSESubscriptionReturn = {
        events: [], // Placeholder - trong thực tế cần track events cho từng eventType
        lastEvent: null,
        eventCount: 0,
        isSubscribed: true,
        subscribe: () => {}, // Placeholder - đã subscribe rồi
        unsubscribe: () => sse.unsubscribe(subscriptionId),
        clearEvents: () => {}, // Placeholder
        connectionInfo: sse.connectionInfo,
        connect: sse.connect,
        disconnect: sse.disconnect,
      };

      newSubscriptions.set(eventType, subscription);
    });

    setSubscriptions(newSubscriptions);

    // Cleanup
    return () => {
      subscriptionIds.forEach(id => {
        sse.unsubscribe(id);
      });
    };
  }, [url, eventTypes, handler, options, sse]);

  return {
    subscriptions,
    connectionInfo: sse.connectionInfo,
    connect: sse.connect,
    disconnect: sse.disconnect,
  };
}

export default useSSESubscription;
