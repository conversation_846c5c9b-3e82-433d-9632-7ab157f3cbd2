import React, { useState } from 'react';
import { Button, FormItem, Input, Select, Icon } from '@/shared/components/common';
import { ConvertField } from './ConvertFieldItem';
import { useTranslation } from 'react-i18next';

interface ConvertFieldFormProps {
  field?: ConvertField;
  onSave: (field: ConvertField) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/chỉnh sửa trường dữ liệu
 */
const ConvertFieldForm: React.FC<ConvertFieldFormProps> = ({
  field,
  onSave,
  onCancel
}) => {
  const { t } = useTranslation('aiAgents');
  const [name, setName] = useState(field?.name || '');
  const [description, setDescription] = useState(field?.description || '');
  const [type, setType] = useState<string>(field?.type || 'text');
  const [required, setRequired] = useState(field?.required || false);

  const isEditing = !!field;

  const handleSave = () => {
    if (!name || !description) {
      alert(t('convertConfig.pleaseEnterAllFields'));
      return;
    }

    const newField: ConvertField = {
      id: field?.id || `field-${Date.now()}`,
      name,
      description,
      enabled: field?.enabled ?? true,
      type,
      required
    };

    onSave(newField);
  };

  return (
    <div className="p-3 sm:p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-3 sm:mb-4">
        {isEditing ? t('convertConfig.editField') : t('convertConfig.addField')}
      </h3>

      <div className="space-y-3 sm:space-y-4">
        <FormItem label={t('convertConfig.fieldName')}>
          <Input
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder={t('convertConfig.fieldNamePlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem label={t('convertConfig.fieldDescription')}>
          <Input
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder={t('convertConfig.fieldDescriptionPlaceholder')}
            fullWidth
          />
        </FormItem>

        <FormItem label={t('convertConfig.fieldType')}>
          <Select
            value={type}
            onChange={(value) => {
              if (typeof value === 'string') {
                setType(value);
              }
            }}
            options={[
              { value: 'text', label: t('convertConfig.text') },
              { value: 'email', label: t('convertConfig.email') },
              { value: 'phone', label: t('convertConfig.phone') },
              { value: 'number', label: t('convertConfig.number') },
              { value: 'date', label: t('convertConfig.date') },
              { value: 'address', label: t('convertConfig.address') },
              { value: 'name', label: t('convertConfig.name') }
            ]}
            fullWidth
          />
        </FormItem>

        <div className="flex items-center">
          <div
            className={`w-5 h-5 rounded border ${required
              ? 'bg-primary border-primary'
              : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600'}
              flex items-center justify-center cursor-pointer`}
            onClick={() => setRequired(!required)}
          >
            {required && (
              <Icon name="check" size="sm" className="text-white" />
            )}
          </div>
          <label className="ml-2 text-sm text-gray-700 dark:text-gray-300 cursor-pointer" onClick={() => setRequired(!required)}>
            {t('convertConfig.fieldRequired')}
          </label>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row sm:justify-end mt-4 gap-2 sm:gap-0">
        <Button
          variant="outline"
          onClick={onCancel}
          className="sm:mr-2 order-2 sm:order-1"
        >
          {t('common.cancel')}
        </Button>
        <Button
          variant="primary"
          onClick={handleSave}
          disabled={!name || !description}
          className="order-1 sm:order-2"
        >
          {isEditing ? t('common.update') : t('common.add')}
        </Button>
      </div>
    </div>
  );
};

export default ConvertFieldForm;
