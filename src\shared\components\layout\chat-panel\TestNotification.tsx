import React from 'react';
import { Button } from '@/shared/components/common';
import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from './NotificationContainer';

const TestNotification: React.FC = () => {
  const { notifications, addNotification, removeNotification } = useNotification();

  const handleAddNotification = (type: 'success' | 'error' | 'warning' | 'info') => {
    const message = `This is a ${type} notification that will disappear in 5 seconds.`;
    const id = addNotification(type, message, 5000);
    console.log(`Added notification with ID: ${id}`);
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Test Notifications</h2>

      <div className="flex flex-wrap gap-2 mb-6">
        <Button onClick={() => handleAddNotification('success')} variant="primary">
          Success
        </Button>
        <Button onClick={() => handleAddNotification('error')} variant="danger">
          Error
        </Button>
        <Button onClick={() => handleAddNotification('warning')} variant="warning">
          Warning
        </Button>
        <Button onClick={() => handleAddNotification('info')} variant="secondary">
          Info
        </Button>
      </div>

      {notifications.length > 0 && (
        <div className="mb-4 w-full max-w-full">
          <h3 className="text-lg font-semibold mb-2">
            Current Notifications ({notifications.length})
          </h3>
          <NotificationContainer notifications={notifications} onRemove={removeNotification} />
        </div>
      )}
    </div>
  );
};

export default TestNotification;
