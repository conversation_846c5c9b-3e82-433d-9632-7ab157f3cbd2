# Hướng dẫn sử dụng Form Components

## 1. Giới thiệu

Tài liệu này cung cấp hướng dẫn chi tiết về cách sử dụng các form components trong dự án RedAI. Các components này được xây dựng dựa trên React Hook Form và Zod, với nhiều tính năng nâng cao để cải thiện trải nghiệm người dùng và developer experience.

## 2. Form Component

`Form` là component chính để quản lý form, tích hợp React Hook Form và Zod.

### 2.1. <PERSON><PERSON><PERSON> sử dụng cơ bản

```tsx
import { Form, FormItem, Input, Button } from '@/shared/components/common';
import { z } from 'zod';
import { useRef } from 'react';
import { FormRef } from '@/shared/components/common/Form/Form';

// Định nghĩa schema validation với Zod
const schema = z.object({
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  email: z.string().email('Email không hợp lệ'),
});

// Định nghĩa kiểu dữ liệu từ schema
type FormValues = z.infer<typeof schema>;

const MyForm = () => {
  // Tạo ref để tương tác với form từ bên ngoài
  const formRef = useRef<FormRef<FormValues>>(null);

  // Xử lý submit form
  const handleSubmit = (values: FormValues) => {
    console.log('Form values:', values);
  };

  // Xử lý lỗi từ API
  const handleApiError = (apiError: { field: string; message: string }) => {
    if (apiError.field && formRef.current) {
      formRef.current.setErrors({
        [apiError.field]: apiError.message,
      });
    }
  };

  return (
    <Form
      ref={formRef}
      schema={schema}
      onSubmit={handleSubmit}
      defaultValues={{ name: '', email: '' }}
    >
      <FormItem name="name" label="Tên">
        <Input />
      </FormItem>
      <FormItem name="email" label="Email">
        <Input type="email" />
      </FormItem>
      <Button type="submit">Gửi</Button>
    </Form>
  );
};
```

### 2.2. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `children` | ReactNode | - | Nội dung của form |
| `schema` | z.ZodType | - | Schema validation của Zod |
| `defaultValues` | Object | - | Giá trị mặc định của form |
| `onSubmit` | Function | - | Callback khi submit form thành công |
| `onError` | Function | - | Callback khi form có lỗi |
| `id` | string | - | ID của form |
| `className` | string | '' | Class bổ sung |
| `mode` | 'onSubmit' \| 'onChange' \| 'onBlur' \| 'onTouched' \| 'all' | 'onSubmit' | Mode validation của form |
| `shouldFocusError` | boolean | true | Tự động focus vào field lỗi đầu tiên |
| `autoComplete` | string | - | Thuộc tính autoComplete của form |
| `validateOnBlur` | boolean | false | Validate khi blur field |
| `validateOnChange` | boolean | false | Validate khi change field |
| `resetOnSubmitSuccess` | boolean | false | Reset form sau khi submit thành công |
| `confirmOnDirty` | boolean | false | Hiển thị confirm dialog khi user navigate away với unsaved changes |
| `scrollToError` | boolean | false | Tự động scroll đến field lỗi đầu tiên |
| `focusOnError` | boolean | true | Tự động focus vào field lỗi đầu tiên |
| `submitOnEnter` | boolean | true | Submit form khi nhấn Enter |
| `disabled` | boolean | false | Disable toàn bộ form |
| `loading` | boolean | false | Hiển thị loading state |
| `successMessage` | string | - | Thông báo thành công sau khi submit |
| `errorMessage` | string | - | Thông báo lỗi sau khi submit |

### 2.3. FormRef

`FormRef` cung cấp các methods để tương tác với form từ bên ngoài.

| Method | Mô tả |
|--------|-------|
| `setErrors` | Set lỗi cho các field từ bên ngoài (ví dụ: từ API) |
| `reset` | Reset form về giá trị mặc định |
| `getFormMethods` | Lấy form methods từ React Hook Form |

## 3. FormItem Component

`FormItem` là component bọc các input field với label, error message, và help text.

### 3.1. Cách sử dụng cơ bản

```tsx
<FormItem
  name="email"
  label="Email"
  required
  helpText="Chúng tôi sẽ không chia sẻ email của bạn với bất kỳ ai"
>
  <Input type="email" />
</FormItem>
```

### 3.2. Cách sử dụng nâng cao

```tsx
<FormItem
  name="email"
  label="Email"
  required
  tooltip="Nhập địa chỉ email của bạn"
  description="Chúng tôi sẽ không chia sẻ email của bạn với bất kỳ ai"
  prefix={<Icon name="mail" />}
  suffix={<Icon name="info" />}
  labelPosition="left"
  errorAnimation="fadeIn"
  successMessage="Email hợp lệ"
  validateStatus="success"
  size="md"
  colon
  asterisk
>
  <Input type="email" />
</FormItem>
```

### 3.3. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `name` | string | - | Tên của field |
| `label` | ReactNode | - | Label của field |
| `children` | ReactNode | - | Input field |
| `helpText` | ReactNode | - | Text trợ giúp hiển thị dưới field |
| `required` | boolean | false | Đánh dấu field là bắt buộc |
| `className` | string | '' | Class bổ sung |
| `labelClassName` | string | '' | Class bổ sung cho label |
| `errorClassName` | string | '' | Class bổ sung cho error message |
| `helpTextClassName` | string | '' | Class bổ sung cho help text |
| `inline` | boolean | false | Hiển thị label và field trên cùng một dòng |
| `hideError` | boolean | false | Ẩn error message |
| `tooltip` | ReactNode | - | Tooltip cho label |
| `description` | ReactNode | - | Mô tả cho field |
| `prefix` | ReactNode | - | Nội dung hiển thị trước input |
| `suffix` | ReactNode | - | Nội dung hiển thị sau input |
| `labelPosition` | 'top' \| 'left' \| 'right' | 'top' | Vị trí của label |
| `errorAnimation` | 'none' \| 'fadeIn' \| 'slideDown' | 'none' | Animation khi hiển thị lỗi |
| `successMessage` | ReactNode | - | Thông báo thành công |
| `validateStatus` | 'success' \| 'warning' \| 'error' \| 'validating' | - | Trạng thái validation |
| `size` | 'sm' \| 'md' \| 'lg' | 'md' | Kích thước của field |
| `colon` | boolean | false | Hiển thị dấu hai chấm sau label |
| `asterisk` | boolean | false | Hiển thị dấu sao cho field bắt buộc |

## 4. Layout Components

### 4.1. FormGrid

`FormGrid` tạo grid layout cho form.

```tsx
<FormGrid
  columns={2}
  columnsMd={2}
  columnsSm={1}
  gap="md"
  rowGap="md"
  responsive
>
  <FormItem name="firstName" label="Họ">
    <Input />
  </FormItem>
  <FormItem name="lastName" label="Tên">
    <Input />
  </FormItem>
  <FormItem name="email" label="Email" className="col-span-2">
    <Input type="email" />
  </FormItem>
</FormGrid>
```

#### 4.1.1. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `children` | ReactNode | - | Nội dung của grid |
| `columns` | number | 2 | Số cột của grid |
| `columnsMd` | number | 2 | Số cột của grid trên màn hình medium |
| `columnsSm` | number | 1 | Số cột của grid trên màn hình small |
| `gap` | 'none' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' | 'md' | Khoảng cách giữa các items |
| `rowGap` | 'none' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' | - | Khoảng cách giữa các hàng |
| `className` | string | '' | Class bổ sung |
| `responsive` | boolean | false | Tự động điều chỉnh số cột theo kích thước màn hình |

### 4.2. FormHorizontal

`FormHorizontal` tạo horizontal layout cho form (label bên trái, field bên phải).

```tsx
<FormHorizontal
  labelWidth="md"
  fieldWidth="full"
  gap="md"
  rowGap="md"
  responsive
  labelAlign="right"
  wrapperAlign="left"
  colon
  labelWrap
>
  <FormItem name="name" label="Họ tên">
    <Input />
  </FormItem>
  <FormItem name="email" label="Email">
    <Input type="email" />
  </FormItem>
</FormHorizontal>
```

#### 4.2.1. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `children` | ReactNode | - | Nội dung của form |
| `labelWidth` | 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| 'auto' \| 'full' | 'md' | Chiều rộng của label |
| `fieldWidth` | 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' \| 'auto' \| 'full' | 'full' | Chiều rộng của field |
| `gap` | 'none' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' | 'md' | Khoảng cách giữa label và field |
| `rowGap` | 'none' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' | 'md' | Khoảng cách giữa các hàng |
| `className` | string | '' | Class bổ sung |
| `labelClassName` | string | '' | Class bổ sung cho label |
| `fieldClassName` | string | '' | Class bổ sung cho field |
| `responsive` | boolean | false | Tự động chuyển sang layout dọc trên mobile |
| `labelAlign` | 'left' \| 'right' \| 'center' | 'left' | Alignment của label |
| `wrapperAlign` | 'left' \| 'right' \| 'center' | 'left' | Alignment của wrapper |
| `colon` | boolean | false | Hiển thị dấu hai chấm sau label |
| `labelWrap` | boolean | false | Cho phép label wrap xuống dòng |

### 4.3. FormInline

`FormInline` tạo inline layout cho form.

```tsx
<FormInline
  gap="md"
  align="center"
  wrap
  responsive
  labelInline
>
  <FormItem name="name" label="Họ tên">
    <Input />
  </FormItem>
  <FormItem name="email" label="Email">
    <Input type="email" />
  </FormItem>
  <Button type="submit">Gửi</Button>
</FormInline>
```

#### 4.3.1. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `children` | ReactNode | - | Nội dung của form |
| `gap` | 'none' \| 'xs' \| 'sm' \| 'md' \| 'lg' \| 'xl' | 'md' | Khoảng cách giữa các items |
| `className` | string | '' | Class bổ sung |
| `align` | 'start' \| 'center' \| 'end' \| 'baseline' \| 'stretch' | 'center' | Alignment của các items |
| `wrap` | boolean | true | Cho phép items wrap xuống dòng |
| `responsive` | boolean | false | Tự động chuyển sang layout dọc trên mobile |
| `labelInline` | boolean | false | Hiển thị label inline với input |

## 5. FormArray Component

`FormArray` quản lý mảng các field trong form.

### 5.1. Cách sử dụng cơ bản

```tsx
<FormArray
  name="addresses"
  title="Địa chỉ"
  description="Thêm các địa chỉ của bạn"
  renderItem={(index, field, remove) => (
    <div>
      <FormItem name={`addresses.${index}.street`} label="Đường">
        <Input />
      </FormItem>
      <FormItem name={`addresses.${index}.city`} label="Thành phố">
        <Input />
      </FormItem>
    </div>
  )}
  defaultValue={{ street: '', city: '' }}
/>
```

### 5.2. Cách sử dụng với drag-and-drop

```tsx
<FormArray
  name="addresses"
  title="Địa chỉ"
  description="Thêm các địa chỉ của bạn"
  sortable
  sortableHandle
  sortableAnimation="fade"
  sortableAxis="vertical"
  renderItem={(index, field, remove) => (
    <div>
      <FormItem name={`addresses.${index}.street`} label="Đường">
        <Input />
      </FormItem>
      <FormItem name={`addresses.${index}.city`} label="Thành phố">
        <Input />
      </FormItem>
    </div>
  )}
  defaultValue={{ street: '', city: '' }}
/>
```

### 5.3. Props

| Prop | Kiểu dữ liệu | Mặc định | Mô tả |
|------|--------------|----------|-------|
| `name` | string | - | Tên của field array |
| `renderItem` | Function | - | Render function cho mỗi item |
| `defaultValue` | Object | {} | Giá trị mặc định cho item mới |
| `title` | ReactNode | - | Tiêu đề của form array |
| `description` | ReactNode | - | Mô tả của form array |
| `addButtonText` | string | 'Add Item' | Text của nút thêm item |
| `controls` | 'all' \| 'add' \| 'remove' \| 'none' | 'all' | Hiển thị các controls |
| `controlsPosition` | 'top' \| 'bottom' \| 'both' | 'bottom' | Vị trí của controls |
| `buttonSize` | 'sm' \| 'md' \| 'lg' | 'md' | Kích thước của button |
| `minItems` | number | 0 | Số lượng items tối thiểu |
| `maxItems` | number | Infinity | Số lượng items tối đa |
| `className` | string | '' | Class bổ sung |
| `sortable` | boolean | false | Bật/tắt tính năng drag-and-drop |
| `sortableHandle` | boolean | false | Sử dụng handle cho drag-and-drop |
| `sortableAnimation` | 'none' \| 'fade' \| 'slide' | 'none' | Animation khi drag-and-drop |
| `sortableAxis` | 'vertical' \| 'horizontal' | 'vertical' | Trục drag-and-drop |
| `onSortEnd` | Function | - | Callback khi sort kết thúc |
| `virtualized` | boolean | false | Sử dụng virtualization cho arrays lớn |
| `itemClassName` | string | '' | Class bổ sung cho mỗi item |
| `itemStyle` | Object | - | Style bổ sung cho mỗi item |
| `dragHandleClassName` | string | '' | Class bổ sung cho drag handle |
| `dragHandleStyle` | Object | - | Style bổ sung cho drag handle |

## 6. Hooks

### 6.1. useFormErrors

`useFormErrors` là hook để xử lý lỗi form từ API một cách dễ dàng.

```tsx
import { useFormErrors } from '@/shared/hooks';

const MyForm = () => {
  const { formRef, setFormErrors, resetForm } = useFormErrors<LoginFormValues>();

  const handleSubmit = async (values) => {
    try {
      const result = await api.login(values);
      if (!result.success && result.errors) {
        setFormErrors(result.errors);
      }
    } catch (error) {
      // Xử lý lỗi
    }
  };

  return (
    <Form ref={formRef} onSubmit={handleSubmit}>
      {/* Form fields */}
    </Form>
  );
};
```

### 6.2. useApiForm

`useApiForm` là hook để xử lý form với API một cách đầy đủ.

```tsx
import { useApiForm } from '@/shared/hooks';

const MyForm = () => {
  const { formRef, handleSubmit, isSubmitting, isSuccess, error } = useApiForm<LoginFormValues>({
    apiCall: api.login,
    onSuccess: (data) => {
      // Xử lý khi đăng nhập thành công
      navigate('/dashboard');
    },
    resetOnSuccess: true
  });

  return (
    <Form ref={formRef} onSubmit={handleSubmit}>
      {/* Form fields */}
    </Form>
  );
};
```

## 7. Best Practices

### 7.1. Sử dụng Zod schema

```tsx
// Định nghĩa schema validation với Zod
const schema = z.object({
  name: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  password: z
    .string()
    .min(8, 'Mật khẩu phải có ít nhất 8 ký tự')
    .regex(/[A-Z]/, 'Mật khẩu phải chứa ít nhất một chữ hoa')
    .regex(/[a-z]/, 'Mật khẩu phải chứa ít nhất một chữ thường')
    .regex(/[0-9]/, 'Mật khẩu phải chứa ít nhất một số'),
  confirmPassword: z.string().min(1, 'Vui lòng xác nhận mật khẩu'),
});

// Thêm validation phụ thuộc giữa các fields
const schemaWithDependencies = schema.refine(
  (data) => data.password === data.confirmPassword,
  {
    message: 'Mật khẩu xác nhận không khớp',
    path: ['confirmPassword'],
  }
);
```

### 7.2. Sử dụng FormRef

```tsx
const formRef = useRef<FormRef<FormValues>>(null);

// Reset form
const handleReset = () => {
  formRef.current?.reset();
};

// Set errors từ API
const handleApiError = (apiError) => {
  if (apiError.field) {
    formRef.current?.setErrors({
      [apiError.field]: apiError.message,
    });
  }
};

// Lấy form methods
const handleGetFormValues = () => {
  const formMethods = formRef.current?.getFormMethods();
  const values = formMethods?.getValues();
  console.log('Current form values:', values);
};
```

### 7.3. Sử dụng FormArray

```tsx
// Sử dụng FormArray với validation
const schema = z.object({
  addresses: z.array(
    z.object({
      street: z.string().min(1, 'Đường là bắt buộc'),
      city: z.string().min(1, 'Thành phố là bắt buộc'),
    })
  ),
});

// Trong component
<FormArray
  name="addresses"
  title="Địa chỉ"
  description="Thêm các địa chỉ của bạn"
  renderItem={(index, field, remove) => (
    <div>
      <FormItem name={`addresses.${index}.street`} label="Đường">
        <Input />
      </FormItem>
      <FormItem name={`addresses.${index}.city`} label="Thành phố">
        <Input />
      </FormItem>
    </div>
  )}
  defaultValue={{ street: '', city: '' }}
/>
```
