import React from 'react';
import { Icon } from '@/shared/components/common';
import TableRow from './TableRow';
import TableCell from './TableCell';

interface TableExpandableRowProps<T = unknown> {
  /**
   * D<PERSON> liệu của hàng
   */
  record: T;

  /**
   * Chỉ số của hàng
   */
  index: number;

  /**
   * Số lượng cột
   */
  colSpan: number;

  /**
   * Đã mở rộng hay chưa
   */
  expanded: boolean;

  /**
   * Hàm render nội dung mở rộng
   */
  expandedRowRender: (record: T, index: number) => React.ReactNode | undefined;

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Component hàng mở rộng
 */
function TableExpandableRow<T>({
  record,
  index,
  colSpan,
  expanded,
  expandedRowRender,
  className = '',
}: TableExpandableRowProps<T>) {
  // Kết hợp tất cả các lớp
  const rowClasses = ['bg-gray-50 dark:bg-gray-800/50', className].join(' ');

  // Nếu không mở rộng, không hiển thị gì cả
  if (!expanded) {
    return null;
  }

  return (
    <TableRow className={rowClasses}>
      <TableCell colSpan={colSpan} className="p-0">
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          {expandedRowRender(record, index)}
        </div>
      </TableCell>
    </TableRow>
  );
}

interface ExpandButtonProps {
  /**
   * Đã mở rộng hay chưa
   */
  expanded: boolean;

  /**
   * Callback khi thay đổi
   */
  onChange: () => void;

  /**
   * Đã vô hiệu hóa hay chưa
   */
  disabled?: boolean;

  /**
   * Class tùy chỉnh
   */
  className?: string;
}

/**
 * Component nút mở rộng
 */
export const ExpandButton: React.FC<ExpandButtonProps> = ({
  expanded,
  onChange,
  disabled = false,
  className = '',
}) => {
  // Xử lý sự kiện click
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!disabled) {
      onChange();
    }
  };

  // Kết hợp tất cả các lớp
  const buttonClasses = [
    'p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50',
    disabled
      ? 'text-gray-400 cursor-not-allowed'
      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 cursor-pointer',
    className,
  ].join(' ');

  return (
    <button
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled}
      aria-label={expanded ? 'Thu gọn' : 'Mở rộng'}
      type="button"
    >
      <Icon name={expanded ? 'chevron-down' : 'chevron-right'} size="sm" />
    </button>
  );
};

export default TableExpandableRow;
