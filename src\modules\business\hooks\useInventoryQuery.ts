import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { InventoryService } from '../services/inventory.service';
import {
  InventoryQueryParams,
  UpdateInventoryQuantityDto,
  CreateInventoryDto,
  WarehouseProductsQueryParams,
} from '../types/inventory.types';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Interface cho API Error Response
 */
interface ApiErrorResponse {
  response?: {
    data?: {
      message?: string;
      code?: number;
    };
  };
  message?: string;
}

// Query keys
export const INVENTORY_QUERY_KEYS = {
  all: ['inventory'] as const,
  lists: () => [...INVENTORY_QUERY_KEYS.all, 'list'] as const,
  list: (params: InventoryQueryParams) => [...INVENTORY_QUERY_KEYS.lists(), params] as const,
  details: () => [...INVENTORY_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...INVENTORY_QUERY_KEYS.details(), id] as const,
  warehouse: (warehouseId: number) => [...INVENTORY_QUERY_KEYS.all, 'warehouse', warehouseId] as const,
  warehouseProducts: (params: WarehouseProductsQueryParams) => [...INVENTORY_QUERY_KEYS.all, 'warehouse-products', params] as const,
};

/**
 * Hook để lấy danh sách inventory items
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useInventoryItems = (params?: InventoryQueryParams) => {
  const defaultParams: InventoryQueryParams = { page: 1, limit: 10 };
  const queryParams = params || defaultParams;

  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.list(queryParams),
    queryFn: () => InventoryService.getInventoryItems(params),
    select: (data) => data.result,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để lấy danh sách inventory items theo warehouse
 * @param warehouseId ID của kho
 * @param params Tham số truy vấn bổ sung
 * @returns Query object
 */
export const useInventoryItemsByWarehouse = (
  warehouseId: number,
  params?: Omit<InventoryQueryParams, 'warehouseId'>
) => {
  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.warehouse(warehouseId),
    queryFn: () => InventoryService.getInventoryItemsByWarehouse(warehouseId, params),
    select: (data) => data.result,
    enabled: !!warehouseId,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để lấy chi tiết inventory item
 * @param id ID của inventory item
 * @returns Query object
 */
export const useInventoryItem = (id: number) => {
  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.detail(id),
    queryFn: () => InventoryService.getInventoryItemById(id),
    select: (data) => data.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để cập nhật số lượng inventory
 * @returns Mutation object
 */
export const useUpdateInventoryQuantity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateInventoryQuantityDto }) =>
      InventoryService.updateInventoryQuantity(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.detail(variables.id) });

      // Invalidate warehouse products queries if warehouseId is provided
      if (variables.data.warehouseId) {
        queryClient.invalidateQueries({
          queryKey: ['inventory', 'warehouseProducts'],
          predicate: (query) => {
            const queryKey = query.queryKey as unknown[];
            return queryKey.includes('warehouseProducts');
          }
        });
      }

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Cập nhật số lượng thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật số lượng';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để tạo mới inventory item
 * @returns Mutation object
 */
export const useCreateInventoryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInventoryDto) => InventoryService.createInventoryItem(data),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Tạo mới inventory thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo mới inventory';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để xóa inventory item
 * @returns Mutation object
 */
export const useDeleteInventoryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => InventoryService.deleteInventoryItem(id),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Xóa inventory thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa inventory';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để xóa nhiều inventory items
 * @returns Mutation object
 */
export const useBulkDeleteInventories = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (inventoryIds: number[]) => InventoryService.bulkDeleteInventories(inventoryIds),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công - sẽ được handle bởi component
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi - sẽ được handle bởi component
      const message = error?.response?.data?.message;
      throw new Error(message || 'Error deleting inventory items');
    },
  });
};

/**
 * Hook để cập nhật số lượng hàng loạt
 * @returns Mutation object
 */
export const useBulkUpdateQuantities = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: Array<{ id: number; data: UpdateInventoryQuantityDto }>) =>
      InventoryService.bulkUpdateQuantities(updates),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Cập nhật số lượng hàng loạt thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật số lượng hàng loạt';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để lấy danh sách sản phẩm trong warehouse với thông tin chi tiết
 * @param params Tham số truy vấn bao gồm warehouseId
 * @returns Query object
 */
export const useWarehouseProducts = (params: WarehouseProductsQueryParams) => {
  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.warehouseProducts(params),
    queryFn: () => InventoryService.getWarehouseProducts(params),
    select: (data) => data.result,
    enabled: !!params.warehouseId,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để xóa sản phẩm khỏi warehouse
 * @returns Mutation object
 */
export const useRemoveProductFromWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, productId }: { warehouseId: number; productId: number }) =>
      InventoryService.removeProductFromWarehouse(warehouseId, productId),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Xóa sản phẩm khỏi kho thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa sản phẩm khỏi kho';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để xóa nhiều sản phẩm khỏi warehouse
 * @returns Mutation object
 */
export const useRemoveMultipleProductsFromWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ warehouseId, productIds }: { warehouseId: number; productIds: number[] }) =>
      InventoryService.removeMultipleProductsFromWarehouse(warehouseId, productIds),
    onSuccess: (_, { productIds }) => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: `Xóa ${productIds.length} sản phẩm khỏi kho thành công`
      });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa sản phẩm khỏi kho';
      NotificationUtil.error({ message });
    },
  });
};
