import React from 'react';

export interface BadgeProps {
  /**
   * Nội dung hiển thị trong badge
   */
  children?: React.ReactNode;

  /**
   * Variant của badge
   */
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';

  /**
   * <PERSON><PERSON>ch thước của badge
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Hiển thị dưới dạng dot (chỉ hiển thị một chấm tròn)
   */
  dot?: boolean;

  /**
   * Hiển thị với góc bo tròn hoàn toàn
   */
  rounded?: boolean;

  /**
   * Vị trí của badge khi được sử dụng như một overlay
   */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';

  /**
   * Giá trị số để hiển thị (nếu > max, hiển thị max+)
   */
  count?: number;

  /**
   * Gi<PERSON> trị tối đa để hiển thị, nếu count > max thì hiển thị max+
   */
  max?: number;

  /**
   * Hiển thị badge ngay cả khi count = 0
   */
  showZero?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Style bổ sung
   */
  style?: React.CSSProperties;
}

/**
 * Badge component hiển thị các chỉ báo nhỏ như số thông báo, trạng thái, v.v.
 *
 * @example
 * // Badge cơ bản
 * <Badge>99</Badge>
 *
 * @example
 * // Badge dạng dot
 * <Badge dot variant="danger" />
 *
 * @example
 * // Badge với số lượng
 * <Badge count={99} max={99} />
 */
const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  dot = false,
  rounded = false,
  position = 'top-right',
  count,
  max = 99,
  showZero = false,
  className = '',
  style,
}) => {
  // Xác định nội dung hiển thị
  const hasCount = count !== undefined;
  const showBadge = hasCount ? count > 0 || showZero : true;
  const displayCount = hasCount && max !== undefined && count > max ? `${max}+` : count;

  // Variant classes
  const variantClasses = {
    primary: 'bg-primary text-white',
    secondary: 'bg-muted text-muted-foreground',
    success: 'bg-green-500 text-white',
    warning: 'bg-yellow-500 text-white',
    danger: 'bg-red-500 text-white',
    info: 'bg-blue-500 text-white',
  };

  // Size classes
  const sizeClasses = {
    sm: dot ? 'w-1.5 h-1.5' : 'text-xs px-1.5 py-0.5 min-w-[18px] h-[18px]',
    md: dot ? 'w-2 h-2' : 'text-xs px-2 py-0.5 min-w-[20px] h-[20px]',
    lg: dot ? 'w-2.5 h-2.5' : 'text-sm px-2.5 py-0.5 min-w-[22px] h-[22px]',
  };

  // Position classes (for standalone badge)
  const positionClasses = {
    'top-right': 'top-0 right-0 -translate-y-1/2 translate-x-1/2',
    'top-left': 'top-0 left-0 -translate-y-1/2 -translate-x-1/2',
    'bottom-right': 'bottom-0 right-0 translate-y-1/2 translate-x-1/2',
    'bottom-left': 'bottom-0 left-0 translate-y-1/2 -translate-x-1/2',
  };

  // Shape classes
  const shapeClass = rounded || dot ? 'rounded-full' : 'rounded';

  // Combine all classes for the badge
  const badgeClasses = [
    variantClasses[variant],
    sizeClasses[size],
    shapeClass,
    'inline-flex items-center justify-center font-medium',
    className,
  ].join(' ');

  // Standalone badge (không có children)
  if (!children) {
    if (!showBadge) return null;

    return (
      <span className={badgeClasses} style={style}>
        {!dot && displayCount}
      </span>
    );
  }

  // Badge with children (overlay)
  return (
    <div className="relative inline-flex">
      {children}

      {showBadge && (
        <span className={`absolute ${positionClasses[position]} ${badgeClasses}`} style={style}>
          {!dot && displayCount}
        </span>
      )}
    </div>
  );
};

export default Badge;
