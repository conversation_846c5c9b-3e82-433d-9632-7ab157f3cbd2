/**
 * Service for payment API
 */

import { apiClient } from '@/shared/api';
import {
  PaymentDto,
  PaymentResponse,
} from './marketplace-api.service';

/**
 * Base URL for payment API
 */
const BASE_URL = '/user/marketplace/payment';

/**
 * Payment service
 */
export const PaymentService = {
  /**
   * Process payment
   */
  processPayment: async (data: PaymentDto): Promise<PaymentResponse> => {
    try {
      const response = await apiClient.post<PaymentResponse>(BASE_URL, data);
      return response.result;
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  },
};
