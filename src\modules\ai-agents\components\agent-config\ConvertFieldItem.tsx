import React from 'react';
import { But<PERSON>, Icon } from '@/shared/components/common';

// Interface cho một trường dữ liệu
export interface ConvertField {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  required?: boolean;
  type?: string;
}

interface ConvertFieldItemProps {
  field: ConvertField;
  onToggle: (id: string) => void;
  onEdit: () => void;
  onDelete: (id: string) => void;
}

/**
 * Component hiển thị một trường dữ liệu trong ConvertConfig
 */
const ConvertFieldItem: React.FC<ConvertFieldItemProps> = ({
  field,
  onToggle,
  onEdit,
  onDelete
}) => {
  return (
    <div className={`flex flex-wrap sm:flex-nowrap items-start p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 ${field.enabled ? '' : 'opacity-70'}`}>
      {/* Checkbox */}
      <div className="flex-shrink-0 mt-0.5">
        <div
          className={`w-5 h-5 rounded border ${field.enabled
            ? 'bg-primary border-primary'
            : 'bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600'}
            flex items-center justify-center cursor-pointer`}
          onClick={() => onToggle(field.id)}
        >
          {field.enabled && (
            <Icon name="check" size="sm" className="text-white" />
          )}
        </div>
      </div>

      {/* Thông tin trường */}
      <div className="flex-1 min-w-0 ml-3 mb-2 sm:mb-0">
        <div className="flex flex-wrap items-center">
          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 mr-2">
            {field.name}
            {field.required && (
              <span className="text-red-500 ml-1">*</span>
            )}
          </h4>
          {field.type && (
            <span className="mt-1 sm:mt-0 px-2 py-0.5 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 rounded-full">
              {field.type}
            </span>
          )}
        </div>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          {field.description}
        </p>
      </div>

      {/* Nút chỉnh sửa và xóa */}
      <div className="flex space-x-2 w-full sm:w-auto justify-end sm:ml-2">
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
          onClick={onEdit}
          aria-label="Chỉnh sửa trường"
        >
          <Icon name="edit" size="sm" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-red-500 dark:hover:text-red-400"
          onClick={() => onDelete(field.id)}
          aria-label="Xóa trường"
        >
          <Icon name="trash" size="sm" />
        </Button>
      </div>
    </div>
  );
};

export default ConvertFieldItem;
