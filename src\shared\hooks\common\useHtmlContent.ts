/**
 * Hook để lấy nội dung HTML từ URL CDN
 */
import { useState, useEffect, useCallback, useMemo } from 'react';
import axios, { AxiosRequestConfig } from 'axios';

/**
 * Trạng thái của quá trình lấy nội dung HTML
 */
export enum HtmlContentStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * Kết quả trả về từ hook useHtmlContent
 */
export interface UseHtmlContentResult {
  /**
   * Nội dung HTML đã lấy được
   */
  content: string;

  /**
   * Trạng thái của quá trình lấy nội dung
   */
  status: HtmlContentStatus;

  /**
   * Lỗi nếu có
   */
  error: Error | null;

  /**
   * Hàm để lấy lại nội dung
   */
  refetch: () => Promise<void>;

  /**
   * Hàm để lấy nội dung từ một URL khác
   */
  fetchFromUrl: (url: string) => Promise<void>;

  /**
   * Kiểm tra xem nội dung đã lấy được có phải là HTML hợp lệ không
   */
  isValidHtml: boolean;

  /**
   * Độ dài của nội dung đã lấy được
   */
  contentLength: number;
}

/**
 * Tùy chọn cho hook useHtmlContent
 */
export interface UseHtmlContentOptions {
  /**
   * Có tự động lấy nội dung khi hook được gọi không
   * @default true
   */
  autoFetch?: boolean;

  /**
   * Cấu hình request Axios
   */
  axiosConfig?: AxiosRequestConfig;

  /**
   * Hàm xử lý nội dung trước khi trả về
   * @param content Nội dung HTML gốc
   * @returns Nội dung HTML đã xử lý
   */
  transformContent?: (content: string) => string;
}

/**
 * Hook để lấy nội dung HTML từ URL CDN
 *
 * @param initialUrl URL ban đầu để lấy nội dung HTML
 * @param options Tùy chọn cho hook
 * @returns Kết quả bao gồm nội dung HTML và các hàm tiện ích
 *
 * @example
 * // Sử dụng cơ bản
 * const { content, status, error } = useHtmlContent('https://cdn.redai.vn/blogs/DOCUMENT/content-29/1747469099105-5ce91453-9f5b-4a67-a6a7-82d10c367ac1');
 *
 * @example
 * // Sử dụng với tùy chọn
 * const { content, status, error, refetch, fetchFromUrl } = useHtmlContent(
 *   'https://cdn.redai.vn/blogs/DOCUMENT/content-29/1747469099105-5ce91453-9f5b-4a67-a6a7-82d10c367ac1',
 *   {
 *     autoFetch: false,
 *     transformContent: (html) => html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, ''),
 *     axiosConfig: { headers: { 'Cache-Control': 'no-cache' } }
 *   }
 * );
 */
export function useHtmlContent(
  initialUrl: string,
  options: UseHtmlContentOptions = {}
): UseHtmlContentResult {
  const {
    autoFetch = true,
    axiosConfig = {},
    transformContent = (content) => content
  } = options;

  // State để lưu trữ nội dung HTML
  const [content, setContent] = useState<string>('');

  // State để lưu trữ trạng thái
  const [status, setStatus] = useState<HtmlContentStatus>(HtmlContentStatus.IDLE);

  // State để lưu trữ lỗi
  const [error, setError] = useState<Error | null>(null);

  // State để lưu trữ URL hiện tại
  const [url, setUrl] = useState<string>(initialUrl);

  /**
   * Hàm để lấy nội dung HTML từ URL
   */
  const fetchContent = useCallback(async (targetUrl: string): Promise<void> => {
    // Kiểm tra URL hợp lệ
    if (!targetUrl || !targetUrl.startsWith('http')) {
      setStatus(HtmlContentStatus.ERROR);
      setError(new Error('Invalid URL. URL must start with http:// or https://'));
      return;
    }

    try {
      setStatus(HtmlContentStatus.LOADING);
      setError(null);

      // Cấu hình mặc định cho request
      const defaultConfig: AxiosRequestConfig = {
        responseType: 'text',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Cache-Control': 'no-cache',
        },
        timeout: 30000, // 30 giây timeout
      };

      // Gửi request để lấy nội dung HTML
      const response = await axios.get(targetUrl, {
        ...defaultConfig,
        ...axiosConfig
      });

      // Xử lý nội dung trước khi lưu vào state
      const processedContent = transformContent(response.data);

      // Kiểm tra nội dung có rỗng không
      if (!processedContent || processedContent.trim() === '') {
        setStatus(HtmlContentStatus.ERROR);
        setError(new Error('Received empty content from URL'));
        return;
      }

      setContent(processedContent);
      setStatus(HtmlContentStatus.SUCCESS);

      // Log thành công
    } catch (err) {
      setStatus(HtmlContentStatus.ERROR);

      // Xử lý lỗi chi tiết hơn
      if (axios.isAxiosError(err)) {
        if (err.response) {
          // Lỗi từ server (status code không phải 2xx)
          setError(new Error(`Server error: ${err.response.status} ${err.response.statusText}`));
        } else if (err.request) {
          // Lỗi không nhận được response
          if (err.code === 'ECONNABORTED') {
            setError(new Error('Request timeout. Server took too long to respond.'));
          } else if (err.message.includes('Network Error')) {
            setError(new Error('Network error. This might be a CORS issue or the server is unreachable.'));
          } else {
            setError(new Error(`Request error: ${err.message}`));
          }
        } else {
          // Lỗi khi thiết lập request
          setError(new Error(`Request setup error: ${err.message}`));
        }
      } else {
        // Lỗi không phải từ Axios
        setError(err instanceof Error ? err : new Error('Failed to fetch HTML content'));
      }

    }
  }, [axiosConfig, transformContent]);

  /**
   * Hàm để lấy lại nội dung từ URL hiện tại
   */
  const refetch = useCallback(async (): Promise<void> => {
    await fetchContent(url);
  }, [fetchContent, url]);

  /**
   * Hàm để lấy nội dung từ một URL khác
   */
  const fetchFromUrl = useCallback(async (newUrl: string): Promise<void> => {
    setUrl(newUrl);
    await fetchContent(newUrl);
  }, [fetchContent]);

  // Tự động lấy nội dung khi hook được gọi hoặc URL thay đổi
  useEffect(() => {
    if (autoFetch && url) {
      fetchContent(url);
    }
  }, [autoFetch, fetchContent, url]);

  // Kiểm tra xem nội dung có phải là HTML hợp lệ không
  const isValidHtml = useMemo(() => {
    if (!content) return false;

    // Kiểm tra cơ bản: nội dung có chứa ít nhất một thẻ HTML không
    const hasHtmlTags = /<[a-z][\s\S]*>/i.test(content);

    // Kiểm tra nâng cao: nội dung có cấu trúc HTML cơ bản không
    const hasBasicHtmlStructure = (
      content.includes('<html') ||
      content.includes('<body') ||
      content.includes('<div') ||
      content.includes('<p')
    );

    return hasHtmlTags && hasBasicHtmlStructure;
  }, [content]);

  return {
    content,
    status,
    error,
    refetch,
    fetchFromUrl,
    isValidHtml,
    contentLength: content.length
  };
}

export default useHtmlContent;
