import { useState, useCallback, useRef, useEffect } from 'react';
import { ZoomConfig } from '../types';

interface UseZoomOptions {
  /**
   * Zoom configuration
   */
  config: ZoomConfig;

  /**
   * Initial zoom level
   */
  initialZoom?: number;

  /**
   * Callback when zoom level changes
   */
  onZoomChange?: (zoomLevel: number) => void;
}

interface UseZoomResult {
  /**
   * Current zoom level
   */
  zoomLevel: number;

  /**
   * Set zoom level
   */
  setZoomLevel: (level: number) => void;

  /**
   * Zoom in by one step
   */
  zoomIn: () => void;

  /**
   * Zoom out by one step
   */
  zoomOut: () => void;

  /**
   * Reset zoom to initial level
   */
  resetZoom: () => void;

  /**
   * Handle mouse wheel event for zooming
   */
  handleWheel: (e: React.WheelEvent) => void;

  /**
   * Handle touch events for pinch zoom
   */
  touchStartHandler: (e: React.TouchEvent) => void;
  touchMoveHandler: (e: React.TouchEvent) => void;
  touchEndHandler: () => void;

  /**
   * Current position for panning
   */
  position: { x: number; y: number };

  /**
   * Set position for panning
   */
  setPosition: (pos: { x: number; y: number }) => void;

  /**
   * Handle mouse down event for panning
   */
  handleMouseDown: (e: React.MouseEvent) => void;

  /**
   * Handle mouse move event for panning
   */
  handleMouseMove: (e: React.MouseEvent) => void;

  /**
   * Handle mouse up event for panning
   */
  handleMouseUp: () => void;

  /**
   * Whether currently panning
   */
  isPanning: boolean;
}

/**
 * Hook for handling zoom and pan functionality
 */
const useZoom = ({ config, initialZoom = 1, onZoomChange }: UseZoomOptions): UseZoomResult => {
  const { maxZoom = 3, minZoom = 1, zoomStep = 0.5 } = config;

  // State for zoom level
  const [zoomLevel, setZoomLevelState] = useState(initialZoom);

  // State for panning
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [startPanPosition, setStartPanPosition] = useState({ x: 0, y: 0 });

  // State for pinch zoom
  const touchDistance = useRef<number | null>(null);

  // Set zoom level with constraints
  const setZoomLevel = useCallback(
    (level: number) => {
      const newZoomLevel = Math.min(Math.max(level, minZoom), maxZoom);
      setZoomLevelState(newZoomLevel);

      if (onZoomChange) {
        onZoomChange(newZoomLevel);
      }

      // Reset position if zooming out to 1
      if (newZoomLevel === 1) {
        setPosition({ x: 0, y: 0 });
      }
    },
    [minZoom, maxZoom, onZoomChange]
  );

  // Zoom in by one step
  const zoomIn = useCallback(() => {
    setZoomLevel(zoomLevel + zoomStep);
  }, [zoomLevel, zoomStep, setZoomLevel]);

  // Zoom out by one step
  const zoomOut = useCallback(() => {
    setZoomLevel(zoomLevel - zoomStep);
  }, [zoomLevel, zoomStep, setZoomLevel]);

  // Reset zoom to initial level
  const resetZoom = useCallback(() => {
    setZoomLevel(initialZoom);
    setPosition({ x: 0, y: 0 });
  }, [initialZoom, setZoomLevel]);

  // Handle mouse wheel for zooming
  const handleWheel = useCallback(
    (e: React.WheelEvent) => {
      e.preventDefault();

      if (e.deltaY < 0) {
        zoomIn();
      } else {
        zoomOut();
      }
    },
    [zoomIn, zoomOut]
  );

  // Handle touch events for pinch zoom
  const getTouchDistance = (touches: React.TouchList): number => {
    if (touches.length < 2) return 0;

    const dx = (touches[0]?.clientX || 0) - (touches[1]?.clientX || 0);
    const dy = (touches[0]?.clientY || 0) - (touches[1]?.clientY || 0);
    return Math.sqrt(dx * dx + dy * dy);
  };

  const touchStartHandler = useCallback(
    (e: React.TouchEvent) => {
      if (e.touches.length === 2) {
        touchDistance.current = getTouchDistance(e.touches);
      } else if (e.touches.length === 1 && zoomLevel > 1) {
        setIsPanning(true);
        setStartPanPosition({
          x: (e.touches[0]?.clientX || 0) - position.x,
          y: (e.touches[0]?.clientY || 0) - position.y,
        });
      }
    },
    [zoomLevel, position]
  );

  const touchMoveHandler = useCallback(
    (e: React.TouchEvent) => {
      e.preventDefault();

      if (e.touches.length === 2 && touchDistance.current !== null) {
        const currentDistance = getTouchDistance(e.touches);
        const delta = currentDistance - touchDistance.current;

        if (Math.abs(delta) > 10) {
          const newZoom = zoomLevel + (delta > 0 ? zoomStep : -zoomStep);
          setZoomLevel(newZoom);
          touchDistance.current = currentDistance;
        }
      } else if (e.touches.length === 1 && isPanning) {
        setPosition({
          x: (e.touches[0]?.clientX || 0) - startPanPosition.x,
          y: (e.touches[0]?.clientY || 0) - startPanPosition.y,
        });
      }
    },
    [zoomLevel, zoomStep, setZoomLevel, isPanning, startPanPosition]
  );

  const touchEndHandler = useCallback(() => {
    touchDistance.current = null;
    setIsPanning(false);
  }, []);

  // Handle mouse events for panning
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (zoomLevel > 1) {
        setIsPanning(true);
        setStartPanPosition({
          x: e.clientX - position.x,
          y: e.clientY - position.y,
        });
      }
    },
    [zoomLevel, position]
  );

  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (isPanning) {
        setPosition({
          x: e.clientX - startPanPosition.x,
          y: e.clientY - startPanPosition.y,
        });
      }
    },
    [isPanning, startPanPosition]
  );

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Reset position when zoom level changes to 1
  useEffect(() => {
    if (zoomLevel === 1) {
      setPosition({ x: 0, y: 0 });
    }
  }, [zoomLevel]);

  return {
    zoomLevel,
    setZoomLevel,
    zoomIn,
    zoomOut,
    resetZoom,
    handleWheel,
    touchStartHandler,
    touchMoveHandler,
    touchEndHandler,
    position,
    setPosition,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    isPanning,
  };
};

export default useZoom;
