/**
 * PasswordInput Component
 * Input component với toggle hiển thị/ẩn password
 */
import React, { useState } from 'react';
import Input, { InputProps } from '../Input';
import Icon from '../Icon';

export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  /**
   * Hiển thị password mặc định
   */
  defaultVisible?: boolean;

  /**
   * Custom icon cho trạng thái hiển thị
   */
  visibleIcon?: string;

  /**
   * Custom icon cho trạng thái ẩn
   */
  hiddenIcon?: string;
}

/**
 * PasswordInput component với toggle visibility
 */
const PasswordInput: React.FC<PasswordInputProps> = ({
  defaultVisible = false,
  visibleIcon = 'eye-off',
  hiddenIcon = 'eye',
  ...inputProps
}) => {
  const [isVisible, setIsVisible] = useState(defaultVisible);

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  return (
    <Input
      {...inputProps}
      type={isVisible ? 'text' : 'password'}
      rightIcon={
        <div
          className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors duration-200"
          onClick={toggleVisibility}
          role="button"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              toggleVisibility();
            }
          }}
          aria-label={isVisible ? 'Ẩn mật khẩu' : 'Hiển thị mật khẩu'}
        >
          <Icon name={isVisible ? visibleIcon : hiddenIcon} size="sm" />
        </div>
      }
    />
  );
};

export default PasswordInput;
