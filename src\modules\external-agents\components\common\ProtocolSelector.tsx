import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Input } from '@/shared/components/common';
import ProtocolBadge from '../indicators/ProtocolBadge';
import { ProtocolType, ProtocolConfig } from '../../types';
import { useProtocols, useProtocolDetection } from '../../hooks';
import { getProtocolLabel, detectProtocolFromUrl } from '../../utils';

interface ProtocolSelectorProps {
  value?: ProtocolType;
  endpoint?: string;
  onSelect: (protocol: ProtocolType, config?: ProtocolConfig) => void;
  onDetect?: (endpoint: string) => void;
  showAutoDetect?: boolean;
  className?: string;
}

const ProtocolSelector: React.FC<ProtocolSelectorProps> = ({
  value,
  endpoint,
  onSelect,
  onDetect,
  showAutoDetect = true,
  className,
}) => {
  const { t } = useTranslation(['external-agents']);
  const [detectionEndpoint, setDetectionEndpoint] = useState(endpoint || '');

  // API hooks
  const { data: protocols } = useProtocols();
  const detectMutation = useProtocolDetection();

  const protocolOptions = Object.values(ProtocolType).map(protocol => ({
    value: protocol,
    label: getProtocolLabel(protocol),
    description: t(`external-agents:protocol.description.${protocol}`),
  }));

  const handleProtocolSelect = (protocol: ProtocolType) => {
    const protocolConfig = protocols?.find(p => p.type === protocol);
    onSelect(protocol, protocolConfig);
  };

  const handleAutoDetect = async () => {
    if (!detectionEndpoint.trim()) {
      return;
    }

    try {
      // First try local detection
      const localDetection = detectProtocolFromUrl(detectionEndpoint);
      if (localDetection) {
        handleProtocolSelect(localDetection);
        return;
      }

      // Then try API detection
      const result = await detectMutation.mutateAsync(detectionEndpoint);
      if (result.detectedProtocol) {
        handleProtocolSelect(result.detectedProtocol);
      }

      // Call parent handler if provided
      onDetect?.(detectionEndpoint);
    } catch (error) {
      console.error('Protocol detection failed:', error);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Auto Detection */}
      {showAutoDetect && (
        <Card>
          <div className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('external-agents:protocol.autoDetect')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mb-4">
              {t('external-agents:protocol.autoDetectDescription')}
            </Typography>

            <div className="flex gap-3">
              <div className="flex-1">
                <Input
                  value={detectionEndpoint}
                  onChange={e => setDetectionEndpoint(e.target.value)}
                  placeholder="https://api.example.com"
                />
              </div>
              <Button
                onClick={handleAutoDetect}
                disabled={!detectionEndpoint.trim() || detectMutation.isPending}
                variant="outline"
              >
                {detectMutation.isPending
                  ? t('external-agents:protocol.detecting')
                  : t('external-agents:protocol.detect')}
              </Button>
            </div>

            {detectMutation.data && (
              <div className="mt-4 p-4 bg-muted rounded-md">
                <Typography variant="body2" className="font-medium mb-2">
                  {t('external-agents:protocol.detectionResult')}:
                </Typography>
                {detectMutation.data.detectedProtocol ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <ProtocolBadge protocol={detectMutation.data.detectedProtocol} />
                      <Typography variant="body2">
                        {t('external-agents:protocol.confidence')}:{' '}
                        {Math.round(detectMutation.data.confidence * 100)}%
                      </Typography>
                    </div>
                    {detectMutation.data.supportedFeatures.length > 0 && (
                      <div>
                        <Typography variant="caption" className="text-muted-foreground">
                          {t('external-agents:protocol.supportedFeatures')}:
                        </Typography>
                        <Typography variant="body2">
                          {detectMutation.data.supportedFeatures.join(', ')}
                        </Typography>
                      </div>
                    )}
                  </div>
                ) : (
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('external-agents:protocol.noProtocolDetected')}
                  </Typography>
                )}
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Manual Selection */}
      <Card>
        <div className="p-6">
          <Typography variant="h3" className="mb-4">
            {t('external-agents:protocol.manualSelection')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {protocolOptions.map(option => (
              <div
                key={option.value}
                className={`
                  p-4 border rounded-lg cursor-pointer transition-all hover:shadow-md
                  ${
                    value === option.value
                      ? 'border-primary bg-primary/5 shadow-sm'
                      : 'border-border hover:border-primary/50'
                  }
                `}
                onClick={() => handleProtocolSelect(option.value)}
              >
                <div className="flex items-center justify-between mb-2">
                  <ProtocolBadge protocol={option.value} />
                  {value === option.value && <div className="w-2 h-2 bg-primary rounded-full" />}
                </div>
                <Typography variant="body2" className="text-muted-foreground">
                  {option.description}
                </Typography>
              </div>
            ))}
          </div>
        </div>
      </Card>

      {/* Selected Protocol Info */}
      {value && (
        <Card>
          <div className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('external-agents:protocol.selectedProtocol')}
            </Typography>
            <div className="flex items-center gap-3">
              <ProtocolBadge protocol={value} size="lg" />
              <div>
                <Typography variant="body1" className="font-medium">
                  {getProtocolLabel(value)}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {t(`external-agents:protocol.description.${value}`)}
                </Typography>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ProtocolSelector;
