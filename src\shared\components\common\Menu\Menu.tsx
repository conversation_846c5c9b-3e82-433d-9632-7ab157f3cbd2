import React, { useMemo } from 'react';
import { MenuMode, MenuTheme } from './MenuContext';
import { MenuProvider } from './MenuContextProvider';
import { useTheme } from '@/shared/contexts/theme';

export interface MenuItem {
  /**
   * Key duy nhất của menu item
   */
  key: string;

  /**
   * Label hiển thị
   */
  label: React.ReactNode;

  /**
   * Icon hiển thị (tùy chọn)
   */
  icon?: string | React.ReactNode;

  /**
   * Đường dẫn (tùy chọn)
   */
  path?: string;

  /**
   * Các menu items con (submenu)
   */
  children?: MenuItem[];

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Dữ liệu bổ sung
   */
  data?: Record<string, unknown>;

  /**
   * Loại item (divider)
   */
  type?: 'divider';
}

export type MenuVariant = 'default' | 'primary' | 'secondary' | 'ghost' | 'bordered';

export interface MenuProps {
  /**
   * Các menu items
   */
  items?: MenuItem[];

  /**
   * Chế độ hiển thị của menu
   * @default 'horizontal'
   */
  mode?: MenuMode;

  /**
   * Trạng thái thu gọn (chỉ áp dụng cho vertical và inline mode)
   * @default false
   */
  collapsed?: boolean;

  /**
   * Sự kiện khi chọn một menu item
   */
  onSelect?: (key: string) => void;

  /**
   * Key của menu item đang được chọn
   */
  selectedKey?: string;

  /**
   * Các key của menu items đang được mở rộng (cho submenu)
   */
  expandedKeys?: string[];

  /**
   * Theme của menu
   * @default 'default'
   */
  theme?: MenuTheme;

  /**
   * Variant của menu
   * @default 'default'
   */
  variant?: MenuVariant;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Style bổ sung
   */
  style?: React.CSSProperties;

  /**
   * Children của menu (thay thế cho items)
   */
  children?: React.ReactNode;
}

/**
 * Component Menu nâng cao với nhiều tính năng
 */
const Menu: React.FC<MenuProps> = ({
  mode = 'horizontal',
  collapsed = false,
  onSelect,
  selectedKey,
  expandedKeys = [],
  theme: propTheme,
  variant = 'default',
  className = '',
  style,
  children,
}) => {
  const { themeMode } = useTheme();

  // Sử dụng theme từ prop hoặc từ app theme
  const theme = useMemo<MenuTheme>(() => {
    if (propTheme) return propTheme;
    return themeMode === 'dark' ? 'dark' : 'light';
  }, [themeMode, propTheme]);

  // Xác định các class dựa trên props
  const menuClasses = useMemo(() => {
    const baseClasses = 'overflow-hidden transition-all duration-300';

    const modeClasses = {
      horizontal: 'flex flex-row items-center',
      vertical: collapsed ? 'w-16' : 'w-64',
      inline: collapsed ? 'w-16' : 'w-64',
    }[mode];

    const themeClasses = {
      default: 'bg-white dark:bg-dark-light',
      dark: 'bg-dark-light',
      light: 'bg-white',
    }[theme];

    const variantClasses = {
      default: '',
      primary: 'bg-primary/5 dark:bg-primary/10 border-primary/20',
      secondary: 'bg-gray-50 dark:bg-dark-lighter',
      ghost: 'bg-transparent',
      bordered: 'border border-gray-200 dark:border-gray-700 rounded-md',
    }[variant];

    return `${baseClasses} ${modeClasses} ${themeClasses} ${variantClasses} ${className}`;
  }, [mode, theme, variant, collapsed, className]);

  return (
    <MenuProvider
      mode={mode}
      theme={theme}
      collapsed={collapsed}
      selectedKey={selectedKey || undefined}
      expandedKeys={expandedKeys}
      onSelect={onSelect}
    >
      <div
        className={menuClasses}
        style={style}
        role="menu"
        aria-orientation={mode === 'horizontal' ? 'horizontal' : 'vertical'}
      >
        {children}
      </div>
    </MenuProvider>
  );
};

export default React.memo(Menu);
