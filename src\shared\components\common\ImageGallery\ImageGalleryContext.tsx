import React, { createContext, useState, useMemo, useCallback } from 'react';
import {
  GalleryImage,
  LightboxConfig,
  ThumbnailsConfig,
  ZoomConfig,
  GalleryLayout,
  ColumnsConfig,
} from './types';

interface ImageGalleryContextProps {
  // Data
  images: GalleryImage[];

  // Layout
  layout: GalleryLayout;
  columns: ColumnsConfig;
  gap: number | string;

  // Lightbox
  lightboxEnabled: boolean;
  lightboxConfig: LightboxConfig;
  isLightboxOpen: boolean;
  currentImageIndex: number;
  openLightbox: (index: number) => void;
  closeLightbox: () => void;
  goToNext: () => void;
  goToPrev: () => void;
  goToImage: (index: number) => void;

  // Thumbnails
  thumbnailsEnabled: boolean;
  thumbnailsConfig: ThumbnailsConfig;

  // Zoom
  zoomEnabled: boolean;
  zoomConfig: ZoomConfig;
  zoomLevel: number;
  setZoomLevel: (level: number) => void;
  resetZoom: () => void;

  // Lazy Loading
  lazyLoadEnabled: boolean;

  // Callbacks
  onImageClick?: ((image: GalleryImage, index: number) => void) | undefined;
  onImageChange?: ((currentImage: GalleryImage, index: number) => void) | undefined;
}

// Default values
const defaultLightboxConfig: LightboxConfig = {
  showCaption: true,
  showCounter: true,
  showCloseButton: true,
  showNavigation: true,
  closeOnBackdropClick: true,
  closeOnEsc: true,
  animationDuration: 300,
  backdropOpacity: 0.9,
};

const defaultThumbnailsConfig: ThumbnailsConfig = {
  position: 'bottom',
  size: 60,
  gap: 8,
  visibleItems: 5,
  autoScroll: true,
};

const defaultZoomConfig: ZoomConfig = {
  maxZoom: 3,
  minZoom: 1,
  zoomStep: 0.5,
  showControls: true,
};

// Create context with default values
const ImageGalleryContext = createContext<ImageGalleryContextProps | undefined>(undefined);

export interface ImageGalleryProviderProps {
  children: React.ReactNode;
  images: GalleryImage[];
  layout?: GalleryLayout;
  columns?: ColumnsConfig;
  gap?: number | string;
  lightbox?: boolean;
  lightboxConfig?: Partial<LightboxConfig>;
  thumbnails?: boolean;
  thumbnailsConfig?: Partial<ThumbnailsConfig>;
  zoom?: boolean;
  zoomConfig?: Partial<ZoomConfig>;
  lazyLoad?: boolean;
  onImageClick?: ((image: GalleryImage, index: number) => void) | undefined;
  onImageChange?: ((currentImage: GalleryImage, index: number) => void) | undefined;
}

export const ImageGalleryProvider: React.FC<ImageGalleryProviderProps> = ({
  children,
  images,
  layout = 'grid',
  columns = { xs: 1, sm: 2, md: 3, lg: 4, xl: 5 },
  gap = 16,
  lightbox = true,
  lightboxConfig = {},
  thumbnails = true,
  thumbnailsConfig = {},
  zoom = true,
  zoomConfig = {},
  lazyLoad = true,
  onImageClick,
  onImageChange,
}) => {
  // State
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [zoomLevel, setZoomLevel] = useState(1);

  // Merge configs with defaults
  const mergedLightboxConfig = useMemo(
    () => ({
      ...defaultLightboxConfig,
      ...lightboxConfig,
    }),
    [lightboxConfig]
  );

  const mergedThumbnailsConfig = useMemo(
    () => ({
      ...defaultThumbnailsConfig,
      ...thumbnailsConfig,
    }),
    [thumbnailsConfig]
  );

  const mergedZoomConfig = useMemo(
    () => ({
      ...defaultZoomConfig,
      ...zoomConfig,
    }),
    [zoomConfig]
  );

  // Lightbox handlers
  const openLightbox = useCallback((index: number) => {
    setCurrentImageIndex(index);
    setIsLightboxOpen(true);
    setZoomLevel(1); // Reset zoom when opening lightbox
  }, []);

  const closeLightbox = useCallback(() => {
    setIsLightboxOpen(false);
    setZoomLevel(1); // Reset zoom when closing lightbox
  }, []);

  const goToNext = useCallback(() => {
    if (images.length <= 1) return;

    setCurrentImageIndex(prevIndex => {
      const newIndex = (prevIndex + 1) % images.length;
      if (onImageChange) {
        const image = images[newIndex];
        if (image !== undefined) {
          onImageChange(image, newIndex);
        }
      }
      return newIndex;
    });
    setZoomLevel(1); // Reset zoom when changing image
  }, [images, onImageChange]);

  const goToPrev = useCallback(() => {
    if (images.length <= 1) return;

    setCurrentImageIndex(prevIndex => {
      const newIndex = (prevIndex - 1 + images.length) % images.length;
      if (onImageChange) {
        const image = images[newIndex];
        if (image !== undefined) {
          onImageChange(image, newIndex);
        }
      }
      return newIndex;
    });
    setZoomLevel(1); // Reset zoom when changing image
  }, [images, onImageChange]);

  const goToImage = useCallback(
    (index: number) => {
      if (index < 0 || index >= images.length) return;

      setCurrentImageIndex(index);
      if (onImageChange) {
        const image = images[index];
        if (image !== undefined) {
          onImageChange(image, index);
        }
      }
      setZoomLevel(1); // Reset zoom when changing image
    },
    [images, onImageChange]
  );

  // Zoom handlers
  const resetZoom = useCallback(() => {
    setZoomLevel(1);
  }, []);

  // Context value
  const contextValue = useMemo(
    () => ({
      // Data
      images,

      // Layout
      layout,
      columns,
      gap,

      // Lightbox
      lightboxEnabled: lightbox,
      lightboxConfig: mergedLightboxConfig,
      isLightboxOpen,
      currentImageIndex,
      openLightbox,
      closeLightbox,
      goToNext,
      goToPrev,
      goToImage,

      // Thumbnails
      thumbnailsEnabled: thumbnails,
      thumbnailsConfig: mergedThumbnailsConfig,

      // Zoom
      zoomEnabled: zoom,
      zoomConfig: mergedZoomConfig,
      zoomLevel,
      setZoomLevel,
      resetZoom,

      // Lazy Loading
      lazyLoadEnabled: lazyLoad,

      // Callbacks
      onImageClick,
      onImageChange,
    }),
    [
      images,
      layout,
      columns,
      gap,
      lightbox,
      mergedLightboxConfig,
      isLightboxOpen,
      currentImageIndex,
      openLightbox,
      closeLightbox,
      goToNext,
      goToPrev,
      goToImage,
      thumbnails,
      mergedThumbnailsConfig,
      zoom,
      mergedZoomConfig,
      zoomLevel,
      resetZoom,
      lazyLoad,
      onImageClick,
      onImageChange,
    ]
  );

  return (
    <ImageGalleryContext.Provider value={contextValue}>{children}</ImageGalleryContext.Provider>
  );
};

// Hook đã được chuyển sang file useImageGallery.ts

export default ImageGalleryContext;
