import { TextareaHTMLAttributes } from 'react';

/**
 * <PERSON><PERSON><PERSON> thước của Textarea
 */
export type TextareaSize = 'sm' | 'md' | 'lg';

/**
 * Trạng thái của Textarea
 */
export type TextareaStatus = 'default' | 'error' | 'success' | 'warning';

/**
 * Props cho Textarea component
 */
export interface TextareaProps
  extends Omit<TextareaHTMLAttributes<HTMLTextAreaElement>, 'onResize'> {
  /**
   * Chiều rộng 100%
   * @default false
   */
  fullWidth?: boolean;
  /**
   * Kích thước của Textarea
   * @default 'md'
   */
  size?: TextareaSize;

  /**
   * Trạng thái của Textarea
   * @default 'default'
   */
  status?: TextareaStatus;

  /**
   * Tự động điều chỉnh chiều cao theo nội dung
   * @default false
   */
  autoSize?: boolean | { minRows?: number; maxRows?: number };

  /**
   * Hi<PERSON><PERSON> thị số ký tự đã nhập
   * @default false
   */
  showCount?: boolean;

  /**
   * Số ký tự tối đa
   */
  maxLength?: number;

  /**
   * Placeholder khi không có nội dung
   */
  placeholder?: string;

  /**
   * Vô hiệu hóa Textarea
   * @default false
   */
  disabled?: boolean;

  /**
   * Chỉ đọc
   * @default false
   */
  readOnly?: boolean;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Hiển thị border
   * @default true
   */
  bordered?: boolean;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;

  /**
   * Callback khi focus
   */
  onFocus?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;

  /**
   * Callback khi blur
   */
  onBlur?: (e: React.FocusEvent<HTMLTextAreaElement>) => void;

  /**
   * Callback khi nhấn phím
   */
  onKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;

  /**
   * Callback khi nhấn phím Enter
   */
  onPressEnter?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;

  /**
   * Callback khi resize
   */
  onResize?: (size: { width: number; height: number }) => void;

  /**
   * Hiển thị clear button
   * @default false
   */
  allowClear?: boolean;

  /**
   * Format hiển thị số ký tự
   */
  countFormatter?: (count: number, maxLength?: number) => string;
}
