/**
 * DTO tiêu chuẩn cho phản hồi API
 */
export interface ApiResponseDto<T = unknown> {
  code: number;

  message: string;

  result: T;
}

/**
 * Metadata cho phân trang
 */
export interface PaginationMeta {
  /**
   * Tổng số mục
   */
  totalItems: number;

  /**
   * Số mục trên trang hiện tại
   */
  itemCount: number;

  /**
   * Số mục trên mỗi trang
   */
  itemsPerPage: number;

  /**
   * Tổng số trang
   */
  totalPages: number;

  /**
   * Trang hiện tại
   */
  currentPage: number;

  /**
   * C<PERSON> phần tử
   */
  hasItems?: boolean;
}

/**
 * Kết quả phân trang
 */
export interface PaginatedResult<T> {
  /**
   * Danh sách mục
   */
  items: T[];

  /**
   * Metadata phân trang
   */
  meta: PaginationMeta;
}
