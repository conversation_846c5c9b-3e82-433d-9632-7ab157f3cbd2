/**
 * <PERSON>ung cấp cấu hình mặc định cho useDataTable
 * @module useDataTableConfig
 */
import { useTranslation } from 'react-i18next';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { UseDataTableOptions, PaginatedData } from './useDataTable';
import { FilterOption } from './useFilterOptions';

/**
 * Hook tạo cấu hình mặc định cho useDataTable
 * @template T Kiểu dữ liệu của dòng trong bảng
 * @template TQueryParams Kiểu dữ liệu của query params
 * @param options Tùy chọn bổ sung
 * @returns Cấu hình mặc định cho useDataTable
 */
export function useDataTableConfig<T, TQueryParams>(options: {
  columns: TableColumn<T>[];
  filterOptions?: FilterOption[];
  defaultFilterId?: string;
  createQueryParams: (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }) => TQueryParams;
  showDateFilter?: boolean;
  apiData?: PaginatedData<T> | undefined;
  isLoading?: boolean;
}): UseDataTableOptions<T, TQueryParams> {
  const { t } = useTranslation(['common']);

  const config: UseDataTableOptions<T, TQueryParams> = {
    columns: options.columns,
    tableDataOptions: {
      defaultPage: 1,
      defaultPageSize: 10,
      defaultSearchTerm: '',
      defaultSortBy: null,
      defaultSortDirection: null,
    },
    columnVisibilityOptions: {
      includeAllOption: true,
      allOptionLabel: t('common:all'),
    },
    createQueryParams: options.createQueryParams,
    apiData: options.apiData,
    isLoading: options.isLoading ?? false,
  };

  if (options.filterOptions) {
    config.filterOptions = {
      options: options.filterOptions,
      defaultSelectedId: options.defaultFilterId || 'all',
    };
  }

  if (options.showDateFilter) {
    config.dateRangeOptions = {
      convertToISOString: true,
    };
  }

  return config;
}
