import React, { useState, useRef } from 'react';
import { Typography, Button, Input } from '@/shared/components/common';
import { Asset } from '../types';
import { Image, Upload, Search, Trash2 } from 'lucide-react';

interface AssetsPanelProps {
  assets: Asset[];
  onAddAsset: (asset: Asset) => void;
  onDeleteAsset: (assetId: string) => void;
  onSelectAsset: (asset: Asset) => void;
}

const AssetsPanel: React.FC<AssetsPanelProps> = ({
  assets,
  onAddAsset,
  onDeleteAsset,
  onSelectAsset
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory,] = useState<string>('all');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // <PERSON>ích thước icon
  const iconSize = 16;

  // Lọc assets theo từ khóa tìm kiếm và danh mục
  const filteredAssets = assets.filter(asset => {
    const matchesSearch =
      asset.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.alt?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory === 'all' || asset.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Xử lý khi upload file
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    Array.from(files).forEach(file => {
      // Chỉ chấp nhận file hình ảnh
      if (!file.type.startsWith('image/')) return;

      const reader = new FileReader();
      reader.onload = (event) => {
        const newAsset: Asset = {
          id: `asset-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'image',
          src: event.target?.result as string,
          alt: file.name,
          name: file.name,
          size: file.size,
          category: selectedCategory !== 'all' ? selectedCategory : 'uploads'
        };

        onAddAsset(newAsset);
      };

      reader.readAsDataURL(file);
    });

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    setShowUploadDialog(false);
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <Image size={20} className="mr-2 text-muted-foreground" />
          <Typography variant="h5" className="text-lg font-medium">Hình ảnh</Typography>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowUploadDialog(true)}
            title="Tải lên tài nguyên mới"
          >
            <Upload size={iconSize} className="mr-1" />
            Tải lên
          </Button>
        </div>
      </div>

      <div className="mb-4">
        <div className="relative">
          <span className="absolute left-2 top-2.5 text-gray-400">
            <Search size={iconSize} />
          </span>
          <Input
            placeholder="Tìm kiếm assets..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {filteredAssets.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          <Typography variant="body2">Không có tài nguyên nào</Typography>
          <Button
            variant="outline"
            className="mt-2"
            onClick={() => setShowUploadDialog(true)}
          >
            <Upload size={iconSize} className="mr-2" />
            Tải lên tài nguyên
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-3">
          {filteredAssets.map(asset => (
            <div
              key={asset.id}
              className="border border-border rounded-md overflow-hidden hover:border-accent hover:shadow-md cursor-pointer group relative bg-card transition-all duration-200"
              onClick={() => onSelectAsset(asset)}
            >
              <div className="aspect-video bg-muted flex items-center justify-center overflow-hidden">
                {asset.type === 'image' && (
                  <img
                    src={asset.src}
                    alt={asset.alt || ''}
                    className="w-full h-full object-cover"
                  />
                )}
              </div>

              <div className="p-2">
                <Typography variant="body2" className="text-xs truncate">
                  {asset.name || asset.alt || 'Untitled'}
                </Typography>
              </div>

              <button
                className="absolute top-1 right-1 bg-card rounded-full p-1 shadow-md opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive/10"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteAsset(asset.id);
                }}
                title="Xóa tài nguyên"
              >
                <Trash2 size={14} className="text-destructive" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Upload Dialog */}
      {showUploadDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-card rounded-lg p-6 max-w-md w-full border border-border">
            <Typography variant="h3" className="text-lg font-medium mb-4">
              Tải lên tài nguyên
            </Typography>

            <div
              className="border-2 border-dashed border-border rounded-md p-8 cursor-pointer hover:bg-muted transition-colors flex flex-col items-center justify-center"
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload size={32} className="mb-4 text-muted-foreground" />
              <Typography variant="body2" className="text-center text-foreground">
                Nhấp để chọn file hoặc kéo thả file vào đây
              </Typography>
              <Typography variant="body2" className="text-center text-muted-foreground text-xs mt-1">
                Hỗ trợ: JPG, PNG, GIF, SVG, WebP
              </Typography>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                className="hidden"
                onChange={handleFileUpload}
              />
            </div>

            <div className="flex justify-end mt-4 space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowUploadDialog(false)}
              >
                Hủy
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssetsPanel;
