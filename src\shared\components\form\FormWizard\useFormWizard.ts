import { useContext } from 'react';
import { FieldValues } from 'react-hook-form';
import { FormWizardContextType } from './FormWizard.types';
import { FormWizardContext } from './FormWizardContext';

/**
 * Hook để sử dụng FormWizard context
 */
export const useFormWizard = <
  TFormValues extends FieldValues = FieldValues,
>(): FormWizardContextType<TFormValues> => {
  const context = useContext(FormWizardContext);
  if (!context) {
    throw new Error('useFormWizard must be used within a FormWizard component');
  }
  // Đây là một type assertion cần thiết vì context được chia sẻ giữa các component
  return context as unknown as FormWizardContextType<TFormValues>;
};
