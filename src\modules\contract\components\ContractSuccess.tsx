/**
 * Component hiển thị thành công sau khi ký hợp đồng
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Typography, Button, Icon, Card } from '@/shared/components/common';
import { ContractStepProps } from '../types';

const ContractSuccess: React.FC<ContractStepProps> = ({ data }) => {
  const { t } = useTranslation('contract');
  const navigate = useNavigate();

  // Mock contract ID
  const contractId = `CT${Date.now().toString().slice(-6)}`;

  const handleGoHome = () => {
    navigate('/');
  };

  const handleViewContract = () => {
    // In real app, open contract details page in new tab
    window.open(`/contract/view/${contractId}`, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="w-full text-center">
      {/* Success Icon */}
      <div className="mb-8">
        <div className="w-20 h-20 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon name="check-circle" size="xl" className="text-success" />
        </div>
        <Typography variant="h3" className="text-success mb-2">
          {t('contract:success.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('contract:success.message')}
        </Typography>
      </div>

      {/* Contract Info */}
      <Card className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              {t('contract:success.contractId')}
            </Typography>
            <Typography variant="body1" className="font-medium">
              {contractId}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              Loại hợp đồng
            </Typography>
            <Typography variant="body1" className="font-medium">
              {data.type === 'business' ? t('contract:types.business') : t('contract:types.personal')}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              Ngày ký
            </Typography>
            <Typography variant="body1" className="font-medium">
              {new Date().toLocaleDateString('vi-VN')}
            </Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-muted-foreground mb-1">
              Trạng thái
            </Typography>
            <Typography variant="body1" className="font-medium text-success">
              Đã ký thành công
            </Typography>
          </div>
        </div>
      </Card>

      {/* Customer Info */}
      <Card className="mb-6 text-left">
        <Typography variant="h5" className="mb-3">
          Thông tin khách hàng
        </Typography>
        {data.type === 'business' && data.businessInfo ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <Typography variant="body2" className="text-muted-foreground">Tên công ty</Typography>
              <Typography variant="body1">{data.businessInfo.companyName}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Mã số thuế</Typography>
              <Typography variant="body1">{data.businessInfo.taxCode}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Người đại diện</Typography>
              <Typography variant="body1">{data.businessInfo.representative}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Email</Typography>
              <Typography variant="body1">{data.businessInfo.companyEmail}</Typography>
            </div>
          </div>
        ) : data.personalInfo ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <Typography variant="body2" className="text-muted-foreground">Họ và tên</Typography>
              <Typography variant="body1">{data.personalInfo.fullName}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Số CCCD</Typography>
              <Typography variant="body1">{data.personalInfo.idNumber}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Số điện thoại</Typography>
              <Typography variant="body1">{data.personalInfo.phone}</Typography>
            </div>
            <div>
              <Typography variant="body2" className="text-muted-foreground">Địa chỉ</Typography>
              <Typography variant="body1">{data.personalInfo.address}</Typography>
            </div>
          </div>
        ) : null}
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button
          variant="outline"
          onClick={handleViewContract}
        >
          Xem hợp đồng
        </Button>
        <Button
          variant="primary"
          onClick={handleGoHome}
        >
          Về trang chủ
        </Button>
      </div>
    </div>
  );
};

export default ContractSuccess;
