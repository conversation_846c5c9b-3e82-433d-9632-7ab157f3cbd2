import { useState, useEffect, useMemo } from 'react';

interface UseFormControlGroupProps<T> {
  /**
   * Gi<PERSON> trị đã chọn
   */
  value?: T;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: T) => void;

  /**
   * Vô hiệu hóa toàn bộ group
   */
  disabled?: boolean;

  /**
   * Hướng hiển thị: ngang hoặc dọc
   */
  direction?: 'horizontal' | 'vertical';
}

/**
 * Custom hook for form control group components (CheckboxGroup, RadioGroup)
 * Provides shared logic and styling for form control groups
 */
export function useFormControlGroup<T>({
  value,
  onChange,
  disabled = false,
  direction = 'vertical',
}: UseFormControlGroupProps<T>) {
  // State nội bộ để theo dõi giá trị đã chọn
  const [selectedValue, setSelectedValue] = useState<T>(value as T);

  // Cập nhật state nội bộ khi prop value thay đổi
  useEffect(() => {
    setSelectedValue(value as T);
  }, [value]);

  // Xử lý khi một item thay đổi
  const handleItemChange = (itemValue: unknown, isChecked?: boolean) => {
    if (disabled) return;

    let newValue: T;

    if (Array.isArray(selectedValue)) {
      // Cho CheckboxGroup
      if (isChecked) {
        // Thêm giá trị vào danh sách đã chọn
        newValue = [...selectedValue, itemValue] as T;
      } else {
        // Loại bỏ giá trị khỏi danh sách đã chọn
        newValue = selectedValue.filter(val => val !== itemValue) as T;
      }
    } else {
      // Cho RadioGroup
      newValue = itemValue as T;
    }

    // Cập nhật state nội bộ
    setSelectedValue(newValue);

    // Gọi callback onChange
    if (onChange) {
      onChange(newValue);
    }
  };

  // Direction classes
  const directionClasses = useMemo(
    () => ({
      horizontal: 'flex flex-row flex-wrap gap-4',
      vertical: 'flex flex-col gap-2',
    }),
    []
  );

  // Memoize selected class
  const getSelectedClass = useMemo(
    () => (direction === 'horizontal' ? directionClasses.horizontal : directionClasses.vertical),
    [direction, directionClasses]
  );

  return {
    selectedValue,
    handleItemChange,
    directionClasses,
    selectedClass: getSelectedClass,
  };
}
