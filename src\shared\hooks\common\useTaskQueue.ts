/**
 * Hook quản lý hàng đợi các tác vụ
 */
import { useState, useCallback, useEffect, useRef } from 'react';
import { v4 as uuidv4 } from 'uuid';
import {
  Task,
  TaskStatus,
  TaskType,
  CreateApiCallTaskParams,
  CreateFileUploadTaskParams,
  CreateCustomTaskParams,
  ApiCallTask,
  FileUploadTask,
  CustomTask,
} from '@/shared/types/task-queue.types';

/**
 * Tham số cho hook useTaskQueue
 */
export interface UseTaskQueueOptions {
  /**
   * Số lượng task tối đa có thể chạy đồng thời
   * @default 3
   */
  concurrency?: number;

  /**
   * Số lần thử lại mặc định cho các task
   * @default 3
   */
  defaultMaxRetries?: number;

  /**
   * Thời gian tự động xóa task đã hoàn thành (ms)
   * @default 60000 (1 phút)
   */
  autoRemoveCompletedAfter?: number;

  /**
   * <PERSON><PERSON> tự động bắt đầu queue khi có task mới không
   * @default true
   */
  autoStart?: boolean;
}

// Khóa lưu trữ trong localStorage
const TASK_QUEUE_STORAGE_KEY = 'task_queue_state';

/**
 * Lưu trạng thái của Task Queue vào localStorage
 */
const saveTaskQueueState = (tasks: Task[], isRunning: boolean) => {
  try {
    // Chuyển đổi các đối tượng Date thành chuỗi ISO
    const serializedTasks = tasks.map(task => ({
      ...task,
      createdAt: task.createdAt.toISOString(),
      startedAt: task.startedAt ? task.startedAt.toISOString() : undefined,
      completedAt: task.completedAt ? task.completedAt.toISOString() : undefined,
      // Loại bỏ các hàm callback và dữ liệu không thể serialize
      execute: undefined,
      onSuccess: undefined,
      onError: undefined,
      onProgress: undefined,
      error: task.error ? (task.error instanceof Error ? task.error.message : String(task.error)) : undefined,
    }));

    const state = {
      tasks: serializedTasks,
      isRunning,
      timestamp: new Date().toISOString(),
    };

    localStorage.setItem(TASK_QUEUE_STORAGE_KEY, JSON.stringify(state));
  } catch (error) {
    console.error('Lỗi khi lưu trạng thái Task Queue:', error);
  }
};

/**
 * Khôi phục trạng thái của Task Queue từ localStorage
 */
const loadTaskQueueState = (): { tasks: Task[]; isRunning: boolean } | null => {
  try {
    const stateJson = localStorage.getItem(TASK_QUEUE_STORAGE_KEY);
    if (!stateJson) return null;

    const state = JSON.parse(stateJson);

    // Chuyển đổi chuỗi ISO thành đối tượng Date
    const deserializedTasks = state.tasks.map((task: {
      id: string;
      title: string;
      description?: string;
      type: string;
      status: string;
      progress: number;
      createdAt: string;
      startedAt?: string;
      completedAt?: string;
      error?: string;
      [key: string]: unknown;
    }) => ({
      ...task,
      createdAt: new Date(task.createdAt),
      startedAt: task.startedAt ? new Date(task.startedAt) : undefined,
      completedAt: task.completedAt ? new Date(task.completedAt) : undefined,
      error: task.error ? new Error(task.error) : undefined,
    }));

    return {
      tasks: deserializedTasks,
      isRunning: state.isRunning,
    };
  } catch (error) {
    console.error('Lỗi khi khôi phục trạng thái Task Queue:', error);
    return null;
  }
};

/**
 * Hook quản lý hàng đợi các tác vụ
 */
export function useTaskQueue({
  concurrency = 3,
  defaultMaxRetries = 3,
  autoRemoveCompletedAfter = 60000,
  autoStart = true,
}: UseTaskQueueOptions = {}) {
  // Khôi phục trạng thái từ localStorage
  const initialState = loadTaskQueueState();

  // Danh sách các task
  const [tasks, setTasks] = useState<Task[]>(initialState?.tasks || []);

  // Trạng thái của queue
  const [isRunning, setIsRunning] = useState<boolean>(initialState?.isRunning || autoStart);

  // Số lượng task đang chạy
  const [runningCount, setRunningCount] = useState<number>(0);

  // Tham chiếu đến danh sách task (để sử dụng trong các hàm callback)
  const tasksRef = useRef<Task[]>([]);
  tasksRef.current = tasks;

  /**
   * Thêm task API call vào queue
   */
  const addApiCallTask = useCallback(
    (params: CreateApiCallTaskParams): string => {
      const {
        title,
        description,
        execute,
        onSuccess,
        onError,
        cancellable = true,
        retryable = true,
        maxRetries = defaultMaxRetries,
        metadata,
      } = params;

      const id = uuidv4();
      const task: ApiCallTask = {
        id,
        title,
        type: TaskType.API_CALL,
        status: TaskStatus.PENDING,
        progress: 0,
        createdAt: new Date(),
        execute,
        cancellable,
        retryable,
        retryCount: 0,
        maxRetries,
        ...(description && { description }),
        ...(onSuccess && { onSuccess }),
        ...(onError && { onError }),
        ...(metadata && { metadata }),
      };

      setTasks(prevTasks => [...prevTasks, task]);

      if (autoStart && !isRunning) {
        setIsRunning(true);
      }

      return id;
    },
    [autoStart, defaultMaxRetries, isRunning]
  );

  /**
   * Thêm task upload file vào queue
   */
  const addFileUploadTask = useCallback(
    (params: CreateFileUploadTaskParams): string => {
      const {
        title,
        description,
        file,
        uploadUrl,
        getUploadUrl,
        execute,
        onSuccess,
        onError,
        onProgress,
        cancellable = true,
        retryable = true,
        maxRetries = defaultMaxRetries,
        metadata,
      } = params;

      // Tạo thumbnail cho file ảnh
      let thumbnail: string | undefined;
      if (file.type.startsWith('image/')) {
        thumbnail = URL.createObjectURL(file);
      }

      const id = uuidv4();
      const task: FileUploadTask = {
        id,
        title,
        type: TaskType.FILE_UPLOAD,
        status: TaskStatus.PENDING,
        progress: 0,
        createdAt: new Date(),
        file,
        execute,
        cancellable,
        retryable,
        retryCount: 0,
        maxRetries,
        ...(description && { description }),
        ...(uploadUrl && { uploadUrl }),
        ...(getUploadUrl && { getUploadUrl }),
        ...(onSuccess && { onSuccess }),
        ...(onError && { onError }),
        ...(onProgress && { onProgress }),
        ...(metadata && { metadata }),
        ...(thumbnail && { thumbnail }),
      };

      setTasks(prevTasks => {
        const updatedTasks = [...prevTasks, task];

        // Lưu trạng thái mới vào localStorage
        saveTaskQueueState(updatedTasks, isRunning);

        return updatedTasks;
      });

      if (autoStart && !isRunning) {
        setIsRunning(true);
      }

      return id;
    },
    [autoStart, defaultMaxRetries, isRunning]
  );

  /**
   * Thêm task tùy chỉnh vào queue
   */
  const addCustomTask = useCallback(
    (params: CreateCustomTaskParams): string => {
      const {
        title,
        description,
        execute,
        onSuccess,
        onError,
        onProgress,
        cancellable = true,
        retryable = true,
        maxRetries = defaultMaxRetries,
        metadata,
      } = params;

      const id = uuidv4();
      const task: CustomTask = {
        id,
        title,
        type: TaskType.CUSTOM,
        status: TaskStatus.PENDING,
        progress: 0,
        createdAt: new Date(),
        execute,
        cancellable,
        retryable,
        retryCount: 0,
        maxRetries,
        ...(description && { description }),
        ...(onSuccess && { onSuccess }),
        ...(onError && { onError }),
        ...(onProgress && { onProgress }),
        ...(metadata && { metadata }),
      };

      setTasks(prevTasks => {
        const updatedTasks = [...prevTasks, task];

        // Lưu trạng thái mới vào localStorage
        saveTaskQueueState(updatedTasks, isRunning);

        return updatedTasks;
      });

      if (autoStart && !isRunning) {
        setIsRunning(true);
      }

      return id;
    },
    [autoStart, defaultMaxRetries, isRunning]
  );

  /**
   * Cập nhật trạng thái của task
   */
  const updateTask = useCallback((id: string, updates: Partial<Task>) => {
    setTasks(prevTasks => {
      const updatedTasks = prevTasks.map(task =>
        (task.id === id ? ({ ...task, ...updates } as Task) : task)
      );

      // Lưu trạng thái mới vào localStorage
      saveTaskQueueState(updatedTasks, isRunning);

      return updatedTasks;
    });
  }, [isRunning]);

  /**
   * Xóa task khỏi queue
   */
  const removeTask = useCallback((id: string) => {
    setTasks(prevTasks => {
      const taskToRemove = prevTasks.find(task => task.id === id);

      // Giải phóng tài nguyên nếu là task upload file có thumbnail
      if (taskToRemove?.type === TaskType.FILE_UPLOAD && taskToRemove.thumbnail) {
        URL.revokeObjectURL(taskToRemove.thumbnail);
      }

      const updatedTasks = prevTasks.filter(task => task.id !== id);

      // Lưu trạng thái mới vào localStorage
      saveTaskQueueState(updatedTasks, isRunning);

      return updatedTasks;
    });
  }, [isRunning]);

  /**
   * Xóa tất cả các task đã hoàn thành, bị lỗi hoặc đã hủy
   */
  const clearCompletedTasks = useCallback(() => {
    setTasks(prevTasks => {
      // Giải phóng tài nguyên cho các task upload file có thumbnail
      prevTasks.forEach(task => {
        if (
          (task.status === TaskStatus.SUCCESS ||
           task.status === TaskStatus.ERROR ||
           task.status === TaskStatus.CANCELLED) &&
          task.type === TaskType.FILE_UPLOAD &&
          (task as FileUploadTask).thumbnail
        ) {
          URL.revokeObjectURL((task as FileUploadTask).thumbnail!);
        }
      });

      // Chỉ giữ lại các task đang chạy hoặc đang chờ
      const updatedTasks = prevTasks.filter(
        task => task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING
      );

      // Lưu trạng thái mới vào localStorage
      saveTaskQueueState(updatedTasks, isRunning);

      return updatedTasks;
    });
  }, [isRunning]);

  /**
   * Hủy task
   */
  const cancelTask = useCallback((id: string) => {
    setTasks(prevTasks => {
      const taskIndex = prevTasks.findIndex(task => task.id === id);

      if (taskIndex === -1) return prevTasks;

      const task = prevTasks[taskIndex];
      if (!task) return prevTasks;

      if (!task.cancellable) return prevTasks;

      if (task.status === TaskStatus.PENDING || task.status === TaskStatus.RUNNING) {
        const updatedTask: Task = {
          ...task,
          status: TaskStatus.CANCELLED,
          completedAt: new Date(),
        };

        const updatedTasks = [...prevTasks];
        updatedTasks[taskIndex] = updatedTask;

        // Lưu trạng thái mới vào localStorage
        saveTaskQueueState(updatedTasks, isRunning);

        return updatedTasks;
      }

      return prevTasks;
    });
  }, [isRunning]);

  /**
   * Thử lại task
   */
  const retryTask = useCallback(
    (id: string) => {
      setTasks(prevTasks => {
        const taskIndex = prevTasks.findIndex(task => task.id === id);

        if (taskIndex === -1) return prevTasks;

        const task = prevTasks[taskIndex];
        if (!task) return prevTasks;

        if (!task.retryable || task.status !== TaskStatus.ERROR) return prevTasks;

        if (task.retryCount >= task.maxRetries) return prevTasks;

        const updatedTask: Task = {
          ...task,
          status: TaskStatus.PENDING,
          progress: 0,
          retryCount: task.retryCount + 1,
        };

        // Remove error property if it exists
        if ('error' in updatedTask) {
          delete (updatedTask as { error?: Error }).error;
        }

        const updatedTasks = [...prevTasks];
        updatedTasks[taskIndex] = updatedTask;

        if (!isRunning) {
          setIsRunning(true);
        }

        // Lưu trạng thái mới vào localStorage
        saveTaskQueueState(updatedTasks, isRunning);

        return updatedTasks;
      });
    },
    [isRunning]
  );

  /**
   * Bắt đầu xử lý queue
   */
  const startQueue = useCallback(() => {
    setIsRunning(true);
    // Lưu trạng thái mới vào localStorage
    saveTaskQueueState(tasks, true);
  }, [tasks]);

  /**
   * Dừng xử lý queue
   */
  const pauseQueue = useCallback(() => {
    setIsRunning(false);
    // Lưu trạng thái mới vào localStorage
    saveTaskQueueState(tasks, false);
  }, [tasks]);

  /**
   * Xử lý task API call
   */
  const processApiCallTask = useCallback(
    async (task: ApiCallTask) => {
      try {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.RUNNING,
          startedAt: new Date(),
          progress: 0,
        });

        // Thực thi API call
        const result = await task.execute();

        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.SUCCESS,
          completedAt: new Date(),
          progress: 100,
          result,
        });

        // Gọi callback onSuccess
        if (task.onSuccess) {
          task.onSuccess(result);
        }

        return result;
      } catch (error) {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.ERROR,
          completedAt: new Date(),
          error: error instanceof Error ? error : new Error(String(error)),
        });

        // Gọi callback onError
        if (task.onError && error instanceof Error) {
          task.onError(error);
        }

        throw error;
      }
    },
    [updateTask]
  );

  /**
   * Xử lý task upload file
   */
  const processFileUploadTask = useCallback(
    async (task: FileUploadTask) => {
      try {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.RUNNING,
          startedAt: new Date(),
          progress: 0,
        });

        // Lấy URL upload nếu chưa có
        let uploadUrl = task.uploadUrl;
        if (!uploadUrl && task.getUploadUrl) {
          uploadUrl = await task.getUploadUrl();
          updateTask(task.id, { uploadUrl });
        }

        if (!uploadUrl) {
          throw new Error('Không có URL để upload file');
        }

        // Callback theo dõi tiến trình
        const handleProgress = (progress: number) => {
          updateTask(task.id, { progress });
          if (task.onProgress) {
            task.onProgress(progress);
          }
        };

        // Thực thi upload
        const result = await task.execute(uploadUrl, task.file, handleProgress);

        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.SUCCESS,
          completedAt: new Date(),
          progress: 100,
          result,
        });

        // Gọi callback onSuccess
        if (task.onSuccess) {
          task.onSuccess(result);
        }

        return result;
      } catch (error) {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.ERROR,
          completedAt: new Date(),
          error: error instanceof Error ? error : new Error(String(error)),
        });

        // Gọi callback onError
        if (task.onError && error instanceof Error) {
          task.onError(error);
        }

        throw error;
      }
    },
    [updateTask]
  );

  /**
   * Xử lý task tùy chỉnh
   */
  const processCustomTask = useCallback(
    async (task: CustomTask) => {
      try {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.RUNNING,
          startedAt: new Date(),
          progress: 0,
        });

        // Callback theo dõi tiến trình
        const handleProgress = (progress: number) => {
          updateTask(task.id, { progress });
          if (task.onProgress) {
            task.onProgress(progress);
          }
        };

        // Thực thi task
        const result = await task.execute(handleProgress);

        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.SUCCESS,
          completedAt: new Date(),
          progress: 100,
          result,
        });

        // Gọi callback onSuccess
        if (task.onSuccess) {
          task.onSuccess(result);
        }

        return result;
      } catch (error) {
        // Cập nhật trạng thái task
        updateTask(task.id, {
          status: TaskStatus.ERROR,
          completedAt: new Date(),
          error: error instanceof Error ? error : new Error(String(error)),
        });

        // Gọi callback onError
        if (task.onError && error instanceof Error) {
          task.onError(error);
        }

        throw error;
      }
    },
    [updateTask]
  );

  /**
   * Xử lý một task
   */
  const processTask = useCallback(
    async (task: Task) => {
      switch (task.type) {
        case TaskType.API_CALL:
          return processApiCallTask(task as ApiCallTask);
        case TaskType.FILE_UPLOAD:
          return processFileUploadTask(task as FileUploadTask);
        case TaskType.CUSTOM:
          return processCustomTask(task as CustomTask);
        default:
          throw new Error(`Không hỗ trợ loại task: ${(task as { type: string }).type}`);
      }
    },
    [processApiCallTask, processFileUploadTask, processCustomTask]
  );

  /**
   * Xử lý queue
   */
  useEffect(() => {
    if (!isRunning) return;

    const processQueue = async () => {
      // Lấy danh sách task đang chờ
      const pendingTasks = tasksRef.current.filter(task => task.status === TaskStatus.PENDING);

      // Nếu không có task nào đang chờ, dừng xử lý
      if (pendingTasks.length === 0) return;

      // Lấy số lượng task đang chạy
      const runningTasks = tasksRef.current.filter(task => task.status === TaskStatus.RUNNING);

      // Nếu đã đạt giới hạn concurrency, dừng xử lý
      if (runningTasks.length >= concurrency) return;

      // Lấy task tiếp theo để xử lý
      const nextTask = pendingTasks[0];
      if (!nextTask) return;

      // Xử lý task
      processTask(nextTask).catch(() => {
        // Lỗi đã được xử lý trong processTask
      });
    };

    // Chạy xử lý queue ngay lập tức và sau đó theo interval
    processQueue();

    // Chạy xử lý queue theo interval
    const intervalId = setInterval(processQueue, 100);

    return () => {
      clearInterval(intervalId);
    };
  }, [isRunning, concurrency, processTask, tasks.length]);

  /**
   * Cập nhật số lượng task đang chạy
   */
  useEffect(() => {
    const count = tasks.filter(task => task.status === TaskStatus.RUNNING).length;
    setRunningCount(count);
  }, [tasks]);

  /**
   * Tự động xóa task đã hoàn thành sau một khoảng thời gian
   */
  useEffect(() => {
    if (autoRemoveCompletedAfter <= 0) return;

    const checkCompletedTasks = () => {
      const now = new Date().getTime();

      setTasks(prevTasks => {
        const tasksToRemove: string[] = [];

        prevTasks.forEach(task => {
          if (
            (task.status === TaskStatus.SUCCESS || task.status === TaskStatus.ERROR) &&
            task.completedAt
          ) {
            const completedTime = task.completedAt.getTime();
            if (now - completedTime > autoRemoveCompletedAfter) {
              tasksToRemove.push(task.id);

              // Giải phóng tài nguyên nếu là task upload file có thumbnail
              if (task.type === TaskType.FILE_UPLOAD && (task as FileUploadTask).thumbnail) {
                URL.revokeObjectURL((task as FileUploadTask).thumbnail!);
              }
            }
          }
        });

        if (tasksToRemove.length === 0) return prevTasks;

        const updatedTasks = prevTasks.filter(task => !tasksToRemove.includes(task.id));

        // Lưu trạng thái mới vào localStorage
        saveTaskQueueState(updatedTasks, isRunning);

        return updatedTasks;
      });
    };

    const intervalId = setInterval(checkCompletedTasks, 5000);

    return () => {
      clearInterval(intervalId);
    };
  }, [autoRemoveCompletedAfter, isRunning]);

  /**
   * Giải phóng tài nguyên khi unmount
   */
  useEffect(() => {
    return () => {
      // Giải phóng tất cả các thumbnail
      tasks.forEach(task => {
        if (task.type === TaskType.FILE_UPLOAD && (task as FileUploadTask).thumbnail) {
          URL.revokeObjectURL((task as FileUploadTask).thumbnail!);
        }
      });
    };
  }, [tasks]);

  return {
    // Danh sách task
    tasks,

    // Trạng thái queue
    isRunning,
    runningCount,

    // Thêm task
    addApiCallTask,
    addFileUploadTask,
    addCustomTask,

    // Quản lý task
    updateTask,
    removeTask,
    cancelTask,
    retryTask,
    clearCompletedTasks,

    // Quản lý queue
    startQueue,
    pauseQueue,
  };
}

export default useTaskQueue;
