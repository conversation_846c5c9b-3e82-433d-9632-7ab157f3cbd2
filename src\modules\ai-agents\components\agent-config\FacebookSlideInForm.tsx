import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useGetFacebookPages } from '@/modules/integration/facebook/hooks/useFacebook';
import { FacebookPageDto, FacebookPageQueryDto, FacebookPageSortBy, SortOrder } from '@/modules/integration/facebook/types/facebook.types';
import {
  Button,
  Card,
  Icon,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Facebook Page - mapped từ FacebookPageDto
 */
interface FacebookPage {
  id: string;
  name: string;
  imageUrl?: string;
  category?: string;
  followers?: number;
  isConnected?: boolean;
}

/**
 * Props cho component FacebookSlideInForm
 */
interface FacebookSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các trang Facebook
   */
  onSelect: (selectedPages: FacebookPage[]) => void;

  /**
   * Danh sách ID của các trang đã chọn
   */
  selectedPageIds?: string[];
}

/**
 * Component form trượt để chọn các trang Facebook để tích hợp
 */
const FacebookSlideInForm: React.FC<FacebookSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedPageIds = [],
}) => {
  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedPageIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<FacebookPageQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: FacebookPageSortBy.PAGE_NAME,
    sortOrder: SortOrder.ASC,
  });

  // API hook để lấy dữ liệu Facebook Pages
  const { data: pagesData, isLoading } = useGetFacebookPages(queryParams);

  // Transform API data to component format
  const pages: FacebookPage[] = useMemo(() => {
    if (!pagesData?.result?.items) return [];

    return pagesData.result.items.map((page: FacebookPageDto): FacebookPage => ({
      id: page.facebookPageId,
      name: page.pageName,
      imageUrl: page.avatarPage || undefined,
      category: undefined, // API không có category
      followers: undefined, // API không có followers
      isConnected: page.isActive && !page.isError,
    }));
  }, [pagesData]);

  // Pagination data
  const totalItems = pagesData?.result?.meta?.totalItems || 0;
  const currentPage = queryParams.page || 1;
  const itemsPerPage = queryParams.limit || 10;

  // Cấu hình cột cho bảng
  const columns: TableColumn<FacebookPage>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'page',
      title: 'Trang Facebook',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.imageUrl ? (
            <img
              src={record.imageUrl}
              alt={record.name}
              className="w-10 h-10 rounded-full mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <Icon name="facebook" size="md" className="text-blue-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              ID: {record.id}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: 'Danh mục',
      dataIndex: 'category',
      width: '20%',
    },
    {
      key: 'followers',
      title: 'Người theo dõi',
      dataIndex: 'followers',
      width: '20%',
      render: (followers) => followers?.toLocaleString() || '0',
    },
    {
      key: 'status',
      title: 'Trạng thái',
      width: '20%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              Đã kết nối
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              Chưa kết nối
            </span>
          )}
        </div>
      ),
    },
  ];



  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedPageIds.length ||
      selectedIds.some(id => !selectedPageIds.includes(id)) ||
      selectedPageIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedPageIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: 'ASC' | 'DESC') => {
    const sortBy = column === 'pageName' ? FacebookPageSortBy.PAGE_NAME : FacebookPageSortBy.CREATED_AT;
    const sortOrder = direction === 'ASC' ? SortOrder.ASC : SortOrder.DESC;

    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      sortBy,
      sortOrder,
    }));
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Lấy thông tin đầy đủ của các trang đã chọn từ API data
      const selectedPages = pages.filter(page =>
        selectedIds.includes(page.id)
      );

      onSelect(selectedPages);
      onClose();
    } catch (error) {
      console.error('Error saving selected Facebook pages:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    // Reset search khi đóng
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      search: '',
      page: 1,
    }));
    onClose();
  }, [hasChanges, onClose, setQueryParams]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('pageName', queryParams.sortOrder === 'ASC' ? 'DESC' : 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setQueryParams((prev: FacebookPageQueryDto) => ({ ...prev, facebookPersonalId: undefined })),
    },
    {
      id: 'filter-connected',
      label: 'Đã kết nối',
      onClick: () => { },
    },
    {
      id: 'filter-not-connected',
      label: 'Chưa kết nối',
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn trang Facebook</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<FacebookPage>
            columns={columns}
            data={pages}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== itemsPerPage) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default FacebookSlideInForm;
