import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Plus, Trash2, Eye, Code } from 'lucide-react';
import {
  Button,
  Input,
  Alert,
  FormItem,
  Card,
  Select,
  Chip
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks';
import { EmailBuilder } from '@/shared/components/email-builder';
// import { createEmailTemplateSchema, type CreateEmailTemplateFormData } from '../../schemas/email.schema';
import { useCreateEmailTemplateAdapter } from '../../hooks/email/useEmailTemplatesAdapter';
import { EmailTemplateType } from '../../types/email.types';

interface CreateEmailTemplateFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo Email Template theo quy tắc RedAI
 */
export function CreateEmailTemplateForm({ onSuccess, onCancel }: CreateEmailTemplateFormProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const [previewMode, setPreviewMode] = useState<'design' | 'code'>('design');
  const createTemplate = useCreateEmailTemplateAdapter();

  // Sử dụng useFormErrors theo quy tắc RedAI
  const { setFormErrors } = useFormErrors<Record<string, string>>();

  // State cho form data
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    htmlContent: '',
    textContent: '',
    type: EmailTemplateType.NEWSLETTER,
    previewText: '',
    variables: [],
    tags: [],
  });

  // State cho variables và tags
  const [variables, setVariables] = useState<Array<{
    name: string;
    type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
    defaultValue: string;
    required: boolean;
    description: string;
  }>>([]);
  const [tags, setTags] = useState<string[]>([]);

  // Handle form submission
  const handleSubmit = async () => {
    // Validate form data
    const errors: Record<string, string> = {};

    if (!formData.name.trim()) {
      errors.name = t('marketing:email.templates.form.validation.nameRequired', 'Tên template là bắt buộc');
    }

    if (!formData.subject.trim()) {
      errors.subject = t('marketing:email.templates.form.validation.subjectRequired', 'Tiêu đề email là bắt buộc');
    }

    if (!formData.htmlContent.trim()) {
      errors.htmlContent = t('marketing:email.templates.form.validation.contentRequired', 'Nội dung HTML là bắt buộc');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      const submitData = {
        ...formData,
        variables,
        tags: tags.filter(tag => tag.trim() !== ''),
      };



      await createTemplate.mutateAsync(submitData);
      onSuccess?.();
    } catch {
      // Error được handle trong hook
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string | number | string[] | number[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle email builder content change
  const handleEmailBuilderChange = useCallback((html: string) => {
    setFormData(prev => ({ ...prev, htmlContent: html }));
  }, []);

  // Variable management
  const addVariable = () => {
    setVariables(prev => [...prev, {
      name: '',
      type: 'TEXT' as const,
      defaultValue: '',
      required: false,
      description: '',
    }]);
  };

  const removeVariable = (index: number) => {
    setVariables(prev => prev.filter((_, i) => i !== index));
  };

  const updateVariable = (index: number, field: string, value: string | boolean | number | string[] | number[]) => {
    setVariables(prev => prev.map((variable, i) =>
      i === index ? { ...variable, [field]: value } : variable
    ));
  };

  const templateTypes = [
    { value: EmailTemplateType.NEWSLETTER, label: t('marketing:email.templates.types.newsletter', 'Newsletter') },
    { value: EmailTemplateType.PROMOTIONAL, label: t('marketing:email.templates.types.promotional', 'Khuyến mãi') },
    { value: EmailTemplateType.TRANSACTIONAL, label: t('marketing:email.templates.types.transactional', 'Giao dịch') },
    { value: EmailTemplateType.WELCOME, label: t('marketing:email.templates.types.welcome', 'Chào mừng') },
    { value: EmailTemplateType.ABANDONED_CART, label: t('marketing:email.templates.types.abandonedCart', 'Giỏ hàng bỏ quên') },
    { value: EmailTemplateType.FOLLOW_UP, label: t('marketing:email.templates.types.followUp', 'Theo dõi') },
  ];

  const variableTypes = [
    { value: 'TEXT', label: t('marketing:email.templates.variableTypes.text', 'Văn bản') },
    { value: 'NUMBER', label: t('marketing:email.templates.variableTypes.number', 'Số') },
    { value: 'DATE', label: t('marketing:email.templates.variableTypes.date', 'Ngày tháng') },
    { value: 'URL', label: t('marketing:email.templates.variableTypes.url', 'Đường dẫn') },
    { value: 'IMAGE', label: t('marketing:email.templates.variableTypes.image', 'Hình ảnh') },
  ];

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message={t('marketing:email.templates.form.instructions.title', 'Hướng dẫn tạo Email Template')}
        description={t('marketing:email.templates.form.instructions.description', 'Sử dụng Email Builder để tạo nội dung email đẹp mắt. Thêm biến động bằng cú pháp {variable_name}. Preview text sẽ hiển thị trong inbox preview.')}
      />

      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
        <div className="space-y-6">
          {/* Basic Info */}
          <Card title={t('marketing:email.templates.form.basicInfo.title', 'Thông tin cơ bản')}>
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.templates.form.name.label', 'Tên Template')}
                name="name"
                required
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:email.templates.form.name.placeholder', 'Ví dụ: Newsletter tháng 1, Khuyến mãi Black Friday...')}
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.templates.form.type.label', 'Loại Template')}
                name="type"
                required
              >
                <Select
                  fullWidth
                  value={formData.type}
                  onChange={(value) => handleInputChange('type', value)}
                  options={templateTypes}
                  placeholder={t('marketing:email.templates.form.type.placeholder', 'Chọn loại template')}
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.templates.form.subject.label', 'Tiêu đề Email')}
                name="subject"
                required
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:email.templates.form.subject.placeholder', 'Ví dụ: 🎉 Khuyến mãi đặc biệt dành cho bạn!')}
                  value={formData.subject}
                  onChange={(e) => handleInputChange('subject', e.target.value)}
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.templates.form.previewText.label', 'Preview Text')}
                name="previewText"
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:email.templates.form.previewText.placeholder', 'Ví dụ: Đừng bỏ lỡ ưu đãi lên đến 50% cho tất cả sản phẩm...')}
                  value={formData.previewText || ''}
                  onChange={(e) => handleInputChange('previewText', e.target.value)}
                />
              </FormItem>
            </div>
          </Card>

          {/* Content Editor */}
          <Card
            title={t('marketing:email.templates.form.content.title', 'Nội dung Email')}
            extra={
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={previewMode === 'design' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewMode('design')}
                >
                  <Eye className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.designMode', 'Design')}
                </Button>
                <Button
                  type="button"
                  variant={previewMode === 'code' ? 'primary' : 'outline'}
                  size="sm"
                  onClick={() => setPreviewMode('code')}
                >
                  <Code className="h-4 w-4 mr-1" />
                  {t('marketing:email.templates.form.content.codeMode', 'HTML')}
                </Button>
              </div>
            }
          >
            {previewMode === 'code' ? (
              <FormItem
                label={t('marketing:email.templates.form.htmlContent.label', 'HTML Content')}
                name="htmlContent"
                required
              >
                <textarea
                  className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  placeholder={t('marketing:email.templates.form.htmlContent.placeholder', '<!DOCTYPE html>\n<html>\n<head>\n  <title>Email Template</title>\n</head>\n<body>\n  <h1>Xin chào {customer_name}!</h1>\n  <p>Cảm ơn bạn đã đăng ký newsletter của chúng tôi.</p>\n</body>\n</html>')}
                  value={formData.htmlContent}
                  onChange={(e) => handleInputChange('htmlContent', e.target.value)}
                />
              </FormItem>
            ) : (
              <div className="border border-border rounded-lg overflow-hidden min-h-[500px] bg-card">
                <EmailBuilder
                  initialValue={formData.htmlContent}
                  onContentChange={handleEmailBuilderChange}
                />
              </div>
            )}

            <FormItem
              label={t('marketing:email.templates.form.textContent.label', 'Text Content (Tùy chọn)')}
              name="textContent"
            >
              <textarea
                className="w-full p-3 border border-border rounded-md min-h-[120px] bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                placeholder={t('marketing:email.templates.form.textContent.placeholder', 'Xin chào {customer_name}!\n\nCảm ơn bạn đã đăng ký newsletter của chúng tôi...')}
                value={formData.textContent || ''}
                onChange={(e) => handleInputChange('textContent', e.target.value)}
              />
            </FormItem>
          </Card>

          {/* Variables */}
          <Card
            title={t('marketing:email.templates.form.variables.title', 'Biến động')}
            extra={
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addVariable}
                className="gap-2"
              >
                <Plus className="h-4 w-4" />
                {t('marketing:email.templates.form.variables.addButton', 'Thêm biến')}
              </Button>
            }
          >
            {variables.length > 0 && (
              <div className="space-y-4">
                {variables.map((variable, index) => (
                  <div key={index} className="space-y-4 p-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem
                        label={t('marketing:email.templates.form.variables.name', 'Tên biến')}
                        name={`variable-name-${index}`}
                        required
                      >
                        <Input
                          fullWidth
                          placeholder="customer_name"
                          value={variable.name}
                          onChange={(e) => updateVariable(index, 'name', e.target.value)}
                        />
                      </FormItem>
                      <FormItem
                        label={t('marketing:email.templates.form.variables.type', 'Loại')}
                        name={`variable-type-${index}`}
                        required
                      >
                        <Select
                          fullWidth
                          value={variable.type}
                          onChange={(value) => updateVariable(index, 'type', value)}
                          options={variableTypes}
                          placeholder={t('marketing:email.templates.form.variables.typePlaceholder', 'Chọn loại')}
                        />
                      </FormItem>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItem
                        label={t('marketing:email.templates.form.variables.defaultValue', 'Giá trị mặc định')}
                        name={`variable-default-${index}`}
                      >
                        <Input
                          fullWidth
                          placeholder={t('marketing:email.templates.form.variables.defaultValuePlaceholder', 'Khách hàng')}
                          value={variable.defaultValue}
                          onChange={(e) => updateVariable(index, 'defaultValue', e.target.value)}
                        />
                      </FormItem>
                      <FormItem
                        label={t('marketing:email.templates.form.variables.description', 'Mô tả')}
                        name={`variable-description-${index}`}
                      >
                        <Input
                          fullWidth
                          placeholder={t('marketing:email.templates.form.variables.descriptionPlaceholder', 'Tên khách hàng')}
                          value={variable.description}
                          onChange={(e) => updateVariable(index, 'description', e.target.value)}
                        />
                      </FormItem>
                    </div>
                    <div className="flex justify-end">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeVariable(index)}
                        className="text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Tags */}
          <Card
            title={t('marketing:email.templates.form.tags.title', 'Tags')}
          >
            <FormItem
              label={t('marketing:email.templates.form.tags.label', 'Tags')}
              name="tags"
            >
              <div className="space-y-2">
                <Input
                  fullWidth
                  placeholder={t('marketing:email.templates.form.tags.placeholder', 'Nhập tag và nhấn Enter')}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      e.preventDefault();
                      e.stopPropagation();

                      const value = e.currentTarget.value.trim();
                      if (value && !tags.includes(value)) {
                        const newTags = [...tags, value];
                        setTags(newTags);
                        e.currentTarget.value = '';
                      }
                    }
                  }}
                />
                <div className="flex flex-wrap gap-1 mt-2">
                  {tags.map((tag, index) => (
                    <Chip
                      key={`tag-${index}-${tag}`}
                      size="sm"
                      closable
                      onClose={() => {
                        const newTags = tags.filter(t => t !== tag);
                        setTags(newTags);
                      }}
                    >
                      {tag}
                    </Chip>
                  ))}
                </div>
              </div>
            </FormItem>
          </Card>

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onCancel?.()}
              disabled={createTemplate.isPending}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              disabled={createTemplate.isPending}
            >
              {createTemplate.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {t('marketing:email.templates.form.submitButton', 'Tạo Template')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}
