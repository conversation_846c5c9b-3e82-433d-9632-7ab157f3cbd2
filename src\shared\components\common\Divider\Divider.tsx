/**
 * Component tạo đường phân cách
 */
import React from 'react';

export interface DividerProps {
  /**
   * Hướng của divider
   */
  orientation?: 'horizontal' | 'vertical';

  /**
   * <PERSON><PERSON> dày của divider
   */
  thickness?: 'thin' | 'medium' | 'thick';

  /**
   * <PERSON><PERSON><PERSON> sắc của divider
   */
  color?: 'default' | 'primary' | 'light' | 'dark';

  /**
   * Khoảng cách xung quanh divider
   */
  spacing?: 'none' | 'small' | 'medium' | 'large';

  /**
   * Nội dung hiển thị ở giữa divider
   */
  children?: React.ReactNode;

  /**
   * Vị trí của nội dung
   */
  textPosition?: 'left' | 'center' | 'right';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component tạo đường phân cách
 */
const Divider: React.FC<DividerProps> = ({
  orientation = 'horizontal',
  thickness = 'thin',
  color = 'default',
  spacing = 'medium',
  children,
  textPosition = 'center',
  className = '',
}) => {
  // Xác định class dựa trên orientation
  const orientationClasses = {
    horizontal: 'w-full',
    vertical: 'h-full',
  }[orientation];

  // Xác định class dựa trên thickness
  const thicknessClasses = {
    thin: orientation === 'horizontal' ? 'border-t' : 'border-l',
    medium: orientation === 'horizontal' ? 'border-t-2' : 'border-l-2',
    thick: orientation === 'horizontal' ? 'border-t-4' : 'border-l-4',
  }[thickness];

  // Xác định class dựa trên color
  const colorClasses = {
    default: 'border-gray-200 dark:border-gray-700',
    primary: 'border-primary',
    light: 'border-gray-100',
    dark: 'border-gray-800',
  }[color];

  // Xác định class dựa trên spacing
  const spacingClasses = {
    none: 'my-0',
    small: orientation === 'horizontal' ? 'my-2' : 'mx-2',
    medium: orientation === 'horizontal' ? 'my-4' : 'mx-4',
    large: orientation === 'horizontal' ? 'my-6' : 'mx-6',
  }[spacing];

  // Nếu có children, hiển thị divider với text
  if (children && orientation === 'horizontal') {
    // Xác định class dựa trên textPosition
    const textPositionClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
    }[textPosition];

    return (
      <div className={`flex items-center ${spacingClasses} ${className}`}>
        <div className={`flex-grow ${thicknessClasses} ${colorClasses}`} />
        <span className={`px-3 text-sm text-gray-500 dark:text-gray-400 ${textPositionClasses}`}>
          {children}
        </span>
        <div className={`flex-grow ${thicknessClasses} ${colorClasses}`} />
      </div>
    );
  }

  // Divider thông thường
  return (
    <div
      className={`${orientationClasses} ${thicknessClasses} ${colorClasses} ${spacingClasses} ${className}`}
      role="separator"
      aria-orientation={orientation}
    />
  );
};

export default Divider;
