/**
 * Hook quản lý các hàm xử lý cho ActiveFilters component
 * @module useActiveFilters
 */
import { useCallback } from 'react';
import { SortOrder } from '@/shared/components/common/Table/types';
import { TFunction } from 'i18next';

// Các hàm chuyển đổi đã được chuyển vào component nơi cần sử dụng

/**
 * Interface cho các tham số đầu vào của hook useActiveFilters
 */
export interface UseActiveFiltersParams<T = string | number | boolean | undefined> {
  /**
   * Hàm xử lý tìm kiếm
   */
  handleSearch: (term: string) => void;

  /**
   * Hàm xử lý thay đổi filter
   */
  setSelectedFilterId: (id: string) => void;

  /**
   * Hàm xử lý thay đổi khoảng thời gian
   */
  setDateRange: (range: [Date | null, Date | null]) => void;

  /**
   * Hàm xử lý thay đổi sắp xếp
   */
  handleSortChange: (sortBy: string | null, sortDirection: SortOrder) => void;

  /**
   * Gi<PERSON> trị filter hiện tại
   */
  selectedFilterValue: T;

  /**
   * Map giá trị filter với nhãn hiển thị
   */
  filterValueLabelMap?: Record<string, string>;

  /**
   * Hàm dịch
   */
  t: TFunction;
}

/**
 * Hook quản lý các hàm xử lý cho ActiveFilters component
 * @template T Kiểu dữ liệu của giá trị filter
 * @param params Tham số đầu vào
 * @returns Các hàm xử lý và nhãn filter
 */
export function useActiveFilters<T = string | number | boolean | undefined>(
  params: UseActiveFiltersParams<T>
) {
  const {
    handleSearch,
    setSelectedFilterId,
    setDateRange,
    handleSortChange,
    selectedFilterValue,
    filterValueLabelMap = {},
    t,
  } = params;

  /**
   * Xóa tìm kiếm
   */
  const handleClearSearch = useCallback(() => {
    handleSearch('');
  }, [handleSearch]);

  /**
   * Xóa filter
   */
  const handleClearFilter = useCallback(() => {
    setSelectedFilterId('all');
  }, [setSelectedFilterId]);

  /**
   * Xóa khoảng thời gian
   */
  const handleClearDateRange = useCallback(() => {
    setDateRange([null, null]);
  }, [setDateRange]);

  /**
   * Xóa sắp xếp
   */
  const handleClearSort = useCallback(() => {
    handleSortChange(null, null);
  }, [handleSortChange]);

  /**
   * Xóa tất cả
   */
  const handleClearAll = useCallback(() => {
    handleClearSearch();
    handleClearFilter();
    handleClearDateRange();
    handleClearSort();
  }, [handleClearSearch, handleClearFilter, handleClearDateRange, handleClearSort]);

  /**
   * Lấy nhãn cho filter
   */
  const getFilterLabel = useCallback(() => {
    // Nếu có trong map, trả về giá trị từ map
    if (
      (selectedFilterValue !== undefined &&
        selectedFilterValue !== null &&
        typeof selectedFilterValue === 'string') ||
      typeof selectedFilterValue === 'number'
    ) {
      const key = String(selectedFilterValue);
      if (filterValueLabelMap[key]) {
        return filterValueLabelMap[key];
      }
    }

    // Nếu là chuỗi, thử dịch
    if (typeof selectedFilterValue === 'string') {
      return t(`common:${selectedFilterValue}`, selectedFilterValue);
    }

    // Trả về giá trị dưới dạng chuỗi
    return selectedFilterValue !== undefined && selectedFilterValue !== null
      ? String(selectedFilterValue)
      : '';
  }, [selectedFilterValue, filterValueLabelMap, t]);

  return {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  };
}
