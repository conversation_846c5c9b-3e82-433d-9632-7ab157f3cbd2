import React from 'react';
import { StepNavigationProps } from './StepNavigation.types';
import { Button, Icon } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';

/**
 * StepNavigation component điều hướng giữa các bước
 *
 * @example
 * ```tsx
 * <StepNavigation
 *   currentStep={1}
 *   totalSteps={3}
 *   onNext={handleNext}
 *   onPrevious={handlePrevious}
 *   onSubmit={handleSubmit}
 *   isNextDisabled={!isValid}
 *   position="bottom"
 *   align="between"
 * />
 * ```
 */
const StepNavigation: React.FC<StepNavigationProps> = ({
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSubmit,
  isNextDisabled = false,
  isPreviousDisabled = false,
  isSubmitDisabled = false,
  showPrevious = true,
  showNext = true,
  showSubmit = true,
  nextLabel,
  previousLabel,
  submitLabel,
  nextIcon,
  previousIcon,
  submitIcon,
  position = 'bottom',
  align = 'between',
  className = '',
}) => {
  const { t } = useTranslation();

  // Kiểm tra xem có phải bước đầu tiên không
  const isFirstStep = currentStep === 0;

  // Kiểm tra xem có phải bước cuối cùng không
  const isLastStep = currentStep === totalSteps - 1;

  // Lấy label cho các nút
  const getNextLabel = () => nextLabel || t('common.next');
  const getPreviousLabel = () => previousLabel || t('common.previous');
  const getSubmitLabel = () => submitLabel || t('common.submit');

  // Lấy icon cho các nút
  const getNextIcon = () => nextIcon || <Icon name="chevron-right" />;
  const getPreviousIcon = () => previousIcon || <Icon name="arrow-left" />;
  const getSubmitIcon = () => submitIcon || <Icon name="check" />;

  // Lấy class cho container
  const getContainerClass = () => {
    let positionClass = '';
    let alignClass = '';

    // Position class
    switch (position) {
      case 'top':
        positionClass = 'mb-6';
        break;
      case 'bottom':
        positionClass = 'mt-6';
        break;
      case 'left':
        positionClass = 'mr-6';
        break;
      case 'right':
        positionClass = 'ml-6';
        break;
      default:
        positionClass = 'mt-6';
    }

    // Align class
    switch (align) {
      case 'start':
        alignClass = 'justify-start';
        break;
      case 'center':
        alignClass = 'justify-center';
        break;
      case 'end':
        alignClass = 'justify-end';
        break;
      case 'between':
        alignClass = 'justify-between';
        break;
      case 'around':
        alignClass = 'justify-around';
        break;
      case 'evenly':
        alignClass = 'justify-evenly';
        break;
      default:
        alignClass = 'justify-between';
    }

    return `flex ${position === 'left' || position === 'right' ? 'flex-col' : 'flex-row'} ${alignClass} ${positionClass} ${className}`;
  };

  return (
    <div className={getContainerClass()}>
      {/* Previous button */}
      {showPrevious && !isFirstStep && (
        <Button
          variant="outline"
          onClick={onPrevious}
          disabled={isPreviousDisabled}
          leftIcon={getPreviousIcon()}
        >
          {getPreviousLabel()}
        </Button>
      )}

      {/* Spacer for alignment when previous button is hidden */}
      {(!showPrevious || isFirstStep) && align === 'between' && <div></div>}

      {/* Next/Submit button */}
      {isLastStep
        ? showSubmit && (
            <Button
              variant="primary"
              onClick={onSubmit}
              disabled={isSubmitDisabled}
              rightIcon={getSubmitIcon()}
            >
              {getSubmitLabel()}
            </Button>
          )
        : showNext && (
            <Button
              variant="primary"
              onClick={onNext}
              disabled={isNextDisabled}
              rightIcon={getNextIcon()}
            >
              {getNextLabel()}
            </Button>
          )}
    </div>
  );
};

export default StepNavigation;
