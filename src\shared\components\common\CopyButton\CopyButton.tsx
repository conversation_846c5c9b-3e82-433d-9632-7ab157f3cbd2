import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button, Icon, Tooltip } from '@/shared/components/common';

export interface CopyButtonProps {
  /**
   * Nội dung cần sao chép
   */
  text: string;

  /**
   * <PERSON><PERSON>ch thước của nút
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Variant của nút
   */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback được gọi sau khi sao chép thành công
   */
  onCopied?: () => void;

  /**
   * Thời gian hiển thị thông báo đã sao chép (ms)
   */
  notificationDuration?: number;
}

/**
 * Component nút sao chép với tooltip thông báo
 */
const CopyButton: React.FC<CopyButtonProps> = ({
  text,
  size = 'md',
  variant = 'outline',
  className = '',
  onCopied,
  notificationDuration = 2000,
}) => {
  const { t } = useTranslation();
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(text);
    setCopied(true);

    if (onCopied) {
      onCopied();
    }

    setTimeout(() => {
      setCopied(false);
    }, notificationDuration);
  };

  return (
    <Tooltip content={copied ? t('common.copied', 'Đã sao chép') : t('common.copy', 'Sao chép')}>
      <Button variant={variant} onClick={handleCopy} className={className}>
        <Icon name="copy" size={size} />
      </Button>
    </Tooltip>
  );
};

export default CopyButton;
