import React from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, Button, Icon, Typography } from '../index';

export interface DeleteConfirmModalProps {
  /**
   * Trạng thái hiển thị của modal
   */
  isOpen: boolean;

  /**
   * Callback khi đóng modal
   */
  onClose: () => void;

  /**
   * Callback khi xác nhận xóa
   */
  onConfirm: () => void;

  /**
   * Tiêu đề của modal
   * @default 'Xác nhận xóa'
   */
  title?: string;

  /**
   * Nội dung thông báo xác nhận
   * @default 'Bạn có chắc chắn muốn xóa mục này?'
   */
  message?: string;

  /**
   * Nội dung chi tiết (hiển thị dưới message)
   */
  description?: string;

  /**
   * Tên của mục cần xóa (sẽ được hiển thị trong message)
   */
  itemName?: string;

  /**
   * Văn bản nút hủy
   * @default 'Hủy'
   */
  cancelText?: string;

  /**
   * Văn bản nút xác nhận
   * @default 'Xóa'
   */
  confirmText?: string;

  /**
   * Kích thước của modal
   * @default 'sm'
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';

  /**
   * Trạng thái loading của nút xác nhận
   * @default false
   */
  isLoading?: boolean;

  /**
   * Hiển thị icon cảnh báo
   * @default true
   */
  showIcon?: boolean;
}

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  description,
  itemName,
  cancelText,
  confirmText,
  size = 'sm',
  isLoading = false,
  showIcon = true,
}) => {
  const { t } = useTranslation();

  // Sử dụng giá trị mặc định từ i18n nếu không có giá trị được truyền vào
  const modalTitle = title || t('common.confirmDelete', 'Xác nhận xóa');
  const modalMessage =
    message ||
    (itemName
      ? t('common.confirmDeleteWithName', 'Bạn có chắc chắn muốn xóa "{{itemName}}"?', { itemName })
      : t('common.confirmDeleteItem', 'Bạn có chắc chắn muốn xóa mục này?'));
  const modalCancelText = cancelText || t('common.cancel', 'Hủy');
  const modalConfirmText = confirmText || t('common.delete', 'Xóa');

  // Tạo footer tùy chỉnh
  const customFooter = (
    <div className="flex justify-end space-x-3">
      <Button variant="outline" onClick={onClose} disabled={isLoading}>
        {modalCancelText}
      </Button>
      <Button variant="danger" onClick={onConfirm} isLoading={isLoading}>
        {modalConfirmText}
      </Button>
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={modalTitle}
      size={size}
      footer={customFooter} // Truyền footer tùy chỉnh
    >
      <div className="p-4">
        <div className="flex items-start mb-4">
          {showIcon && (
            <div className="flex-shrink-0 mr-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-red-100 dark:bg-red-900/20">
                <Icon name="warning" size="md" className="text-red-500 dark:text-red-400" />
              </div>
            </div>
          )}
          <div>
            <Typography variant="body1" className="mb-2">
              {modalMessage}
            </Typography>
            {description && (
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                {description}
              </Typography>
            )}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteConfirmModal;
