import { useMemo } from 'react';
import { CONTAINER_MAX_WIDTHS } from '@/shared/constants/breakpoints.ts';
import { useCurrentBreakpoint } from './useMediaQuery.ts';

/**
 * Hook to get the container width based on the current breakpoint
 * Returns the max width for the current breakpoint
 *
 * @param fluid If true, returns '100%' for all breakpoints
 * @returns The container width for the current breakpoint
 *
 * @example
 * const containerWidth = useContainerWidth();
 * // Returns '640px' on sm, '768px' on md, etc.
 *
 * @example
 * const fluidContainerWidth = useContainerWidth(true);
 * // Returns '100%' on all breakpoints
 */
function useContainerWidth(fluid: boolean = false): string {
  const currentBreakpoint = useCurrentBreakpoint();

  return useMemo(() => {
    if (fluid) {
      return '100%';
    }

    // For xs breakpoint, use 100%
    if (currentBreakpoint === 'xs') {
      return '100%';
    }

    // For other breakpoints, use the max width from constants
    return CONTAINER_MAX_WIDTHS[currentBreakpoint];
  }, [currentBreakpoint, fluid]);
}

export default useContainerWidth;
