import React, { useState } from 'react';
import { Button, Input, Typography } from '@/shared/components/common';
import { Asset, AssetCategory } from '../types';
import { INITIAL_ASSETS } from '../constants';
import {
  Upload as UploadIcon,
  Search as SearchIcon,
  Folder as FolderIcon,
  Check
} from 'lucide-react';

// Kích thước icon
const iconSize = 16;

interface AssetManagerProps {
  onSelectAsset: (asset: Asset) => void;
  onUploadAsset?: (file: File) => Promise<void>;
  assets?: Asset[];
}

const AssetManager: React.FC<AssetManagerProps> = ({
  onSelectAsset,
  onUploadAsset,
  assets = INITIAL_ASSETS
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedAssets, setSelectedAssets] = useState<string[]>([]);

  // Tạ<PERSON> danh sách các danh mục từ assets
  const categories: AssetCategory[] = [
    { id: 'all', name: '<PERSON><PERSON><PERSON> c<PERSON>', count: assets.length },
    ...Array.from(
      new Set(assets.filter(a => a.category).map(a => a.category))
    ).map(category => ({
      id: category || 'uncategorized',
      name: category || 'Chưa phân loại',
      count: assets.filter(a => a.category === category).length
    }))
  ];

  // Lọc assets theo danh mục và từ khóa tìm kiếm
  const filteredAssets = assets.filter(asset => {
    const matchesCategory = selectedCategory === 'all' || asset.category === selectedCategory;
    const matchesSearch = !searchQuery ||
      asset.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      asset.alt?.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // Xử lý khi chọn asset
  const handleSelectAsset = (asset: Asset) => {
    if (selectedAssets.includes(asset.id)) {
      setSelectedAssets(selectedAssets.filter(id => id !== asset.id));
    } else {
      setSelectedAssets([...selectedAssets, asset.id]);
    }
    onSelectAsset(asset);
  };

  // Xử lý khi upload file
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0 && onUploadAsset) {
      onUploadAsset(files[0] as File);
    }
  };

  return (
    <div className="h-full overflow-y-auto p-4">
      <div className="mb-4">
        <Typography variant="h3" className="text-lg font-medium mb-2">Quản lý tài nguyên</Typography>

        <div className="flex items-center mb-4">
          <div className="relative flex-1">
            <span className="absolute left-2 top-2.5 text-gray-400"><SearchIcon size={iconSize} /></span>
            <Input
              placeholder="Tìm kiếm tài nguyên..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>

          <div className="ml-2">
            <label className="cursor-pointer">
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button variant="outline" className="flex items-center">
                <span className="mr-1"><UploadIcon size={iconSize} /></span>
                Tải lên
              </Button>
            </label>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mb-4">
          {categories.map(category => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? 'primary' : 'outline'}
              onClick={() => setSelectedCategory(category.id)}
              className="text-xs"
            >
              <span className="mr-1"><FolderIcon size={iconSize} /></span>
              {category.name} ({category.count})
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {filteredAssets.map(asset => (
          <div
            key={asset.id}
            className={`cursor-pointer border rounded-md overflow-hidden hover:bg-gray-50 ${
              selectedAssets.includes(asset.id) ? 'border-blue-500 ring-2 ring-blue-200' : ''
            }`}
            onClick={() => handleSelectAsset(asset)}
          >
            <div className="relative">
              <img
                src={asset.src}
                alt={asset.alt || asset.name || 'Asset'}
                className="w-full h-auto object-cover aspect-video"
              />
              {selectedAssets.includes(asset.id) && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                  <Check size={12} />
                </div>
              )}
            </div>
            <div className="p-2">
              <Typography variant="body2" className="text-xs truncate">
                {asset.name || 'Untitled'}
              </Typography>
            </div>
          </div>
        ))}
      </div>

      {filteredAssets.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Typography variant="body1">Không tìm thấy tài nguyên nào</Typography>
        </div>
      )}
    </div>
  );
};

export default AssetManager;
