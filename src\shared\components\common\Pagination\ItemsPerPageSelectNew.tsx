import React, { useState, useRef, useEffect } from 'react';
import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

export interface ItemsPerPageSelectNewProps {
  /**
   * Giá trị hiện tại
   */
  value: number;

  /**
   * Các tùy chọn có sẵn
   */
  options: number[];

  /**
   * Sự kiện khi giá trị thay đổi
   */
  onChange: (value: number) => void;

  /**
   * Vô hiệu hóa bộ chọn
   */
  disabled?: boolean;

  /**
   * Kích thước của bộ chọn
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Nhãn tùy chỉnh cho selector
   */
  labelText?: string;
}

/**
 * Component bộ chọn số lượng mục trên mỗi trang (phiên bản mới)
 */
const ItemsPerPageSelectNew: React.FC<ItemsPerPageSelectNewProps> = ({
  value,
  options,
  onChange,
  disabled = false,
  size = 'md',
  className = '',
  labelText,
}) => {
  // Sử dụng hook translation
  const { t } = useTranslation(['common', 'pagination']);
  
  // Sử dụng hook theme
  useTheme();

  // State cho dropdown
  const [isOpen, setIsOpen] = useState(false);

  // Refs
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Xác định các lớp kích thước
  const sizeClasses = {
    sm: 'h-7 text-xs',
    md: 'h-9 text-sm',
    lg: 'h-11 text-base',
  }[size];

  // Xác định kích thước icon
  const iconSize = {
    sm: 'xs',
    md: 'sm',
    lg: 'md',
  }[size] as 'xs' | 'sm' | 'md';

  // Xử lý sự kiện click bên ngoài để đóng dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    // Xử lý sự kiện scroll để đóng dropdown khi scroll bên ngoài dropdown
    const handleScroll = (event: Event) => {
      if (
        event.target instanceof Node &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    // Xử lý sự kiện resize để đóng dropdown
    const handleResize = () => {
      setIsOpen(false);
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  // Xử lý sự kiện thay đổi giá trị
  const handleChange = (option: number) => {
    console.log(`ItemsPerPageSelectNew handleChange: option=${option}, current value=${value}`);
    onChange(option);
    setIsOpen(false);
  };

  // Tính toán vị trí dropdown
  const getDropdownPosition = () => {
    if (!buttonRef.current) return {};

    const rect = buttonRef.current.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    // Ước tính chiều cao của dropdown
    const dropdownHeight = options.length * 36; // 36px cho mỗi option

    // Kiểm tra xem có đủ không gian bên dưới không
    const spaceBelow = windowHeight - rect.bottom;
    const showAbove = spaceBelow < dropdownHeight && rect.top > dropdownHeight;

    console.log(
      `Dropdown position: spaceBelow=${spaceBelow}, dropdownHeight=${dropdownHeight}, showAbove=${showAbove}`
    );

    if (showAbove) {
      // Hiển thị dropdown phía trên nếu không đủ không gian bên dưới
      return {
        position: 'fixed' as const,
        bottom: windowHeight - rect.top + 5, // +5 để tạo khoảng cách
        left: rect.left,
        width: rect.width,
        zIndex: 9999,
      };
    } else {
      // Hiển thị dropdown phía dưới (mặc định)
      return {
        position: 'fixed' as const,
        top: rect.bottom + 5, // +5 để tạo khoảng cách
        left: rect.left,
        width: rect.width,
        zIndex: 9999,
      };
    }
  };

  // Render dropdown
  const renderDropdown = () => {
    if (!isOpen) return null;

    return createPortal(
      <div
        ref={dropdownRef}
        style={getDropdownPosition()}
        className="bg-card shadow-lg max-h-60 overflow-y-auto animate-fadeIn rounded-md"
      >
        {options.map(option => (
          <button
            key={option}
            className={`
              block w-full text-center px-3 py-2 ${sizeClasses}
              ${option === value ? 'bg-card-muted font-medium' : ''}
              hover:bg-card-muted text-foreground
              transition-colors duration-200
            `}
            onClick={() => handleChange(option)}
            aria-label={t('pagination:itemsPerPage', { count: option })}
          >
            {option}
          </button>
        ))}
      </div>,
      document.body
    );
  };

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        ref={buttonRef}
        className={`
          flex items-center justify-center px-3
          rounded-md bg-card text-foreground
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-card-muted hover:shadow-sm'}
          transition-all duration-200 ${sizeClasses}
        `}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        type="button"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={labelText || t('pagination:selectItemsPerPage')}
      >
        <span className="mr-1">{value}</span>
        <Icon name="chevron-down" size={iconSize} />
      </button>

      {renderDropdown()}
    </div>
  );
};

export default ItemsPerPageSelectNew;
