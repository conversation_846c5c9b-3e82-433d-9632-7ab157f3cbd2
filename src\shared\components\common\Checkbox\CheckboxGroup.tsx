import React from 'react';
import Checkbox, { CheckboxVariant } from './Checkbox';
import {
  FormControlSize,
  FormControlColor,
} from '@/shared/components/common/Form/utils/formControlUtils';
import { useFormControlGroup } from '@/shared/components/common/Form/hooks/useFormControlGroup';

export interface CheckboxOption {
  /**
   * Nhãn hiển thị cho tùy chọn
   */
  label: string | React.ReactNode;

  /**
   * Giá trị của tùy chọn
   */
  value: string | number | readonly string[];

  /**
   * Vô hiệu hóa tùy chọn này
   */
  disabled?: boolean;
}

export interface CheckboxGroupProps {
  /**
   * Danh sách các tùy chọn
   */
  options: CheckboxOption[];

  /**
   * Giá trị đã chọn
   */
  value?: Array<string | number | readonly string[]>;

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (values: Array<string | number | readonly string[]>) => void;

  /**
   * <PERSON><PERSON> hiệu hóa toàn bộ group
   */
  disabled?: boolean;

  /**
   * CSS classes tùy chỉnh
   */
  className?: string;

  /**
   * Hướng hiển thị: ngang hoặc dọc
   */
  direction?: 'horizontal' | 'vertical';

  /**
   * Kích thước của các checkbox
   */
  size?: FormControlSize;

  /**
   * Biến thể của checkbox
   */
  variant?: CheckboxVariant;

  /**
   * Màu sắc của checkbox
   */
  color?: FormControlColor;

  /**
   * Tên của group, dùng khi submit form
   */
  name?: string;
}

/**
 * Component CheckboxGroup cho phép người dùng chọn nhiều tùy chọn từ một danh sách
 */
const CheckboxGroup: React.FC<CheckboxGroupProps> = ({
  options = [],
  value = [],
  onChange,
  disabled = false,
  className = '',
  direction = 'vertical',
  size = 'md',
  variant = 'filled',
  color = 'primary',
  name = '',
}) => {
  // Sử dụng custom hook để xử lý logic chung
  const {
    selectedValue: selectedValues,
    handleItemChange,
    selectedClass,
  } = useFormControlGroup<Array<string | number | readonly string[]>>({
    value: value || [],
    onChange: onChange || (() => {}),
    disabled,
    direction,
  });

  // Xử lý khi một checkbox thay đổi
  const handleCheckboxChange = (
    optionValue: string | number | readonly string[],
    checked: boolean
  ) => {
    handleItemChange(optionValue, checked);
  };

  return (
    <div className={`${selectedClass} ${className}`}>
      {options.map((option, index) => (
        <Checkbox
          key={index}
          label={option.label}
          value={option.value}
          checked={selectedValues?.includes(option.value)}
          onChange={checked => handleCheckboxChange(option.value, checked)}
          disabled={disabled || option.disabled || false}
          size={size}
          variant={variant}
          color={color}
          name={name}
        />
      ))}
    </div>
  );
};

export default CheckboxGroup;
