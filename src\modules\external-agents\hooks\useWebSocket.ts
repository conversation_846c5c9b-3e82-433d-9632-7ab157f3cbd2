import { useEffect, useState, useRef, useCallback } from 'react';
import { websocketService } from '../services/websocketService';
import { WebSocketMessage } from '../types/api';

// Hook for WebSocket connection management
export const useWebSocket = (autoConnect = true) => {
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'open' | 'closing' | 'closed'>('closed');
  const [error, setError] = useState<Error | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const connect = useCallback(async () => {
    try {
      setError(null);
      setConnectionStatus('connecting');
      await websocketService.connect();
      setConnectionStatus('open');
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Connection failed'));
      setConnectionStatus('closed');
    }
  }, []);

  const disconnect = useCallback(() => {
    websocketService.disconnect();
    setConnectionStatus('closed');
  }, []);

  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Store the current ref value at effect creation time
    const timeoutRef = reconnectTimeoutRef;

    return () => {
      const currentTimeout = timeoutRef.current;
      if (currentTimeout) {
        clearTimeout(currentTimeout);
      }
      disconnect();
    };
  }, [autoConnect, connect, disconnect]);

  useEffect(() => {
    // Monitor connection status
    const checkStatus = () => {
      setConnectionStatus(websocketService.getStatus());
    };

    const interval = setInterval(checkStatus, 1000);
    checkStatus(); // Initial check

    return () => clearInterval(interval);
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (connectionStatus === 'open') {
      websocketService.send(message);
    } else {
      console.warn('WebSocket is not connected');
    }
  }, [connectionStatus]);

  const subscribe = useCallback((event: string, callback: (data: unknown) => void) => {
    return websocketService.subscribe(event, callback);
  }, []);

  return {
    connectionStatus,
    isConnected: connectionStatus === 'open',
    isConnecting: connectionStatus === 'connecting',
    error,
    connect,
    disconnect,
    sendMessage,
    subscribe,
  };
};

// Hook for subscribing to specific WebSocket events
export const useWebSocketSubscription = <T = unknown>(
  event: string,
  callback: (data: T) => void,
  dependencies: unknown[] = []
) => {
  const [lastMessage, setLastMessage] = useState<T | null>(null);
  const [messageCount, setMessageCount] = useState(0);

  useEffect(() => {
    const wrappedCallback = (data: unknown) => {
      const typedData = data as T;
      setLastMessage(typedData);
      setMessageCount(prev => prev + 1);
      callback(typedData);
    };

    const unsubscribe = websocketService.subscribe(event, wrappedCallback);
    return unsubscribe;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [event, callback, ...dependencies]);

  return {
    lastMessage,
    messageCount,
    resetCount: () => setMessageCount(0),
  };
};

// Hook for WebSocket with automatic reconnection
export const useWebSocketWithReconnect = (
  maxReconnectAttempts = 5,
  reconnectInterval = 5000
) => {
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const webSocket = useWebSocket(false);

  useEffect(() => {
    // Auto-connect on mount
    webSocket.connect();
  }, [webSocket]);

  useEffect(() => {
    // Handle reconnection logic
    if (webSocket.connectionStatus === 'closed' &&
        webSocket.error &&
        reconnectAttempts < maxReconnectAttempts) {

      setIsReconnecting(true);

      const timeout = setTimeout(() => {
        setReconnectAttempts(prev => prev + 1);
        webSocket.connect().finally(() => {
          setIsReconnecting(false);
        });
      }, reconnectInterval);

      return () => clearTimeout(timeout);
    }

    return undefined;
  }, [webSocket, reconnectAttempts, maxReconnectAttempts, reconnectInterval]);

  useEffect(() => {
    // Reset reconnect attempts on successful connection
    if (webSocket.connectionStatus === 'open') {
      setReconnectAttempts(0);
      setIsReconnecting(false);
    }
  }, [webSocket]);

  const manualReconnect = useCallback(() => {
    setReconnectAttempts(0);
    setIsReconnecting(true);
    webSocket.connect().finally(() => {
      setIsReconnecting(false);
    });
  }, [webSocket]);

  return {
    ...webSocket,
    reconnectAttempts,
    maxReconnectAttempts,
    isReconnecting,
    canReconnect: reconnectAttempts < maxReconnectAttempts,
    manualReconnect,
  };
};

// Hook for WebSocket message queue (for offline support)
export const useWebSocketQueue = () => {
  const [messageQueue, setMessageQueue] = useState<WebSocketMessage[]>([]);
  const webSocket = useWebSocket();

  const queueMessage = useCallback((message: WebSocketMessage) => {
    if (webSocket.isConnected) {
      webSocket.sendMessage(message);
    } else {
      setMessageQueue(prev => [...prev, message]);
    }
  }, [webSocket]);

  useEffect(() => {
    // Send queued messages when connection is established
    if (webSocket.isConnected && messageQueue.length > 0) {
      messageQueue.forEach(message => {
        webSocket.sendMessage(message);
      });
      setMessageQueue([]);
    }
  }, [webSocket, messageQueue]);

  const clearQueue = useCallback(() => {
    setMessageQueue([]);
  }, []);

  return {
    ...webSocket,
    queueMessage,
    messageQueue,
    queueLength: messageQueue.length,
    clearQueue,
  };
};
