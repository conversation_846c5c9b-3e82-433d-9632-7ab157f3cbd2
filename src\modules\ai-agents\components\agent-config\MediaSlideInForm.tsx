import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  <PERSON><PERSON>,
  Card,
  Icon,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { Media } from '@/modules/ai-agents/types/response';
import { useMediaList } from '@/modules/data/media/hooks/useMediaQuery';
import { MediaQueryDto } from '@/modules/data/media/types/media.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import React, { useCallback, useEffect, useState } from 'react';

/**
 * Props cho component MediaSlideInForm
 */
interface MediaSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chọn các media
   */
  onSelect: (selectedMedia: Media[]) => void;

  /**
   * Danh sách ID của các media đã chọn
   */
  selectedMediaIds?: string[];
}

/**
 * Component form trượt để chọn các media
 */
const MediaSlideInForm: React.FC<MediaSlideInFormProps> = ({
  isVisible,
  onClose,
  onSelect,
  selectedMediaIds = [],
}) => {
  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedMediaIds);
  const [hasChanges, setHasChanges] = useState<boolean>(false);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<MediaQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: 'createdAt',
    sortDirection: SortDirection.DESC,
  });

  // API hook để lấy danh sách media
  const { data: mediaResponse, isLoading } = useMediaList(queryParams);

  // Lấy dữ liệu từ API response và chuyển đổi sang format Media
  const mediaItems: Media[] = (mediaResponse?.items || []).map(item => ({
    id: item.id,
    name: item.name,
    type: item.viewUrl?.includes('image') ? 'image' as const :
          item.viewUrl?.includes('video') ? 'video' as const :
          item.viewUrl?.includes('audio') ? 'audio' as const : 'document' as const,
    url: item.viewUrl || '',
    thumbnailUrl: item.viewUrl || undefined,
    fileSize: item.size,
    createdAt: new Date(item.createdAt * 1000).toISOString().split('T')[0],
    format: item.name.split('.').pop() || undefined,
  }));

  const totalItems = mediaResponse?.meta?.totalItems || 0;

  // Cấu hình cột cho bảng
  const columns: TableColumn<Media>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'media',
      title: 'Media',
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center mr-3 overflow-hidden">
            {record.thumbnailUrl ? (
              <img
                src={record.thumbnailUrl}
                alt={record.name}
                className="w-full h-full object-cover"
              />
            ) : (
              <Icon
                name={
                  record.type === 'image' ? 'image' :
                  record.type === 'video' ? 'video' :
                  record.type === 'audio' ? 'music' : 'file'
                }
                size="md"
                className="text-gray-500"
              />
            )}
          </div>
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              {record.format && `${record.format.toUpperCase()} • `}
              {record.fileSize && `${(record.fileSize / 1024 / 1024).toFixed(2)} MB`}
              {record.duration && ` • ${Math.floor(record.duration / 60)}:${String(record.duration % 60).padStart(2, '0')}`}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: 'Loại',
      dataIndex: 'type',
      width: '20%',
      render: (_, record) => (
        <span className="capitalize">{record.type}</span>
      ),
    },
    {
      key: 'createdAt',
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      width: '20%',
      render: (_, record) => (
        <span>{new Date(record.createdAt).toLocaleDateString('vi-VN')}</span>
      ),
    },
    {
      key: 'preview',
      title: 'Xem trước',
      width: '20%',
      render: (_, record) => (
        <Button
          variant="outline"
          size="sm"
          onClick={(e) => {
            e.stopPropagation();
            window.open(record.url, '_blank');
          }}
        >
          <Icon name="eye" size="sm" className="mr-1" />
          Xem
        </Button>
      ),
    },
  ];

  // Kiểm tra có thay đổi chưa lưu không
  useEffect(() => {
    const hasUnsavedChanges =
      selectedIds.length !== selectedMediaIds.length ||
      selectedIds.some(id => !selectedMediaIds.includes(id)) ||
      selectedMediaIds.some(id => !selectedIds.includes(id));

    setHasChanges(hasUnsavedChanges);
  }, [selectedIds, selectedMediaIds]);

  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams(prev => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction: SortDirection) => {
    setQueryParams(prev => ({
      ...prev,
      sortBy: column,
      sortDirection: direction,
    }));
  };

  // Xử lý lưu
  const handleSave = async () => {
    setIsSubmitting(true);
    try {
      // Lấy thông tin đầy đủ của các media đã chọn
      const selectedMedia = mediaItems.filter(media =>
        selectedIds.includes(media.id)
      );

      onSelect(selectedMedia);
      onClose();
    } catch (error) {
      console.error('Error saving selected media:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý đóng form
  const handleClose = useCallback(() => {
    if (hasChanges) {
      const confirmed = window.confirm(
        'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn đóng form?'
      );
      if (!confirmed) return;
    }

    setQueryParams(prev => ({ ...prev, search: '' }));
    onClose();
  }, [hasChanges, onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: 'Sắp xếp theo',
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: 'Tên',
      onClick: () => handleSortChange('name', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'sort-date',
      label: 'Ngày tạo',
      onClick: () => handleSortChange('createdAt', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'sort-size',
      label: 'Kích thước',
      onClick: () => handleSortChange('size', queryParams.sortDirection === SortDirection.ASC ? SortDirection.DESC : SortDirection.ASC),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: 'Lọc theo',
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: 'Tất cả',
      onClick: () => setQueryParams(prev => ({ ...prev, status: undefined })),
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">Chọn media</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            Đóng
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<Media>
            columns={columns}
            data={mediaItems}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: queryParams.page || 1,
              pageSize: queryParams.limit || 10,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== (queryParams.limit || 10)) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            Hủy
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || !hasChanges}
          >
            Lưu
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default MediaSlideInForm;
