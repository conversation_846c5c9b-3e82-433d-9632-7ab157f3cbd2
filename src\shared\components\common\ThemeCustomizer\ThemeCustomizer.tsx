import React, { useState, useEffect } from 'react';
import { useTheme } from '@/shared/contexts/theme';

import { useTranslation } from 'react-i18next';

export interface ThemeCustomizerProps {
  /**
   * Hiển thị nút reset
   */
  showReset?: boolean;

  /**
   * Hiển thị nút lưu
   */
  showSave?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Callback khi đóng
   */
  onClose?: () => void;
}

/**
 * Component cho phép người dùng tùy chỉnh theme
 */
const ThemeCustomizer: React.FC<ThemeCustomizerProps> = ({
  showReset = true,
  showSave = true,
  className = '',
  onClose,
}) => {
  const { t } = useTranslation();
  const {
    themeMode,
    currentTheme,
    setThemeMode,
    setCustomTheme,
    saveCurrentTheme,
    resetToDefaultTheme,
  } = useTheme();

  // State cho các màu sắc ch<PERSON>h
  const [primaryColor, setPrimaryColor] = useState(currentTheme.semanticColors.primary);
  const [secondaryColor, setSecondaryColor] = useState(currentTheme.semanticColors.secondary);
  const [backgroundColor, setBackgroundColor] = useState(currentTheme.semanticColors.background);
  const [foregroundColor, setForegroundColor] = useState(currentTheme.semanticColors.foreground);
  const [borderRadius, setBorderRadius] = useState(currentTheme.borderRadius.md);

  // Cập nhật state khi theme thay đổi
  useEffect(() => {
    setPrimaryColor(currentTheme.semanticColors.primary);
    setSecondaryColor(currentTheme.semanticColors.secondary);
    setBackgroundColor(currentTheme.semanticColors.background);
    setForegroundColor(currentTheme.semanticColors.foreground);
    setBorderRadius(currentTheme.borderRadius.md);
  }, [currentTheme]);

  // Xử lý khi thay đổi màu sắc
  const handleColorChange = (
    type: 'primary' | 'secondary' | 'background' | 'foreground',
    color: string
  ) => {
    switch (type) {
      case 'primary':
        setPrimaryColor(color);
        break;
      case 'secondary':
        setSecondaryColor(color);
        break;
      case 'background':
        setBackgroundColor(color);
        break;
      case 'foreground':
        setForegroundColor(color);
        break;
    }

    // Áp dụng thay đổi ngay lập tức
    applyCustomTheme({
      primary: type === 'primary' ? color : primaryColor,
      secondary: type === 'secondary' ? color : secondaryColor,
      background: type === 'background' ? color : backgroundColor,
      foreground: type === 'foreground' ? color : foregroundColor,
      borderRadius: borderRadius,
    });
  };

  // Xử lý khi thay đổi border radius
  const handleBorderRadiusChange = (value: string) => {
    setBorderRadius(value);

    // Áp dụng thay đổi ngay lập tức
    applyCustomTheme({
      primary: primaryColor,
      secondary: secondaryColor,
      background: backgroundColor,
      foreground: foregroundColor,
      borderRadius: value,
    });
  };

  // Áp dụng theme tùy chỉnh
  const applyCustomTheme = ({
    primary,
    secondary,
    background,
    foreground,
    borderRadius,
  }: {
    primary: string;
    secondary: string;
    background: string;
    foreground: string;
    borderRadius: string;
  }) => {
    setCustomTheme({
      semanticColors: {
        primary,
        secondary,
        background,
        foreground,
        muted: foreground + '80', // 50% opacity
        border: foreground + '40', // 25% opacity
        card: background,
        cardMuted: background === '#ffffff' ? '#f8f9fa' : '#1e1e2d',
        accent: primary,
        accentForeground: '#ffffff',
        accentMuted: primary + '20',
        destructive: '#ef4444',
        destructiveForeground: '#ffffff',
        destructiveMuted: '#fee2e2',
        success: '#10b981',
        successForeground: '#ffffff',
        successMuted: '#10b98120',
        warning: '#f59e0b',
        warningForeground: '#ffffff',
        warningMuted: '#f59e0b20',
        error: '#ef4444',
        errorForeground: '#ffffff',
        errorMuted: '#ef444420',
        info: '#3b82f6',
        infoForeground: '#ffffff',
        infoMuted: '#3b82f620',
        primaryForeground: '#ffffff',
        primaryMuted: primary + '20',
        secondaryForeground: '#111827',
        secondaryMuted: secondary + '20',
      },
      borderRadius: {
        none: '0',
        sm: '0.125rem',
        md: borderRadius,
        lg: '0.75rem',
        xl: '1rem',
        '2xl': '1.5rem',
        '3xl': '2rem',
        full: '9999px',
      },
    });
  };

  // Xử lý khi chọn theme có sẵn
  const handleThemeSelect = (mode: 'light' | 'dark') => {
    setThemeMode(mode);
  };

  // Xử lý khi lưu theme
  const handleSave = () => {
    saveCurrentTheme();
  };

  // Xử lý khi reset theme
  const handleReset = () => {
    resetToDefaultTheme();
  };

  return (
    <div className={`p-4 bg-card rounded-lg shadow-md ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">{t('theme.customizer.title', 'Theme Customizer')}</h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-muted hover:text-foreground transition-colors"
            aria-label={t('common.close')}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        )}
      </div>

      <div className="space-y-4">
        {/* Theme Mode Selector */}
        <div>
          <label className="block text-sm font-medium mb-2">
            {t('theme.customizer.mode', 'Theme Mode')}
          </label>
          <div className="flex space-x-2">
            <button
              className={`px-3 py-2 rounded-md ${
                themeMode === 'light'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-card-muted text-muted'
              }`}
              onClick={() => handleThemeSelect('light')}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                />
              </svg>
              {t('common.light')}
            </button>
            <button
              className={`px-3 py-2 rounded-md ${
                themeMode === 'dark'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-card-muted text-muted'
              }`}
              onClick={() => handleThemeSelect('dark')}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                />
              </svg>
              {t('common.dark')}
            </button>
          </div>
        </div>

        {/* Primary Color */}
        <div>
          <label htmlFor="primary-color" className="block text-sm font-medium mb-2">
            {t('theme.customizer.primaryColor', 'Primary Color')}
          </label>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-md mr-2" style={{ backgroundColor: primaryColor }} />
            <input
              id="primary-color"
              type="color"
              value={primaryColor}
              onChange={e => handleColorChange('primary', e.target.value)}
              className="w-full h-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Secondary Color */}
        <div>
          <label htmlFor="secondary-color" className="block text-sm font-medium mb-2">
            {t('theme.customizer.secondaryColor', 'Secondary Color')}
          </label>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-md mr-2" style={{ backgroundColor: secondaryColor }} />
            <input
              id="secondary-color"
              type="color"
              value={secondaryColor}
              onChange={e => handleColorChange('secondary', e.target.value)}
              className="w-full h-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Background Color */}
        <div>
          <label htmlFor="background-color" className="block text-sm font-medium mb-2">
            {t('theme.customizer.backgroundColor', 'Background Color')}
          </label>
          <div className="flex items-center">
            <div
              className="w-8 h-8 rounded-md mr-2 border border-border"
              style={{ backgroundColor: backgroundColor }}
            />
            <input
              id="background-color"
              type="color"
              value={backgroundColor}
              onChange={e => handleColorChange('background', e.target.value)}
              className="w-full h-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Text Color */}
        <div>
          <label htmlFor="text-color" className="block text-sm font-medium mb-2">
            {t('theme.customizer.textColor', 'Text Color')}
          </label>
          <div className="flex items-center">
            <div className="w-8 h-8 rounded-md mr-2" style={{ backgroundColor: foregroundColor }} />
            <input
              id="text-color"
              type="color"
              value={foregroundColor}
              onChange={e => handleColorChange('foreground', e.target.value)}
              className="w-full h-10 cursor-pointer"
            />
          </div>
        </div>

        {/* Border Radius */}
        <div>
          <label htmlFor="border-radius" className="block text-sm font-medium mb-2">
            {t('theme.customizer.borderRadius', 'Border Radius')}
          </label>
          <div className="flex items-center">
            <input
              id="border-radius"
              type="range"
              min="0"
              max="1.5"
              step="0.125"
              value={parseFloat(borderRadius)}
              onChange={e => handleBorderRadiusChange(`${e.target.value}rem`)}
              className="w-full h-2 bg-card-muted rounded-lg appearance-none cursor-pointer"
            />
            <span className="ml-2 text-sm">{borderRadius}</span>
          </div>
          <div className="flex justify-between mt-1 text-xs text-muted">
            <span>0</span>
            <span>0.5rem</span>
            <span>1rem</span>
            <span>1.5rem</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-2 mt-6">
          {showReset && (
            <button
              onClick={handleReset}
              className="px-3 py-2 rounded-md bg-card-muted text-muted hover:text-foreground transition-colors"
            >
              {t('common.reset')}
            </button>
          )}
          {showSave && (
            <button
              onClick={handleSave}
              className="px-3 py-2 rounded-md bg-primary text-primary-foreground"
            >
              {t('common.save')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ThemeCustomizer;
