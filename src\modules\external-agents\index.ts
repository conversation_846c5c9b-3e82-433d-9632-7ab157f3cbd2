// External Agents Module Exports
export * from './types';
export { EXTERNAL_AGENT_QUERY_KEYS } from './constants';
export * from './api';
export * from './services';
export * from './hooks';
export * from './utils';

// Components - Cards
export { default as ExternalAgentCard } from './components/cards/ExternalAgentCard';
export { default as AgentStatusCard } from './components/cards/AgentStatusCard';

// Components - Forms
export { default as ExternalAgentForm } from './components/forms/ExternalAgentForm';
export { default as ProtocolConfigForm } from './components/forms/ProtocolConfigForm';
export { default as AuthenticationForm } from './components/forms/AuthenticationForm';

// Components - Indicators
export { default as StatusIndicator } from './components/indicators/StatusIndicator';
export { default as ProtocolBadge } from './components/indicators/ProtocolBadge';
export { default as CapabilityMatrix } from './components/indicators/CapabilityMatrix';

// Components - Common
export { default as ProtocolSelector } from './components/common/ProtocolSelector';
export { default as ConnectionTester } from './components/common/ConnectionTester';
export { default as PerformanceChart } from './components/common/PerformanceChart';
export { default as MessageLogTable } from './components/common/MessageLogTable';

// Components - Modals
export { default as ProtocolDetectionModal } from './components/modals/ProtocolDetectionModal';
export { default as ConnectionTestModal } from './components/modals/ConnectionTestModal';
export { default as BulkOperationsModal } from './components/modals/BulkOperationsModal';

// Pages
export { default as ExternalAgentsPage } from './pages/ExternalAgentsPage';
export { default as AgentDetailPage } from './pages/AgentDetailPage';
export { default as AgentTestingPage } from './pages/AgentTestingPage';
export { default as AgentAnalyticsPage } from './pages/AgentAnalyticsPage';
export { default as MessageHistoryPage } from './pages/MessageHistoryPage';

// Routers
export { default as externalAgentRoutes } from './routers/externalAgentRoutes';

// Locales
export * from './locales';
