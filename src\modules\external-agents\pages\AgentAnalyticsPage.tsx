import React from 'react';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Card, Typography } from '@/shared/components/common';

const AgentAnalyticsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation(['external-agents']);

  return (
    <div className="w-full bg-background text-foreground">
      <Card>
        <div className="p-8 text-center">
          <Typography variant="h2" className="mb-4">
            {t('external-agents:analytics.title')}
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Agent Analytics Page for ID: {id}
          </Typography>
          <Typography variant="body2" className="text-muted-foreground mt-2">
            This page will be implemented in a future task.
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default AgentAnalyticsPage;
