import React from 'react';
import Typography from './Typography';
import { Card } from '@/shared/components/common';

const TypographyDemo: React.FC = () => {
  return (
    <div className="container mx-auto p-4 space-y-8">
      <Card title="Typography Variants" className="p-6">
        <div className="space-y-4">
          <Typography variant="h1">Heading 1</Typography>
          <Typography variant="h2">Heading 2</Typography>
          <Typography variant="h3">Heading 3</Typography>
          <Typography variant="h4">Heading 4</Typography>
          <Typography variant="h5">Heading 5</Typography>
          <Typography variant="h6">Heading 6</Typography>
          <Typography variant="subtitle1">Subtitle 1</Typography>
          <Typography variant="subtitle2">Subtitle 2</Typography>
          <Typography variant="body1">
            Body 1 - Regular paragraph text with standard size.
          </Typography>
          <Typography variant="body2">
            Body 2 - Smaller paragraph text for less important content.
          </Typography>
          <Typography variant="caption">
            Caption - Very small text for captions and labels
          </Typography>
          <Typography variant="overline">Overline - Small uppercase text</Typography>
          <Typography variant="code">
            const code = "Code text for displaying code snippets";
          </Typography>
        </div>
      </Card>

      <Card title="Typography Colors" className="p-6">
        <div className="space-y-2">
          <Typography color="default">Default Text Color</Typography>
          <Typography color="primary">Primary Text Color</Typography>
          <Typography color="secondary">Secondary Text Color</Typography>
          <Typography color="success">Success Text Color</Typography>
          <Typography color="warning">Warning Text Color</Typography>
          <Typography color="danger">Danger Text Color</Typography>
          <Typography color="info">Info Text Color</Typography>
          <Typography color="light">Light Text Color</Typography>
          <Typography color="dark">Dark Text Color</Typography>
          <Typography color="muted">Muted Text Color</Typography>
        </div>
      </Card>

      <Card title="Typography Weights" className="p-6">
        <div className="space-y-2">
          <Typography weight="thin">Thin (100)</Typography>
          <Typography weight="extralight">Extra Light (200)</Typography>
          <Typography weight="light">Light (300)</Typography>
          <Typography weight="normal">Normal (400)</Typography>
          <Typography weight="medium">Medium (500)</Typography>
          <Typography weight="semibold">Semi Bold (600)</Typography>
          <Typography weight="bold">Bold (700)</Typography>
          <Typography weight="extrabold">Extra Bold (800)</Typography>
          <Typography weight="black">Black (900)</Typography>
        </div>
      </Card>

      <Card title="Typography Alignment" className="p-6">
        <div className="space-y-4">
          <Typography align="left">Left aligned text (default)</Typography>
          <Typography align="center">Center aligned text</Typography>
          <Typography align="right">Right aligned text</Typography>
          <Typography align="justify">
            Justify aligned text. This paragraph contains more text to demonstrate justified
            alignment. The text will be stretched to fill the entire width of the container,
            creating a clean edge on both the left and right sides.
          </Typography>
        </div>
      </Card>

      <Card title="Typography Transformations" className="p-6">
        <div className="space-y-2">
          <Typography transform="none">Normal text (no transform)</Typography>
          <Typography transform="uppercase">Uppercase text</Typography>
          <Typography transform="lowercase">Lowercase Text (Was Uppercase)</Typography>
          <Typography transform="capitalize">capitalize each word</Typography>
        </div>
      </Card>

      <Card title="Typography Decorations" className="p-6">
        <div className="space-y-2">
          <Typography underline>Underlined text</Typography>
          <Typography strikethrough>Strikethrough text</Typography>
          <Typography italic>Italic text</Typography>
          <Typography underline italic>
            Underlined and italic text
          </Typography>
        </div>
      </Card>

      <Card title="Typography Truncation" className="p-6">
        <div className="space-y-4">
          <div className="max-w-md">
            <Typography truncate>
              This is a very long text that will be truncated with an ellipsis when it reaches the
              end of its container. You can see that it doesn't wrap to a new line.
            </Typography>
          </div>

          <div className="max-w-md">
            <Typography truncate lines={2}>
              This text demonstrates multi-line truncation. It will show exactly 2 lines and then
              truncate with an ellipsis. This is useful for card descriptions, article summaries,
              and other places where you want to show a preview of longer content without taking up
              too much space in the UI.
            </Typography>
          </div>
        </div>
      </Card>

      <Card title="Custom Typography" className="p-6">
        <div className="space-y-4">
          <Typography
            fontSize={{ base: '16px', md: '18px', lg: '20px' }}
            lineHeight="1.8"
            letterSpacing="0.5px"
            color="primary"
            weight="medium"
          >
            This text has responsive font size and custom line height and letter spacing.
          </Typography>

          <Typography component="blockquote" className="border-l-4 border-primary pl-4 italic">
            This is a custom blockquote using the Typography component with a custom element.
          </Typography>
        </div>
      </Card>
    </div>
  );
};

export default TypographyDemo;
