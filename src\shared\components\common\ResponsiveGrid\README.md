# ResponsiveGrid Component

Component hiển thị grid responsive tự động điều chỉnh số cột dựa trên kích thước màn hình và trạng thái chatpanel.

## Tính năng

- Tự động điều chỉnh số cột dựa trên kích thước màn hình
- Tự động điều chỉnh số cột khi chatpanel mở/đóng
- Cho phép tùy chỉnh số cột tối đa cho mỗi breakpoint
- Cho phép tùy chỉnh khoảng cách giữa các phần tử
- Hỗ trợ callback khi số cột thay đổi

## Cách sử dụng

```tsx
import { ResponsiveGrid } from '@/shared/components/common';

const MyComponent = () => {
  return (
    <ResponsiveGrid>
      <Card>Item 1</Card>
      <Card>Item 2</Card>
      <Card>Item 3</Card>
      <Card>Item 4</Card>
    </ResponsiveGrid>
  );
};
```

## Props

| Prop                    | Type             | Default                               | Description                                         |
| ----------------------- | ---------------- | ------------------------------------- | --------------------------------------------------- |
| children                | ReactNode        | -                                     | Các phần tử con sẽ được hiển thị trong grid         |
| gap                     | number \| object | 4                                     | Khoảng cách giữa các phần tử trong grid             |
| maxColumns              | object           | { xs: 1, sm: 2, md: 2, lg: 2, xl: 3 } | Số cột tối đa cho mỗi breakpoint khi chatpanel đóng |
| maxColumnsWithChatPanel | object           | { xs: 1, sm: 1, md: 1, lg: 1, xl: 2 } | Số cột tối đa cho mỗi breakpoint khi chatpanel mở   |
| className               | string           | ''                                    | Class name bổ sung cho grid container               |
| onColumnsChange         | function         | -                                     | Callback được gọi khi số cột thay đổi               |

## Ví dụ

### Cơ bản

```tsx
<ResponsiveGrid>
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</ResponsiveGrid>
```

### Tùy chỉnh số cột

```tsx
<ResponsiveGrid
  maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 5 }}
  maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
>
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</ResponsiveGrid>
```

### Tùy chỉnh khoảng cách

```tsx
<ResponsiveGrid gap={{ xs: 2, sm: 3, md: 4, lg: 5, xl: 6 }}>
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</ResponsiveGrid>
```

### Lắng nghe sự thay đổi số cột

```tsx
const handleColumnsChange = columns => {
  console.log(`Grid now has ${columns} columns`);
};

<ResponsiveGrid onColumnsChange={handleColumnsChange}>
  {items.map(item => (
    <Card key={item.id}>{item.content}</Card>
  ))}
</ResponsiveGrid>;
```

## Breakpoints

Component sử dụng các breakpoints sau:

- xs: < 640px (Mobile)
- sm: 640px - 767px (Small Tablet)
- md: 768px - 1023px (Tablet)
- lg: 1024px - 1279px (Desktop)
- xl: ≥ 1280px (Large Desktop)

## Mặc định

Mặc định, component sẽ hiển thị:

- Mobile (<640px): 1 cột
- Small Tablet (640px-767px): 1-2 cột (tùy thuộc vào trạng thái chatpanel)
- Tablet (768px-1023px): 1-2 cột (tùy thuộc vào trạng thái chatpanel)
- Desktop (1024px-1279px): 1-2 cột (tùy thuộc vào trạng thái chatpanel)
- Large Desktop (≥1280px): 2-3 cột (tùy thuộc vào trạng thái chatpanel)
