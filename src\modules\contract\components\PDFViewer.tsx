/**
 * Component hiển thị PDF từ URL hoặc base64
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';

// CSS để loại bỏ viền đen của iframe và PDF viewer
const pdfViewerStyles = `
  .pdf-viewer-container {
    background: var(--background) !important;
    overflow: hidden !important;
    position: relative !important;
  }

  /* Hide left black border */
  .pdf-viewer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 15px;
    height: 100%;
    background: var(--background);
    z-index: 3;
    pointer-events: none;
  }

  /* Hide right black border */
  .pdf-viewer-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 15px;
    height: 100%;
    background: var(--background);
    z-index: 3;
    pointer-events: none;
  }

  .pdf-viewer-iframe {
    border: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background: var(--background) !important;
    position: relative !important;
    z-index: 2 !important;
  }

  .pdf-viewer-iframe:focus {
    border: 0 !important;
    outline: 0 !important;
    box-shadow: none !important;
  }

  /* Override PDF viewer internal styles */
  .pdf-viewer-iframe embed,
  .pdf-viewer-iframe object {
    border: 0 !important;
    outline: 0 !important;
    background: var(--background) !important;
  }
`;

interface PDFViewerProps {
  /**
   * URL của file PDF
   */
  url?: string;

  /**
   * Dữ liệu base64 của file PDF
   */
  base64?: string;

  /**
   * Tiêu đề hiển thị
   */
  title?: string;

  /**
   * Chiều cao của viewer
   */
  height?: string;

  /**
   * Có hiển thị nút download không
   */
  showDownload?: boolean;

  /**
   * Callback khi download
   */
  onDownload?: () => void;

  /**
   * Class CSS bổ sung
   */
  className?: string;
}

const PDFViewer: React.FC<PDFViewerProps> = ({
  url,
  base64,
  title,
  height = '600px',
  showDownload = true,
  onDownload,
  className = '',
}) => {
  const { t } = useTranslation('contract');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pdfSrc, setPdfSrc] = useState<string>('');

  console.log(title);
  console.log(showDownload);
  console.log(onDownload);

  useEffect(() => {
    if (url) {
      setPdfSrc(url);
      setIsLoading(false);
    } else if (base64) {
      // Convert base64 to blob URL
      try {
        const byteCharacters = atob(base64);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });
        const blobUrl = URL.createObjectURL(blob);
        setPdfSrc(blobUrl);
        setIsLoading(false);
      } catch {
        setError(t('contract.contractDisplay.error'));
        setIsLoading(false);
      }
    } else {
      setError(t('contract.contractDisplay.error'));
      setIsLoading(false);
    }

    // Cleanup blob URL when component unmounts
    return () => {
      if (pdfSrc && pdfSrc.startsWith('blob:')) {
        URL.revokeObjectURL(pdfSrc);
      }
    };
  }, [url, base64, t, pdfSrc]);

  if (isLoading) {
    return (
      <div className={`${className} bg-white`}>
        <div className="flex items-center justify-center p-8" style={{ height }}>
          <div className="text-center">
            <Icon name="loader" size="lg" className="animate-spin mb-4" />
            <Typography variant="body1">{t('contract:contractDisplay.loading')}</Typography>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${className} bg-white`}>
        <div className="flex items-center justify-center p-8" style={{ height }}>
          <div className="text-center">
            <Icon name="alert-circle" size="lg" className="text-error mb-4" />
            <Typography variant="body1" className="text-error">
              {error}
            </Typography>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* CSS để loại bỏ viền đen */}
      <style dangerouslySetInnerHTML={{ __html: pdfViewerStyles }} />

      {/* PDF Viewer với system scroll - loại bỏ viền đen */}
      <div
        className="w-full bg-background overflow-hidden rounded-lg pdf-viewer-container"
        style={{ height: height }}
      >
        <iframe
          src={`${pdfSrc}#toolbar=0&navpanes=0&statusbar=0&messages=0&view=FitH&zoom=page-width`}
          className="w-full h-full border-0 bg-background rounded-lg pdf-viewer-iframe"
          title="PDF Viewer"
          onLoad={() => setIsLoading(false)}
          onError={() => {
            setError(t('contract:contractDisplay.error'));
            setIsLoading(false);
          }}
          style={{
            border: '0 !important',
            outline: '0 !important',
            borderWidth: '0 !important',
            borderStyle: 'none !important',
            borderColor: 'transparent !important',
            height: `calc(${height} + 20px)`,
            width: 'calc(100% + 20px)',
            backgroundColor: 'var(--background)',
            display: 'block',
            minWidth: 'calc(100% + 20px)',
            boxShadow: 'none !important',
            WebkitAppearance: 'none',
            MozAppearance: 'none',
            appearance: 'none',
            margin: '-10px !important',
            padding: '0 !important',
            transform: 'scale(1.02)',
            transformOrigin: 'center',
          }}
        />
      </div>
    </div>
  );
};

export default PDFViewer;
