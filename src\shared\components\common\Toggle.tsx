import { forwardRef } from 'react';

interface ToggleProps {
  label?: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  value?: boolean;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  id?: string;
  name?: string;
  // Thêm các props cho React Hook Form
  onBlur?: () => void;
}

const Toggle = forwardRef<HTMLInputElement, ToggleProps>(
  (
    {
      label,
      checked,
      onChange,
      value,
      size = 'md',
      disabled,
      className = '',
      onBlur,
      ...rest
    }: ToggleProps,
    ref
  ) => {
    // Sử dụng value từ React Hook Form nếu có
    const isChecked = checked !== undefined ? checked : value;
    // Size classes
    const sizeClasses = {
      sm: {
        toggle: 'w-8 h-4',
        circle: 'w-3 h-3',
        translateX: 'translate-x-4',
      },
      md: {
        toggle: 'w-10 h-5',
        circle: 'w-4 h-4',
        translateX: 'translate-x-5',
      },
      lg: {
        toggle: 'w-12 h-6',
        circle: 'w-5 h-5',
        translateX: 'translate-x-6',
      },
    };

    // Handle change
    const handleChange = () => {
      if (!disabled && onChange) {
        onChange(!isChecked);

        // Gọi onBlur để trigger validation của React Hook Form
        if (onBlur) {
          onBlur();
        }
      }
    };

    return (
      <label
        className={`inline-flex items-center cursor-pointer ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
      >
        <div className="relative">
          <input
            type="checkbox"
            className="sr-only"
            checked={isChecked}
            onChange={handleChange}
            disabled={disabled}
            ref={ref}
            {...rest}
          />
          <div
            className={`${sizeClasses[size].toggle} rounded-full ${
              isChecked ? 'bg-primary' : 'bg-gray-300 dark:bg-gray-600'
            }`}
          ></div>
          <div
            className={`absolute top-0.5 left-0.5 bg-white rounded-full transition-transform ${
              sizeClasses[size].circle
            } ${isChecked ? sizeClasses[size].translateX : 'translate-x-0'}`}
          ></div>
        </div>
        {label && <span className="ml-2">{label}</span>}
      </label>
    );
  }
);

Toggle.displayName = 'Toggle';

export default Toggle;
