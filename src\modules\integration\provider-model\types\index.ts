/**
 * Integration Provider Model Types
 */

import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Interface cho thông tin provider model
 */
export interface ProviderModel {
  /**
   * ID của provider model
   */
  id: string;

  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: TypeProviderEnum;

  /**
   * API key của nhà cung cấp (chỉ hiển thị khi edit)
   */
  apiKey?: string;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt: number;

  /**
   * Thời gian cập nhật (unix timestamp)
   */
  updatedAt?: number;

  /**
   * Thông tin người tạo
   */
  createdBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };

  /**
   * Thông tin người cập nhật
   */
  updatedBy?: {
    id: number;
    name: string;
    avatar: string | null;
  };
}

/**
 * Interface cho item trong danh sách provider model
 */
export interface ProviderModelListItem {
  /**
   * ID của provider model
   */
  id: string;

  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: TypeProviderEnum;

  /**
   * Thời gian tạo (unix timestamp)
   */
  createdAt: number;
}

/**
 * Interface cho request tạo provider model mới
 */
export interface CreateProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  name: string;

  /**
   * Loại nhà cung cấp
   */
  type: TypeProviderEnum;

  /**
   * API key của nhà cung cấp
   */
  apiKey: string;
}

/**
 * Interface cho request cập nhật provider model
 */
export interface UpdateProviderModelDto {
  /**
   * Tên định danh cho provider model
   */
  name?: string;

  /**
   * API key của nhà cung cấp
   */
  apiKey?: string;
}

/**
 * Interface cho query parameters
 */
export interface ProviderModelQueryParams extends QueryDto {
  /**
   * Lọc theo loại provider
   */
  type?: TypeProviderEnum;
}

/**
 * Interface cho form data
 */
export interface ProviderModelFormData extends Omit<CreateProviderModelDto, 'type'> {
  type: TypeProviderEnum;
}
