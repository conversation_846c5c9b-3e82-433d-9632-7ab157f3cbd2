import React from 'react';
import Icon from '@/shared/components/common/Icon/Icon';
import type { IconName } from '@/shared/components/common/Icon/Icon';
import { useTheme } from '@/shared/contexts/theme';

export interface FileDisplayProps {
  /**
   * Tên file
   */
  fileName: string;

  /**
   * Kích thước file (bytes)
   */
  fileSize?: number;

  /**
   * Sự kiện khi click vào component
   */
  onClick?: () => void;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị file theo phong cách không có border
 */
const FileDisplay: React.FC<FileDisplayProps> = ({
  fileName,
  fileSize,
  onClick,
  className = '',
}) => {
  const { currentTheme } = useTheme();

  // Lấy phần mở rộng từ tên file
  const getFileExtension = (): string => {
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // Lấy icon dựa trên phần mở rộng của file
  const getFileIcon = (): IconName => {
    const extension = getFileExtension();

    if (['pdf'].includes(extension)) return 'pdf';
    if (['doc'].includes(extension)) return 'file-text';
    if (['docx'].includes(extension)) return 'file-text';
    if (['xls', 'xlsx', 'csv'].includes(extension)) return 'sheet';
    if (['ppt', 'pptx'].includes(extension)) return 'presentation';
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return 'image';
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return 'film';
    if (['mp3', 'wav', 'ogg'].includes(extension)) return 'file';
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return 'file';
    if (['html', 'htm'].includes(extension)) return 'code';
    if (['txt', 'md'].includes(extension)) return 'file-text';
    if (['json', 'xml'].includes(extension)) return 'code';

    return 'file';
  };

  // Lấy màu cho icon dựa trên phần mở rộng
  const getFileIconColor = (): string => {
    const extension = getFileExtension();

    if (['pdf'].includes(extension)) return currentTheme.semanticColors.destructive;
    if (['doc', 'docx'].includes(extension)) return currentTheme.semanticColors.primary;
    if (['xls', 'xlsx', 'csv'].includes(extension)) return currentTheme.semanticColors.success;
    if (['ppt', 'pptx'].includes(extension)) return currentTheme.semanticColors.warning;
    if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension)) return '#9333ea'; // purple
    if (['mp4', 'avi', 'mov', 'wmv'].includes(extension)) return '#ec4899'; // pink
    if (['mp3', 'wav', 'ogg'].includes(extension)) return '#eab308'; // yellow
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) return currentTheme.semanticColors.muted;

    return currentTheme.semanticColors.muted;
  };

  // Định dạng kích thước file
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  return (
    <div
      className={`
        flex items-center py-2 px-3
        ${onClick ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      <div className="flex-shrink-0 mr-3">
        <Icon
          name={getFileIcon()}
          size="md"
          color={getFileIconColor()}
        />
      </div>

      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate break-words overflow-hidden">
          {fileName}
        </div>
        {fileSize !== undefined && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {formatFileSize(fileSize)}
          </div>
        )}
      </div>
    </div>
  );
};

export default FileDisplay;
