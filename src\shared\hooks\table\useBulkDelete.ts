import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';

export interface UseBulkDeleteOptions {
  /**
   * Hàm xóa nhiều items
   */
  deleteMutation: (ids: string[]) => Promise<void>;

  /**
   * Namespace cho i18n messages
   */
  i18nNamespace?: string;

  /**
   * Tên entity để hiển thị trong thông báo (ví dụ: 'URL', 'File', 'User')
   */
  entityName?: string;

  /**
   * Custom messages cho thông báo
   */
  messages?: {
    selectToDelete?: string;
    confirmMessage?: string;
    successMessage?: string;
    errorMessage?: string;
  };

  /**
   * Callback sau khi xóa thành công
   */
  onSuccess?: (deletedCount: number) => void;

  /**
   * Callback khi có lỗi
   */
  onError?: (error: unknown) => void;
}

export interface UseBulkDeleteResult {
  /**
   * <PERSON>ố lượng items sẽ được xóa
   */
  bulkDeleteCount: number;

  /**
   * Trạng thái hiển thị modal xác nhận
   */
  showBulkDeleteConfirm: boolean;

  /**
   * Hiển thị modal xác nhận xóa nhiều
   */
  handleShowBulkDeleteConfirm: (selectedIds: string[], hasData: boolean) => void;

  /**
   * Hủy xóa nhiều
   */
  handleCancelBulkDelete: () => void;

  /**
   * Xác nhận xóa nhiều
   */
  handleConfirmBulkDelete: (selectedIds: string[], onResetSelection: () => void) => Promise<void>;

  /**
   * Message để hiển thị trong modal
   */
  getConfirmMessage: () => string;
}

/**
 * Hook tái sử dụng cho logic bulk delete
 */
export function useBulkDelete({
  deleteMutation,
  i18nNamespace = 'common',
  entityName = 'item',
  messages,
  onSuccess,
  onError,
}: UseBulkDeleteOptions): UseBulkDeleteResult {
  const { t } = useTranslation([i18nNamespace, 'common']);

  const [bulkDeleteCount, setBulkDeleteCount] = useState(0);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Hiển thị modal xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback((selectedIds: string[], hasData: boolean) => {
    if (selectedIds.length === 0 || !hasData) {
      NotificationUtil.warning({
        message: messages?.selectToDelete || t('common:selectToDelete', `Vui lòng chọn ít nhất một ${entityName} để xóa`),
        duration: 3000,
      });
      return;
    }

    setBulkDeleteCount(selectedIds.length);
    setShowBulkDeleteConfirm(true);
  }, [messages?.selectToDelete, t, entityName]);

  // Hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
    setBulkDeleteCount(0);
  }, []);

  // Xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async (
    selectedIds: string[],
    onResetSelection: () => void
  ) => {
    if (selectedIds.length === 0) return;

    try {
      const deletedCount = bulkDeleteCount;

      await deleteMutation(selectedIds);

      // Reset states
      setShowBulkDeleteConfirm(false);
      setBulkDeleteCount(0);
      onResetSelection();

      NotificationUtil.success({
        message: messages?.successMessage ||
          t(`${i18nNamespace}:bulkDeleteSuccess`, `Xóa ${deletedCount} ${entityName} đã chọn thành công`),
      });

      onSuccess?.(deletedCount);
    } catch (error) {
      console.error('Error deleting multiple items:', error);

      NotificationUtil.error({
        message: messages?.errorMessage ||
          t(`${i18nNamespace}:bulkDeleteError`, `Lỗi khi xóa các ${entityName} đã chọn`),
      });

      // Reset states ngay cả khi có lỗi
      setShowBulkDeleteConfirm(false);
      setBulkDeleteCount(0);
      onResetSelection();

      onError?.(error);
    }
  }, [bulkDeleteCount, deleteMutation, messages, t, i18nNamespace, entityName, onSuccess, onError]);

  // Lấy message để hiển thị trong modal
  const getConfirmMessage = useCallback(() => {
    return messages?.confirmMessage ||
      t(`${i18nNamespace}:confirmBulkDeleteMessage`, `Bạn có chắc chắn muốn xóa ${bulkDeleteCount} ${entityName} đã chọn?`);
  }, [messages?.confirmMessage, t, i18nNamespace, bulkDeleteCount, entityName]);

  return {
    bulkDeleteCount,
    showBulkDeleteConfirm,
    handleShowBulkDeleteConfirm,
    handleCancelBulkDelete,
    handleConfirmBulkDelete,
    getConfirmMessage,
  };
}
