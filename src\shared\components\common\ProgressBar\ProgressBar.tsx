import React from 'react';

export interface ProgressBarProps {
  /**
   * <PERSON><PERSON><PERSON> trị hiện tại của thanh tiến trình (0-100)
   */
  value: number;

  /**
   * M<PERSON>u sắc của thanh tiến trình
   */
  color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';

  /**
   * Kích thước của thanh tiến trình
   */
  size?: 'xs' | 'sm' | 'md' | 'lg';

  /**
   * Hiển thị giá trị phần trăm
   */
  showValue?: boolean;

  /**
   * Định dạng giá trị hiển thị
   */
  valueFormat?: (value: number) => string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Hiệu ứng gradient
   */
  gradient?: boolean;

  /**
   * Hiệu ứng animation
   */
  animated?: boolean;

  /**
   * Hiển thị dạng tròn
   */
  rounded?: boolean;
}

/**
 * Component hiển thị thanh tiến trình
 */
const ProgressBar: React.FC<ProgressBarProps> = ({
  value,
  color = 'primary',
  size = 'md',
  showValue = false,
  valueFormat,
  className = '',
  gradient = false,
  animated = false,
  rounded = true,
}) => {
  // Đảm bảo giá trị nằm trong khoảng 0-100
  const normalizedValue = Math.min(Math.max(value, 0), 100);

  // Xác định kích thước dựa trên prop size
  const sizeClasses = {
    xs: 'h-1',
    sm: 'h-1.5',
    md: 'h-2',
    lg: 'h-3',
  }[size];

  // Xác định màu sắc dựa trên prop color
  const colorClasses = {
    primary: 'bg-primary dark:bg-primary-dark',
    success: 'bg-green-500 dark:bg-green-400',
    warning: 'bg-yellow-500 dark:bg-yellow-400',
    danger: 'bg-red-500 dark:bg-red-400',
    info: 'bg-blue-500 dark:bg-blue-400',
  }[color];

  // Xác định gradient nếu được yêu cầu
  const gradientClasses = gradient
    ? {
        primary:
          'bg-gradient-to-r from-primary to-primary-light dark:from-primary-dark dark:to-primary',
        success:
          'bg-gradient-to-r from-green-600 to-green-400 dark:from-green-500 dark:to-green-300',
        warning:
          'bg-gradient-to-r from-yellow-600 to-yellow-400 dark:from-yellow-500 dark:to-yellow-300',
        danger: 'bg-gradient-to-r from-red-600 to-red-400 dark:from-red-500 dark:to-red-300',
        info: 'bg-gradient-to-r from-blue-600 to-blue-400 dark:from-blue-500 dark:to-blue-300',
      }[color]
    : '';

  // Xác định animation nếu được yêu cầu
  const animatedClasses = animated ? 'animate-pulse' : '';

  // Xác định bo tròn
  const roundedClasses = rounded ? 'rounded-full' : 'rounded';

  // Định dạng giá trị hiển thị
  const formattedValue = valueFormat ? valueFormat(normalizedValue) : `${normalizedValue}%`;

  return (
    <div className={`w-full ${className}`}>
      <div
        className={`w-full ${sizeClasses} bg-gray-200 dark:bg-dark-lighter ${roundedClasses} overflow-hidden`}
      >
        <div
          className={`${sizeClasses} ${colorClasses} ${gradientClasses} ${animatedClasses} ${roundedClasses} transition-all duration-300 ease-in-out`}
          style={{ width: `${normalizedValue}%` }}
        />
      </div>
      {showValue && (
        <div className="mt-1 text-xs text-gray-600 dark:text-gray-400 text-right">
          {formattedValue}
        </div>
      )}
    </div>
  );
};

export default ProgressBar;
