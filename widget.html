<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RedAI Chat Widget Demo</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #333;
      margin-top: 0;
    }
    p {
      color: #666;
      line-height: 1.6;
    }
    .code-block {
      background-color: #f7f7f7;
      padding: 15px;
      border-radius: 5px;
      overflow-x: auto;
      margin: 20px 0;
    }
    pre {
      margin: 0;
    }
    code {
      font-family: 'Courier New', Courier, monospace;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>RedAI Chat Widget Demo</h1>
    <p>Đây là trang demo cho RedAI Chat Widget. Widget chat sẽ xuất hiện ở góc dưới bên phải màn hình.</p>

    <h2>Cách nhúng widget vào website của bạn</h2>
    <p>Chỉ cần thêm đoạn script sau vào trang web của bạn:</p>

    <div class="code-block">
      <pre><code>&lt;script
  src="https://your-cdn-url.com/redai-chat-widget.js"
  data-redai-chat
  data-auto-init="true"
  data-api-key="YOUR_API_KEY"
  data-position="bottom-right"
  data-primary-color="#ff3333"
  data-title="RedAI Chat"
  data-greeting="Xin chào! Tôi có thể giúp gì cho bạn?"
  data-dark-mode="false"
&gt;&lt;/script&gt;</code></pre>
    </div>

    <h2>Cấu hình tùy chọn</h2>
    <p>Bạn có thể tùy chỉnh widget bằng cách thay đổi các thuộc tính data:</p>
    <ul>
      <li><strong>data-auto-init</strong>: Tự động khởi tạo widget khi trang tải xong (true/false)</li>
      <li><strong>data-api-key</strong>: Khóa API của bạn</li>
      <li><strong>data-position</strong>: Vị trí của widget (bottom-right, bottom-left, top-right, top-left)</li>
      <li><strong>data-primary-color</strong>: Màu chủ đạo của widget</li>
      <li><strong>data-title</strong>: Tiêu đề của cửa sổ chat</li>
      <li><strong>data-greeting</strong>: Lời chào khi mở chat</li>
      <li><strong>data-dark-mode</strong>: Chế độ tối (true/false)</li>
    </ul>

    <h2>Khởi tạo bằng JavaScript</h2>
    <p>Bạn cũng có thể khởi tạo widget bằng JavaScript:</p>

    <div class="code-block">
      <pre><code>&lt;script src="https://your-cdn-url.com/redai-chat-widget.js"&gt;&lt;/script&gt;
&lt;script&gt;
  // Tạo instance mới với cấu hình tùy chỉnh
  const chatWidget = new RedAIChat({
    apiKey: 'YOUR_API_KEY',
    position: 'bottom-right',
    primaryColor: '#ff3333',
    title: 'RedAI Chat',
    greeting: 'Xin chào! Tôi có thể giúp gì cho bạn?',
    darkMode: false
  });

  // Khởi tạo widget
  chatWidget.init();

  // Hoặc sử dụng instance mặc định
  // window.redaiChat.init();
&lt;/script&gt;</code></pre>
    </div>
  </div>

  <!-- Nhúng RedAI Chat Widget -->
  <script src="./dist/widget/redai-chat-widget.js" data-redai-chat data-auto-init="true"></script>
</body>
</html>
