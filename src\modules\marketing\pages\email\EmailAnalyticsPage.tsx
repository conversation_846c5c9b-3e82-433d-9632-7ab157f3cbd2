import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Select,
  ResponsiveGrid,
  Table,
  Button,
  StatusBadge
} from '@/shared/components/common';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from '@/shared/components/charts';
import {
  TrendingUp,
  Mail,
  MousePointer,
  Eye,
  Users,
  BarChart3,
  Download,
  Filter
} from 'lucide-react';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';

// Types for analytics data
interface CampaignAnalytics {
  id: string;
  name: string;
  sentAt: Date;
  recipients: number;
  opened: number;
  clicked: number;
  unsubscribed: number;
  openRate: number;
  clickRate: number;
  status: 'completed' | 'sending';
}

// Mock data for analytics
const mockAnalyticsData = {
  overview: {
    totalSent: 125000,
    totalOpened: 87500,
    totalClicked: 31250,
    totalUnsubscribed: 1250,
    openRate: 70,
    clickRate: 25,
    unsubscribeRate: 1,
    bounceRate: 2.5
  },
  campaigns: [
    {
      id: '1',
      name: 'Newsletter tháng 1',
      sentAt: new Date('2024-01-15'),
      recipients: 12500,
      opened: 8750,
      clicked: 3125,
      unsubscribed: 125,
      openRate: 70,
      clickRate: 25,
      status: 'completed' as const
    },
    {
      id: '2',
      name: 'Khuyến mãi cuối năm',
      sentAt: new Date('2024-01-10'),
      recipients: 25000,
      opened: 17500,
      clicked: 6250,
      unsubscribed: 250,
      openRate: 70,
      clickRate: 25,
      status: 'completed' as const
    },
    {
      id: '3',
      name: 'Chào mừng khách hàng mới',
      sentAt: new Date('2024-01-05'),
      recipients: 5000,
      opened: 3500,
      clicked: 1250,
      unsubscribed: 50,
      openRate: 70,
      clickRate: 25,
      status: 'completed' as const
    }
  ],
  timeRange: [
    { date: '2024-01-01', sent: 5000, opened: 3500, clicked: 1250 },
    { date: '2024-01-02', sent: 7500, opened: 5250, clicked: 1875 },
    { date: '2024-01-03', sent: 10000, opened: 7000, clicked: 2500 },
    { date: '2024-01-04', sent: 8000, opened: 5600, clicked: 2000 },
    { date: '2024-01-05', sent: 12000, opened: 8400, clicked: 3000 }
  ],
  // Dữ liệu cho biểu đồ xu hướng theo tuần
  weeklyTrend: [
    { week: 'Tuần 1', sent: 25000, opened: 17500, clicked: 6250, unsubscribed: 125 },
    { week: 'Tuần 2', sent: 32000, opened: 22400, clicked: 8000, unsubscribed: 160 },
    { week: 'Tuần 3', sent: 28000, opened: 19600, clicked: 7000, unsubscribed: 140 },
    { week: 'Tuần 4', sent: 35000, opened: 24500, clicked: 8750, unsubscribed: 175 },
    { week: 'Tuần 5', sent: 30000, opened: 21000, clicked: 7500, unsubscribed: 150 }
  ],
  // Dữ liệu cho biểu đồ so sánh chiến dịch
  campaignComparison: [
    { campaign: 'Newsletter tháng 1', sent: 12500, opened: 8750, clicked: 3125 },
    { campaign: 'Khuyến mãi cuối năm', sent: 21000, opened: 14700, clicked: 5250 },
    { campaign: 'Chào mừng khách hàng mới', sent: 4500, opened: 3150, clicked: 1125 },
    { campaign: 'Sản phẩm mới', sent: 8000, opened: 5600, clicked: 2000 },
    { campaign: 'Black Friday', sent: 15000, opened: 10500, clicked: 3750 }
  ],
  // Dữ liệu cho biểu đồ phân bố trạng thái
  statusDistribution: [
    { name: 'Đã gửi', value: 45, color: '#10B981' },
    { name: 'Đang gửi', value: 15, color: '#3B82F6' },
    { name: 'Đã lên lịch', value: 25, color: '#8B5CF6' },
    { name: 'Bản nháp', value: 10, color: '#F59E0B' },
    { name: 'Tạm dừng', value: 5, color: '#EF4444' }
  ]
};

const EmailAnalyticsPage: React.FC = () => {
  const { t } = useTranslation(['common', 'marketing']);
  const [selectedCampaign, setSelectedCampaign] = useState<string>('all');

  // Table columns for campaign analytics
  const columns = useMemo(() => [
    {
      title: t('marketing:email.analytics.campaign', 'Chiến dịch'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
      render: (value: unknown, record: CampaignAnalytics) => (
        <div>
          <Typography variant="body2" className="font-medium">{value as string}</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {new Date(record.sentAt).toLocaleDateString('vi-VN')}
          </Typography>
        </div>
      )
    },
    {
      title: t('marketing:email.analytics.recipients', 'Người nhận'),
      dataIndex: 'recipients',
      key: 'recipients',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2">{(value as number).toLocaleString()}</Typography>
      )
    },
    {
      title: t('marketing:email.analytics.openRate', 'Tỷ lệ mở'),
      dataIndex: 'openRate',
      key: 'openRate',
      sortable: true,
      render: (value: unknown, record: CampaignAnalytics) => (
        <div>
          <Typography variant="body2" className="font-medium">{value as number}%</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.opened.toLocaleString()} / {record.recipients.toLocaleString()}
          </Typography>
        </div>
      )
    },
    {
      title: t('marketing:email.analytics.clickRate', 'Tỷ lệ click'),
      dataIndex: 'clickRate',
      key: 'clickRate',
      sortable: true,
      render: (value: unknown, record: CampaignAnalytics) => (
        <div>
          <Typography variant="body2" className="font-medium">{value as number}%</Typography>
          <Typography variant="caption" className="text-muted-foreground">
            {record.clicked.toLocaleString()} / {record.recipients.toLocaleString()}
          </Typography>
        </div>
      )
    },
    {
      title: t('marketing:email.analytics.status', 'Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      render: (value: unknown) => (
        <StatusBadge
          variant={(value as string) === 'completed' ? 'success' : 'warning'}
          text={(value as string) === 'completed' ? 'Hoàn thành' : 'Đang gửi'}
        />
      )
    }
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig({
    columns,
    createQueryParams: (params) => params
  }));

  // Campaign options for filter
  const campaignOptions = [
    { value: 'all', label: t('marketing:email.analytics.allCampaigns', 'Tất cả chiến dịch') },
    ...mockAnalyticsData.campaigns.map(campaign => ({
      value: campaign.id,
      label: campaign.name
    }))
  ];

  // Overview stats
  const overviewStats = [
    {
      title: t('marketing:email.analytics.totalSent', 'Tổng email gửi'),
      value: mockAnalyticsData.overview.totalSent.toLocaleString(),
      icon: <Mail className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: t('marketing:email.analytics.totalOpened', 'Tổng email mở'),
      value: mockAnalyticsData.overview.totalOpened.toLocaleString(),
      icon: <Eye className="h-5 w-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      title: t('marketing:email.analytics.totalClicked', 'Tổng click'),
      value: mockAnalyticsData.overview.totalClicked.toLocaleString(),
      icon: <MousePointer className="h-5 w-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      title: t('marketing:email.analytics.totalUnsubscribed', 'Tổng hủy đăng ký'),
      value: mockAnalyticsData.overview.totalUnsubscribed.toLocaleString(),
      icon: <Users className="h-5 w-5" />,
      color: 'text-red-600',
      bgColor: 'bg-red-100'
    }
  ];

  const performanceStats = [
    {
      title: t('marketing:email.analytics.openRate', 'Tỷ lệ mở'),
      value: `${mockAnalyticsData.overview.openRate}%`,
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'text-green-600'
    },
    {
      title: t('marketing:email.analytics.clickRate', 'Tỷ lệ click'),
      value: `${mockAnalyticsData.overview.clickRate}%`,
      icon: <MousePointer className="h-5 w-5" />,
      color: 'text-blue-600'
    },
    {
      title: t('marketing:email.analytics.bounceRate', 'Tỷ lệ bounce'),
      value: `${mockAnalyticsData.overview.bounceRate}%`,
      icon: <BarChart3 className="h-5 w-5" />,
      color: 'text-orange-600'
    },
    {
      title: t('marketing:email.analytics.unsubscribeRate', 'Tỷ lệ hủy đăng ký'),
      value: `${mockAnalyticsData.overview.unsubscribeRate}%`,
      icon: <Users className="h-5 w-5" />,
      color: 'text-red-600'
    }
  ];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div>
          <Typography variant="h1" className="mb-2">
            {t('marketing:email.analytics.title', 'Analytics')}
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            {t('marketing:email.analytics.description', 'Báo cáo hiệu quả email marketing')}
          </Typography>
        </div>

        <div className="flex items-center space-x-2 mt-4 sm:mt-0">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            {t('common:export', 'Xuất báo cáo')}
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Typography variant="body2" className="font-medium">
              {t('common:filters', 'Bộ lọc')}
            </Typography>
          </div>

          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="w-full sm:w-48">
              <Select
                value={selectedCampaign}
                onChange={(value) => setSelectedCampaign(value as string)}
                options={campaignOptions}
                placeholder={t('marketing:email.analytics.selectCampaign', 'Chọn chiến dịch')}
                size="sm"
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Overview Stats */}
      <div className="mb-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:email.analytics.overview', 'Tổng quan')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, lg: 4 }}>
          {overviewStats.map((stat, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <Typography variant="caption" className="text-muted-foreground mb-1">
                    {stat.title}
                  </Typography>
                  <Typography variant="h2" className="font-bold">
                    {stat.value}
                  </Typography>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <div className={stat.color}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Performance Stats */}
      <div className="mb-6">
        <Typography variant="h3" className="mb-4">
          {t('marketing:email.analytics.performance', 'Hiệu suất')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, lg: 4 }}>
          {performanceStats.map((stat, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center space-x-3">
                <div className={stat.color}>
                  {stat.icon}
                </div>
                <div>
                  <Typography variant="caption" className="text-muted-foreground">
                    {stat.title}
                  </Typography>
                  <Typography variant="h3" className="font-bold">
                    {stat.value}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Charts Section */}
      <div className="space-y-6 mb-6">
        {/* Xu hướng theo thời gian */}
        <Card className="p-6">
          <Typography variant="h3" className="mb-4">
            {t('marketing:email.analytics.weeklyTrend', 'Xu hướng theo tuần')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <LineChart
              data={mockAnalyticsData.weeklyTrend}
              xAxisKey="week"
              lines={[
                {
                  dataKey: 'sent',
                  name: t('marketing:email.analytics.sent', 'Đã gửi'),
                  color: '#3B82F6',
                  strokeWidth: 3
                },
                {
                  dataKey: 'opened',
                  name: t('marketing:email.analytics.opened', 'Đã mở'),
                  color: '#10B981',
                  strokeWidth: 3
                },
                {
                  dataKey: 'clicked',
                  name: t('marketing:email.analytics.clicked', 'Đã click'),
                  color: '#8B5CF6',
                  strokeWidth: 3
                }
              ]}
              height={400}
              showGrid
              showTooltip
              showLegend
              xAxisLabel={t('marketing:email.analytics.week', 'Tuần')}
              yAxisLabel={t('marketing:email.analytics.count', 'Số lượng')}
            />
          </div>
        </Card>

        <ResponsiveGrid maxColumns={{ xs: 1, lg: 2 }}>
          {/* So sánh hiệu suất chiến dịch */}
          <Card className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('marketing:email.analytics.campaignComparison', 'So sánh chiến dịch')}
            </Typography>
            <div style={{ height: 350, width: '100%' }}>
              <BarChart
                data={mockAnalyticsData.campaignComparison}
                xAxisKey="campaign"
                bars={[
                  {
                    dataKey: 'sent',
                    name: t('marketing:email.analytics.sent', 'Đã gửi'),
                    color: '#3B82F6'
                  },
                  {
                    dataKey: 'opened',
                    name: t('marketing:email.analytics.opened', 'Đã mở'),
                    color: '#10B981'
                  },
                  {
                    dataKey: 'clicked',
                    name: t('marketing:email.analytics.clicked', 'Đã click'),
                    color: '#8B5CF6'
                  }
                ]}
                height={350}
                showGrid
                showTooltip
                showLegend
                xAxisLabel={t('marketing:email.analytics.campaign', 'Chiến dịch')}
                yAxisLabel={t('marketing:email.analytics.count', 'Số lượng')}
              />
            </div>
          </Card>

          {/* Phân bố trạng thái */}
          <Card className="p-6">
            <Typography variant="h3" className="mb-4">
              {t('marketing:email.analytics.statusDistribution', 'Phân bố trạng thái')}
            </Typography>
            <div style={{ height: 350, width: '100%' }}>
              <PieChart
                data={mockAnalyticsData.statusDistribution}
                slices={[
                  {
                    nameKey: 'name',
                    valueKey: 'value',
                    color: '#3B82F6'
                  }
                ]}
                height={350}
                showTooltip
                showLegend
                colorScheme={mockAnalyticsData.statusDistribution.map(item => item.color)}
              />
            </div>
          </Card>
        </ResponsiveGrid>

        {/* Biểu đồ vùng cho xu hướng hàng ngày */}
        <Card className="p-6">
          <Typography variant="h3" className="mb-4">
            {t('marketing:email.analytics.dailyTrend', 'Xu hướng hàng ngày')}
          </Typography>
          <div style={{ height: 400, width: '100%' }}>
            <AreaChart
              data={mockAnalyticsData.timeRange}
              xAxisKey="date"
              areas={[
                {
                  dataKey: 'sent',
                  name: t('marketing:email.analytics.sent', 'Đã gửi'),
                  strokeColor: '#3B82F6',
                  fillColor: '#3B82F6',
                  fillOpacity: 0.3
                },
                {
                  dataKey: 'opened',
                  name: t('marketing:email.analytics.opened', 'Đã mở'),
                  strokeColor: '#10B981',
                  fillColor: '#10B981',
                  fillOpacity: 0.3
                },
                {
                  dataKey: 'clicked',
                  name: t('marketing:email.analytics.clicked', 'Đã click'),
                  strokeColor: '#8B5CF6',
                  fillColor: '#8B5CF6',
                  fillOpacity: 0.3
                }
              ]}
              height={400}
              showGrid
              showTooltip
              showLegend
              xAxisLabel={t('marketing:email.analytics.date', 'Ngày')}
              yAxisLabel={t('marketing:email.analytics.count', 'Số lượng')}
            />
          </div>
        </Card>
      </div>

      {/* Campaign Performance Table */}
      <Card>
        <div className="mb-4">
          <Typography variant="h3">
            {t('marketing:email.analytics.campaignPerformance', 'Hiệu quả từng chiến dịch')}
          </Typography>
        </div>
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mockAnalyticsData.campaigns}
          loading={false}
        />
      </Card>
    </div>
  );
};

export default EmailAnalyticsPage;
