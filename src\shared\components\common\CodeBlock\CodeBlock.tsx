import React, { useState } from 'react';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus, vs } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Button, Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';

export interface CodeBlockProps {
  /**
   * Code to display
   */
  code: string;

  /**
   * Programming language
   */
  language?: string;

  /**
   * Show line numbers
   */
  showLineNumbers?: boolean;

  /**
   * Additional class names
   */
  className?: string;
}

/**
 * CodeBlock component for displaying code with syntax highlighting
 */
const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = 'javascript',
  showLineNumbers = true,
  className = '',
}) => {
  const { currentTheme } = useTheme();
  const [copied, setCopied] = useState(false);

  // Choose theme based on current app theme
  const codeTheme = currentTheme.mode === 'dark' ? vscDarkPlus : vs;

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(code);
    setCopied(true);

    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <div className={`relative rounded-md overflow-hidden ${className}`}>
      <div className="absolute right-2 top-2 z-10">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCopy}
          className="bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700"
        >
          {copied ? (
            <>
              <Icon name="check" size="sm" className="mr-1" />
              <span>Đã sao chép</span>
            </>
          ) : (
            <>
              <Icon name="copy" size="sm" className="mr-1" />
              <span>Sao chép</span>
            </>
          )}
        </Button>
      </div>

      <SyntaxHighlighter
        language={language}
        style={codeTheme}
        showLineNumbers={showLineNumbers}
        wrapLines
        customStyle={{
          margin: 0,
          padding: '1.5rem',
          fontSize: '0.875rem',
          borderRadius: '0.375rem',
        }}
      >
        {code}
      </SyntaxHighlighter>
    </div>
  );
};

export default CodeBlock;
