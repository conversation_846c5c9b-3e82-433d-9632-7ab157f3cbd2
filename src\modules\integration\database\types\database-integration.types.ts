/**
 * Database Integration Types
 */

/**
 * Supported Database Types
 */
export type DatabaseType = 
  | 'mysql'
  | 'postgresql'
  | 'mongodb'
  | 'redis'
  | 'sqlite';

/**
 * Database Connection Status
 */
export type DatabaseConnectionStatus = 'active' | 'inactive' | 'error' | 'testing' | 'pending';

/**
 * Database Connection Configuration
 */
export interface DatabaseConnectionConfig {
  id: string;
  name: string;
  type: DatabaseType;
  displayName: string;
  description?: string;
  status: DatabaseConnectionStatus;
  credentials: DatabaseCredentials;
  settings: DatabaseSettings;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  lastTestedAt?: string;
  testResult?: DatabaseTestResult;
}

/**
 * Database Credentials - Dynamic based on database type
 */
export interface DatabaseCredentials {
  // MySQL & PostgreSQL
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database?: string;
  
  // PostgreSQL specific
  schema?: string;
  
  // MongoDB
  connectionString?: string;
  collection?: string;
  
  // Redis
  databaseIndex?: number;
  
  // SQLite
  filePath?: string;
  mode?: 'readonly' | 'readwrite' | 'create';
  
  // SSL/TLS options
  ssl?: boolean;
  sslCert?: string;
  sslKey?: string;
  sslCa?: string;
  
  // Common
  [key: string]: string | number | boolean | undefined;
}

/**
 * Database Settings
 */
export interface DatabaseSettings {
  connectionTimeout: number;
  queryTimeout: number;
  maxConnections: number;
  retryConfig: DatabaseRetryConfig;
  poolConfig: DatabasePoolConfig;
  enableLogging: boolean;
  timezone: string;
}

/**
 * Database Retry Configuration
 */
export interface DatabaseRetryConfig {
  maxRetries: number;
  retryDelay: number; // milliseconds
  backoffMultiplier: number;
}

/**
 * Database Pool Configuration
 */
export interface DatabasePoolConfig {
  min: number;
  max: number;
  acquireTimeoutMillis: number;
  idleTimeoutMillis: number;
}

/**
 * Database Test Result
 */
export interface DatabaseTestResult {
  success: boolean;
  message: string;
  responseTime: number; // milliseconds
  timestamp: string;
  details?: {
    version?: string;
    serverInfo?: string;
    error?: string;
  };
}

/**
 * Database Connection Form Data
 */
export interface DatabaseConnectionFormData {
  name: string;
  type: DatabaseType;
  displayName: string;
  description?: string;
  credentials: DatabaseCredentials;
  settings: Partial<DatabaseSettings>;
  isDefault: boolean;
}

/**
 * Database Connection Query Parameters
 */
export interface DatabaseConnectionQueryParams {
  page?: number;
  limit?: number;
  status?: DatabaseConnectionStatus;
  type?: DatabaseType;
  search?: string;
  sortBy?: 'name' | 'type' | 'status' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Database Connection API Response
 */
export interface DatabaseConnectionResponse {
  items: DatabaseConnectionConfig[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

/**
 * Database Test Request
 */
export interface DatabaseTestRequest {
  connectionId: string;
  testQuery?: string;
}

/**
 * Database Connection Template for different types
 */
export interface DatabaseConnectionTemplate {
  type: DatabaseType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  documentationUrl: string;
  requiredCredentials: string[];
  optionalCredentials: string[];
  defaultSettings: Partial<DatabaseSettings>;
  configurationSteps: string[];
  defaultPort: number;
  testQuery: string;
}
