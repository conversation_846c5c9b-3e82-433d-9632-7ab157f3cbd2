import React from 'react';

import { cn } from '@/shared/utils/cn';

interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
  padding?: boolean | string;
  fullHeight?: boolean;
}

/**
 * PageWrapper component - Wrapper chung cho các trang
 * Cung cấp styling chuẩn: full width, min height screen, theme colors và padding
 * 
 * @example
 * // Sử dụng cơ bản
 * <PageWrapper>
 *   <div>Nội dung trang</div>
 * </PageWrapper>
 * 
 * @example
 * // Tùy chỉnh padding
 * <PageWrapper padding="p-8">
 *   <div>Nội dung với padding lớn hơn</div>
 * </PageWrapper>
 * 
 * @example
 * // Không padding
 * <PageWrapper padding={false}>
 *   <div>Nội dung không có padding</div>
 * </PageWrapper>
 */
const PageWrapper: React.FC<PageWrapperProps> = ({
  children,
  className = '',
  padding = true,
  fullHeight = true,
}) => {
  // Xử lý padding
  const paddingClasses = typeof padding === 'boolean' 
    ? (padding ? '' : '') 
    : padding;

  // Xử lý full height
  const heightClasses = fullHeight ? 'min-h-screen h-full' : '';

  return (
    <div 
      className={cn(
        'w-full bg-background text-foreground',
        heightClasses,
        paddingClasses,
        className
      )}
    >
      {children}
    </div>
  );
};

export default PageWrapper;
