/**
 * <PERSON><PERSON> hợp đồng nguyên tắc
 */
import React, { useState } from 'react';
import { Stepper } from '@/shared/components/common';
import { ContractType, ContractStep, ContractData } from '../types';
import {
  ContractTypeSelector,
  TermsAcceptance,
  BusinessInfoForm,
  PersonalInfoForm,
  ContractDisplay,
  ContractSigning,
  HandSignature,
  OTPVerification,
  ContractSuccess,
} from '../components';

const ContractPrinciplePage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<ContractStep>(ContractStep.TYPE_SELECTION);
  const [isLoading, setIsLoading] = useState(false);
  const [contractData, setContractData] = useState<ContractData>({
    termsAccepted: false,
    isCompleted: false,
  });

  // Xác định các bước theo loại hợp đồng
  const getStepsForType = (type: ContractType): ContractStep[] => {
    const commonSteps = [
      ContractStep.TYPE_SELECTION,
      ContractStep.TERMS_ACCEPTANCE,
      ContractStep.INFO_FORM,
      ContractStep.CONTRACT_DISPLAY,
    ];

    if (type === ContractType.BUSINESS) {
      return [...commonSteps, ContractStep.CONTRACT_SIGNING, ContractStep.COMPLETED];
    } else {
      return [
        ...commonSteps,
        ContractStep.HAND_SIGNATURE,
        ContractStep.OTP_VERIFICATION,
        ContractStep.COMPLETED,
      ];
    }
  };

  const steps = contractData.type ? getStepsForType(contractData.type) : [ContractStep.TYPE_SELECTION];
  const currentStepIndex = steps.indexOf(currentStep);

  // Tạo stepItems cho Stepper component - các bước thực tế
  const getStepTitle = (step: ContractStep): string => {
    switch (step) {
      case ContractStep.TYPE_SELECTION:
        return 'Chọn loại hợp đồng';
      case ContractStep.TERMS_ACCEPTANCE:
        return 'Chấp nhận điều khoản';
      case ContractStep.INFO_FORM:
        return 'Thông tin hợp đồng';
      case ContractStep.CONTRACT_DISPLAY:
        return 'Xem hợp đồng';
      case ContractStep.CONTRACT_SIGNING:
        return 'Ký hợp đồng';
      case ContractStep.HAND_SIGNATURE:
        return 'Ký tay';
      case ContractStep.OTP_VERIFICATION:
        return 'Xác thực OTP';
      case ContractStep.COMPLETED:
        return 'Hoàn thành';
      default:
        return 'Bước';
    }
  };

  const stepItems = steps
    .filter(step => step !== ContractStep.COMPLETED) // Bỏ bước hoàn thành khỏi stepper
    .map((step, index) => ({
      id: (index + 1).toString(),
      title: getStepTitle(step),
      status: index < currentStepIndex ? 'completed' as const :
              index === currentStepIndex ? 'processing' as const :
              'waiting' as const,
    }));

  const handleNext = async (updatedData: Partial<ContractData>) => {
    setIsLoading(true);

    try {
      // Cập nhật dữ liệu
      const newData = { ...contractData, ...updatedData };
      setContractData(newData);

      // Chuyển sang bước tiếp theo
      const nextStepIndex = currentStepIndex + 1;
      if (nextStepIndex < steps.length) {
        setCurrentStep(steps[nextStepIndex]);
      }

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      console.error('Error in handleNext:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePrevious = () => {
    const prevStepIndex = currentStepIndex - 1;
    if (prevStepIndex >= 0) {
      setCurrentStep(steps[prevStepIndex]);
    }
  };

  const renderStepContent = () => {
    const stepProps = {
      data: contractData,
      onNext: handleNext,
      onPrevious: handlePrevious,
      isLoading,
    };

    switch (currentStep) {
      case ContractStep.TYPE_SELECTION:
        return <ContractTypeSelector {...stepProps} />;

      case ContractStep.TERMS_ACCEPTANCE:
        return <TermsAcceptance {...stepProps} />;

      case ContractStep.INFO_FORM:
        return contractData.type === ContractType.BUSINESS ? (
          <BusinessInfoForm {...stepProps} />
        ) : contractData.type === ContractType.PERSONAL ? (
          <PersonalInfoForm {...stepProps} />
        ) : (
          <ContractTypeSelector {...stepProps} />
        );

      case ContractStep.CONTRACT_DISPLAY:
        return <ContractDisplay {...stepProps} />;

      case ContractStep.CONTRACT_SIGNING:
        return <ContractSigning {...stepProps} />;

      case ContractStep.HAND_SIGNATURE:
        return <HandSignature {...stepProps} />;

      case ContractStep.OTP_VERIFICATION:
        return <OTPVerification {...stepProps} />;

      case ContractStep.COMPLETED:
        return <ContractSuccess {...stepProps} />;

      default:
        return <ContractTypeSelector {...stepProps} />;
    }
  };



  return (
    <div className="min-h-screen bg-background p-4">
      {/* Progress Stepper */}
      {currentStep !== ContractStep.COMPLETED && (
        <div className="mb-8">
          <Stepper steps={stepItems} showStepIcons />
        </div>
      )}

      {/* Main Content */}
      <div className="w-full">
        <div className="animate-fade-in">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default ContractPrinciplePage;
