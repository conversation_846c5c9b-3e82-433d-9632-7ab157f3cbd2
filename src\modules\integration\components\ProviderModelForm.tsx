import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, Button, Input, Icon, Typography } from '@/shared/components/common';
// Import không cần thiết nữa vì đã inline schema trong component
import {
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderIcon
} from '../types/provider-model.types';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          {/* <Icon name={getProviderIcon(provider)} size="md" /> */}
        </div>
        <div className="font-medium text-gray-900 dark:text-gray-100">{name}</div>
      </div>
    </Card>
  );
};

interface ProviderModelFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: ProviderModel | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: CreateProviderModelDto | UpdateProviderModelDto) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

type FormData = {
  name: string;
  type: TypeProviderEnum;
  apiKey?: string;
};

/**
 * Form tạo/chỉnh sửa Provider Model
 */
const ProviderModelForm: React.FC<ProviderModelFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  const isEditMode = !!initialData;

  // State để quản lý provider được chọn
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    initialData?.type || TypeProviderEnum.OPENAI
  );

  // Tạo schema động dựa trên mode
  const formSchema = useMemo(() => {
    if (isEditMode) {
      // Trong edit mode, API key không bắt buộc và không cần provider field
      return z.object({
        name: z
          .string()
          .min(1, t('integration:providerModel.validation.name.required'))
          .max(255, t('integration:providerModel.validation.name.maxLength'))
          .trim(),
        type: z.nativeEnum(TypeProviderEnum),
        apiKey: z
          .string()
          .optional()
          .refine((val) => !val || val.length >= 10, {
            message: t('integration:providerModel.validation.apiKey.minLength')
          }),
      });
    }
    // Cho create mode, sử dụng schema với provider field nhưng validate với type field
    return z.object({
      name: z
        .string()
        .min(1, t('integration:providerModel.validation.name.required'))
        .max(255, t('integration:providerModel.validation.name.maxLength'))
        .trim(),
      type: z.nativeEnum(TypeProviderEnum, {
        errorMap: () => ({ message: t('integration:providerModel.validation.type.invalid') })
      }),
      apiKey: z
        .string()
        .min(1, t('integration:providerModel.validation.apiKey.required'))
        .min(10, t('integration:providerModel.validation.apiKey.minLength'))
        .regex(/^[a-zA-Z0-9\-_.]+$/, t('integration:providerModel.validation.apiKey.format')),
    });
  }, [isEditMode, t]);

  // Setup form với react-hook-form
  const {
    control,
    handleSubmit,
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      name: initialData?.name || '',
      type: selectedProvider,
      apiKey: '', // Không hiển thị API key cũ vì lý do bảo mật
    },
  });

  // Cập nhật selectedProvider khi initialData thay đổi
  useEffect(() => {
    if (initialData?.type) {
      setSelectedProvider(initialData.type);
      setValue('type', initialData.type);
    }
  }, [initialData?.type, setValue]);

  // Xử lý submit form
  const handleFormSubmit = (data: FormData) => {
    if (isEditMode) {
      // Chỉ gửi các field đã thay đổi cho update
      const updateData: UpdateProviderModelDto = {};
      if (data.name && data.name.trim()) updateData.name = data.name.trim();
      if (data.apiKey && data.apiKey.trim()) updateData.apiKey = data.apiKey.trim();
      onSubmit(updateData);
    } else {
      // Gửi đầy đủ data cho create
      const createData: CreateProviderModelDto = {
        name: data.name.trim(),
        provider: selectedProvider,
        apiKey: data.apiKey?.trim() || '',
      };
      onSubmit(createData);
    }
  };

  // Xử lý khi chọn provider
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    if (!readOnly && !isSubmitting && !isEditMode) {
      setSelectedProvider(provider);
      setValue('type', provider); // Cập nhật form value
    }
  };

  // Danh sách providers với tên hiển thị
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'openai' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'google' },
    { type: TypeProviderEnum.META, name: 'meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'deepSeek' },
    { type: TypeProviderEnum.XAI, name: 'grok' },
  ];

  return (
    <div className="space-y-6">
      {/* Header với Title */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center space-x-3">
          <Icon
            name={readOnly ? "eye" : isEditMode ? "edit" : "plus"}
            size="lg"
            className="text-primary dark:text-primary-400"
          />
          <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white">
            {readOnly
              ? t('integration:providerModel.form.view')
              : isEditMode
                ? t('integration:providerModel.form.edit')
                : t('integration:providerModel.form.create')}
          </Typography>
        </div>
        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mt-2">
          {readOnly
            ? t('integration:providerModel.form.viewDescription')
            : isEditMode
              ? t('integration:providerModel.form.editDescription')
              : t('integration:providerModel.form.createDescription')}
        </Typography>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="space-y-6">

          {/* Provider Type */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('integration:providerModel.form.fields.type')}
              {!isEditMode && <span className="text-red-500 ml-1">*</span>}
            </label>

            {isEditMode ? (
              // Read-only display for edit mode
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="mr-3">
                    <Icon name={getProviderIcon(selectedProvider)} size="md" />
                  </div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {providers.find(p => p.type === selectedProvider)?.name}
                  </div>
                  <div className="ml-auto text-sm text-gray-500 dark:text-gray-400">
                    ({t('integration:providerModel.form.cannotChange')})
                  </div>
                </div>
              </div>
            ) : (
              // Selectable cards for create mode
              <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
                {providers.map((provider) => (
                  <ProviderCard
                    key={provider.type}
                    provider={provider.type}
                    name={provider.name}
                    isSelected={selectedProvider === provider.type}
                    onClick={handleProviderSelect}
                    disabled={readOnly || isSubmitting}
                  />
                ))}
              </div>
            )}

            {/* Hidden input để Form component có thể track giá trị */}
            <Controller
              name="type"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="hidden"
                  value={selectedProvider}
                />
              )}
            />
          </div>

          {/* Provider Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('integration:providerModel.form.fields.name')}
              <span className="text-red-500 ml-1">*</span>
            </label>
            <Controller
              name="name"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  placeholder={t('integration:providerModel.form.fields.namePlaceholder')}
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  error={fieldState.error?.message}
                />
              )}
            />
          </div>

          {/* API Key */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('integration:providerModel.form.fields.apiKey')}
              {!isEditMode && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Controller
              name="apiKey"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  type="password"
                  placeholder={
                    isEditMode
                      ? t('integration:providerModel.form.fields.apiKeyPlaceholderEdit')
                      : t('integration:providerModel.form.fields.apiKeyPlaceholder')
                  }
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  error={fieldState.error?.message}
                />
              )}
            />
          </div>

          {/* Hiển thị thông tin bổ sung khi ở chế độ xem */}
          {readOnly && initialData && (
            <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdAt')}:
                  </span>
                  <div className="mt-1">
                    {new Date(initialData.createdAt).toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdBy')}:
                  </span>
                  <div className="mt-1">
                    {initialData.createdBy?.name || 'N/A'}
                  </div>
                </div>
                {initialData.updatedAt && (
                  <>
                    <div>
                      <span className="font-medium text-gray-500 dark:text-gray-400">
                        {t('common:updatedAt')}:
                      </span>
                      <div className="mt-1">
                        {new Date(initialData.updatedAt).toLocaleString()}
                      </div>
                    </div>
                    {initialData.updatedBy && (
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">
                          {t('common:updatedBy')}:
                        </span>
                        <div className="mt-1">
                          {initialData.updatedBy.name}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('integration:providerModel.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('integration:providerModel.actions.save')}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default ProviderModelForm;
