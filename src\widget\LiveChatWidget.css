/**
 * RedAI Chat Widget CSS
 * Sử dụng CSS variables từ theme của dự án
 */

/* <PERSON><PERSON> báo CSS variables mặc định */
#redai-chat-widget-container {
  --redai-chat-primary-color: var(--color-primary, #ff3333);
  --redai-chat-primary-foreground: var(--color-primary-foreground, #ffffff);
  --redai-chat-background: var(--color-background, #ffffff);
  --redai-chat-foreground: var(--color-foreground, #111827);
  --redai-chat-border: var(--color-border, #e5e7eb);
  --redai-chat-card: var(--color-card, #ffffff);
  --redai-chat-card-muted: var(--color-card-muted, #f9fafb);
  --redai-chat-radius: var(--radius-lg, 0.5rem);
  --redai-chat-shadow: var(
    --shadow-md,
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06)
  );
  --redai-chat-font-family: var(
    --font-family,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    'Open Sans',
    'Helvetica Neue',
    sans-serif
  );
}

/* Chế độ tối */
#redai-chat-widget-container.dark {
  --redai-chat-background: var(--color-background, #111827);
  --redai-chat-foreground: var(--color-foreground, #f9fafb);
  --redai-chat-border: var(--color-border, #374151);
  --redai-chat-card: var(--color-card, #1f2937);
  --redai-chat-card-muted: var(--color-card-muted, #374151);
}

/* Container chính */
.redai-chat-widget {
  position: fixed;
  z-index: 9999;
  font-family: var(--redai-chat-font-family);
  color: var(--redai-chat-foreground);
}

/* Vị trí */
.redai-chat-widget.bottom-right {
  right: 20px;
  bottom: 20px;
}

.redai-chat-widget.bottom-left {
  left: 20px;
  bottom: 20px;
}

.redai-chat-widget.top-right {
  right: 20px;
  top: 20px;
}

.redai-chat-widget.top-left {
  left: 20px;
  top: 20px;
}

/* Nút chat */
.redai-chat-button {
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: var(--redai-chat-shadow) !important;
  transition: transform 0.3s ease !important;
}

.redai-chat-button:hover {
  transform: scale(1.05) !important;
}

/* Container chat */
.redai-chat-container {
  width: 350px;
  height: 450px;
  background-color: var(--redai-chat-card);
  border-radius: var(--redai-chat-radius);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: var(--redai-chat-shadow);
  border: 1px solid var(--redai-chat-border);
}

/* Header */
.redai-chat-header {
  background-color: var(--redai-chat-primary-color);
  color: var(--redai-chat-primary-foreground);
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.redai-chat-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.redai-chat-close {
  color: var(--redai-chat-primary-foreground) !important;
  padding: 4px !important;
}

/* Vùng tin nhắn */
.redai-chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: var(--redai-chat-card-muted);
}

.redai-chat-message {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  min-width: 0;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.redai-chat-message-content {
  padding: 10px 14px;
  border-radius: var(--redai-chat-radius);
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  overflow: hidden;
}

.redai-chat-message-time {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.7;
}

.redai-bot-message {
  align-self: flex-start;
}

.redai-bot-message .redai-chat-message-content {
  background-color: var(--redai-chat-card);
  color: var(--redai-chat-foreground);
  border: 1px solid var(--redai-chat-border);
  border-bottom-left-radius: 4px;
}

.redai-bot-message .redai-chat-message-time {
  margin-left: 4px;
}

.redai-user-message {
  align-self: flex-end;
}

.redai-user-message .redai-chat-message-content {
  background-color: var(--redai-chat-primary-color);
  color: var(--redai-chat-primary-foreground);
  border-bottom-right-radius: 4px;
}

.redai-user-message .redai-chat-message-time {
  align-self: flex-end;
  margin-right: 4px;
}

/* Vùng nhập liệu */
.redai-chat-input-container {
  padding: 12px;
  border-top: 1px solid var(--redai-chat-border);
  background-color: var(--redai-chat-card);
}

.redai-chat-input {
  width: 100% !important;
}

.redai-chat-send {
  margin-left: 8px !important;
  padding: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Responsive */
@media (max-width: 480px) {
  .redai-chat-container {
    width: 300px;
    height: 400px;
  }

  .redai-chat-widget.bottom-right,
  .redai-chat-widget.bottom-left {
    bottom: 10px;
  }

  .redai-chat-widget.bottom-right,
  .redai-chat-widget.top-right {
    right: 10px;
  }

  .redai-chat-widget.bottom-left,
  .redai-chat-widget.top-left {
    left: 10px;
  }
}
