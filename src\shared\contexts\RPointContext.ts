import { createContext } from 'react';

// <PERSON><PERSON><PERSON> nghĩa kiểu dữ liệu cho context
export interface RPointContextType {
  userRPoints: number;
  setUserRPoints: React.Dispatch<React.SetStateAction<number>>;
  deductPoints: (amount: number) => boolean;
  addPoints: (amount: number) => void;
}

// Tạo context với giá trị mặc định
export const RPointContext = createContext<RPointContextType | undefined>(undefined);
