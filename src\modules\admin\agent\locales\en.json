{"agent": {"management": {"title": "Agent Management", "description": "Comprehensive Agent system management"}, "rank": {"title": "Admin - Agent Rank", "description": "Agent ranking management", "pageTitle": "Agent Rank Management", "addRank": "Add New Rank", "editRank": "Edit Agent Rank", "searchPlaceholder": "Search Ranks...", "noSearchResults": "No Ranks found matching your search criteria.", "createFirst": "Create First Agent Rank", "sortBy": "Sort by", "createSuccess": "Success", "createSuccessMessage": "Agent rank has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent rank has been updated successfully", "active": "Active", "inactive": "Inactive", "editAction": "Edit", "deleteAction": "Delete", "confirmDelete": "Confirm Delete Rank", "deleteMessage": "Are you sure you want to delete this rank? This action cannot be undone.", "deleteSuccess": "Rank deleted successfully", "deleteError": "An error occurred while deleting the rank", "list": {"title": "Rank List", "noRanks": "No Ranks", "noRanksDescription": "There are currently no ranks in the system.", "loadError": "Unable to load rank list. Please try again.", "loading": "Loading rank list...", "refreshing": "Refreshing data..."}, "form": {"basicInfo": "Basic Information", "name": "Rank Name", "namePlaceholder": "Enter rank name", "description": "Description", "descriptionPlaceholder": "Enter rank description", "expRange": "Experience Range", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "badge": "Badge", "badgeUpload": "Upload badge", "badgeHelp": "Supported formats: JPG, PNG (single image only)", "currentBadge": "Current Badge", "currentBadgeNote": "Upload new image to replace", "active": "Active", "create": "Create Rank", "update": "Update", "creating": "Creating Rank...", "createSuccess": "Rank created successfully", "createError": "An error occurred while creating the rank", "updateError": "An error occurred while updating the rank"}, "validation": {"nameRequired": "Rank name is required", "descriptionRequired": "Description is required", "minExpInvalid": "Minimum experience must be >= 0", "minExpInteger": "Minimum experience must be an integer", "maxExpInvalid": "Maximum experience must be > 0", "maxExpInteger": "Maximum experience must be an integer", "expRangeInvalid": "Maximum experience must be greater than minimum experience", "expRangeOverlap": "Experience range overlaps with another rank's experience range"}, "edit": {"notFound": "Rank not found"}, "sort": {"name": "Name", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "createdAt": "Created Date"}}, "system": {"title": "Admin - Agent System", "description": "System Agent management", "pageTitle": "System Agent Management", "addAgent": "Add New Agent", "editAgent": "Edit Agent System", "searchPlaceholder": "Search Agents...", "noSearchResults": "No Agents found matching your search criteria.", "createFirst": "Create First System Agent", "viewTrash": "View Trash", "backToMain": "Back to Main List", "cancel": "Cancel", "updateAgent": "Update Agent", "createSuccess": "Success", "createSuccessMessage": "Agent system has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent system has been updated successfully"}, "user": {"title": "Admin - Agent User", "description": "User Agent management"}, "type": {"title": "Admin - Type Agent", "description": "Admin type agent management endpoints"}, "list": {"title": "Agent List", "noAgents": "No Agents", "noAgentsDescription": "There are currently no Agents in the system.", "loadError": "Unable to load Agent list. Please try again.", "loading": "Loading Agent list...", "refreshing": "Refreshing data..."}, "card": {"supervisor": "Supervisor", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "confirmDelete": "Confirm Delete Agent", "deleteMessage": "Are you sure you want to delete this Agent? This action cannot be undone.", "deleteSuccess": "Agent deleted successfully", "deleteError": "An error occurred while deleting the Agent", "updateSuccess": "Agent updated successfully", "updateError": "An error occurred while updating the Agent", "setSupervisor": "Set as Supervisor", "removeSupervisor": "Remove Supervisor", "setSupervisorSuccess": "Successfully set as supervisor", "removeSupervisorSuccess": "Successfully removed supervisor privileges", "supervisorError": "An error occurred while changing supervisor privileges", "restoreSuccess": "Agent restored successfully", "restoreError": "An error occurred while restoring the agent"}, "trash": {"noAgents": "No agents in trash", "noAgentsDescription": "Trash is empty. Deleted agents will appear here."}, "edit": {"notFound": "Agent not found"}, "pagination": {"itemsPerPage": "Items per page", "showingItems": "Showing {from} - {to} of {total} items", "page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "form": {"basicInfo": "Basic Information", "name": "Agent Name", "namePlaceholder": "Enter agent name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent", "description": "Description", "descriptionPlaceholder": "Enter agent description", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "isSupervisor": "Is Supervisor", "create": "Create Agent", "creating": "Creating Agent...", "createSuccess": "Agent created successfully", "createError": "An error occurred while creating the Agent", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar"}, "validation": {"nameRequired": "Agent name is required", "nameCodeRequired": "Identifier code is required", "nameCodeFormat": "Identifier code can only contain lowercase letters, numbers, underscores and hyphens", "instructionRequired": "Instructions are required", "descriptionRequired": "Description is required", "modelRequired": "Model is required", "modelIdInvalid": "Model ID must be a valid UUID"}}}