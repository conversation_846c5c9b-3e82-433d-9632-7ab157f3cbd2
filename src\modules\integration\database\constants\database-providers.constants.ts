import { DatabaseConnectionTemplate, DatabaseSettings } from '../types';

/**
 * Database Provider Types
 */
export const DATABASE_PROVIDER_TYPES = {
  MYSQL: 'mysql',
  POSTGRESQL: 'postgresql',
  MONGODB: 'mongodb',
  REDIS: 'redis',
  SQLITE: 'sqlite',
} as const;

/**
 * Default Database Settings
 */
export const DEFAULT_DATABASE_SETTINGS: DatabaseSettings = {
  connectionTimeout: 30000,
  queryTimeout: 60000,
  maxConnections: 10,
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2,
  },
  poolConfig: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    idleTimeoutMillis: 300000,
  },
  enableLogging: false,
  timezone: 'UTC',
};

/**
 * Database Connection Templates
 */
export const DATABASE_CONNECTION_TEMPLATES: Record<string, DatabaseConnectionTemplate> = {
  [DATABASE_PROVIDER_TYPES.MYSQL]: {
    type: 'mysql',
    name: 'mysql',
    displayName: 'MySQL',
    description: 'MySQL relational database management system',
    icon: 'database',
    documentationUrl: 'https://dev.mysql.com/doc/',
    requiredCredentials: ['host', 'port', 'username', 'password', 'database'],
    optionalCredentials: ['ssl', 'sslCert', 'sslKey', 'sslCa'],
    defaultSettings: {
      connectionTimeout: 30000,
      queryTimeout: 60000,
      maxConnections: 10,
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      poolConfig: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 300000,
      },
      enableLogging: false,
      timezone: 'UTC',
    },
    configurationSteps: [
      'Tạo database và user trong MySQL',
      'Cấp quyền truy cập cho user',
      'Cấu hình firewall cho port 3306',
      'Test kết nối từ ứng dụng',
    ],
    defaultPort: 3306,
    testQuery: 'SELECT 1',
  },

  [DATABASE_PROVIDER_TYPES.POSTGRESQL]: {
    type: 'postgresql',
    name: 'postgresql',
    displayName: 'PostgreSQL',
    description: 'PostgreSQL advanced open source relational database',
    icon: 'database',
    documentationUrl: 'https://www.postgresql.org/docs/',
    requiredCredentials: ['host', 'port', 'username', 'password', 'database'],
    optionalCredentials: ['schema', 'ssl', 'sslCert', 'sslKey', 'sslCa'],
    defaultSettings: {
      connectionTimeout: 30000,
      queryTimeout: 60000,
      maxConnections: 10,
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      poolConfig: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 300000,
      },
      enableLogging: false,
      timezone: 'UTC',
    },
    configurationSteps: [
      'Tạo database và user trong PostgreSQL',
      'Cấp quyền truy cập cho user',
      'Cấu hình pg_hba.conf cho authentication',
      'Cấu hình firewall cho port 5432',
      'Test kết nối từ ứng dụng',
    ],
    defaultPort: 5432,
    testQuery: 'SELECT 1',
  },

  [DATABASE_PROVIDER_TYPES.MONGODB]: {
    type: 'mongodb',
    name: 'mongodb',
    displayName: 'MongoDB',
    description: 'MongoDB NoSQL document database',
    icon: 'database',
    documentationUrl: 'https://docs.mongodb.com/',
    requiredCredentials: ['connectionString', 'database'],
    optionalCredentials: ['collection'],
    defaultSettings: {
      connectionTimeout: 30000,
      queryTimeout: 60000,
      maxConnections: 10,
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      poolConfig: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 300000,
      },
      enableLogging: false,
      timezone: 'UTC',
    },
    configurationSteps: [
      'Tạo MongoDB database',
      'Tạo user với quyền phù hợp',
      'Cấu hình connection string',
      'Cấu hình firewall cho port 27017',
      'Test kết nối từ ứng dụng',
    ],
    defaultPort: 27017,
    testQuery: 'db.runCommand({ping: 1})',
  },

  [DATABASE_PROVIDER_TYPES.REDIS]: {
    type: 'redis',
    name: 'redis',
    displayName: 'Redis',
    description: 'Redis in-memory data structure store',
    icon: 'database',
    documentationUrl: 'https://redis.io/documentation',
    requiredCredentials: ['host', 'port'],
    optionalCredentials: ['password', 'databaseIndex'],
    defaultSettings: {
      connectionTimeout: 30000,
      queryTimeout: 60000,
      maxConnections: 10,
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      poolConfig: {
        min: 2,
        max: 10,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 300000,
      },
      enableLogging: false,
      timezone: 'UTC',
    },
    configurationSteps: [
      'Cài đặt và khởi động Redis server',
      'Cấu hình password (nếu cần)',
      'Cấu hình firewall cho port 6379',
      'Test kết nối từ ứng dụng',
    ],
    defaultPort: 6379,
    testQuery: 'PING',
  },

  [DATABASE_PROVIDER_TYPES.SQLITE]: {
    type: 'sqlite',
    name: 'sqlite',
    displayName: 'SQLite',
    description: 'SQLite lightweight file-based database',
    icon: 'database',
    documentationUrl: 'https://www.sqlite.org/docs.html',
    requiredCredentials: ['filePath'],
    optionalCredentials: ['mode'],
    defaultSettings: {
      connectionTimeout: 30000,
      queryTimeout: 60000,
      maxConnections: 1, // SQLite doesn't support multiple connections
      retryConfig: {
        maxRetries: 3,
        retryDelay: 1000,
        backoffMultiplier: 2,
      },
      poolConfig: {
        min: 1,
        max: 1,
        acquireTimeoutMillis: 30000,
        idleTimeoutMillis: 300000,
      },
      enableLogging: false,
      timezone: 'UTC',
    },
    configurationSteps: [
      'Tạo thư mục chứa database file',
      'Cấu hình quyền truy cập file',
      'Tạo database file (nếu chưa có)',
      'Test kết nối từ ứng dụng',
    ],
    defaultPort: 0, // SQLite doesn't use ports
    testQuery: 'SELECT 1',
  },
};

/**
 * Database Integration API Endpoints
 */
export const DATABASE_INTEGRATION_ENDPOINTS = {
  CONNECTIONS: '/api/v1/integrations/database/connections',
  CONNECTION_DETAIL: (id: string) => `/api/v1/integrations/database/connections/${id}`,
  TEST_CONNECTION: (id: string) => `/api/v1/integrations/database/connections/${id}/test`,
  CONNECTION_STATUS: (id: string) => `/api/v1/integrations/database/connections/${id}/status`,
} as const;

/**
 * Database Integration Query Keys for TanStack Query
 */
export const DATABASE_INTEGRATION_QUERY_KEYS = {
  ALL: ['database-integration'] as const,
  CONNECTIONS: () => [...DATABASE_INTEGRATION_QUERY_KEYS.ALL, 'connections'] as const,
  CONNECTION: (id: string) => [...DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(), id] as const,
  CONNECTION_LIST: (params: Record<string, unknown>) => [...DATABASE_INTEGRATION_QUERY_KEYS.CONNECTIONS(), 'list', params] as const,
} as const;
