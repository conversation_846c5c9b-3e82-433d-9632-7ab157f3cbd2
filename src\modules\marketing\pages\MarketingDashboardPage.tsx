import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  MessageCircle,
  Mail,
  Users,
  TrendingUp,
  ArrowRight,
  BarChart3,
  Target,
  Tag
} from 'lucide-react';
import { Card } from '@/shared/components/common';
import { Button } from '@/shared/components/common';
import { MarketingViewHeader } from '../components/common/MarketingViewHeader';

/**
 * Trang Dashboard tổng quan Marketing
 */
export function MarketingDashboardPage() {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();

  // Mock data cho demo
  const stats = {
    totalAudience: 12450,
    totalSegments: 24,
    activeCampaigns: 8,
    totalTags: 156,
  };

  const channels = [
    {
      id: 'zalo',
      name: 'Zalo Marketing',
      description: 'Quản lý Zalo OA và ZNS campaigns',
      icon: MessageCircle,
      color: 'from-blue-500 to-blue-600',
      stats: {
        accounts: 3,
        followers: 8420,
        messagesSent: 1234,
      },
      path: '/marketing/zalo/overview',
    },
    {
      id: 'email',
      name: 'Email Marketing',
      description: 'Tạo và quản lý email campaigns',
      icon: Mail,
      color: 'from-orange-500 to-red-600',
      stats: {
        templates: 15,
        campaigns: 24,
        emailsSent: 15420,
      },
      path: '/marketing/email/overview',
    },
  ];

  const quickActions = [
    {
      title: 'Quản lý Audience',
      description: 'Tạo và phân đoạn khách hàng',
      icon: Users,
      path: '/marketing/audience',
      color: 'text-blue-600',
    },
    {
      title: 'Quản lý Segments',
      description: 'Tạo nhóm khách hàng theo tiêu chí',
      icon: Target,
      path: '/marketing/segments',
      color: 'text-green-600',
    },
    {
      title: 'Quản lý Tags',
      description: 'Gắn nhãn và phân loại khách hàng',
      icon: Tag,
      path: '/marketing/tags',
      color: 'text-purple-600',
    },
    {
      title: 'Báo cáo Analytics',
      description: 'Xem báo cáo hiệu quả marketing',
      icon: BarChart3,
      path: '/marketing/reports',
      color: 'text-orange-600',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:dashboard.title', 'Marketing Dashboard')}
        description={t('marketing:dashboard.description', 'Tổng quan và quản lý tất cả hoạt động marketing')}
        actions={
          <Button onClick={() => navigate('/marketing/campaigns/create')} className="gap-2">
            <TrendingUp className="h-4 w-4" />
            {t('marketing:dashboard.createCampaign', 'Tạo Chiến dịch')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:dashboard.stats.totalAudience', 'Tổng Audience')}
            </span>
            <Users className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{stats.totalAudience.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:dashboard.stats.audienceGrowth', '+245 tuần này')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:dashboard.stats.segments', 'Segments')}
            </span>
            <Target className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">{stats.totalSegments}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:dashboard.stats.newSegments', '+3 segment mới')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:dashboard.stats.activeCampaigns', 'Chiến dịch đang chạy')}
            </span>
            <TrendingUp className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">{stats.activeCampaigns}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:dashboard.stats.campaignChannels', 'Trên 3 kênh')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:dashboard.stats.tags', 'Tags')}
            </span>
            <Tag className="h-4 w-4 text-purple-600" />
          </div>
          <div className="text-2xl font-bold text-purple-600">{stats.totalTags}</div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:dashboard.stats.newTags', '+12 tag mới')}
          </p>
        </Card>
      </div>

      {/* Marketing Channels */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">
          {t('marketing:dashboard.channels.title', 'Kênh Marketing')}
        </h2>
        <div className="grid gap-4 md:grid-cols-2">
          {channels.map((channel) => {
            const IconComponent = channel.icon;
            return (
              <Card
                key={channel.id}
                className="cursor-pointer hover:shadow-lg transition-all duration-200 p-4"
                onClick={() => navigate(channel.path)}
                hoverable
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={`h-12 w-12 rounded-lg bg-gradient-to-r ${channel.color} flex items-center justify-center text-white shadow-md`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{channel.name}</h3>
                      <p className="text-sm text-muted-foreground">{channel.description}</p>
                    </div>
                  </div>
                  <ArrowRight className="h-5 w-5 text-muted-foreground" />
                </div>

                <div className="mt-4 grid grid-cols-3 gap-4">
                  {Object.entries(channel.stats).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className="text-lg font-semibold">{typeof value === 'number' ? value.toLocaleString() : value}</div>
                      <div className="text-xs text-muted-foreground capitalize">
                        {t(`marketing:dashboard.channels.stats.${key}`, key)}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">
          {t('marketing:dashboard.quickActions.title', 'Thao tác nhanh')}
        </h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <Card
                key={action.title}
                className="cursor-pointer hover:shadow-lg transition-shadow p-4"
                onClick={() => navigate(action.path)}
                hoverable
              >
                <div className="flex items-center space-x-3">
                  <IconComponent className={`h-8 w-8 ${action.color}`} />
                  <div>
                    <h4 className="font-medium">{action.title}</h4>
                    <p className="text-xs text-muted-foreground">{action.description}</p>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Recent Activity */}
      <Card
        title={t('marketing:dashboard.recentActivity.title', 'Hoạt động gần đây')}
        subtitle={t('marketing:dashboard.recentActivity.subtitle', 'Các thay đổi và cập nhật mới nhất')}
        className="p-4"
      >
        <div className="space-y-4">
          {[
            {
              action: t('marketing:dashboard.recentActivity.actions.createSegment', 'Tạo segment mới'),
              description: t('marketing:dashboard.recentActivity.descriptions.segmentVip', 'Segment "Khách hàng VIP" với 245 thành viên'),
              time: t('marketing:dashboard.recentActivity.time.hoursAgo', '2 giờ trước'),
              type: 'segment',
              color: 'bg-green-500',
            },
            {
              action: t('marketing:dashboard.recentActivity.actions.sendEmail', 'Gửi email campaign'),
              description: t('marketing:dashboard.recentActivity.descriptions.newsletter', 'Campaign "Newsletter tháng 1" đã gửi đến 1,250 người'),
              time: t('marketing:dashboard.recentActivity.time.hoursAgo4', '4 giờ trước'),
              type: 'email',
              color: 'bg-orange-500',
            },
            {
              action: t('marketing:dashboard.recentActivity.actions.connectZalo', 'Kết nối Zalo OA'),
              description: t('marketing:dashboard.recentActivity.descriptions.zaloOa', 'Đã kết nối thành công OA "Shop ABC"'),
              time: t('marketing:dashboard.recentActivity.time.dayAgo', '1 ngày trước'),
              type: 'zalo',
              color: 'bg-blue-500',
            },
            {
              action: t('marketing:dashboard.recentActivity.actions.addTags', 'Thêm tags mới'),
              description: t('marketing:dashboard.recentActivity.descriptions.newTags', 'Đã thêm 12 tags cho phân loại khách hàng'),
              time: t('marketing:dashboard.recentActivity.time.daysAgo2', '2 ngày trước'),
              type: 'tag',
              color: 'bg-purple-500',
            },
          ].map((activity, index) => (
            <div key={index} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-muted/50 transition-colors">
              <div className={`h-2 w-2 rounded-full ${activity.color}`} />
              <div className="flex-1">
                <h4 className="font-medium">{activity.action}</h4>
                <p className="text-sm text-muted-foreground">{activity.description}</p>
              </div>
              <div className="text-xs text-muted-foreground">{activity.time}</div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}

export default MarketingDashboardPage;
