import { ReactNode, memo } from 'react';
import { ScrollArea } from '@/shared/components/common';
import ViewHeaderAdmin from './ViewHeaderAdmin';

interface ViewPanelProps {
  title: string;
  children: ReactNode;
  actions?: ReactNode;
}

const ViewPanelAdmin = memo<ViewPanelProps>(({ title, children, actions }) => {
  return (
    <div className="flex flex-col h-full w-full bg-white dark:bg-dark">
      <ViewHeaderAdmin title={title} actions={actions} />
      <ScrollArea
        className="flex-1 p-4 w-full"
        height="100%"
        autoHide={true}
        direction="vertical"
        invisible={false}
      >
        <div className="view-panel-content w-full max-w-full overflow-hidden">{children}</div>
      </ScrollArea>
    </div>
  );
});

ViewPanelAdmin.displayName = 'ViewPanelAdmin';

export default ViewPanelAdmin;
