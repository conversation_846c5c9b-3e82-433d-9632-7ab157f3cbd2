import { ExternalAgentStatus, ProtocolType, MessageType } from '../types';

// Date formatting
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
};

export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  });
};

export const formatRelativeTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return `${diffInSeconds} giây trước`;
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} phút trước`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} giờ trước`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 30) {
    return `${diffInDays} ngày trước`;
  }

  return formatDate(dateString);
};

// Duration formatting
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }

  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

// Response time formatting
export const formatResponseTime = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  return `${(milliseconds / 1000).toFixed(2)}s`;
};

// File size formatting
export const formatFileSize = (bytes: number): string => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(unitIndex === 0 ? 0 : 2)} ${units[unitIndex]}`;
};

// Number formatting
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('vi-VN').format(num);
};

export const formatPercentage = (value: number, total: number): string => {
  if (total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(1)}%`;
};

// Status formatting
export const formatStatus = (status: ExternalAgentStatus): string => {
  const statusMap: Record<ExternalAgentStatus, string> = {
    [ExternalAgentStatus.ACTIVE]: 'Hoạt động',
    [ExternalAgentStatus.INACTIVE]: 'Không hoạt động',
    [ExternalAgentStatus.CONNECTING]: 'Đang kết nối',
    [ExternalAgentStatus.ERROR]: 'Lỗi',
    [ExternalAgentStatus.MAINTENANCE]: 'Bảo trì',
  };
  return statusMap[status] || status;
};

// Protocol formatting
export const formatProtocol = (protocol: ProtocolType): string => {
  const protocolMap: Record<ProtocolType, string> = {
    [ProtocolType.MCP]: 'Model Context Protocol',
    [ProtocolType.GOOGLE_AGENT]: 'Google Agent',
    [ProtocolType.REST_API]: 'REST API',
    [ProtocolType.WEBSOCKET]: 'WebSocket',
    [ProtocolType.GRPC]: 'gRPC',
    [ProtocolType.CUSTOM]: 'Tùy chỉnh',
  };
  return protocolMap[protocol] || protocol;
};

// Message type formatting
export const formatMessageType = (type: MessageType): string => {
  const typeMap: Record<MessageType, string> = {
    [MessageType.REQUEST]: 'Yêu cầu',
    [MessageType.RESPONSE]: 'Phản hồi',
    [MessageType.ERROR]: 'Lỗi',
    [MessageType.NOTIFICATION]: 'Thông báo',
  };
  return typeMap[type] || type;
};

// URL formatting
export const formatUrl = (url: string, maxLength = 50): string => {
  if (url.length <= maxLength) {
    return url;
  }

  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname;
    const path = urlObj.pathname;

    if (domain.length + path.length <= maxLength - 3) {
      return `${domain}${path}`;
    }

    return `${domain}...${path.slice(-20)}`;
  } catch {
    return url.length > maxLength ? `${url.slice(0, maxLength - 3)}...` : url;
  }
};

// JSON formatting
export const formatJson = (obj: unknown, indent = 2): string => {
  try {
    return JSON.stringify(obj, null, indent);
  } catch {
    return String(obj);
  }
};

// Error formatting
export const formatError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Lỗi không xác định';
};

// Capability formatting
export const formatCapabilities = (capabilities: string[]): string => {
  if (capabilities.length === 0) {
    return 'Không có khả năng nào';
  }
  if (capabilities.length <= 3) {
    return capabilities.join(', ');
  }
  return `${capabilities.slice(0, 3).join(', ')} và ${capabilities.length - 3} khả năng khác`;
};

// Tags formatting
export const formatTags = (tags: string[]): string => {
  if (tags.length === 0) {
    return 'Không có thẻ';
  }
  if (tags.length <= 3) {
    return tags.join(', ');
  }
  return `${tags.slice(0, 3).join(', ')} +${tags.length - 3}`;
};

// Uptime formatting
export const formatUptime = (uptimePercentage: number): string => {
  return `${uptimePercentage.toFixed(2)}%`;
};

// Success rate formatting
export const formatSuccessRate = (successful: number, total: number): string => {
  if (total === 0) return '0%';
  const rate = (successful / total) * 100;
  return `${rate.toFixed(1)}%`;
};
