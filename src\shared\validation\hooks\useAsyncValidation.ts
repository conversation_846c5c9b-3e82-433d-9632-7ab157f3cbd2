import { useState, useCallback, useRef, useEffect } from 'react';
import {
  AsyncValidator,
  ValidationResult,
  ValidationContext,
  AsyncValidationOptions,
  ValidationCache,
  ValidationCacheEntry,
  ValidationMetrics
} from '../types';

/**
 * Simple in-memory validation cache
 */
class MemoryValidationCache implements ValidationCache {
  private cache = new Map<string, ValidationCacheEntry>();

  get(key: string): ValidationCacheEntry | undefined {
    const entry = this.cache.get(key);
    if (entry && Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return undefined;
    }
    return entry;
  }

  set(key: string, entry: ValidationCacheEntry): void {
    this.cache.set(key, entry);
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

/**
 * Async validation hook with caching, retries, and timeout support
 */
export const useAsyncValidation = <T = unknown>(
  validator: AsyncValidator<T>,
  options: AsyncValidationOptions<T> = {}
) => {
  const {
    debounceMs = 500,
    timeout = 5000,
    retries = 2,
    retryDelay = 1000,
    cache = true,
    cacheKey,

  } = options;

  // State
  const [isValidating, setIsValidating] = useState(false);
  const [result, setResult] = useState<ValidationResult | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [metrics, setMetrics] = useState<ValidationMetrics>({
    totalValidations: 0,
    successfulValidations: 0,
    failedValidations: 0,
    averageValidationTime: 0,
    cacheHitRate: 0,
    asyncValidationCount: 0,
  });

  // Refs
  const debounceTimeout = useRef<NodeJS.Timeout | null>(null);
  const abortController = useRef<AbortController | null>(null);
  const validationCache = useRef<ValidationCache>(new MemoryValidationCache());
  const validationTimes = useRef<number[]>([]);

  /**
   * Generate cache key for validation
   */
  const generateCacheKey = useCallback((
    value: T,
    context: ValidationContext
  ): string => {
    if (cacheKey) {
      return cacheKey(value, context);
    }

    return `${context.field}:${JSON.stringify(value)}:${JSON.stringify(context.formValues)}`;
  }, [cacheKey]);

  /**
   * Update metrics
   */
  const updateMetrics = useCallback((
    success: boolean,
    validationTime: number,
    cacheHit: boolean
  ) => {
    setMetrics(prev => {
      const newTimes = [...validationTimes.current, validationTime];
      if (newTimes.length > 100) {
        newTimes.shift(); // Keep only last 100 measurements
      }
      validationTimes.current = newTimes;

      const totalValidations = prev.totalValidations + 1;
      const successfulValidations = prev.successfulValidations + (success ? 1 : 0);
      const failedValidations = prev.failedValidations + (success ? 0 : 1);
      const averageValidationTime = newTimes.reduce((a, b) => a + b, 0) / newTimes.length;
      const cacheHitRate = cacheHit
        ? (prev.cacheHitRate * prev.totalValidations + 1) / totalValidations
        : (prev.cacheHitRate * prev.totalValidations) / totalValidations;

      return {
        totalValidations,
        successfulValidations,
        failedValidations,
        averageValidationTime,
        cacheHitRate,
        asyncValidationCount: prev.asyncValidationCount + 1,
      };
    });
  }, []);

  /**
   * Perform validation with retry logic
   */
  const performValidationWithRetry = useCallback(async (
    value: T,
    context: ValidationContext,
    attempt = 0
  ): Promise<ValidationResult> => {
    try {
      // Create abort controller for this validation
      abortController.current = new AbortController();

      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error('Validation timeout'));
        }, timeout);
      });

      // Create validation promise
      const validationPromise = validator(value, {
        ...context,
        meta: {
          ...context.meta,
          signal: abortController.current.signal,
          attempt,
        },
      });

      // Race between validation and timeout
      const result = await Promise.race([validationPromise, timeoutPromise]);

      return result;
    } catch (error) {
      if (attempt < retries) {
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return performValidationWithRetry(value, context, attempt + 1);
      }

      throw error;
    }
  }, [validator, timeout, retries, retryDelay]);

  /**
   * Validate with caching and metrics
   */
  const validate = useCallback(async (
    value: T,
    context: ValidationContext,
    immediate = false
  ): Promise<ValidationResult> => {
    const startTime = Date.now();

    try {
      // Check cache first
      let cacheHit = false;
      if (cache) {
        const key = generateCacheKey(value, context);
        const cachedEntry = validationCache.current.get(key);

        if (cachedEntry) {
          cacheHit = true;
          updateMetrics(true, Date.now() - startTime, true);
          return cachedEntry.result;
        }
      }

      // Clear existing debounce timeout
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
        debounceTimeout.current = null;
      }

      // Abort any ongoing validation
      if (abortController.current) {
        abortController.current.abort();
      }

      const performValidation = async (): Promise<ValidationResult> => {
        setIsValidating(true);
        setError(null);

        try {
          const result = await performValidationWithRetry(value, context);

          // Cache the result
          if (cache) {
            const key = generateCacheKey(value, context);
            const cacheEntry: ValidationCacheEntry = {
              result,
              timestamp: Date.now(),
              ttl: 5 * 60 * 1000, // 5 minutes default TTL
            };
            validationCache.current.set(key, cacheEntry);
          }

          setResult(result);
          updateMetrics(true, Date.now() - startTime, cacheHit);

          return result;
        } catch (error) {
          const validationError = error instanceof Error ? error : new Error('Unknown validation error');
          setError(validationError);
          updateMetrics(false, Date.now() - startTime, cacheHit);

          // Return error result
          const errorResult: ValidationResult = {
            isValid: false,
            errors: [{
              field: context.field,
              message: validationError.message,
              code: 'ASYNC_VALIDATION_ERROR',
              severity: 'error',
            }],
            warnings: [],
            infos: [],
          };

          setResult(errorResult);
          return errorResult;
        } finally {
          setIsValidating(false);
        }
      };

      if (immediate || debounceMs <= 0) {
        return performValidation();
      } else {
        // Debounced validation
        return new Promise((resolve) => {
          debounceTimeout.current = setTimeout(async () => {
            const result = await performValidation();
            resolve(result);
          }, debounceMs);
        });
      }
    } catch (error) {
      updateMetrics(false, Date.now() - startTime, false);
      throw error;
    }
  }, [
    cache,
    generateCacheKey,
    debounceMs,
    performValidationWithRetry,
    updateMetrics,
  ]);

  /**
   * Clear validation state
   */
  const clear = useCallback(() => {
    if (debounceTimeout.current) {
      clearTimeout(debounceTimeout.current);
      debounceTimeout.current = null;
    }

    if (abortController.current) {
      abortController.current.abort();
      abortController.current = null;
    }

    setIsValidating(false);
    setResult(null);
    setError(null);
  }, []);

  /**
   * Clear cache
   */
  const clearCache = useCallback(() => {
    validationCache.current.clear();
  }, []);

  /**
   * Get cache size
   */
  const getCacheSize = useCallback(() => {
    return validationCache.current.size();
  }, []);

  /**
   * Preload validation for a value
   */
  const preload = useCallback(async (
    value: T,
    context: ValidationContext
  ): Promise<void> => {
    if (!cache) return;

    const key = generateCacheKey(value, context);
    const cachedEntry = validationCache.current.get(key);

    if (!cachedEntry) {
      try {
        await validate(value, context, true);
      } catch (error) {
        // Ignore preload errors
        console.warn('Preload validation failed:', error);
      }
    }
  }, [cache, generateCacheKey, validate]);

  /**
   * Batch validate multiple values
   */
  const batchValidate = useCallback(async (
    values: Array<{ value: T; context: ValidationContext }>,
    immediate = false
  ): Promise<ValidationResult[]> => {
    const promises = values.map(({ value, context }) =>
      validate(value, context, immediate)
    );

    return Promise.all(promises);
  }, [validate]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeout.current) {
        clearTimeout(debounceTimeout.current);
      }

      if (abortController.current) {
        abortController.current.abort();
      }
    };
  }, []);

  return {
    // State
    isValidating,
    result,
    error,
    metrics,

    // Methods
    validate,
    clear,
    clearCache,
    getCacheSize,
    preload,
    batchValidate,

    // Computed properties
    isValid: result?.isValid ?? true,
    hasErrors: result ? result.errors.length > 0 : false,
    hasWarnings: result ? result.warnings.length > 0 : false,
    errors: result?.errors ?? [],
    warnings: result?.warnings ?? [],
    infos: result?.infos ?? [],
  };
};
