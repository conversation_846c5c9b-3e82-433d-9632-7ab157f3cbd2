import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';
import { RouteGuard } from '@/shared/hoc';

// Lazy load SettingsPage
const SettingsPage = lazy(() => import('../pages/SettingsPage'));

export const settingsRoutes: RouteObject[] = [
  {
    path: '/settings',
    element: (
      <MainLayout title="Cài đặt">
        <Suspense fallback={<Loading />}>
          <RouteGuard component={SettingsPage} type="PROTECT" />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default settingsRoutes;
