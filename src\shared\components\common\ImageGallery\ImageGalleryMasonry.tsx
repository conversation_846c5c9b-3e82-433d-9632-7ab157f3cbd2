import React, { useMemo } from 'react';
import { useImageGallery } from './useImageGallery';
import { GalleryImage } from './types';
import { Loading } from '@/shared/components/common';

interface ImageGalleryMasonryProps {
  className?: string;
}

/**
 * Component hiển thị layout dạng masonry cho Image Gallery
 */
const ImageGalleryMasonry: React.FC<ImageGalleryMasonryProps> = ({ className = '' }) => {
  const { images, columns, gap, lightboxEnabled, openLightbox, onImageClick, lazyLoadEnabled } =
    useImageGallery();

  // Tính toán columns dựa trên responsive config
  const masonryColumns = useMemo(() => {
    if (typeof columns === 'number') {
      return columns;
    }

    // Default responsive columns
    return {
      xs: columns.xs || 1,
      sm: columns.sm || 2,
      md: columns.md || 3,
      lg: columns.lg || 4,
      xl: columns.xl || 5,
    };
  }, [columns]);

  // Chia hình ảnh vào các columns
  const columnedImages = useMemo(() => {
    const columnCount =
      typeof masonryColumns === 'number' ? masonryColumns : masonryColumns.xs || 1;

    const result: GalleryImage[][] = Array.from({ length: columnCount }, () => []);

    images.forEach((image: GalleryImage, index: number) => {
      const columnIndex = index % columnCount;
      const column = result[columnIndex];
      if (column !== undefined) {
        column.push(image);
      }
    });

    return result;
  }, [images, masonryColumns]);

  // Xử lý click vào hình ảnh
  const handleImageClick = (image: GalleryImage, index: number) => {
    if (lightboxEnabled) {
      // Tính toán index thực tế trong mảng images gốc
      const originalIndex = images.findIndex((img: GalleryImage) => img === image);
      openLightbox(originalIndex);
    }

    if (onImageClick) {
      onImageClick(image, index);
    }
  };

  return (
    <div
      className={`grid gap-${typeof gap === 'number' ? gap : 4} ${className}`}
      style={{
        gridTemplateColumns: `repeat(${typeof masonryColumns === 'number' ? masonryColumns : masonryColumns.xs || 1}, 1fr)`,
        gap: typeof gap === 'string' ? gap : undefined,
      }}
      data-testid="image-gallery-masonry"
    >
      {columnedImages.map((column, columnIndex) => (
        <div key={`masonry-column-${columnIndex}`} className="flex flex-col space-y-4">
          {column.map((image, imageIndex) => {
            // Tính toán index thực tế trong mảng images gốc
            const columnCount =
              typeof masonryColumns === 'number' ? masonryColumns : masonryColumns.xs || 1;
            const originalIndex = columnIndex + imageIndex * columnCount;

            return (
              <MasonryItem
                key={`masonry-image-${columnIndex}-${imageIndex}`}
                image={image}
                index={originalIndex}
                onClick={handleImageClick}
                useLazyLoad={lazyLoadEnabled}
              />
            );
          })}
        </div>
      ))}
    </div>
  );
};

interface MasonryItemProps {
  image: GalleryImage;
  index: number;
  onClick: (image: GalleryImage, index: number) => void;
  useLazyLoad: boolean;
}

const MasonryItem: React.FC<MasonryItemProps> = ({ image, index, onClick, useLazyLoad }) => {
  // Sử dụng lazy load nếu được bật
  const { ref, inView, isLoaded, imageSrc, onLoad } = useLazyLoad
    ? {
        ref: React.createRef<HTMLElement>(),
        inView: true,
        isLoaded: false,
        imageSrc: image.src,
        onLoad: () => {},
      }
    : {
        ref: React.createRef<HTMLElement>(),
        inView: true,
        isLoaded: true,
        imageSrc: image.src,
        onLoad: () => {},
      };

  // Tính toán aspect ratio nếu có width và height
  const aspectRatio = useMemo(() => {
    if (image.width && image.height) {
      return (image.height / image.width) * 100;
    }
    return null;
  }, [image.width, image.height]);

  // Xử lý click vào hình ảnh
  const handleClick = () => {
    onClick(image, index);
  };

  return (
    <figure
      ref={useLazyLoad ? (ref as React.RefObject<HTMLElement>) : undefined}
      className="relative overflow-hidden rounded-lg transition-transform duration-300 hover:scale-[1.02] cursor-pointer shadow-sm dark:shadow-gray-800"
      onClick={handleClick}
      role="button"
      tabIndex={0}
      aria-label={image.alt || `Image ${index + 1}`}
      onKeyDown={e => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <div
        className="bg-gray-100 dark:bg-gray-800"
        style={aspectRatio ? { paddingBottom: `${aspectRatio}%` } : undefined}
      >
        <img
          src={useLazyLoad ? imageSrc : image.src}
          alt={image.alt || `Image ${index + 1}`}
          className={`w-full h-full object-cover transition-opacity duration-300 ${
            useLazyLoad && !isLoaded ? 'opacity-0' : 'opacity-100'
          } ${!aspectRatio ? '' : 'absolute inset-0'}`}
          onLoad={useLazyLoad ? onLoad : undefined}
        />

        {/* Loading indicator */}
        {useLazyLoad && inView && !isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Loading />
          </div>
        )}
      </div>

      {/* Caption */}
      {image.caption && (
        <figcaption className="p-2 text-sm text-gray-600 dark:text-gray-300 truncate">
          {image.caption}
        </figcaption>
      )}
    </figure>
  );
};

export default ImageGalleryMasonry;
