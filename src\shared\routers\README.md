# Router Protection System

Hệ thống bảo vệ route tự động kiểm tra token và điều hướng người dùng dựa trên trạng thái xác thực.

## Tính năng

### 1. Tự động kiểm tra token
- <PERSON><PERSON><PERSON> tra token có tồn tại và còn hạn không
- Tự động xóa token hết hạn khỏi localStorage và Redux store
- Log thông tin debug để dễ dàng troubleshoot

### 2. Phân loại routes tự động
- **Public Routes**: `/auth`, `/admin/auth` - không cần xác thực
- **User Routes**: Tất cả routes không bắt đầu với `/admin` - yêu cầu USER token
- **Admin Routes**: Routes bắt đầu với `/admin` - yêu cầu ADMIN token

### 3. Đ<PERSON><PERSON>u hướng thông minh
- User không có token → `/auth`
- Admin không có token → `/admin/auth`
- User cố truy cập admin route → `/admin/auth`
- Admin cố truy cập user route → `/auth`

## Cách sử dụng

### Cấu trúc hiện tại

```tsx
// src/shared/routers/index.tsx
const router = createBrowserRouter([
  // Public routes (auth routes) - không cần protection
  ...publicRoutes,
  
  // User routes - yêu cầu USER token
  ...wrapRoutesWithAuth(protectedUserRoutes, AuthType.USER, '/auth'),
  
  // Admin routes - yêu cầu ADMIN token
  ...wrapRoutesWithAuth(protectedAdminRoutes, AuthType.ADMIN, '/admin/auth'),
]);
```

### Thêm route mới

1. **User Route**: Thêm vào module tương ứng, sẽ tự động được bảo vệ
```tsx
// Ví dụ: src/modules/data/routers/dataRoutes.tsx
const dataRoutes: RouteObject[] = [
  {
    path: '/data/new-feature',
    element: <NewFeaturePage />
  }
];
```

2. **Admin Route**: Đảm bảo path bắt đầu với `/admin`
```tsx
// Ví dụ: src/modules/admin/data/routers/adminDataRoutes.tsx
const adminDataRoutes: RouteObject[] = [
  {
    path: '/admin/data/new-feature',
    element: <AdminNewFeaturePage />
  }
];
```

3. **Public Route**: Thêm vào `isPublicRoute` function
```tsx
// src/shared/routers/utils/routeProtection.tsx
const isPublicRoute = (path: string): boolean => {
  const publicPaths = [
    '/auth',
    '/admin/auth',
    '/public-page', // Thêm route public mới
  ];
  
  return publicPaths.some(publicPath => path.startsWith(publicPath));
};
```

## Components

### ProtectedRoute
Component wrapper tự động kiểm tra xác thực và điều hướng.

```tsx
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredAuthType?: AuthType;
  redirectTo?: string;
}
```

### wrapRoutesWithAuth
Hàm utility để wrap nhiều routes với protection.

```tsx
wrapRoutesWithAuth(
  routes: RouteObject[], 
  requiredAuthType: AuthType = AuthType.USER,
  redirectTo?: string
): RouteObject[]
```

### categorizeRoutes
Hàm phân loại routes thành user, admin và public routes.

```tsx
categorizeRoutes(routes: RouteObject[]) => {
  userRoutes: RouteObject[];
  adminRoutes: RouteObject[];
  publicRoutes: RouteObject[];
}
```

## Testing

### Route Protection Test Page
Truy cập `/test/route-protection` để test hệ thống protection:

- Hiển thị trạng thái xác thực hiện tại
- Test navigation giữa các loại routes
- Test behavior khi clear auth
- Debug logs trong console

### Debug Logs
Hệ thống tự động log thông tin debug:

```javascript
// Console logs
ProtectedRoute check: {
  currentPath: "/data",
  requiredAuthType: "user", 
  currentAuthType: "user",
  isAuthenticated: true,
  isTokenValid: true,
  redirectTo: "/auth"
}

Route categorization: {
  publicRoutes: 8,
  userRoutes: 25,
  adminRoutes: 15,
  publicPaths: ["/auth", "/admin/auth"],
  userPaths: ["/data", "/business", "/profile", "/marketing", "/rpoint"],
  adminPaths: ["/admin", "/admin/user", "/admin/business", "/admin/data", "/admin/marketing"]
}
```

## Troubleshooting

### Token không được kiểm tra
1. Kiểm tra `useAuthCommon` hook có hoạt động đúng không
2. Xem console logs để debug
3. Kiểm tra localStorage có chứa token không

### Redirect loop
1. Kiểm tra route có được phân loại đúng không
2. Đảm bảo auth routes được đánh dấu là public
3. Kiểm tra token validity logic

### Route không được bảo vệ
1. Kiểm tra route có được include trong `allRoutes` không
2. Xem console logs để kiểm tra categorization
3. Đảm bảo route path đúng format

## Tích hợp với useAuthCommon

Hệ thống sử dụng `useAuthCommon` hook để:
- Kiểm tra `isTokenValid()`
- Lấy `authType` hiện tại
- Gọi `clearAuth()` khi token hết hạn
- Kiểm tra `isAuthenticated` status

Đảm bảo hook này hoạt động đúng để hệ thống protection hoạt động tốt.
