import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import {
  ProductImportState,
  ProductImportStep,
  ProductColumnMapping,
  ProductExcelData,
  ProductValidationResult,
  ProductImportOptions,
  ProductImportProgress,
  ProductImportValidateRequest,
  ProductImportStartRequest,
  ProductImportUrlData,
} from '../types/product-import.types';
import { DEFAULT_PRODUCT_IMPORT_OPTIONS, PRODUCT_IMPORT_QUERY_KEYS } from '../constants/product-import.constants';
import {
  uploadProductFile,
  uploadProductFileFromUrl,
  validateProductImportWithBusinessLogic,
  startProductImportWithBusinessLogic,
  getProductImportProgressWithBusinessLogic,
} from '../services/product-import.service';

// Initial state
const initialState: ProductImportState = {
  step: 'upload',
  mappings: [],
  importOptions: DEFAULT_PRODUCT_IMPORT_OPTIONS,
};

/**
 * Hook for managing product import state and logic
 */
export const useProductImport = () => {
  const [importState, setImportState] = useState<ProductImportState>(initialState);

  // Update import state
  const updateImportState = useCallback((updates: Partial<ProductImportState>) => {
    setImportState(prev => ({
      ...prev,
      ...updates,
    }));
  }, []);

  // Set current step
  const setStep = useCallback((step: ProductImportStep) => {
    updateImportState({ step });
  }, [updateImportState]);

  // Set Excel data (from upload step)
  const setExcelData = useCallback((excelData: ProductExcelData) => {
    updateImportState({ 
      excelData,
      step: 'mapping',
      mappings: [], // Reset mappings when new data is uploaded
    });
  }, [updateImportState]);

  // Update column mappings
  const updateMappings = useCallback((mappings: ProductColumnMapping[]) => {
    updateImportState({ mappings });
  }, [updateImportState]);

  // Add or update a single mapping
  const updateMapping = useCallback((excelColumn: string, productField: string) => {
    setImportState(prev => {
      const existingMappings = prev.mappings.filter(m => m.excelColumn !== excelColumn);
      const newMappings = productField 
        ? [...existingMappings, { excelColumn, productField }]
        : existingMappings;
      
      return {
        ...prev,
        mappings: newMappings,
      };
    });
  }, []);

  // Set validation result (from preview step)
  const setValidationResult = useCallback((validationResult: ProductValidationResult) => {
    updateImportState({ 
      validationResult,
      step: 'preview',
    });
  }, [updateImportState]);

  // Update import options
  const updateImportOptions = useCallback((options: Partial<ProductImportOptions>) => {
    setImportState(prev => ({
      ...prev,
      importOptions: {
        ...prev.importOptions,
        ...options,
      },
    }));
  }, []);

  // Start import process
  const startImport = useCallback((importProgress: ProductImportProgress) => {
    updateImportState({
      importProgress,
      step: 'importing',
    });
  }, [updateImportState]);

  // Update import progress
  const updateImportProgress = useCallback((progress: Partial<ProductImportProgress>) => {
    setImportState(prev => ({
      ...prev,
      importProgress: prev.importProgress ? {
        ...prev.importProgress,
        ...progress,
      } : undefined,
    }));
  }, []);

  // Reset to initial state
  const resetImport = useCallback(() => {
    setImportState(initialState);
  }, []);

  // Go to previous step
  const goToPreviousStep = useCallback(() => {
    setImportState(prev => {
      const stepOrder: ProductImportStep[] = ['upload', 'mapping', 'preview', 'importing'];
      const currentIndex = stepOrder.indexOf(prev.step);
      
      if (currentIndex > 0) {
        return {
          ...prev,
          step: stepOrder[currentIndex - 1],
        };
      }
      
      return prev;
    });
  }, []);

  // Go to next step
  const goToNextStep = useCallback(() => {
    setImportState(prev => {
      const stepOrder: ProductImportStep[] = ['upload', 'mapping', 'preview', 'importing'];
      const currentIndex = stepOrder.indexOf(prev.step);
      
      if (currentIndex < stepOrder.length - 1) {
        return {
          ...prev,
          step: stepOrder[currentIndex + 1],
        };
      }
      
      return prev;
    });
  }, []);

  // Check if can proceed to next step
  const canProceedToNextStep = useCallback(() => {
    switch (importState.step) {
      case 'upload':
        return !!importState.excelData;
      case 'mapping':
        return importState.mappings.length > 0;
      case 'preview':
        return !!importState.validationResult;
      case 'importing':
        return false; // Cannot proceed from importing step
      default:
        return false;
    }
  }, [importState]);

  // Get current step index
  const getCurrentStepIndex = useCallback(() => {
    const stepOrder: ProductImportStep[] = ['upload', 'mapping', 'preview', 'importing'];
    return stepOrder.indexOf(importState.step);
  }, [importState.step]);

  // Check if import is completed
  const isImportCompleted = useCallback(() => {
    return importState.importProgress?.status === 'completed';
  }, [importState.importProgress]);

  // Check if import failed
  const isImportFailed = useCallback(() => {
    return importState.importProgress?.status === 'failed';
  }, [importState.importProgress]);

  // Check if import is in progress
  const isImportInProgress = useCallback(() => {
    return importState.importProgress?.status === 'processing';
  }, [importState.importProgress]);

  return {
    // State
    importState,

    // Actions
    updateImportState,
    setStep,
    setExcelData,
    updateMappings,
    updateMapping,
    setValidationResult,
    updateImportOptions,
    startImport,
    updateImportProgress,
    resetImport,
    goToPreviousStep,
    goToNextStep,

    // Computed values
    canProceedToNextStep: canProceedToNextStep(),
    currentStepIndex: getCurrentStepIndex(),
    isImportCompleted: isImportCompleted(),
    isImportFailed: isImportFailed(),
    isImportInProgress: isImportInProgress(),
  };
};

/**
 * Hook for uploading product file
 */
export const useUploadProductFile = () => {
  return useMutation({
    mutationFn: ({ file, hasHeader }: { file: File; hasHeader: boolean }) =>
      uploadProductFile(file, hasHeader),
    onError: (error) => {
      console.error('Product file upload error:', error);
    },
  });
};

/**
 * Hook for uploading product file from URL
 */
export const useUploadProductFileFromUrl = () => {
  return useMutation({
    mutationFn: (urlData: ProductImportUrlData) => uploadProductFileFromUrl(urlData),
    onError: (error) => {
      console.error('Product file URL upload error:', error);
    },
  });
};

/**
 * Hook for validating product import data
 */
export const useValidateProductImport = () => {
  return useMutation({
    mutationFn: (request: ProductImportValidateRequest) =>
      validateProductImportWithBusinessLogic(request),
    onError: (error) => {
      console.error('Product import validation error:', error);
    },
  });
};

/**
 * Hook for starting product import
 */
export const useStartProductImport = () => {
  return useMutation({
    mutationFn: (request: ProductImportStartRequest) =>
      startProductImportWithBusinessLogic(request),
    onError: (error) => {
      console.error('Product import start error:', error);
    },
  });
};

/**
 * Hook for getting product import progress
 */
export const useProductImportProgress = (jobId: string | undefined, enabled: boolean = true) => {
  return useQuery({
    queryKey: PRODUCT_IMPORT_QUERY_KEYS.PROGRESS(jobId || ''),
    queryFn: () => getProductImportProgressWithBusinessLogic(jobId!),
    enabled: enabled && !!jobId,
    refetchInterval: (query) => {
      // Stop polling if import is completed or failed
      const data = query.state.data;
      if (data?.progress?.status === 'completed' || data?.progress?.status === 'failed') {
        return false;
      }
      return 1000; // Poll every 1 second
    },
    staleTime: 0, // Always fetch fresh data
  });
};
