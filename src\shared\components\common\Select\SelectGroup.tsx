import React, { ReactNode } from 'react';

export interface SelectGroupProps {
  /**
   * Label của group
   */
  label: string;

  /**
   * Nội dung của group (các options)
   */
  children: ReactNode;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị một group trong Select
 */
const SelectGroup: React.FC<SelectGroupProps> = ({ label, children, className = '' }) => {
  return (
    <div className={`py-1 ${className}`} role="group" aria-label={label}>
      <div className="px-4 py-1 text-xs font-medium text-muted uppercase tracking-wider">
        {label}
      </div>
      {children}
    </div>
  );
};

export default SelectGroup;
