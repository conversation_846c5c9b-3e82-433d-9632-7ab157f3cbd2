import React, { useState, ReactNode, useEffect, useRef } from 'react';
import Icon from '@/shared/components/common/Icon';
import Badge from '@/shared/components/common/Badge';
import '@/shared/styles/animation.css';

export type FormSectionVariant = 'default' | 'bordered' | 'elevated' | 'gradient';
export type FormSectionSize = 'sm' | 'md' | 'lg';
export type FormSectionIconPosition = 'left' | 'right';

export interface FormSectionProps {
  /**
   * Tiêu đề của section
   */
  title: string;

  /**
   * Mô tả của section (optional)
   */
  description?: string;

  /**
   * Nội dung của section
   */
  children: ReactNode;

  /**
   * Có thể đóng/mở section hay không
   * @default false
   */
  collapsible?: boolean;

  /**
   * Trạng thái mặc định của section (mở/đóng)
   * @default true
   */
  defaultExpanded?: boolean;

  /**
   * Biến thể của section
   * @default 'default'
   */
  variant?: FormSectionVariant;

  /**
   * Kích thước của section
   * @default 'md'
   */
  size?: FormSectionSize;

  /**
   * Icon tùy chỉnh cho header (thay thế cho chevron-up/down)
   */
  icon?: ReactNode;

  /**
   * Vị trí của icon
   * @default 'right'
   */
  iconPosition?: FormSectionIconPosition;

  /**
   * Badge hiển thị bên cạnh tiêu đề
   */
  badge?: ReactNode | string | number;

  /**
   * Màu nền của header
   */
  headerBgColor?: string;

  /**
   * Màu nền của content
   */
  contentBgColor?: string;

  /**
   * Hiệu ứng animation khi đóng/mở
   * @default true
   */
  animated?: boolean;

  /**
   * Loại animation
   * @default 'slide'
   */
  animationType?: 'fade' | 'slide' | 'both';

  /**
   * Thời gian animation (ms)
   * @default 300
   */
  animationDuration?: number;

  /**
   * Class bổ sung cho section
   */
  className?: string;

  /**
   * Class bổ sung cho tiêu đề
   */
  titleClassName?: string;

  /**
   * Class bổ sung cho mô tả
   */
  descriptionClassName?: string;

  /**
   * Class bổ sung cho nội dung
   */
  contentClassName?: string;

  /**
   * Class bổ sung cho header
   */
  headerClassName?: string;

  /**
   * Callback khi trạng thái đóng/mở thay đổi
   */
  onExpandChange?: (expanded: boolean) => void;

  /**
   * ID của accordion group (nếu section thuộc accordion)
   */
  accordionId?: string;

  /**
   * ID của section (dùng cho accordion)
   */
  id?: string;
}

/**
 * Component FormSection dùng để nhóm các field trong form thành các section
 * Hỗ trợ collapsible (có thể đóng/mở), animation, và nhiều biến thể
 */
const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  collapsible = false,
  defaultExpanded = true,
  variant = 'default',
  size = 'md',
  icon,
  iconPosition = 'right',
  badge,
  headerBgColor,
  contentBgColor,
  animated = true,
  animationType = 'slide',
  animationDuration = 300,
  className = '',
  titleClassName = '',
  descriptionClassName = '',
  contentClassName = '',
  headerClassName = '',
  onExpandChange,
  accordionId,
  id,
}) => {
  const [expanded, setExpanded] = useState(defaultExpanded);
  const [height, setHeight] = useState<number | 'auto'>(defaultExpanded ? 'auto' : 0);
  const contentRef = useRef<HTMLDivElement>(null);
  const firstRender = useRef(true);

  // Xử lý accordion
  useEffect(() => {
    if (accordionId && id) {
      const handleAccordionChange = (e: CustomEvent) => {
        if (e.detail.accordionId === accordionId && e.detail.sectionId !== id) {
          setExpanded(false);
          if (animated) {
            setHeight(0);
          }
        }
      };

      // Sử dụng CustomEvent thay vì any
      window.addEventListener(
        'accordion-change' as keyof WindowEventMap,
        handleAccordionChange as EventListener
      );
      return () => {
        window.removeEventListener(
          'accordion-change' as keyof WindowEventMap,
          handleAccordionChange as EventListener
        );
      };
    }
    return undefined;
  }, [accordionId, id, animated]);

  // Xử lý animation height
  useEffect(() => {
    if (!animated || !contentRef.current || animationType === 'fade') return undefined;

    if (firstRender.current) {
      firstRender.current = false;
      return undefined;
    }

    if (expanded) {
      // Sử dụng kết hợp cả height và transform để animation mượt hơn
      const sectionHeight = contentRef.current.scrollHeight;
      setHeight(sectionHeight);

      // Đặt height thành auto sau khi animation hoàn tất
      const timer = setTimeout(() => {
        setHeight('auto');
      }, animationDuration);

      return () => clearTimeout(timer);
    } else {
      // Đặt height cụ thể trước khi bắt đầu animation
      const sectionHeight = contentRef.current.scrollHeight;
      setHeight(sectionHeight);

      // Trigger reflow để đảm bảo animation hoạt động
      // Sử dụng void để tránh lỗi no-unused-expressions
      void contentRef.current.offsetHeight;

      // Bắt đầu animation thu gọn
      setHeight(0);

      return undefined;
    }
  }, [expanded, animated, animationDuration, animationType]);

  const handleToggle = () => {
    const newExpanded = !expanded;
    setExpanded(newExpanded);
    onExpandChange?.(newExpanded);

    // Dispatch event for accordion
    if (accordionId && id && newExpanded) {
      const event = new CustomEvent('accordion-change', {
        detail: { accordionId, sectionId: id },
      });
      window.dispatchEvent(event);
    }
  };

  // Xác định các class dựa trên props
  const getVariantClasses = () => {
    switch (variant) {
      case 'bordered':
        return 'border-2 border-gray-300 dark:border-gray-600';
      case 'elevated':
        return 'border border-gray-200 dark:border-gray-700 shadow-md';
      case 'gradient':
        return 'border border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900';
      default:
        return 'border border-gray-200 dark:border-gray-700';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          header: 'px-3 py-2',
          title: 'text-base',
          description: 'text-xs',
          content: 'p-3',
        };
      case 'lg':
        return {
          header: 'px-6 py-4',
          title: 'text-xl',
          description: 'text-base',
          content: 'p-6',
        };
      default:
        return {
          header: 'px-4 py-3',
          title: 'text-lg',
          description: 'text-sm',
          content: 'p-4',
        };
    }
  };

  const sizeClasses = getSizeClasses();

  // Base classes
  const sectionClasses = `mb-6 ${getVariantClasses()} ${className} ${
    !expanded && collapsible ? 'section-collapsed' : 'rounded-lg'
  }`;

  const headerClasses = `${sizeClasses.header} ${
    headerBgColor || 'bg-gray-50 dark:bg-gray-800'
  } ${expanded ? 'rounded-t-lg' : 'rounded-lg'} ${collapsible ? 'cursor-pointer' : ''} ${headerClassName}`;

  const titleClasses = `${sizeClasses.title} font-medium text-gray-900 dark:text-white ${titleClassName}`;

  const descriptionClasses = `mt-1 ${sizeClasses.description} text-gray-600 dark:text-gray-400 ${descriptionClassName}`;

  const contentWrapperClasses = `${
    animated && animationType !== 'fade' ? 'overflow-hidden transition-all duration-300' : ''
  }`;

  const contentClasses = `${sizeClasses.content} ${
    contentBgColor || 'bg-white dark:bg-gray-900'
  } rounded-b-lg ${contentClassName} ${
    animated && animationType === 'fade'
      ? expanded
        ? 'animate-fade-in duration-300'
        : 'animate-fade-out duration-300 hidden'
      : ''
  }`;

  // Animation style for height transition
  const contentStyle = animated
    ? animationType === 'fade'
      ? {
          opacity: expanded ? 1 : 0,
          transition: `opacity ${animationDuration}ms ease-in-out`,
        }
      : {
          height: typeof height === 'number' ? `${height}px` : height,
          opacity: expanded ? 1 : 0,
          transform: expanded ? 'scaleY(1)' : 'scaleY(0.95)',
          transformOrigin: 'top',
          transition: `height ${animationDuration}ms ease-in-out,
                      opacity ${animationDuration}ms ease-in-out,
                      transform ${animationDuration}ms ease-in-out`,
        }
    : {};

  return (
    <div className={sectionClasses}>
      <div
        className={headerClasses}
        onClick={collapsible ? handleToggle : undefined}
        role={collapsible ? 'button' : undefined}
        tabIndex={collapsible ? 0 : undefined}
        aria-expanded={collapsible ? expanded : undefined}
      >
        <div
          className={`flex items-center justify-between ${iconPosition === 'left' ? 'flex-row-reverse' : ''}`}
        >
          <div className="flex items-center gap-2">
            {iconPosition === 'left' &&
              collapsible &&
              (icon || (
                <Icon
                  name={expanded ? 'chevron-up' : 'chevron-down'}
                  size="sm"
                  className={`text-gray-500 dark:text-gray-400 ${expanded ? 'animate-fade-in' : 'animate-fade-in'} duration-300`}
                />
              ))}
            <h3 className={titleClasses}>{title}</h3>
            {badge &&
              (typeof badge === 'string' || typeof badge === 'number' ? (
                <Badge variant="primary" size="sm">
                  {badge}
                </Badge>
              ) : (
                badge
              ))}
          </div>
          {iconPosition === 'right' &&
            collapsible &&
            (icon || (
              <Icon
                name={expanded ? 'chevron-up' : 'chevron-down'}
                size="sm"
                className={`text-gray-500 dark:text-gray-400 transition-transform duration-300 ${expanded ? 'rotate-0' : 'rotate-180'}`}
              />
            ))}
        </div>
        {description && <p className={descriptionClasses}>{description}</p>}
      </div>
      <div className={contentWrapperClasses} style={contentStyle}>
        <div ref={contentRef} className={contentClasses}>
          {children}
        </div>
      </div>
    </div>
  );
};

export default FormSection;
