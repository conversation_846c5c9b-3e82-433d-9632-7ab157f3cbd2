import React, { useMemo, useCallback } from 'react';
import { useTheme } from '@/shared/contexts';

export interface PaginationButtonProps {
  /**
   * Nội dung của nút (số trang, dấu chấm lửng hoặc icon)
   */
  page: number | string | React.ReactNode;

  /**
   * Trạng thái active của nút
   */
  isActive?: boolean;

  /**
   * Sự kiện khi nhấp vào nút
   */
  onClick?: () => void;

  /**
   * Vô hiệu hóa nút
   */
  disabled?: boolean;

  /**
   * Nhãn ARIA cho khả năng truy cập
   */
  ariaLabel?: string;

  /**
   * Kích thước của nút
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Loại bỏ border cho nút
   * @default false
   */
  borderless?: boolean;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component nút phân trang
 */
const PaginationButton: React.FC<PaginationButtonProps> = ({
  page,
  isActive = false,
  onClick,
  disabled = false,
  ariaLabel,
  size = 'md',
  borderless = false,
  className = '',
}) => {
  // Sử dụng hook theme mới
  useTheme();
  // Xác định xem nút có phải là dấu chấm lửng không
  const isEllipsis = page === '...' || page === '…';

  // Xác định các lớp kích thước
  const sizeClasses = useMemo(() => {
    return {
      sm: 'h-8 w-8 text-xs',
      md: 'h-10 w-10 text-sm',
      lg: 'h-12 w-12 text-base',
    }[size];
  }, [size]);

  // Xác định các lớp trạng thái
  const stateClasses = useMemo(() => {
    return isActive
      ? 'bg-primary text-primary-foreground font-medium shadow-sm'
      : 'bg-transparent text-foreground hover:bg-card-muted';
  }, [isActive]);

  // Xác định các lớp vô hiệu hóa
  const disabledClasses = useMemo(() => {
    return disabled || isEllipsis ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
  }, [disabled, isEllipsis]);

  // Kết hợp tất cả các lớp
  const buttonClasses = useMemo(() => {
    return [
      'flex items-center justify-center rounded-full',
      !borderless ? (isActive ? '' : 'border border-border') : '',
      'transition-all duration-200',
      sizeClasses,
      stateClasses,
      disabledClasses,
      isActive ? 'transform hover:scale-105' : 'hover:shadow-sm',
      className,
    ].join(' ');
  }, [sizeClasses, stateClasses, disabledClasses, borderless, isActive, className]);

  // Xử lý sự kiện nhấp chuột
  const handleClick = useCallback(() => {
    if (!disabled && !isEllipsis && onClick) {
      onClick();
    }
  }, [disabled, isEllipsis, onClick]);

  return (
    <button
      className={buttonClasses}
      onClick={handleClick}
      disabled={disabled || isEllipsis}
      aria-label={ariaLabel}
      aria-current={isActive ? 'page' : undefined}
      type="button"
    >
      {page}
    </button>
  );
};

export default PaginationButton;
