import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { CustomGroupFormService } from '../services/custom-group-form.service';
import type {
  CustomGroupFormQueryParams,
  CreateCustomGroupFormData,
  UpdateCustomGroupFormData,
} from '../services/custom-group-form.service';

/**
 * Query keys cho custom group form
 */
export const CUSTOM_GROUP_FORM_QUERY_KEYS = {
  ALL: ['customGroupForms'] as const,
  LIST: (params: CustomGroupFormQueryParams) => [...CUSTOM_GROUP_FORM_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: number) => [...CUSTOM_GROUP_FORM_QUERY_KEYS.ALL, 'detail', id] as const,
};

/**
 * Hook để lấy danh sách nhóm trường tùy chỉnh
 */
export const useCustomGroupForms = (params?: CustomGroupFormQueryParams) => {
  return useQuery({
    queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.LIST(params || {}),
    queryFn: () => CustomGroupFormService.getCustomGroupForms(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết nhóm trường tùy chỉnh
 */
export const useCustomGroupForm = (id: number) => {
  return useQuery({
    queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.DETAIL(id),
    queryFn: () => CustomGroupFormService.getCustomGroupFormById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để tạo nhóm trường tùy chỉnh mới
 */
export const useCreateCustomGroupForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCustomGroupFormData) =>
      CustomGroupFormService.createCustomGroupForm(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật nhóm trường tùy chỉnh
 */
export const useUpdateCustomGroupForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCustomGroupFormData }) =>
      CustomGroupFormService.updateCustomGroupForm(id, data),
    onSuccess: (_, { id }) => {
      // Invalidate và refetch danh sách và chi tiết
      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.ALL,
      });
      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.DETAIL(id),
      });
    },
  });
};

/**
 * Hook để xóa nhóm trường tùy chỉnh
 */
export const useDeleteCustomGroupForm = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => CustomGroupFormService.deleteCustomGroupForm(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: CUSTOM_GROUP_FORM_QUERY_KEYS.ALL,
      });
    },
  });
};
