import { useState, useMemo, useCallback, useEffect } from 'react';
import { useForm, FormProvider, FieldValues, DefaultValues } from 'react-hook-form';
import { FormStep, FormWizardContextType, FormWizardProps, StepStatus } from './FormWizard.types';
import { FormWizardContext } from './FormWizardContext';

/**
 * FormWizard component quản lý form nhiều bước
 *
 * @example
 * ```tsx
 * <FormWizard
 *   steps={[
 *     {
 *       id: 'personal',
 *       title: 'Thông tin cá nhân',
 *       validationSchema: personalInfoSchema,
 *       content: <PersonalInfoForm />
 *     },
 *     {
 *       id: 'contact',
 *       title: 'Thông tin liên hệ',
 *       validationSchema: contactInfoSchema,
 *       content: <ContactInfoForm />
 *     },
 *     {
 *       id: 'review',
 *       title: 'Xác nhận',
 *       content: <ReviewForm />
 *     }
 *   ]}
 *   initialValues={{
 *     firstName: '',
 *     lastName: '',
 *     email: '',
 *     phone: ''
 *   }}
 *   onSubmit={handleSubmit}
 *   validateOnStepChange
 * />
 * ```
 */
const FormWizard = <TFormValues extends FieldValues = FieldValues>({
  steps,
  initialValues,
  onSubmit,
  onStepChange,
  validateOnStepChange = true,
  allowBackNavigation = true,
  allowJumpNavigation = false,
  className = '',
  contentClassName = '',
  children,
}: FormWizardProps<TFormValues>) => {
  // Lọc các bước thỏa mãn điều kiện
  const filteredSteps = useMemo(() => {
    return steps.filter(step => !step.condition || step.condition(initialValues || {}));
  }, [steps, initialValues]);

  // State cho bước hiện tại
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // State cho trạng thái các bước
  const [stepStatus, setStepStatus] = useState<Record<string, StepStatus>>(() => {
    const initialStatus: Record<string, StepStatus> = {};
    filteredSteps.forEach((step, index) => {
      initialStatus[step.id] = index === 0 ? StepStatus.ACTIVE : StepStatus.PENDING;
    });
    return initialStatus;
  });

  // Khởi tạo form với React Hook Form
  const formMethods = useForm<TFormValues>({
    defaultValues: initialValues as DefaultValues<TFormValues>,
    mode: 'onChange',
  });

  const { handleSubmit, trigger, getValues, formState } = formMethods;

  // Bước hiện tại
  const currentStep = useMemo(
    () => filteredSteps[currentStepIndex],
    [filteredSteps, currentStepIndex]
  );

  // Cập nhật khi bước thay đổi
  useEffect(() => {
    if (currentStep?.validationSchema) {
      formMethods.clearErrors();
    }
  }, [currentStep, formMethods]);

  // Kiểm tra xem có phải bước đầu tiên không
  const isFirstStep = currentStepIndex === 0;

  // Kiểm tra xem có phải bước cuối cùng không
  const isLastStep = currentStepIndex === filteredSteps.length - 1;

  // Chuyển đến bước tiếp theo
  const goToNextStep = useCallback(async () => {
    // Nếu là bước cuối cùng, submit form
    if (isLastStep) {
      return;
    }

    // Validate bước hiện tại nếu cần
    if (validateOnStepChange && currentStep?.validationSchema) {
      const isValid = await trigger();
      if (!isValid) {
        setStepStatus(prev => ({
          ...prev,
          [currentStep.id]: StepStatus.ERROR,
        }));

        // Gọi callback onError nếu có
        if (currentStep.onError) {
          currentStep.onError(formState.errors);
        }

        return;
      }
    }

    // Lấy dữ liệu hiện tại
    const currentData = getValues();

    // Gọi callback onComplete của bước hiện tại nếu có
    if (currentStep?.onComplete) {
      currentStep.onComplete(currentData);
    }

    // Cập nhật trạng thái bước hiện tại
    setStepStatus(prev => ({
      ...prev,
      [currentStep?.id ?? '']: StepStatus.COMPLETED,
    }));

    // Bước tiếp theo
    const nextStepIndex = currentStepIndex + 1;
    const nextStep = filteredSteps[nextStepIndex];

    // Gọi callback onStepChange nếu có
    if (onStepChange) {
      await onStepChange(currentStep as FormStep<TFormValues>, nextStep as FormStep<TFormValues>, currentData);
    }

    // Gọi callback onActivate của bước tiếp theo nếu có
    if (nextStep?.onActivate) {
      nextStep.onActivate(currentData);
    }

    // Cập nhật trạng thái bước tiếp theo
    setStepStatus(prev => ({
      ...prev,
      [nextStep?.id ?? '']: StepStatus.ACTIVE,
    }));

    // Chuyển đến bước tiếp theo
    setCurrentStepIndex(nextStepIndex);
  }, [
    isLastStep,
    validateOnStepChange,
    currentStep,
    trigger,
    getValues,
    formState.errors,
    currentStepIndex,
    filteredSteps,
    onStepChange,
    setStepStatus,
  ]);

  // Chuyển đến bước trước
  const goToPreviousStep = useCallback(() => {
    if (isFirstStep || !allowBackNavigation) {
      return;
    }

    // Bước trước
    const previousStepIndex = currentStepIndex - 1;
    const previousStep = filteredSteps[previousStepIndex];

    // Cập nhật trạng thái bước hiện tại
    setStepStatus(prev => ({
      ...prev,
      [currentStep?.id ?? '']: StepStatus.PENDING,
    }));

    // Cập nhật trạng thái bước trước
    setStepStatus(prev => ({
      ...prev,
      [previousStep?.id ?? '']: StepStatus.ACTIVE,
    }));

    // Chuyển đến bước trước
    setCurrentStepIndex(previousStepIndex);

    // Gọi callback onActivate của bước trước nếu có
    if (previousStep?.onActivate) {
      previousStep.onActivate(getValues());
    }
  }, [isFirstStep, allowBackNavigation, currentStepIndex, filteredSteps, currentStep, getValues]);

  // Chuyển đến bước cụ thể
  const goToStep = useCallback(
    (stepId: string) => {
      if (!allowJumpNavigation) {
        return;
      }

      const stepIndex = filteredSteps.findIndex(step => step.id === stepId);
      if (stepIndex === -1 || stepIndex === currentStepIndex) {
        return;
      }

      // Cập nhật trạng thái bước hiện tại
      setStepStatus(prev => ({
        ...prev,
        [currentStep?.id ?? '']: StepStatus.PENDING,
      }));

      // Cập nhật trạng thái bước mới
      setStepStatus(prev => ({
        ...prev,
        [stepId]: StepStatus.ACTIVE,
      }));

      // Chuyển đến bước mới
      setCurrentStepIndex(stepIndex);

      // Gọi callback onActivate của bước mới nếu có
      const newStep = filteredSteps[stepIndex];
      if (newStep?.onActivate) {
        newStep.onActivate(getValues());
      }
    },
    [allowJumpNavigation, filteredSteps, currentStepIndex, currentStep, getValues]
  );

  // Submit form
  const submitForm = useCallback(() => {
    handleSubmit((data: FieldValues) => {
      onSubmit(data as unknown as TFormValues);
    })();
  }, [handleSubmit, onSubmit]);

  // Context value
  const contextValue = useMemo(
    () => ({
      steps: filteredSteps,
      currentStep,
      currentStepIndex,
      stepStatus,
      goToNextStep,
      goToPreviousStep,
      goToStep,
      isFirstStep,
      isLastStep,
      formMethods,
      submitForm,
    }),
    [
      filteredSteps,
      currentStep,
      currentStepIndex,
      stepStatus,
      goToNextStep,
      goToPreviousStep,
      goToStep,
      isFirstStep,
      isLastStep,
      formMethods,
      submitForm,
    ]
  );

  // Render content của bước hiện tại
  const renderStepContent = () => {
    if (typeof currentStep?.content === 'function') {
      // Sử dụng type assertion để tránh lỗi TypeScript
      return (currentStep.content as unknown as (methods: typeof formMethods) => JSX.Element)(
        formMethods
      );
    }
    return currentStep?.content;
  };

  // Sử dụng type assertion để tránh lỗi TypeScript
  const typedContextValue = contextValue as FormWizardContextType<Record<string, unknown>>;

  return (
    <FormWizardContext.Provider value={typedContextValue}>
      <FormProvider {...formMethods}>
        <div className={`form-wizard ${className}`}>
          {/* Render children nếu có */}
          {children && typeof children === 'function'
            ? (
                children as (context: FormWizardContextType<Record<string, unknown>>) => JSX.Element
              )(typedContextValue)
            : children}

          {/* Render content của bước hiện tại nếu không có children là function */}
          {typeof children !== 'function' && (
            <div className={`form-wizard-content ${contentClassName}`}>{renderStepContent()}</div>
          )}
        </div>
      </FormProvider>
    </FormWizardContext.Provider>
  );
};

export default FormWizard;
