import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import { SelectOption as SelectOptionType } from './Select';

export interface TypeaheadSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | string[]) => void;

  /**
   * Options
   */
  options: SelectOptionType[];

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Số lượng gợi ý hiển thị
   */
  maxSuggestions?: number;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * <PERSON><PERSON>ch thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;

  /**
   * Highlight text khớp với từ khóa tìm kiếm
   */
  highlightMatch?: boolean;
}

/**
 * Component TypeaheadSelect - Select với gợi ý khi gõ
 */
const TypeaheadSelect = forwardRef<HTMLInputElement, TypeaheadSelectProps>(
  (
    {
      value,
      onChange,
      options = [],
      multiple = false,
      maxSuggestions = 5,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      highlightMatch = true,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [selectedValues, setSelectedValues] = useState<string[]>(
      multiple
        ? Array.isArray(value)
          ? (value as string[])
          : []
        : value !== undefined
          ? [value as string]
          : []
    );
    const [selectedLabels, setSelectedLabels] = useState<string[]>([]);
    const [suggestions, setSuggestions] = useState<SelectOptionType[]>([]);
    const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);

    const typeaheadRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as string[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string] : []);
      }
    }, [value, multiple]);

    // Update selectedLabels when selectedValues changes
    useEffect(() => {
      const labels = selectedValues.map(val => {
        const option = options.find(opt => opt.value.toString() === val);
        return option ? option.label : val;
      });

      setSelectedLabels(labels);
    }, [selectedValues, options]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (typeaheadRef.current && !typeaheadRef.current.contains(event.target as Node)) {
          setIsOpen(false);
          setInputValue('');
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Update suggestions when input value changes
    useEffect(() => {
      if (!inputValue) {
        setSuggestions([]);
        return;
      }

      const filtered = options
        .filter(
          option =>
            option.label.toLowerCase().includes(inputValue.toLowerCase()) &&
            !selectedValues.includes(option.value.toString())
        )
        .slice(0, maxSuggestions);

      setSuggestions(filtered);
      setHighlightedIndex(filtered.length > 0 ? 0 : -1);
    }, [inputValue, options, selectedValues, maxSuggestions]);

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setInputValue(value);

      if (value && !isOpen) {
        setIsOpen(true);
      }
    };

    // Handle option selection
    const handleSelectOption = (option: SelectOptionType) => {
      let newSelectedValues: string[];

      if (multiple) {
        newSelectedValues = [...selectedValues, option.value.toString()];
      } else {
        newSelectedValues = [option.value.toString()];
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues);
        } else {
          const firstValue = newSelectedValues[0];
          if (firstValue !== undefined) {
            onChange(firstValue);
          }
        }
      }

      // Clear input and close dropdown for single select
      setInputValue('');
      if (!multiple) {
        setIsOpen(false);
      }
    };

    // Handle remove tag
    const handleRemoveTag = (value: string) => {
      const newSelectedValues = selectedValues.filter(val => val !== value);
      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues);
        } else {
          onChange(newSelectedValues[0] || '');
        }
      }
    };

    // Handle key down
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      // Close dropdown on Escape
      if (e.key === 'Escape') {
        setIsOpen(false);
        setInputValue('');
        return;
      }

      // Navigate through suggestions with arrow keys
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setHighlightedIndex(prev => (prev < suggestions.length - 1 ? prev + 1 : 0));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setHighlightedIndex(prev => (prev > 0 ? prev - 1 : suggestions.length - 1));
      } else if (e.key === 'Enter' && highlightedIndex >= 0) {
        e.preventDefault();
        const selectedSuggestion = suggestions[highlightedIndex];
        if (selectedSuggestion !== undefined) {
          handleSelectOption(selectedSuggestion);
        }
      } else if (e.key === 'Backspace' && !inputValue && selectedValues.length > 0) {
        // Remove last tag when pressing Backspace with empty input
        const lastValue = selectedValues[selectedValues.length - 1];
        if (lastValue !== undefined) {
          handleRemoveTag(lastValue);
        }
      }
    };

    // Highlight matching text
    const highlightText = (text: string, query: string) => {
      if (!highlightMatch || !query) return text;

      const regex = new RegExp(`(${query})`, 'gi');
      const parts = text.split(regex);

      return (
        <>
          {parts.map((part, i) =>
            regex.test(part) ? (
              <span key={i} className="bg-primary/20 font-medium">
                {part}
              </span>
            ) : (
              part
            )
          )}
        </>
      );
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={typeaheadRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Input container */}
        <div
          className={`
          flex flex-wrap items-center
          border-0 dark:border rounded-md bg-card-muted
          min-h-${sizeClasses.split(' ')[0]}
          ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
          ${error ? 'dark:border-red-500' : 'dark:border-border'}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
          p-1
        `}
          onClick={() => {
            if (!disabled && inputRef.current) {
              inputRef.current.focus();
            }
          }}
        >
          {/* Selected tags */}
          {multiple &&
            selectedLabels.map((label, index) => (
              <div
                key={`tag-${index}`}
                className="flex items-center bg-gray-100 dark:bg-dark-lighter rounded-md m-1 px-2 py-1 text-sm"
              >
                <span>{label}</span>
                <button
                  type="button"
                  className="ml-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={e => {
                    e.stopPropagation();
                    const valueToRemove = selectedValues[index];
                    if (valueToRemove !== undefined) {
                      handleRemoveTag(valueToRemove);
                    }
                  }}
                  disabled={disabled}
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ))}

          {/* Input */}
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => inputValue && setIsOpen(true)}
            placeholder={selectedValues.length === 0 ? placeholder : ''}
            disabled={disabled}
            className="flex-grow min-w-[50px] px-2 py-1 bg-transparent border-none focus:outline-none text-foreground"
          />

          {/* Dropdown toggle */}
          <div className="px-2 cursor-pointer" onClick={() => !disabled && setIsOpen(!isOpen)}>
            <svg
              className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Suggestions dropdown */}
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-dark-light rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in">
            {suggestions.length > 0 ? (
              <div role="listbox">
                {suggestions.map((option, index) => (
                  <div
                    key={`suggestion-${option.value}`}
                    role="option"
                    aria-selected={index === highlightedIndex}
                    className={`
                    px-4 py-2 cursor-pointer
                    ${index === highlightedIndex ? 'bg-primary-light/20 dark:bg-primary/20' : 'hover:bg-gray-100 dark:hover:bg-dark-lighter'}
                  `}
                    onClick={() => handleSelectOption(option)}
                    onMouseEnter={() => setHighlightedIndex(index)}
                  >
                    {option.icon && <span className="mr-2">{option.icon}</span>}
                    <span>{highlightText(option.label, inputValue)}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                {t('common.noResults', 'No results found')}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);

TypeaheadSelect.displayName = 'TypeaheadSelect';

export default TypeaheadSelect;
