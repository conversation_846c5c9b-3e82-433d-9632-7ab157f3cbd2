import { TFunction } from 'i18next';
import { ValidationMessageProvider, ValidationContext } from './types';

/**
 * Default validation messages in English
 */
export const DEFAULT_VALIDATION_MESSAGES = {
  // Required validation
  REQUIRED: 'This field is required',

  // Length validations
  MIN_LENGTH: 'Must be at least {min} characters long',
  MAX_LENGTH: 'Must be at most {max} characters long',
  EXACT_LENGTH: 'Must be exactly {length} characters long',

  // Value validations
  MIN_VALUE: 'Must be at least {min}',
  MAX_VALUE: 'Must be at most {max}',
  BETWEEN: 'Must be between {min} and {max}',

  // Type validations
  INVALID_EMAIL: 'Must be a valid email address',
  INVALID_URL: 'Must be a valid URL',
  INVALID_PHONE: 'Must be a valid phone number',
  INVALID_PHONE_VN: 'Must be a valid Vietnamese phone number',
  INVALID_NUMBER: 'Must be a valid number',
  INVALID_INTEGER: 'Must be a valid integer',
  INVALID_DATE: 'Must be a valid date',
  INVALID_PATTERN: 'Invalid format',

  // Business validations
  INVALID_CITIZEN_ID_VN: 'Must be a valid Vietnamese citizen ID',
  INVALID_NAME_VN: 'Must be a valid Vietnamese name',
  INVALID_CURRENCY: 'Must be a valid currency amount',
  INVALID_CREDIT_CARD: 'Must be a valid credit card number',
  INVALID_UUID: 'Must be a valid UUID',

  // Password validations
  WEAK_PASSWORD: 'Password must contain at least 8 characters, including uppercase, lowercase, number and special character',
  PASSWORD_MISMATCH: 'Passwords do not match',

  // Date validations
  NOT_FUTURE_DATE: 'Must be a future date',
  NOT_PAST_DATE: 'Must be a past date',
  MIN_AGE_NOT_MET: 'Must be at least {minAge} years old',

  // File validations
  INVALID_FILE: 'Must be a valid file',
  FILE_TOO_LARGE: 'File size must be less than {maxSize}MB',
  INVALID_FILE_TYPE: 'File type must be one of: {allowedTypes}',

  // Numeric validations
  NOT_POSITIVE: 'Must be a positive number',
  NOT_NEGATIVE: 'Must be a negative number',
  NOT_ZERO: 'Must not be zero',

  // Array validations
  MIN_ITEMS: 'Must have at least {min} items',
  MAX_ITEMS: 'Must have at most {max} items',
  UNIQUE_ITEMS: 'All items must be unique',

  // Custom validations
  CUSTOM_ERROR: 'Validation failed',
  ASYNC_ERROR: 'Async validation failed',
  NETWORK_ERROR: 'Network error during validation',
  TIMEOUT_ERROR: 'Validation timeout',
};

/**
 * Vietnamese validation messages
 */
export const VIETNAMESE_VALIDATION_MESSAGES = {
  // Required validation
  REQUIRED: 'Trường này là bắt buộc',

  // Length validations
  MIN_LENGTH: 'Phải có ít nhất {min} ký tự',
  MAX_LENGTH: 'Không được vượt quá {max} ký tự',
  EXACT_LENGTH: 'Phải có đúng {length} ký tự',

  // Value validations
  MIN_VALUE: 'Phải có giá trị ít nhất {min}',
  MAX_VALUE: 'Không được vượt quá {max}',
  BETWEEN: 'Phải có giá trị từ {min} đến {max}',

  // Type validations
  INVALID_EMAIL: 'Email không hợp lệ',
  INVALID_URL: 'URL không hợp lệ',
  INVALID_PHONE: 'Số điện thoại không hợp lệ',
  INVALID_PHONE_VN: 'Số điện thoại Việt Nam không hợp lệ',
  INVALID_NUMBER: 'Phải là số hợp lệ',
  INVALID_INTEGER: 'Phải là số nguyên hợp lệ',
  INVALID_DATE: 'Ngày không hợp lệ',
  INVALID_PATTERN: 'Định dạng không hợp lệ',

  // Business validations
  INVALID_CITIZEN_ID_VN: 'Số CMND/CCCD không hợp lệ',
  INVALID_NAME_VN: 'Tên tiếng Việt không hợp lệ',
  INVALID_CURRENCY: 'Số tiền không hợp lệ',
  INVALID_CREDIT_CARD: 'Số thẻ tín dụng không hợp lệ',
  INVALID_UUID: 'UUID không hợp lệ',

  // Password validations
  WEAK_PASSWORD: 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường, số và ký tự đặc biệt',
  PASSWORD_MISMATCH: 'Mật khẩu không khớp',

  // Date validations
  NOT_FUTURE_DATE: 'Phải là ngày trong tương lai',
  NOT_PAST_DATE: 'Phải là ngày trong quá khứ',
  MIN_AGE_NOT_MET: 'Phải đủ {minAge} tuổi',

  // File validations
  INVALID_FILE: 'File không hợp lệ',
  FILE_TOO_LARGE: 'Kích thước file phải nhỏ hơn {maxSize}MB',
  INVALID_FILE_TYPE: 'Loại file phải là: {allowedTypes}',

  // Numeric validations
  NOT_POSITIVE: 'Phải là số dương',
  NOT_NEGATIVE: 'Phải là số âm',
  NOT_ZERO: 'Không được bằng 0',

  // Array validations
  MIN_ITEMS: 'Phải có ít nhất {min} mục',
  MAX_ITEMS: 'Không được vượt quá {max} mục',
  UNIQUE_ITEMS: 'Tất cả các mục phải là duy nhất',

  // Custom validations
  CUSTOM_ERROR: 'Xác thực thất bại',
  ASYNC_ERROR: 'Xác thực bất đồng bộ thất bại',
  NETWORK_ERROR: 'Lỗi mạng trong quá trình xác thực',
  TIMEOUT_ERROR: 'Hết thời gian xác thực',
};

/**
 * Message interpolation utility
 */
export const interpolateMessage = (
  template: string,
  variables: Record<string, unknown> = {}
): string => {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
};

/**
 * Create a validation message provider
 */
export const createMessageProvider = (
  messages: Record<string, string> = DEFAULT_VALIDATION_MESSAGES,
  t?: TFunction
): ValidationMessageProvider => {
  return (code: string, _context: ValidationContext, meta: Record<string, unknown> = {}) => {
    // Try to get message from translation function first
    if (t) {
      const translatedMessage = t(`validation.${code.toLowerCase()}`, {
        defaultValue: '',
        ...meta
      });
      if (translatedMessage) {
        return translatedMessage;
      }
    }

    // Fallback to predefined messages
    const template = messages[code] || messages['CUSTOM_ERROR'] || 'Validation failed';
    return interpolateMessage(template, meta);
  };
};

/**
 * Default message provider
 */
export const defaultMessageProvider = createMessageProvider();

/**
 * Vietnamese message provider
 */
export const vietnameseMessageProvider = createMessageProvider(VIETNAMESE_VALIDATION_MESSAGES);

/**
 * Message provider factory
 */
export const MessageProviders = {
  /**
   * Create a message provider with custom messages
   */
  create: (
    messages: Record<string, string>,
    t?: TFunction
  ): ValidationMessageProvider => {
    return createMessageProvider(messages, t);
  },

  /**
   * Create a multilingual message provider
   */
  multilingual: (
    messagesByLocale: Record<string, Record<string, string>>,
    locale: string,
    t?: TFunction
  ): ValidationMessageProvider => {
    const messages = messagesByLocale[locale] || messagesByLocale['en'] || DEFAULT_VALIDATION_MESSAGES;
    return createMessageProvider(messages, t);
  },

  /**
   * Combine multiple message providers
   */
  combine: (...providers: ValidationMessageProvider[]): ValidationMessageProvider => {
    return (code: string, context: ValidationContext, meta: Record<string, unknown> = {}) => {
      for (const provider of providers) {
        const message = provider(code, context, meta);
        if (message && message !== 'Validation failed') {
          return message;
        }
      }
      return 'Validation failed';
    };
  },

  /**
   * Create a cached message provider
   */
  cached: (provider: ValidationMessageProvider): ValidationMessageProvider => {
    const cache = new Map<string, string>();

    return (code: string, context: ValidationContext, meta: Record<string, unknown> = {}) => {
      const cacheKey = `${code}:${context.field}:${JSON.stringify(meta)}`;

      if (cache.has(cacheKey)) {
        return cache.get(cacheKey)!;
      }

      const message = provider(code, context, meta);
      cache.set(cacheKey, message);

      return message;
    };
  },

  /**
   * Create a fallback message provider
   */
  withFallback: (
    primary: ValidationMessageProvider,
    fallback: ValidationMessageProvider
  ): ValidationMessageProvider => {
    return (code: string, context: ValidationContext, meta: Record<string, unknown> = {}) => {
      try {
        const message = primary(code, context, meta);
        if (message && message !== 'Validation failed') {
          return message;
        }
      } catch (error) {
        console.warn('Primary message provider failed:', error);
      }

      return fallback(code, context, meta);
    };
  },
};

/**
 * Validation message utilities
 */
export const MessageUtils = {
  /**
   * Format a validation message with field name
   */
  formatWithField: (message: string, fieldName: string): string => {
    return `${fieldName}: ${message}`;
  },

  /**
   * Capitalize first letter of a message
   */
  capitalize: (message: string): string => {
    return message.charAt(0).toUpperCase() + message.slice(1);
  },

  /**
   * Convert message to sentence case
   */
  toSentenceCase: (message: string): string => {
    return message.charAt(0).toUpperCase() + message.slice(1).toLowerCase();
  },

  /**
   * Truncate message to specified length
   */
  truncate: (message: string, maxLength: number): string => {
    if (message.length <= maxLength) return message;
    return message.slice(0, maxLength - 3) + '...';
  },

  /**
   * Extract error code from message
   */
  extractCode: (message: string): string | null => {
    const match = message.match(/\[([A-Z_]+)\]/);
    return match?.[1] ?? null;
  },

  /**
   * Add error code to message
   */
  addCode: (message: string, code: string): string => {
    return `${message} [${code}]`;
  },

  /**
   * Remove error code from message
   */
  removeCode: (message: string): string => {
    return message.replace(/\s*\[[A-Z_]+\]$/, '');
  },
};
