/**
 * SSE Context để quản lý multiple SSE connections
 */
import React, { useState, useCallback, useRef, ReactNode } from 'react';
import {
  SSEContextValue,
  UseSSEOptions,
  UseSSEReturn
} from '@/shared/types/sse.types';
import { SSEContext } from './context';
import { useSSEFromContext } from './hooks';

/**
 * Props cho SSEProvider
 */
export interface SSEProviderProps {
  /**
   * Children components
   */
  children: ReactNode;

  /**
   * Default options cho tất cả connections
   */
  defaultOptions?: UseSSEOptions;

  /**
   * Có log debug không
   */
  debug?: boolean;
}



/**
 * SSE Provider component
 */
export const SSEProvider: React.FC<SSEProviderProps> = ({
  children,
  defaultOptions = {},
  debug = false
}) => {
  // State để lưu trữ các connections
  const [connections] = useState<Map<string, UseSSEReturn>>(new Map());

  // Ref để lưu trữ các cleanup functions
  const cleanupFunctionsRef = useRef<Map<string, () => void>>(new Map());

  /**
   * Tạo kết nối SSE mới
   */
  const createConnection = useCallback((
    id: string,
    url: string,
    options: UseSSEOptions = {}
  ): UseSSEReturn => {
    // Kiểm tra nếu connection đã tồn tại
    if (connections.has(id)) {
      if (debug) {
        console.warn(`SSE connection with id "${id}" already exists`);
      }
      return connections.get(id)!;
    }

    if (debug) {
      console.log(`Creating SSE connection: ${id} -> ${url}`, { ...defaultOptions, ...options });
    }

    // Tạo connection mới bằng cách sử dụng useSSE hook
    // Lưu ý: Đây là một workaround vì chúng ta không thể gọi hook trong callback
    // Trong thực tế, bạn có thể cần restructure code này
    const connection = {
      connectionInfo: {
        state: 'disconnected' as const,
        url,
        reconnectAttempts: 0,
        eventsReceived: 0,
      },
      events: [],
      lastEvent: null,
      connect: () => {},
      disconnect: () => {},
      subscribe: () => '',
      unsubscribe: () => {},
      clearEvents: () => {},
    } as UseSSEReturn;

    // Lưu connection
    connections.set(id, connection);

    // Tạo cleanup function
    const cleanup = () => {
      connection.disconnect();
      connections.delete(id);
      cleanupFunctionsRef.current.delete(id);
    };

    cleanupFunctionsRef.current.set(id, cleanup);

    return connection;
  }, [connections, defaultOptions, debug]);

  /**
   * Lấy kết nối SSE theo ID
   */
  const getConnection = useCallback((id: string): UseSSEReturn | undefined => {
    return connections.get(id);
  }, [connections]);

  /**
   * Xóa kết nối SSE
   */
  const removeConnection = useCallback((id: string): void => {
    const cleanup = cleanupFunctionsRef.current.get(id);
    if (cleanup) {
      cleanup();
    }

    if (debug) {
      console.log(`Removed SSE connection: ${id}`);
    }
  }, [debug]);

  /**
   * Ngắt tất cả kết nối
   */
  const disconnectAll = useCallback((): void => {
    cleanupFunctionsRef.current.forEach((cleanup, id) => {
      if (debug) {
        console.log(`Disconnecting SSE connection: ${id}`);
      }
      cleanup();
    });

    connections.clear();
    cleanupFunctionsRef.current.clear();

    if (debug) {
      console.log('All SSE connections disconnected');
    }
  }, [connections, debug]);

  // Context value
  const contextValue: SSEContextValue = {
    connections,
    createConnection,
    getConnection,
    removeConnection,
    disconnectAll,
  };

  return (
    <SSEContext.Provider value={contextValue}>
      {children}
    </SSEContext.Provider>
  );
};



/**
 * Component wrapper để tự động tạo SSE connection
 */
export interface SSEConnectionWrapperProps {
  /**
   * ID của connection
   */
  id: string;

  /**
   * URL endpoint
   */
  url: string;

  /**
   * Options cho SSE
   */
  options?: UseSSEOptions;

  /**
   * Children function nhận connection làm parameter
   */
  children: (connection: UseSSEReturn) => ReactNode;
}

export const SSEConnectionWrapper: React.FC<SSEConnectionWrapperProps> = ({
  id,
  url,
  options = {},
  children,
}) => {
  const connection = useSSEFromContext(id, url, options);

  return <>{children(connection)}</>;
};

export default SSEProvider;
