# Hướng dẫn sử dụng Table Hooks

Bộ hooks này được thiết kế để đơn giản hóa việc quản lý dữ liệu bảng với các tính năng phổ biến như phân trang, sắ<PERSON> xếp, t<PERSON><PERSON> kiếm, lọ<PERSON> và hiển thị cột.

## Cài đặt

Các hooks được đặt trong thư mục `src/shared/hooks/table` và có thể được import trực tiếp:

```typescript
import {
  useTableData,
  useColumnVisibility,
  useFilterOptions,
  useDateRangeFilter,
  useDataTable,
  useTableDataProcessor,
  useFilterMenuItems,
  useStatusFilterOptions,
  useBooleanFilterOptions,
  createDefaultDataTableConfig,
} from '@/shared/hooks/table';
```

## <PERSON><PERSON><PERSON> hooks có sẵn

### 1. useTableData

Hook quản lý dữ liệu bảng với phân trang, sắp xếp và tìm kiếm.

```typescript
const tableData = useTableData<AffiliateRankQueryDto>({
  defaultPage: 1,
  defaultPageSize: 10,
  defaultSearchTerm: '',
  defaultSortBy: null,
  defaultSortDirection: null,
  createQueryParams: (page, pageSize, searchTerm, sortBy, sortDirection) => {
    return {
      page,
      limit: pageSize,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection || undefined,
    };
  },
});

// Sử dụng các state và handlers
const { currentPage, pageSize, searchTerm, sortBy, sortDirection, queryParams } = tableData;
const { handleSearch, handlePageChange, handleSortChange } = tableData;
```

### 2. useColumnVisibility

Hook quản lý hiển thị cột trong bảng.

```typescript
const columnVisibility = useColumnVisibility<AffiliateRankDto>({
  columns, // Danh sách cột của bảng
  includeAllOption: true, // Có bao gồm tùy chọn "Tất cả" hay không
  allOptionLabel: 'Tất cả', // Nhãn cho tùy chọn "Tất cả"
});

// Sử dụng các state và handlers
const { visibleColumns, visibleTableColumns } = columnVisibility;
const { handleColumnVisibilityChange, setVisibleColumns } = columnVisibility;
```

### 3. useFilterOptions

Hook quản lý các tùy chọn lọc.

```typescript
const filterOptions: FilterOption[] = [
  { id: 'all', label: 'Tất cả', icon: 'list', value: 'all' },
  { id: 'active', label: 'Hoạt động', icon: 'check', value: 'active' },
  { id: 'inactive', label: 'Không hoạt động', icon: 'eye-off', value: 'inactive' },
];

const filter = useFilterOptions({
  options: filterOptions,
  defaultSelectedId: 'all',
  onChange: value => {
    console.log('Filter changed:', value);
  },
});

// Sử dụng các state và handlers
const { selectedId, selectedValue, menuItems } = filter;
const { handleFilterChange, setSelectedId } = filter;
```

### 4. useDateRangeFilter

Hook quản lý lọc theo khoảng thời gian.

```typescript
const dateRange = useDateRangeFilter({
  defaultDateRange: [null, null],
  onChange: range => {
    console.log('Date range changed:', range);
  },
  convertToISOString: true,
});

// Sử dụng các state và handlers
const {
  dateRange: [startDate, endDate],
  hasDateRange,
} = dateRange;
const { handleStartDateChange, handleEndDateChange, handleClearDateRange, setDateRange } =
  dateRange;
const { isoDateRange } = dateRange; // Chuyển đổi thành chuỗi ISO
```

### 5. useDataTable

Hook tổng hợp quản lý dữ liệu bảng với đầy đủ tính năng.

```typescript
const dataTable = useDataTable<AffiliateRankDto, AffiliateRankQueryDto>({
  columns, // Danh sách cột của bảng
  tableDataOptions: {
    defaultPage: 1,
    defaultPageSize: 10,
    defaultSearchTerm: '',
    defaultSortBy: null,
    defaultSortDirection: null,
  },
  columnVisibilityOptions: {
    includeAllOption: true,
    allOptionLabel: 'Tất cả',
  },
  filterOptions: {
    options: filterOptions,
    defaultSelectedId: 'all',
  },
  dateRangeOptions: {
    convertToISOString: true,
  },
  createQueryParams: params => {
    const queryParams: AffiliateRankQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    // Thêm filter theo trạng thái nếu không phải 'all'
    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue;
    }

    // Thêm filter theo khoảng thời gian nếu có
    if (params.dateRange[0] || params.dateRange[1]) {
      const { startDate, endDate } = dataTable?.dateRange.isoDateRange() || {};
      if (startDate) queryParams.startDate = startDate;
      if (endDate) queryParams.endDate = endDate;
    }

    return queryParams;
  },
});

// Sử dụng các state và handlers từ tất cả các hooks
const { tableData, columnVisibility, filter, dateRange, queryParams } = dataTable;
```

## Ví dụ sử dụng đầy đủ

Xem file `src/modules/admin/affiliate/pages/AffiliateRankListPageWithHooks.tsx` để xem ví dụ sử dụng đầy đủ các hooks.

## Lợi ích

1. **Tái sử dụng code**: Giảm thiểu việc lặp lại code giữa các trang.
2. **Dễ bảo trì**: Tách biệt logic xử lý dữ liệu khỏi giao diện người dùng.
3. **Nhất quán**: Đảm bảo các trang có cùng hành vi và giao diện.
4. **Dễ mở rộng**: Dễ dàng thêm tính năng mới vào các hooks.
5. **Dễ test**: Dễ dàng viết unit test cho các hooks.

## Lưu ý

- Các hooks này được thiết kế để hoạt động với API tuân theo chuẩn của RedAI Backend.
- Các hooks này có thể được tùy chỉnh để phù hợp với nhu cầu cụ thể của từng trang.
- Nên sử dụng hook `useDataTable` để có đầy đủ tính năng, hoặc sử dụng các hooks riêng lẻ nếu chỉ cần một số tính năng cụ thể.

## Sử dụng tối ưu (Ngắn gọn)

Sử dụng các hooks mới để làm cho code ngắn gọn hơn:

```typescript
// Định nghĩa columns (giữ nguyên)
const columns = useMemo<TableColumn<AffiliateRankDto>[]>(() => [...], [t]);

// Sử dụng hook tạo filterOptions
const filterOptions = useStatusFilterOptions(
  AffiliateRankStatus.ACTIVE,
  AffiliateRankStatus.INACTIVE
);

// Sử dụng hook useDataTable với cấu hình mặc định
const dataTable = useDataTable(
  createDefaultDataTableConfig<AffiliateRankDto, AffiliateRankQueryDto>({
    columns,
    filterOptions,
    showDateFilter: true,
    createQueryParams: (params) => {
      const queryParams: AffiliateRankQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as AffiliateRankStatus;
      }

      return queryParams;
    },
  })
);

// Gọi API lấy danh sách
const { data: rankData, isLoading } = useRanks(dataTable.queryParams);

// Xử lý dữ liệu từ API
const tableData = useTableDataProcessor(rankData, isLoading);

// Tạo menuItems từ filterOptions
const menuItems = useFilterMenuItems(
  filterOptions,
  dataTable.filter.selectedId,
  dataTable.filter.handleFilterChange
);
```
