/**
 * Hook để upload file lên cloud thông qua URL tạm thời
 */
import { useCallback } from 'react';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import type { CreateFileUploadTaskParams } from '@/shared/types/task-queue.types';

/**
 * Tham số cho hàm uploadToPresignedUrl
 */
export interface UploadToPresignedUrlParams {
  /**
   * URL tạm thời để upload file
   */
  presignedUrl: string;

  /**
   * File cần upload
   */
  file: File;

  /**
   * Callback khi upload thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi upload thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;

  /**
   * Tiêu đề của task trong TaskQueuePanel
   */
  title?: string;

  /**
   * <PERSON>ô tả của task trong TaskQueuePanel
   */
  description?: string;

  /**
   * Metadata bổ sung cho task
   */
  metadata?: Record<string, unknown>;
}

/**
 * Hook để upload file lên cloud thông qua URL tạm thời
 * @returns Các hàm để upload file
 */
export function usePresignedUpload() {
  const { addFileUploadTask } = useTaskQueueContext();

  /**
   * Upload file lên cloud thông qua URL tạm thời
   * @param params Tham số cho việc upload
   * @returns ID của task trong TaskQueuePanel
   */
  const uploadToPresignedUrl = useCallback(
    (params: UploadToPresignedUrlParams): string => {
      const {
        presignedUrl,
        file,
        onSuccess,
        onError,
        onProgress,
        title = `Upload ${file.name}`,
        description = `Đang tải lên ${file.name} (${(file.size / 1024).toFixed(2)} KB)`,
        metadata,
      } = params;

      // Thêm task upload file vào queue
      const taskParams: CreateFileUploadTaskParams = {
        title,
        file,
        uploadUrl: presignedUrl,
        execute: async (url: string, fileToUpload: File, onProgressCallback: (progress: number) => void) => {
          try {
            // Tạo XMLHttpRequest để theo dõi tiến trình
            const xhr = new XMLHttpRequest();

            // Tạo Promise để xử lý kết quả
            const uploadPromise = new Promise<unknown>((resolve, reject) => {
              // Xử lý sự kiện khi upload hoàn thành
              xhr.onload = () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                  resolve({
                    success: true,
                    status: xhr.status,
                    url: presignedUrl,
                    fileName: fileToUpload.name,
                    fileSize: fileToUpload.size,
                  });
                } else {
                  reject(
                    new Error(
                      `Upload thất bại với mã lỗi ${xhr.status}: ${xhr.statusText || 'Không có thông tin lỗi'}`
                    )
                  );
                }
              };

              // Xử lý sự kiện khi upload thất bại
              xhr.onerror = () => {
                reject(new Error('Lỗi kết nối khi upload file'));
              };

              // Xử lý sự kiện khi upload bị hủy
              xhr.onabort = () => {
                reject(new Error('Upload đã bị hủy'));
              };

              // Xử lý sự kiện khi tiến trình upload thay đổi
              xhr.upload.onprogress = (event) => {
                if (event.lengthComputable) {
                  const progress = Math.round((event.loaded / event.total) * 100);
                  onProgressCallback(progress);
                }
              };
            });

            // Mở kết nối
            xhr.open('PUT', url, true);

            // Gửi file
            xhr.send(fileToUpload);

            // Trả về kết quả
            return await uploadPromise;
          } catch (error) {
            console.error('Lỗi khi upload file:', error);
            throw error;
          }
        },
        cancellable: true,
        retryable: true,
        maxRetries: 3,
        metadata: {
          fileType: file.type,
          fileSize: file.size,
          fileName: file.name,
          ...metadata,
        },
      };

      if (description) taskParams.description = description;
      if (onSuccess) taskParams.onSuccess = onSuccess;
      if (onError) taskParams.onError = onError;
      if (onProgress) taskParams.onProgress = onProgress;

      return addFileUploadTask(taskParams);
    },
    [addFileUploadTask]
  );

  /**
   * Upload nhiều file lên cloud thông qua các URL tạm thời
   * @param files Danh sách file cần upload
   * @param getPresignedUrl Hàm để lấy URL tạm thời cho mỗi file
   * @param onAllCompleted Callback khi tất cả các file đã được upload
   * @returns Danh sách ID của các task trong TaskQueuePanel
   */
  const uploadMultipleFiles = useCallback(
    async (
      files: File[],
      getPresignedUrl: (file: File, index: number) => Promise<string>,
      onAllCompleted?: (results: unknown[]) => void
    ): Promise<string[]> => {
      const taskIds: string[] = [];
      const results: unknown[] = [];
      let completedCount = 0;

      // Upload từng file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        if (!file) continue; // Skip undefined files

        try {
          // Lấy URL tạm thời
          const presignedUrl = await getPresignedUrl(file, i);

          // Upload file
          const taskId = uploadToPresignedUrl({
            presignedUrl,
            file,
            title: `Upload ${i + 1}/${files.length}: ${file.name}`,
            description: `Đang tải lên ${file.name} (${(file.size / 1024).toFixed(2)} KB)`,
            onSuccess: (result) => {
              results[i] = result;
              completedCount++;

              // Kiểm tra xem tất cả các file đã được upload chưa
              if (completedCount === files.length && onAllCompleted) {
                onAllCompleted(results);
              }
            },
            metadata: {
              fileIndex: i,
              totalFiles: files.length,
            },
          });

          taskIds.push(taskId);
        } catch (error) {
          console.error(`Lỗi khi upload file ${file.name}:`, error);
        }
      }

      return taskIds;
    },
    [uploadToPresignedUrl]
  );

  return {
    uploadToPresignedUrl,
    uploadMultipleFiles,
  };
}

export default usePresignedUpload;
