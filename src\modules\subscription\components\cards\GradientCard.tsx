/**
 * Gradient Pricing Card - Vibrant gradient design inspired by modern apps
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, Icon } from '@/shared/components/common';
import { ServicePackage, SubscriptionDuration } from '../../types';
import { calculateMonthlyPrice, calculateSavingPercentage } from '../../utils';

interface GradientCardProps {
  package: ServicePackage;
  duration: SubscriptionDuration;
  onSelect: (pkg: ServicePackage) => void;
  className?: string;
}

const GradientCard: React.FC<GradientCardProps> = ({
  package: pkg,
  duration,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const price = pkg.prices[duration];
  const monthlyPrice = calculateMonthlyPrice(price, duration);
  const savingPercentage = calculateSavingPercentage(
    pkg.prices[SubscriptionDuration.MONTHLY],
    price,
    duration
  );

  const isPopular = pkg.isPopular;

  // Define gradient colors based on package type or popularity
  const getGradientClasses = () => {
    if (isPopular) {
      return 'bg-gradient-to-br from-purple-600 via-pink-600 to-red-600';
    }

    switch (pkg.name.toLowerCase()) {
      case 'basic':
        return 'bg-gradient-to-br from-blue-500 to-cyan-500';
      case 'pro':
        return 'bg-gradient-to-br from-purple-500 to-pink-500';
      case 'enterprise':
        return 'bg-gradient-to-br from-orange-500 to-red-500';
      default:
        return 'bg-gradient-to-br from-gray-600 to-gray-800';
    }
  };

  const getAccentGradient = () => {
    if (isPopular) {
      return 'from-purple-400 to-pink-400';
    }

    switch (pkg.name.toLowerCase()) {
      case 'basic':
        return 'from-blue-400 to-cyan-400';
      case 'pro':
        return 'from-purple-400 to-pink-400';
      case 'enterprise':
        return 'from-orange-400 to-red-400';
      default:
        return 'from-gray-400 to-gray-600';
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
            ⭐ Most Popular
          </div>
        </div>
      )}

      <div className={`relative overflow-hidden rounded-2xl ${getGradientClasses()} p-[2px] transition-all duration-300 hover:scale-105 hover:shadow-2xl h-full`}>
        {/* Inner card */}
        <div className="bg-white dark:bg-gray-900 rounded-2xl p-8 h-full flex flex-col">
          {/* Decorative gradient line */}
          <div className={`h-1 w-full bg-gradient-to-r ${getAccentGradient()} rounded-full mb-6`}></div>

          {/* Header */}
          <div className="text-center mb-6">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-4 bg-gradient-to-br ${getAccentGradient()}`}>
              <Icon
                name={pkg.icon || 'package'}
                size="lg"
                className="text-white"
              />
            </div>

            <Typography variant="h4" className="font-bold mb-2 text-gray-900 dark:text-white">
              {t(`subscription:packages.${pkg.name.toLowerCase()}`, { defaultValue: pkg.name })}
            </Typography>

            {pkg.description && (
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                {t(`subscription:packages.description.${pkg.id}`, { defaultValue: pkg.description })}
              </Typography>
            )}
          </div>

          {/* Pricing */}
          <div className="text-center mb-8">
            <div className="relative">
              <div className={`absolute inset-0 bg-gradient-to-r ${getAccentGradient()} rounded-2xl opacity-10`}></div>
              <div className="relative p-6 rounded-2xl">
                <div className="flex items-baseline justify-center mb-2">
                  <span className={`text-5xl font-bold bg-gradient-to-r ${getAccentGradient()} bg-clip-text text-transparent`}>
                    {Math.round(monthlyPrice / 1000)}
                  </span>
                  <span className="text-xl text-gray-600 dark:text-gray-400 ml-2">R-Point</span>
                  <span className="text-gray-500 ml-1">/month</span>
                </div>

                {duration !== SubscriptionDuration.MONTHLY && savingPercentage > 0 && (
                  <div className={`inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r ${getAccentGradient()} text-white text-sm font-medium`}>
                    <Icon name="trending-down" size="sm" className="mr-1" />
                    Save {savingPercentage}%
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-4 mb-8 flex-grow">
            {pkg.features.map((feature, index) => (
              <div key={index} className="flex items-start">
                <div className="flex-shrink-0 mt-1">
                  {typeof feature.value === 'boolean' ? (
                    feature.value ? (
                      <div className={`w-5 h-5 rounded-full bg-gradient-to-r ${getAccentGradient()} flex items-center justify-center`}>
                        <Icon name="check" size="xs" className="text-white" />
                      </div>
                    ) : (
                      <div className="w-5 h-5 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                        <Icon name="x" size="xs" className="text-gray-500" />
                      </div>
                    )
                  ) : (
                    <div className={`w-5 h-5 rounded-full bg-gradient-to-r ${getAccentGradient()} flex items-center justify-center`}>
                      <Icon name="check" size="xs" className="text-white" />
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <Typography variant="body2" className="text-gray-700 dark:text-gray-300">
                    {t(`subscription:packages.features.${feature.name}`, {
                      defaultValue: feature.name,
                    })}
                    {typeof feature.value !== 'boolean' && (
                      <span className="font-semibold text-gray-900 dark:text-white ml-1">
                        {feature.value}
                      </span>
                    )}
                  </Typography>
                </div>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="mt-auto">
            <button
              onClick={() => onSelect(pkg)}
              className={`w-full py-4 px-6 rounded-xl font-semibold text-white transition-all duration-300 transform hover:scale-105 hover:shadow-lg bg-gradient-to-r ${getGradientClasses()} hover:opacity-90`}
            >
              {isPopular ? '🚀 Get Started Now' : 'Choose This Plan'}
            </button>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-4 right-4 opacity-10">
            <div className={`w-20 h-20 rounded-full bg-gradient-to-br ${getAccentGradient()}`}></div>
          </div>
          <div className="absolute bottom-4 left-4 opacity-5">
            <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${getAccentGradient()}`}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GradientCard;
