import type { CloudStorageProviderType } from '../types';

/**
 * Cloud Storage Provider Constants
 */

export const CLOUD_STORAGE_PROVIDER_TYPES: Record<CloudStorageProviderType, {
  id: CloudStorageProviderType;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  website: string;
  supportedFeatures: string[];
  maxFileSize: string;
  supportedFormats: string[];
}> = {
  'google-drive': {
    id: 'google-drive',
    name: 'Google Drive',
    displayName: 'Google Drive',
    description: 'Dịch vụ lưu trữ đám mây của Google',
    icon: 'cloud',
    website: 'https://drive.google.com',
    supportedFeatures: ['upload', 'download', 'share', 'sync', 'search', 'folder'],
    maxFileSize: '5TB',
    supportedFormats: ['*'],
  },
  onedrive: {
    id: 'onedrive',
    name: 'OneDrive',
    displayName: 'Microsoft OneDrive',
    description: 'Dịch vụ lưu trữ đám mây của <PERSON>',
    icon: 'cloud',
    website: 'https://onedrive.live.com',
    supportedFeatures: ['upload', 'download', 'share', 'sync', 'search', 'folder'],
    maxFileSize: '250GB',
    supportedFormats: ['*'],
  },
  dropbox: {
    id: 'dropbox',
    name: 'Dropbox',
    displayName: 'Dropbox',
    description: 'Dịch vụ lưu trữ và đồng bộ file',
    icon: 'cloud',
    website: 'https://dropbox.com',
    supportedFeatures: ['upload', 'download', 'share', 'sync', 'search', 'folder'],
    maxFileSize: '50GB',
    supportedFormats: ['*'],
  },
  box: {
    id: 'box',
    name: 'Box',
    displayName: 'Box',
    description: 'Nền tảng quản lý nội dung đám mây',
    icon: 'cloud',
    website: 'https://box.com',
    supportedFeatures: ['upload', 'download', 'share', 'sync', 'search', 'folder', 'collaboration'],
    maxFileSize: '32GB',
    supportedFormats: ['*'],
  },
};

export const FILE_TYPES = {
  document: {
    id: 'document',
    name: 'Tài liệu',
    icon: 'file-text',
    mimeTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
      'text/rtf',
    ],
    extensions: ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
  },
  spreadsheet: {
    id: 'spreadsheet',
    name: 'Bảng tính',
    icon: 'table',
    mimeTypes: [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv',
    ],
    extensions: ['.xls', '.xlsx', '.csv'],
  },
  presentation: {
    id: 'presentation',
    name: 'Trình chiếu',
    icon: 'presentation',
    mimeTypes: [
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ],
    extensions: ['.ppt', '.pptx'],
  },
  image: {
    id: 'image',
    name: 'Hình ảnh',
    icon: 'image',
    mimeTypes: [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/bmp',
      'image/webp',
      'image/svg+xml',
    ],
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'],
  },
  video: {
    id: 'video',
    name: 'Video',
    icon: 'video',
    mimeTypes: [
      'video/mp4',
      'video/avi',
      'video/mov',
      'video/wmv',
      'video/flv',
      'video/webm',
    ],
    extensions: ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'],
  },
  audio: {
    id: 'audio',
    name: 'Âm thanh',
    icon: 'music',
    mimeTypes: [
      'audio/mp3',
      'audio/wav',
      'audio/flac',
      'audio/aac',
      'audio/ogg',
    ],
    extensions: ['.mp3', '.wav', '.flac', '.aac', '.ogg'],
  },
  archive: {
    id: 'archive',
    name: 'Nén',
    icon: 'archive',
    mimeTypes: [
      'application/zip',
      'application/x-rar-compressed',
      'application/x-7z-compressed',
      'application/x-tar',
    ],
    extensions: ['.zip', '.rar', '.7z', '.tar', '.gz'],
  },
  folder: {
    id: 'folder',
    name: 'Thư mục',
    icon: 'folder',
    mimeTypes: ['application/vnd.google-apps.folder'],
    extensions: [],
  },
};

export const PERMISSION_TYPES = {
  reader: {
    id: 'reader',
    name: 'Xem',
    description: 'Chỉ có thể xem file',
    icon: 'eye',
  },
  writer: {
    id: 'writer',
    name: 'Chỉnh sửa',
    description: 'Có thể xem và chỉnh sửa file',
    icon: 'edit',
  },
  commenter: {
    id: 'commenter',
    name: 'Bình luận',
    description: 'Có thể xem và bình luận',
    icon: 'message-circle',
  },
  owner: {
    id: 'owner',
    name: 'Sở hữu',
    description: 'Toàn quyền quản lý file',
    icon: 'crown',
  },
};

export const SYNC_INTERVALS = [
  { id: 'manual', name: 'Thủ công', description: 'Đồng bộ khi cần thiết' },
  { id: '15min', name: '15 phút', description: 'Đồng bộ mỗi 15 phút' },
  { id: '30min', name: '30 phút', description: 'Đồng bộ mỗi 30 phút' },
  { id: '1hour', name: '1 giờ', description: 'Đồng bộ mỗi giờ' },
  { id: '6hours', name: '6 giờ', description: 'Đồng bộ mỗi 6 giờ' },
  { id: '12hours', name: '12 giờ', description: 'Đồng bộ mỗi 12 giờ' },
  { id: '24hours', name: '24 giờ', description: 'Đồng bộ mỗi ngày' },
];

export const FILE_SIZE_UNITS = [
  { id: 'B', name: 'Bytes', multiplier: 1 },
  { id: 'KB', name: 'Kilobytes', multiplier: 1024 },
  { id: 'MB', name: 'Megabytes', multiplier: 1024 * 1024 },
  { id: 'GB', name: 'Gigabytes', multiplier: 1024 * 1024 * 1024 },
  { id: 'TB', name: 'Terabytes', multiplier: 1024 * 1024 * 1024 * 1024 },
];

export const SORT_OPTIONS = [
  { id: 'name', name: 'Tên', field: 'name' },
  { id: 'modifiedTime', name: 'Ngày sửa đổi', field: 'modifiedAt' },
  { id: 'createdTime', name: 'Ngày tạo', field: 'createdAt' },
  { id: 'size', name: 'Kích thước', field: 'size' },
  { id: 'type', name: 'Loại file', field: 'mimeType' },
];

export const DEFAULT_SYNC_FOLDERS = [
  'Documents',
  'Images',
  'Videos',
  'Downloads',
];

/**
 * OAuth scopes for each provider
 */
export const OAUTH_SCOPES = {
  'google-drive': [
    'https://www.googleapis.com/auth/drive',
    'https://www.googleapis.com/auth/drive.file',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile',
  ],
  onedrive: [
    'Files.ReadWrite.All',
    'User.Read',
    'offline_access',
  ],
  dropbox: [
    'files.content.write',
    'files.content.read',
    'files.metadata.write',
    'files.metadata.read',
    'account_info.read',
  ],
  box: [
    'root_readwrite',
    'manage_managed_users',
    'manage_groups',
  ],
};

/**
 * API endpoints for each provider
 */
export const API_ENDPOINTS = {
  'google-drive': {
    auth: 'https://accounts.google.com/o/oauth2/auth',
    token: 'https://oauth2.googleapis.com/token',
    api: 'https://www.googleapis.com/drive/v3',
  },
  onedrive: {
    auth: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
    token: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
    api: 'https://graph.microsoft.com/v1.0',
  },
  dropbox: {
    auth: 'https://www.dropbox.com/oauth2/authorize',
    token: 'https://api.dropboxapi.com/oauth2/token',
    api: 'https://api.dropboxapi.com/2',
  },
  box: {
    auth: 'https://account.box.com/api/oauth2/authorize',
    token: 'https://api.box.com/oauth2/token',
    api: 'https://api.box.com/2.0',
  },
};
