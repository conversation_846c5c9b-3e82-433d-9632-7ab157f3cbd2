/**
 * Service for product API
 */

import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ApiProduct,
  ApiProductDetail,
  CreateProductDto,
  UpdateProductDto,
  ProductQueryParams,
  UserProductQueryParams,
} from './marketplace-api.service';

/**
 * Base URL for product API
 */
const BASE_URL = '/user/marketplace/products';

/**
 * Product service
 */
export const ProductService = {
  /**
   * Get approved products (public)
   */
  getApprovedProducts: async (params?: ProductQueryParams): Promise<PaginatedResult<ApiProduct>> => {
    try {
      const response = await apiClient.get<PaginatedResult<ApiProduct>>(
        `${BASE_URL}/approved`,
        { params }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching approved products:', error);
      throw error;
    }
  },

  /**
   * Get product detail
   */
  getProductDetail: async (productId: number): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.get<ApiProductDetail>(
        `${BASE_URL}/detail/${productId}`
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching product detail:', error);
      throw error;
    }
  },

  /**
   * Get user products
   */
  getUserProducts: async (params?: UserProductQueryParams): Promise<PaginatedResult<ApiProduct>> => {
    try {
      const response = await apiClient.get<PaginatedResult<ApiProduct>>(
        BASE_URL,
        { params }
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching user products:', error);
      throw error;
    }
  },

  /**
   * Get user product detail
   */
  getUserProductDetail: async (productId: number): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.get<ApiProductDetail>(
        `${BASE_URL}/${productId}`
      );
      return response.result;
    } catch (error) {
      console.error('Error fetching user product detail:', error);
      throw error;
    }
  },

  /**
   * Create product
   */
  createProduct: async (data: CreateProductDto): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.post<ApiProductDetail>(
        BASE_URL,
        data
      );
      return response.result;
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  },

  /**
   * Update product
   */
  updateProduct: async (productId: number, data: UpdateProductDto): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.put<ApiProductDetail>(
        `${BASE_URL}/${productId}`,
        data
      );
      return response.result;
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  },

  /**
   * Submit product for approval
   */
  submitProductForApproval: async (productId: number): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.post<ApiProductDetail>(
        `${BASE_URL}/${productId}/pending`
      );
      return response.result;
    } catch (error) {
      console.error('Error submitting product for approval:', error);
      throw error;
    }
  },

  /**
   * Cancel product submission
   */
  cancelProductSubmission: async (productId: number): Promise<ApiProductDetail> => {
    try {
      const response = await apiClient.post<ApiProductDetail>(
        `${BASE_URL}/${productId}/cancel-submission`
      );
      return response.result;
    } catch (error) {
      console.error('Error canceling product submission:', error);
      throw error;
    }
  },

  /**
   * Delete product
   */
  deleteProduct: async (productId: number): Promise<void> => {
    try {
      await apiClient.delete(`${BASE_URL}/${productId}`);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw error;
    }
  },
};
