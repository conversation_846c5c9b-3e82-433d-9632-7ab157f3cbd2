/**
 * SSE (Server-Sent Events) Utilities - Main Export File
 * 
 * Bộ tiện ích hoàn chỉnh để làm việc với Server-Sent Events trong React
 */

// Types
export * from '../types/sse.types';

// Core Utils
export { SSEClient } from '../utils/sse-client';

// Hooks
export { default as useSSE } from '../hooks/common/useSSE';
export { 
  default as useSSESubscription, 
  useSSEMultipleSubscriptions 
} from '../hooks/common/useSSESubscription';
export { default as useSSEConnection } from '../hooks/common/useSSEConnection';
export { default as useSSEWithTaskQueue } from '../hooks/common/useSSEWithTaskQueue';

// Context
export * from '../contexts/sse';

// Components
export { default as SSEStatus } from '../components/common/SSEStatus';
export type { SSEStatusProps } from '../components/common/SSEStatus';
export { default as SSENotification } from '../components/common/SSENotification';
export type { 
  SSENotificationProps, 
  SSENotificationItem, 
  NotificationType 
} from '../components/common/SSENotification';

// Re-export commonly used types for convenience
export type {
  SSEEvent,
  SSEConnectionState,
  SSEConnectionInfo,
  UseSSEOptions,
  UseSSEReturn,
  SSEContextValue,
  SSEMetrics,
  SSEEventPattern,
} from '../types/sse.types';
