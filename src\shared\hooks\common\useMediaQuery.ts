import { useState, useEffect, useMemo } from 'react';
import { debounce } from '@/lib/performance.ts';
import { MEDIA_QUERIES, MediaQuery, Breakpoint } from '@/shared/constants/breakpoints.ts';

/**
 * Custom hook for responsive design
 * @param query Media query string
 * @returns Boolean indicating if the media query matches
 *
 * @example
 * // Using a custom query
 * const isWideScreen = useMediaQuery('(min-width: 1400px)');
 *
 * @example
 * // Using predefined media query
 * const isDesktop = useMediaQuery(MEDIA_QUERIES.desktop);
 */
const useMediaQuery = (query: string): boolean => {
  const [matches, setMatches] = useState<boolean>(false);

  useEffect(() => {
    // Check if window is available (for SSR)
    if (typeof window === 'undefined') {
      return;
    }

    const mediaQuery = window.matchMedia(query);

    // Set initial value
    setMatches(mediaQuery.matches);

    // Create handler function
    const handler = debounce(() => {
      setMatches(mediaQuery.matches);
    }, 100);

    // Add event listener with debounce
    mediaQuery.addEventListener('change', handler);

    // Clean up
    return () => {
      mediaQuery.removeEventListener('change', handler);
    };
  }, [query]);

  return matches;
};

/**
 * Hook to check if the current viewport is at least a specific breakpoint
 * @param breakpoint The breakpoint to check
 * @returns Boolean indicating if the current viewport is at least the specified breakpoint
 *
 * @example
 * const isAtLeastMd = useBreakpoint('md');
 * // Equivalent to: useMediaQuery('(min-width: 768px)')
 */
export const useBreakpoint = (breakpoint: Breakpoint): boolean => {
  const query = useMemo(() => MEDIA_QUERIES[breakpoint], [breakpoint]);
  return useMediaQuery(query);
};

/**
 * Hook to check if the current viewport is exactly at a specific breakpoint
 * @param breakpoint The breakpoint to check
 * @returns Boolean indicating if the current viewport is exactly at the specified breakpoint
 *
 * @example
 * const isMdOnly = useBreakpointOnly('md');
 * // Equivalent to: useMediaQuery('(min-width: 768px) and (max-width: 1023px)')
 */
export const useBreakpointOnly = (breakpoint: Breakpoint): boolean => {
  const query = useMemo(() => {
    const key = `${breakpoint}Only` as MediaQuery;
    return MEDIA_QUERIES[key];
  }, [breakpoint]);

  return useMediaQuery(query);
};

/**
 * Hook to get the current active breakpoint
 * @returns The current active breakpoint
 *
 * @example
 * const currentBreakpoint = useCurrentBreakpoint();
 * // Returns 'xs', 'sm', 'md', 'lg', 'xl', or '2xl'
 */
export const useCurrentBreakpoint = (): Breakpoint => {
  const is2Xl = useMediaQuery(MEDIA_QUERIES['2xl']);
  const isXl = useMediaQuery(MEDIA_QUERIES.xl);
  const isLg = useMediaQuery(MEDIA_QUERIES.lg);
  const isMd = useMediaQuery(MEDIA_QUERIES.md);
  const isSm = useMediaQuery(MEDIA_QUERIES.sm);

  if (is2Xl) return '2xl';
  if (isXl) return 'xl';
  if (isLg) return 'lg';
  if (isMd) return 'md';
  if (isSm) return 'sm';
  return 'xs';
};

/**
 * Hook to check if the current viewport is mobile
 * @returns Boolean indicating if the current viewport is mobile
 */
export const useIsMobile = (): boolean => useMediaQuery(MEDIA_QUERIES.mobile);

/**
 * Hook to check if the current viewport is tablet
 * @returns Boolean indicating if the current viewport is tablet
 */
export const useIsTablet = (): boolean => useMediaQuery(MEDIA_QUERIES.tablet);

/**
 * Hook to check if the current viewport is desktop
 * @returns Boolean indicating if the current viewport is desktop
 */
export const useIsDesktop = (): boolean => useMediaQuery(MEDIA_QUERIES.desktop);

/**
 * Hook to check if the current viewport is in portrait orientation
 * @returns Boolean indicating if the current viewport is in portrait orientation
 */
export const useIsPortrait = (): boolean => useMediaQuery(MEDIA_QUERIES.portrait);

/**
 * Hook to check if the current viewport is in landscape orientation
 * @returns Boolean indicating if the current viewport is in landscape orientation
 */
export const useIsLandscape = (): boolean => useMediaQuery(MEDIA_QUERIES.landscape);

export default useMediaQuery;
