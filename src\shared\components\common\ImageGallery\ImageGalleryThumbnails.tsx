import React, { useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useImageGallery } from './useImageGallery';
import { GalleryImage } from './types';

interface ImageGalleryThumbnailsProps {
  currentIndex: number;
  onSelect: (index: number) => void;
  className?: string;
}

/**
 * Component hiển thị thumbnails cho Image Gallery
 */
const ImageGalleryThumbnails: React.FC<ImageGalleryThumbnailsProps> = ({
  currentIndex,
  onSelect,
  className = '',
}) => {
  const { t } = useTranslation();
  const { images, thumbnailsConfig } = useImageGallery();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const thumbnailRefs = useRef<(HTMLButtonElement | null)[]>([]);

  // Tự động scroll đến thumbnail active
  useEffect(() => {
    if (
      thumbnailsConfig.autoScroll &&
      scrollContainerRef.current &&
      thumbnailRefs.current[currentIndex]
    ) {
      const container = scrollContainerRef.current;
      const thumbnail = thumbnailRefs.current[currentIndex];

      if (!thumbnail) return;

      const containerRect = container.getBoundingClientRect();
      const thumbnailRect = thumbnail.getBoundingClientRect();

      // Tính toán vị trí scroll
      let scrollPosition;

      if (thumbnailsConfig.position === 'left' || thumbnailsConfig.position === 'right') {
        // Vertical scrolling
        scrollPosition =
          thumbnail.offsetTop -
          container.offsetTop -
          containerRect.height / 2 +
          thumbnailRect.height / 2;
        container.scrollTop = scrollPosition;
      } else {
        // Horizontal scrolling
        scrollPosition =
          thumbnail.offsetLeft -
          container.offsetLeft -
          containerRect.width / 2 +
          thumbnailRect.width / 2;
        container.scrollLeft = scrollPosition;
      }
    }
  }, [currentIndex, thumbnailsConfig.autoScroll, thumbnailsConfig.position]);

  // Xác định hướng scroll dựa trên position
  const isVertical = thumbnailsConfig.position === 'left' || thumbnailsConfig.position === 'right';

  // Tính toán kích thước và khoảng cách
  const size = thumbnailsConfig.size || 60;
  const gap = thumbnailsConfig.gap || 8;

  return (
    <div
      ref={scrollContainerRef}
      className={`overflow-auto scrollbar-thin scrollbar-thumb-gray-400 dark:scrollbar-thumb-gray-600 scrollbar-track-transparent ${
        isVertical ? 'flex-col h-full max-h-[calc(100vh-200px)]' : 'flex-row w-full'
      } flex ${className}`}
      style={{
        gap: `${gap}px`,
      }}
      data-testid="image-gallery-thumbnails"
    >
      {images.map((image: GalleryImage, index: number) => (
        <button
          key={`thumbnail-${index}`}
          ref={el => (thumbnailRefs.current[index] = el)}
          className={`flex-shrink-0 focus:outline-none focus:ring-2 focus:ring-primary transition-all duration-200 ${
            index === currentIndex
              ? 'ring-2 ring-primary scale-105'
              : 'opacity-70 hover:opacity-100'
          }`}
          style={{
            width: `${size}px`,
            height: `${size}px`,
          }}
          onClick={() => onSelect(index)}
          aria-label={t('components.imageGallery.imageCount', {
            current: index + 1,
            total: images.length,
          })}
          aria-current={index === currentIndex ? 'true' : 'false'}
        >
          <img
            src={image.thumbnail || image.src}
            alt={image.alt || `Thumbnail ${index + 1}`}
            className="w-full h-full object-cover rounded"
          />
        </button>
      ))}
    </div>
  );
};

export default ImageGalleryThumbnails;
