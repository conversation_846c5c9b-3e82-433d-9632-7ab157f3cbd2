{"wizard": {"title": "Create Email Server", "description": "Set up email server configuration with smart wizard", "steps": {"selectProvider": "Select Provider", "selectProviderDesc": "Choose suitable email provider", "configure": "Configure", "configureDesc": "Set up connection information", "review": "Review", "reviewDesc": "Review and complete"}, "selectProvider": {"title": "Select email provider", "description": "Choose an email provider that suits your needs", "popular": "Popular", "filters": "Filters", "searchPlaceholder": "Search providers...", "allProviders": "All providers", "noResults": "No providers found", "tryDifferentFilters": "Try changing filters to find suitable providers", "selected": "Selected", "options": "Options"}, "configuration": {"title": "Configure {{provider}}", "description": "Set up connection information for {{provider}}", "needHelp": "Need setup guide?", "viewGuide": "View guide", "basicSettings": "Basic settings", "serverName": "Server name", "serverNamePlaceholder": "Enter server name", "host": "Host", "port": "Port", "username": "Username", "usernamePlaceholder": "<EMAIL>", "password": "Password", "passwordPlaceholder": "Enter password", "authentication": "Authentication", "providerSettings": "Provider settings", "securitySettings": "Security settings", "useSsl": "Use SSL", "useStartTls": "Use StartTLS", "isActive": "Active", "testConnection": "Test connection", "testNow": "Test now", "testSuccess": "Connection successful!", "testFailed": "Connection failed", "testError": "Error testing connection", "testSubject": "Test Email from RedAI", "testBody": "This is a test email to verify configuration. If you receive this email, the configuration is successful!", "save": "Save configuration", "viewDocs": "View documentation"}, "review": {"title": "Review configuration", "description": "Check information before completing", "providerInfo": "Provider information", "configuration": "Configuration", "category": "Category", "authMethod": "Authentication method", "host": "Host", "port": "Port", "username": "Username", "ssl": "SSL", "startTls": "StartTLS", "complete": "Complete"}}, "provider": {"category": {"title": "Category", "personal": "Personal", "business": "Business", "transactional": "Transactional"}, "features": "Features", "authMethods": "Authentication methods", "authMethod": "Authentication", "popular": "Popular", "popularOnly": "Popular only", "found": "found"}, "setupGuide": {"progress": "Progress", "step": "Step", "completed": "Completed", "code": "Code", "copyCode": "Copy", "tips": "Tips", "previousStep": "Previous step", "nextStep": "Next step", "markComplete": "Mark complete", "additionalResources": "Additional resources", "officialDocs": "Official documentation", "officialDocsDesc": "Detailed guide from provider", "troubleshooting": "Troubleshooting", "troubleshootingDesc": "Solutions for common issues", "pricing": "Pricing", "startOver": "Start over", "startOverDesc": "Reset progress and start from beginning", "allDone": "All done!"}, "smtp": {"title": "SMTP Configuration", "description": "Configure SMTP server for sending emails", "form": {"fields": {"serverName": "Server Name", "host": "SMTP Host", "port": "Port", "username": "Username", "password": "Password", "useSsl": "Use SSL", "useStartTls": "Use StartTLS", "isActive": "Active", "additionalSettings": "Additional Settings (JSON)"}, "placeholders": {"serverName": "Enter SMTP server name", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "Enter password or app password", "additionalSettings": "{\"timeout\": 30000}"}, "test": "Test Connection", "testDescription": "Enter email to receive test email from this SMTP configuration"}, "actions": {"save": "Save Configuration", "sendTest": "Send Test"}, "saveSuccess": "SMTP configuration saved successfully!", "saveError": "Error occurred while saving SMTP configuration"}, "switchToLegacy": "Manual mode", "switchToWizard": "Wizard mode"}