import React from 'react';
import { EmailElement as EmailElementType } from '../types';
import { Button } from '@/shared/components/common';
import { ArrowUp, ArrowDown, Trash2 } from 'lucide-react';

interface EmailElementProps {
  element: EmailElementType;
  index: number;
  isSelected: boolean;
  onSelect: (element: EmailElementType, index: number) => void;
  onDelete: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  totalElements: number;
}

const EmailElement: React.FC<EmailElementProps> = ({
  element,
  index,
  isSelected,
  onSelect,
  onDelete,
  onMoveUp,
  onMoveDown,
  totalElements
}) => {
  // Render phần tử dựa trên loại
  const renderElement = () => {
    switch (element.type) {
      case 'text':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || 'Văn bản' }}
            onDoubleClick={(e) => {
              e.stopPropagation();
              // <PERSON><PERSON> lý chỉnh sửa nội dung text
            }}
          />
        );

      case 'heading': {
        const HeadingTag = element.headingType || 'h2';
        return React.createElement(HeadingTag, {
          style: element.style as React.CSSProperties,
          dangerouslySetInnerHTML: { __html: element.content || 'Tiêu đề' },
          onDoubleClick: (e: React.MouseEvent<HTMLElement>) => {
            e.stopPropagation();
            // Xử lý chỉnh sửa nội dung heading
          }
        });
      }

      case 'image':
        return (
          <div
            style={{
              ...element.style as React.CSSProperties,
              overflow: 'hidden',
              borderRadius: '4px'
            }}
            className="relative"
          >
            <img
              src={element.src || 'https://via.placeholder.com/600x200?text=Hình+ảnh'}
              alt={element.alt || 'Hình ảnh'}
              style={{
                maxWidth: '100%',
                maxHeight: '400px',
                width: 'auto',
                height: 'auto',
                display: 'block',
                objectFit: 'contain'
              }}
              onDoubleClick={(e) => {
                e.stopPropagation();
                // Xử lý chỉnh sửa hình ảnh
              }}
            />
          </div>
        );

      case 'button':
        return (
          <div style={{ textAlign: (element.style?.textAlign as React.CSSProperties['textAlign']) || 'center' }}>
            <a
              href={element.url || '#'}
              style={{
                backgroundColor: element.style?.backgroundColor || '#0070f3',
                color: element.style?.color || '#ffffff',
                padding: element.style?.padding || '10px 20px',
                borderRadius: element.style?.borderRadius || 4,
                display: 'inline-block',
                textDecoration: 'none',
                fontWeight: element.style?.fontWeight as string || 'bold',
                width: element.style?.width as string,
              }}
              onClick={(e) => e.preventDefault()}
            >
              {element.text || 'Nút nhấn'}
            </a>
          </div>
        );

      case 'divider':
        return <hr style={element.style as React.CSSProperties} />;

      case 'spacer':
        return <div style={element.style as React.CSSProperties}>&nbsp;</div>;

      case 'list':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<ul><li>Mục danh sách</li></ul>' }}
          />
        );

      case 'link':
        return (
          <a
            href={element.url || '#'}
            style={element.style as React.CSSProperties}
            onClick={(e) => e.preventDefault()}
          >
            {element.text || 'Liên kết'}
          </a>
        );

      case 'social':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '<div style="text-align: center;"><a href="#" style="margin: 0 10px;">Facebook</a><a href="#" style="margin: 0 10px;">Twitter</a><a href="#" style="margin: 0 10px;">Instagram</a></div>' }}
          />
        );

      case 'header':
      case 'footer':
        return (
          <div
            style={element.style as React.CSSProperties}
            dangerouslySetInnerHTML={{ __html: element.content || '' }}
          />
        );

      case '1column':
        return (
          <div style={element.style as React.CSSProperties} className="column">
            {element.children && element.children.length > 0 ? (
              element.children.map((child) => (
                <div key={child.id} className="column-item">
                  {/* Render phần tử con trong cột */}
                  <EmailElement
                    element={child}
                    index={-1} // Đánh dấu là phần tử con
                    isSelected={false}
                    onSelect={() => {}}
                    onDelete={() => {}}
                    onMoveUp={() => {}}
                    onMoveDown={() => {}}
                    totalElements={0}
                  />
                </div>
              ))
            ) : (
              <div className="text-center p-5 text-muted-foreground">
                Cột trống
              </div>
            )}
          </div>
        );

      case '2columns': {
        // Tìm cột trái và phải
        const leftColumn = element.children?.find(child => child.columnPosition === 'left');
        const rightColumn = element.children?.find(child => child.columnPosition === 'right');

        return (
          <div style={element.style as React.CSSProperties} className="two-columns">
            <div className="column" style={{ width: '50%', display: 'inline-block', verticalAlign: 'top', ...leftColumn?.style as React.CSSProperties }}>
              {leftColumn && leftColumn.children && leftColumn.children.length > 0 ? (
                leftColumn.children.map((child) => (
                  <div key={child.id} className="column-item">
                    {/* Render phần tử con trong cột trái */}
                    <EmailElement
                      element={child}
                      index={-1} // Đánh dấu là phần tử con
                      isSelected={false}
                      onSelect={() => {}}
                      onDelete={() => {}}
                      onMoveUp={() => {}}
                      onMoveDown={() => {}}
                      totalElements={0}
                    />
                  </div>
                ))
              ) : (
                <div className="text-center p-5 text-muted-foreground">
                  Cột trái trống
                </div>
              )}
            </div>
            <div className="column" style={{ width: '50%', display: 'inline-block', verticalAlign: 'top', ...rightColumn?.style as React.CSSProperties }}>
              {rightColumn && rightColumn.children && rightColumn.children.length > 0 ? (
                rightColumn.children.map((child) => (
                  <div key={child.id} className="column-item">
                    {/* Render phần tử con trong cột phải */}
                    <EmailElement
                      element={child}
                      index={-1} // Đánh dấu là phần tử con
                      isSelected={false}
                      onSelect={() => {}}
                      onDelete={() => {}}
                      onMoveUp={() => {}}
                      onMoveDown={() => {}}
                      totalElements={0}
                    />
                  </div>
                ))
              ) : (
                <div className="text-center p-5 text-muted-foreground">
                  Cột phải trống
                </div>
              )}
            </div>
          </div>
        );
      }

      default:
        return <div>{element.type}</div>;
    }
  };

  return (
    <div
      className={`email-element relative p-2 my-2 border-2 transition-all duration-200 overflow-hidden ${isSelected ? 'border-accent bg-accent/5 shadow-sm' : 'border-transparent'} hover:border-accent/50 hover:bg-accent/5 rounded-md`}
      onClick={() => onSelect(element, index)}
    >
      {renderElement()}

      {isSelected && (
        <div className="absolute top-1 right-1 flex gap-1 bg-background/95 backdrop-blur-sm rounded-md p-1 shadow-md border border-border/50">
          {index > 0 && (
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 hover:bg-accent/20 hover:text-accent"
              onClick={(e) => {
                e.stopPropagation();
                onMoveUp();
              }}
              title="Di chuyển lên"
            >
              <ArrowUp size={12} />
            </Button>
          )}

          {index < totalElements - 1 && (
            <Button
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 hover:bg-accent/20 hover:text-accent"
              onClick={(e) => {
                e.stopPropagation();
                onMoveDown();
              }}
              title="Di chuyển xuống"
            >
              <ArrowDown size={12} />
            </Button>
          )}

          <Button
            size="sm"
            variant="ghost"
            className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            title="Xóa phần tử"
          >
            <Trash2 size={12} />
          </Button>
        </div>
      )}
    </div>
  );
};

export default EmailElement;

