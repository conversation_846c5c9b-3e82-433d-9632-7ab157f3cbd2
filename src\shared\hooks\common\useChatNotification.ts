import { useCallback } from 'react';
import {
  addChatNotification,
  removeChatNotification,
  clearChatNotifications,
} from '@/shared/store/slices/chatSlice';
import { NotificationType } from '@/shared/components/common/Notification/Notification';
import { useChatPanel } from '@/shared/contexts/chat-panel';
import { useAppDispatch, useAppSelector } from '@/shared/store';

/**
 * Hook để quản lý thông báo trong chat panel
 */
const useChatNotification = () => {
  const dispatch = useAppDispatch();
  const notifications = useAppSelector((state) => state.chat.notifications);
  const { isChatPanelOpen, setIsChatPanelOpen } = useChatPanel();

  /**
   * Thêm thông báo mới
   * @param type Loại thông báo
   * @param message Nội dung thông báo
   * @param duration Thời gian hiển thị (ms)
   * @returns ID của thông báo
   */
  const addNotification = useCallback(
    (type: NotificationType, message: string, duration = 5000) => {
      // Tạo ID có chứa timestamp để sắp xếp theo thứ tự thời gian
      const timestamp = Date.now();
      const id = `notification-${timestamp}-${Math.random().toString(36).substring(2, 9)}`;

      console.log(`[useChatNotification] Adding notification with timestamp: ${timestamp}`);

      dispatch(
        addChatNotification({
          id,
          type,
          message,
          duration,
        })
      );

      return id;
    },
    [dispatch]
  );

  /**
   * Xóa thông báo
   * @param id ID của thông báo cần xóa
   */
  const removeNotification = useCallback(
    (id: string) => {
      dispatch(removeChatNotification(id));
    },
    [dispatch]
  );

  /**
   * Xóa tất cả thông báo
   */
  const clearNotifications = useCallback(() => {
    dispatch(clearChatNotifications());
  }, [dispatch]);

  /**
   * Hiển thị thông báo
   * @param type Loại thông báo
   * @param message Nội dung thông báo
   * @returns ID của thông báo
   */
  const showNotification = useCallback(
    (type: NotificationType, message: string) => {
      // Mở chat panel nếu chưa mở
      if (!isChatPanelOpen) {
        console.log('[useChatNotification] Chat panel is closed, opening it now');
        setIsChatPanelOpen(true);

        // Thêm một timeout nhỏ để đảm bảo chat panel đã được mở trước khi thêm thông báo
        setTimeout(() => {
          console.log('[useChatNotification] Adding notification after opening chat panel');
          const id = addNotification(type, message, 5000);
          console.log(
            `[useChatNotification] Added notification with delay: ${type} - ${message} - ID: ${id}`
          );
        }, 100);

        // Trả về một ID tạm thời
        const tempId = `temp-${Date.now()}`;
        return tempId;
      }

      // Nếu chat panel đã mở, thêm thông báo ngay lập tức
      const id = addNotification(type, message, 5000);
      console.log(
        `[useChatNotification] Added notification immediately: ${type} - ${message} - ID: ${id}`
      );

      return id;
    },
    [addNotification, isChatPanelOpen, setIsChatPanelOpen]
  );

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
    showNotification,
  };
};

export default useChatNotification;
