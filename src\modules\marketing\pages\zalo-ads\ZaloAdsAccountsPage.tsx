import React, { useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import {
  Plus,
  CreditCard,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import {
  Card,
  Table,
  Chip,
  IconCard,
  Tooltip,
  Button,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { MarketingViewHeader } from '../../components/common/MarketingViewHeader';
import { useZaloAdsAccounts } from '../../hooks/zalo-ads/useZaloAdsAccounts';
import { ConnectZaloAdsAccountForm } from '../../components/zalo-ads/ConnectZaloAdsAccountForm';
import type { ZaloAdsAccountQueryDto, ZaloAdsAccountDto, ZaloAdsAccountStatus } from '../../types/zalo-ads.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý tài khoản Zalo Ads
 */
export function ZaloAdsAccountsPage() {
  const { t } = useTranslation('marketing');
  const [searchParams, setSearchParams] = useSearchParams();
  const [, setSelectedAccount] = React.useState<ZaloAdsAccountDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible: isConnectVisible, showForm: showConnectForm, hideForm: hideConnectForm } = useSlideForm();

  // Kiểm tra nếu có action=connect trong URL thì hiển thị form
  useEffect(() => {
    if (searchParams.get('action') === 'connect') {
      showConnectForm();
      setSearchParams({});
    }
  }, [searchParams, showConnectForm, setSearchParams]);

  const handleViewAccount = useCallback((account: ZaloAdsAccountDto) => {
    setSelectedAccount(account);
    // Navigate to account detail or show detail modal
    console.log('View account:', account.id);
  }, []);

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<ZaloAdsAccountDto>[]>(
    () => [
      {
        key: 'account',
        title: t('marketing:zaloAds.accounts.table.account', 'Tài khoản'),
        dataIndex: 'accountName',
        sortable: true,
        render: (value: unknown, record: ZaloAdsAccountDto) => (
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
              {String(value || '').charAt(0).toUpperCase()}
            </div>
            <div>
              <div className="font-medium">{String(value || '')}</div>
              <div className="text-sm text-muted-foreground">
                ID: {record.accountId}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: 'business',
        title: t('marketing:zaloAds.accounts.table.business', 'Doanh nghiệp'),
        dataIndex: 'businessName',
        render: (value: unknown) => (
          <span className="text-sm">
            {String(value || t('marketing:zaloAds.accounts.table.noBusiness', 'Chưa liên kết'))}
          </span>
        ),
      },
      {
        key: 'status',
        title: t('marketing:zaloAds.accounts.table.status', 'Trạng thái'),
        dataIndex: 'status',
        sortable: true,
        render: (value: unknown) => {
          const status = value as ZaloAdsAccountStatus;
          switch (status) {
            case 'ACTIVE':
              return (
                <Chip variant="success" leftIconName="check-circle">
                  {t('marketing:zaloAds.accounts.status.active', 'Hoạt động')}
                </Chip>
              );
            case 'INACTIVE':
              return (
                <Chip variant="warning" leftIconName="alert-circle">
                  {t('marketing:zaloAds.accounts.status.inactive', 'Không hoạt động')}
                </Chip>
              );
            case 'SUSPENDED':
              return (
                <Chip variant="danger" leftIconName="x-circle">
                  {t('marketing:zaloAds.accounts.status.suspended', 'Bị tạm ngưng')}
                </Chip>
              );
            case 'PENDING_VERIFICATION':
              return (
                <Chip variant="info" leftIconName="alert-circle">
                  {t('marketing:zaloAds.accounts.status.pending', 'Chờ xác minh')}
                </Chip>
              );
            default:
              return <Chip variant="info">{status}</Chip>;
          }
        },
      },
      {
        key: 'balance',
        title: t('marketing:zaloAds.accounts.table.balance', 'Số dư'),
        dataIndex: 'balance',
        sortable: true,
        render: (value: unknown, record: ZaloAdsAccountDto) => (
          <div className="text-right">
            <div className="font-medium">
              {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: record.currency || 'VND',
                maximumFractionDigits: 0
              }).format(Number(value || 0))}
            </div>
            <div className="text-xs text-muted-foreground">
              {record.currency || 'VND'}
            </div>
          </div>
        ),
      },
      {
        key: 'timeZone',
        title: t('marketing:zaloAds.accounts.table.timeZone', 'Múi giờ'),
        dataIndex: 'timeZone',
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {String(value || 'UTC+7')}
          </span>
        ),
      },
      {
        key: 'updatedAt',
        title: t('marketing:zaloAds.accounts.table.updated', 'Cập nhật'),
        dataIndex: 'updatedAt',
        sortable: true,
        render: (value: unknown) => (
          <span className="text-sm text-muted-foreground">
            {value ? new Date(Number(value)).toLocaleDateString('vi-VN') : ''}
          </span>
        ),
      },
      {
        key: 'actions',
        title: t('marketing:zaloAds.accounts.table.actions', 'Thao tác'),
        width: '120px',
        render: (_: unknown, record: ZaloAdsAccountDto) => (
          <div className="flex items-center space-x-2">
            <Tooltip content={t('marketing:zaloAds.accounts.viewAccount', 'Xem chi tiết tài khoản')}>
              <IconCard
                icon="eye"
                variant="default"
                size="sm"
                onClick={() => handleViewAccount(record)}
              />
            </Tooltip>
            <Tooltip content={t('common.edit', 'Chỉnh sửa')}>
              <IconCard
                icon="edit"
                variant="default"
                size="sm"
                onClick={() => {
                  console.log('Edit account:', record.id);
                }}
              />
            </Tooltip>
            <Tooltip content={t('common.delete', 'Xóa')}>
              <IconCard
                icon="trash"
                variant="danger"
                size="sm"
                onClick={() => {
                  console.log('Delete account:', record.id);
                }}
              />
            </Tooltip>
          </div>
        ),
      },
    ],
    [t, handleViewAccount]
  );

  // Tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common.all', 'Tất cả'), icon: 'list', value: 'all' },
      {
        id: 'active',
        label: t('marketing:zaloAds.accounts.status.active', 'Hoạt động'),
        icon: 'check',
        value: 'ACTIVE',
      },
      {
        id: 'inactive',
        label: t('marketing:zaloAds.accounts.status.inactive', 'Không hoạt động'),
        icon: 'pause',
        value: 'INACTIVE',
      },
      {
        id: 'suspended',
        label: t('marketing:zaloAds.accounts.status.suspended', 'Bị tạm ngưng'),
        icon: 'x',
        value: 'SUSPENDED',
      },
      {
        id: 'pending',
        label: t('marketing:zaloAds.accounts.status.pending', 'Chờ xác minh'),
        icon: 'clock',
        value: 'PENDING_VERIFICATION',
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): ZaloAdsAccountQueryDto => {
      const queryParams: ZaloAdsAccountQueryDto = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection: params.sortDirection || undefined,
      };

      if (params.filterValue !== 'all') {
        queryParams.status = params.filterValue as ZaloAdsAccountStatus;
      }

      return queryParams;
    },
    []
  );

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<ZaloAdsAccountDto, ZaloAdsAccountQueryDto>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // API call với query từ dataTable
  const { data: accountsData, isLoading } = useZaloAdsAccounts(dataTable.queryParams);

  // Lưu trữ tham chiếu đến hàm updateTableData
  const updateTableDataRef = React.useRef(dataTable.updateTableData);

  // Cập nhật tham chiếu khi dataTable thay đổi
  useEffect(() => {
    updateTableDataRef.current = dataTable.updateTableData;
  }, [dataTable]);

  // Cập nhật dữ liệu bảng với API data
  useEffect(() => {
    updateTableDataRef.current(accountsData, isLoading);
  }, [accountsData, isLoading]);

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: dataTable.tableData.handleSearch,
      setSelectedFilterId: dataTable.filter.setSelectedId,
      setDateRange: dataTable.dateRange.setDateRange,
      handleSortChange: dataTable.tableData.handleSortChange,
      selectedFilterValue: dataTable.filter.selectedValue,
      filterValueLabelMap: {
        ACTIVE: t('marketing:zaloAds.accounts.status.active', 'Hoạt động'),
        INACTIVE: t('marketing:zaloAds.accounts.status.inactive', 'Không hoạt động'),
        SUSPENDED: t('marketing:zaloAds.accounts.status.suspended', 'Bị tạm ngưng'),
        PENDING_VERIFICATION: t('marketing:zaloAds.accounts.status.pending', 'Chờ xác minh'),
      },
      t,
    });

  const handleConnectSuccess = () => {
    hideConnectForm();
    setSearchParams({});
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <MarketingViewHeader
        title={t('marketing:zaloAds.accounts.title', 'Zalo Ads Accounts')}
        description={t('marketing:zaloAds.accounts.description', 'Quản lý tài khoản quảng cáo Zalo')}
        actions={
          <Button onClick={() => showConnectForm()} className="gap-2">
            <Plus className="h-4 w-4" />
            {t('marketing:zaloAds.accounts.connectAccount', 'Kết nối tài khoản')}
          </Button>
        }
      />

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.accounts.stats.totalAccounts', 'Tổng tài khoản')}
            </span>
            <CreditCard className="h-4 w-4 text-blue-600" />
          </div>
          <div className="text-2xl font-bold text-blue-600">
            {accountsData?.meta.totalItems || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.accounts.stats.connected', 'Đã kết nối')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.accounts.stats.activeAccounts', 'Tài khoản hoạt động')}
            </span>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </div>
          <div className="text-2xl font-bold text-green-600">
            {accountsData?.items.filter((account: ZaloAdsAccountDto) => account.status === 'ACTIVE').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.accounts.stats.readyToUse', 'Sẵn sàng sử dụng')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.accounts.stats.totalBalance', 'Tổng số dư')}
            </span>
            <CreditCard className="h-4 w-4 text-orange-600" />
          </div>
          <div className="text-2xl font-bold text-orange-600">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
              maximumFractionDigits: 0
            }).format(
              accountsData?.items.reduce((sum: number, account: ZaloAdsAccountDto) => sum + account.balance, 0) || 0
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.accounts.stats.availableBalance', 'Số dư khả dụng')}
          </p>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">
              {t('marketing:zaloAds.accounts.stats.pendingAccounts', 'Chờ xác minh')}
            </span>
            <AlertCircle className="h-4 w-4 text-yellow-600" />
          </div>
          <div className="text-2xl font-bold text-yellow-600">
            {accountsData?.items.filter((account: ZaloAdsAccountDto) => account.status === 'PENDING_VERIFICATION').length || 0}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            {t('marketing:zaloAds.accounts.stats.needsVerification', 'Cần xác minh')}
          </p>
        </Card>
      </div>

      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={() => showConnectForm()}
        items={dataTable.menuItems}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
      />

      {/* ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Accounts Table */}
      <Card className="overflow-hidden">
        <Table<ZaloAdsAccountDto>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={accountsData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: accountsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: accountsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* SlideInForm cho kết nối tài khoản */}
      <SlideInForm isVisible={isConnectVisible}>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold">
              {t('marketing:zaloAds.accounts.connect.title', 'Kết nối tài khoản Zalo Ads')}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {t('marketing:zaloAds.accounts.connect.description', 'Kết nối tài khoản Zalo Ads để bắt đầu quảng cáo')}
            </p>
          </div>
          <ConnectZaloAdsAccountForm onSuccess={handleConnectSuccess} onCancel={hideConnectForm} />
        </div>
      </SlideInForm>
    </div>
  );
}

export default ZaloAdsAccountsPage;
