import React from 'react';
import { StepIndicatorProps } from './StepIndicator.types';
import { StepStatus } from '../FormWizard/FormWizard.types';
import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

/**
 * StepIndicator component hiển thị tiến trình các bước
 *
 * @example
 * ```tsx
 * <StepIndicator
 *   steps={[
 *     { id: 'personal', title: 'Thông tin cá nhân' },
 *     { id: 'contact', title: 'Thông tin liên hệ' },
 *     { id: 'review', title: 'Xá<PERSON> nhận' }
 *   ]}
 *   currentStepId="contact"
 *   stepStatus={{
 *     personal: StepStatus.COMPLETED,
 *     contact: StepStatus.ACTIVE,
 *     review: StepStatus.PENDING
 *   }}
 *   orientation="horizontal"
 *   onStepClick={handleStepClick}
 * />
 * ```
 */
const StepIndicator: React.FC<StepIndicatorProps> = ({
  steps,
  currentStepId,
  stepStatus,
  orientation = 'horizontal',
  onStepClick,
  clickable = false,
  showTitle = true,
  showDescription = false,
  showIcon = true,
  showNumber = true,
  className = '',
}) => {
  const { currentTheme } = useTheme();

  // Lấy màu từ theme
  const getStepColor = (status: StepStatus) => {
    switch (status) {
      case StepStatus.COMPLETED:
        return currentTheme.semanticColors.success;
      case StepStatus.ACTIVE:
        return currentTheme.semanticColors.primary;
      case StepStatus.ERROR:
        return currentTheme.semanticColors.destructive;
      default:
        return currentTheme.semanticColors.border;
    }
  };

  // Lấy icon cho bước
  const getStepIcon = (status: StepStatus, index: number) => {
    if (status === StepStatus.COMPLETED) {
      return <Icon name="check" className="text-white" />;
    }

    if (status === StepStatus.ERROR) {
      return <Icon name="x" className="text-white" />;
    }

    if (showNumber) {
      return <span className="text-sm">{index + 1}</span>;
    }

    return null;
  };

  // Xử lý khi click vào bước
  const handleStepClick = (stepId: string) => {
    if (clickable && onStepClick) {
      onStepClick(stepId);
    }
  };

  // Render indicator theo hướng horizontal
  const renderHorizontalIndicator = () => {
    return (
      <div className="flex items-center justify-between w-full">
        {steps.map((step, index) => {
          const status = stepStatus[step.id] || StepStatus.PENDING;
          const isActive = step.id === currentStepId;
          const isCompleted = status === StepStatus.COMPLETED;
          const isError = status === StepStatus.ERROR;
          const stepColor = getStepColor(status);

          return (
            <div
              key={step.id}
              className={`flex flex-col items-center ${clickable ? 'cursor-pointer' : ''}`}
              onClick={() => handleStepClick(step.id)}
            >
              {/* Connector line (left) */}
              {index > 0 && (
                <div
                  className="hidden sm:block absolute h-[2px] top-1/2 -translate-y-1/2"
                  style={{
                    left: `calc(${(index - 1) * (100 / (steps.length - 1))}% + 20px)`,
                    right: `calc(${100 - index * (100 / (steps.length - 1))}% + 20px)`,
                    backgroundColor: isCompleted ? stepColor : currentTheme.semanticColors.border,
                  }}
                ></div>
              )}

              {/* Step circle */}
              <div
                className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full text-white mb-2`}
                style={{
                  backgroundColor: stepColor,
                  borderColor: isActive ? stepColor : 'transparent',
                  borderWidth: isActive ? '2px' : '0',
                }}
              >
                {showIcon && getStepIcon(status, index)}
              </div>

              {/* Step title */}
              {showTitle && (
                <div
                  className={`text-sm font-medium ${
                    isActive
                      ? 'text-foreground'
                      : isCompleted
                        ? 'text-success'
                        : isError
                          ? 'text-destructive'
                          : 'text-muted'
                  }`}
                >
                  {step.title}
                </div>
              )}

              {/* Step description */}
              {showDescription && step.description && (
                <div className="text-xs text-muted mt-1 text-center max-w-[120px]">
                  {step.description}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render indicator theo hướng vertical
  const renderVerticalIndicator = () => {
    return (
      <div className="flex flex-col space-y-4">
        {steps.map((step, index) => {
          const status = stepStatus[step.id] || StepStatus.PENDING;
          const isActive = step.id === currentStepId;
          const isCompleted = status === StepStatus.COMPLETED;
          const isError = status === StepStatus.ERROR;
          const stepColor = getStepColor(status);

          return (
            <div
              key={step.id}
              className={`flex items-start ${clickable ? 'cursor-pointer' : ''}`}
              onClick={() => handleStepClick(step.id)}
            >
              <div className="relative">
                {/* Connector line (top) */}
                {index > 0 && (
                  <div
                    className="absolute w-[2px] top-[-16px] bottom-[16px] left-[15px]"
                    style={{
                      backgroundColor: isCompleted ? stepColor : currentTheme.semanticColors.border,
                    }}
                  ></div>
                )}

                {/* Step circle */}
                <div
                  className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full text-white`}
                  style={{
                    backgroundColor: stepColor,
                    borderColor: isActive ? stepColor : 'transparent',
                    borderWidth: isActive ? '2px' : '0',
                  }}
                >
                  {showIcon && getStepIcon(status, index)}
                </div>

                {/* Connector line (bottom) */}
                {index < steps.length - 1 && (
                  <div
                    className="absolute w-[2px] top-[32px] bottom-[-16px] left-[15px]"
                    style={{
                      backgroundColor:
                        isCompleted || isActive ? stepColor : currentTheme.semanticColors.border,
                    }}
                  ></div>
                )}
              </div>

              <div className="ml-4">
                {/* Step title */}
                {showTitle && (
                  <div
                    className={`text-sm font-medium ${
                      isActive
                        ? 'text-foreground'
                        : isCompleted
                          ? 'text-success'
                          : isError
                            ? 'text-destructive'
                            : 'text-muted'
                    }`}
                  >
                    {step.title}
                  </div>
                )}

                {/* Step description */}
                {showDescription && step.description && (
                  <div className="text-xs text-muted mt-1">{step.description}</div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={`step-indicator ${className}`}>
      {orientation === 'horizontal' ? renderHorizontalIndicator() : renderVerticalIndicator()}
    </div>
  );
};

export default StepIndicator;
