import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { webhookService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import { WebhookConfig } from '../types';

// Get webhooks
export const useWebhooks = (params?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS,
    queryFn: () => webhookService.getWebhooks(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single webhook
export const useWebhook = (id: string, enabled = true) => {
  return useQuery({
    queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS, id],
    queryFn: () => webhookService.getWebhook(id),
    enabled: enabled && !!id,
    staleTime: 2 * 60 * 1000,
  });
};

// Get webhook deliveries
export const useWebhookDeliveries = (
  webhookId: string,
  params?: { page?: number; limit?: number },
  enabled = true
) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOK_DELIVERIES(webhookId),
    queryFn: () => webhookService.getWebhookDeliveries(webhookId, params),
    enabled: enabled && !!webhookId,
    staleTime: 30 * 1000, // 30 seconds
  });
};

// Create webhook mutation
export const useCreateWebhook = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<WebhookConfig, 'id' | 'createdAt' | 'updatedAt'>) =>
      webhookService.createWebhook(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS,
      });
    },
  });
};

// Update webhook mutation
export const useUpdateWebhook = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<WebhookConfig> }) =>
      webhookService.updateWebhook(id, data),
    onSuccess: (updatedWebhook) => {
      queryClient.setQueryData(
        [...EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS, updatedWebhook.id],
        updatedWebhook
      );
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS,
      });
    },
  });
};

// Delete webhook mutation
export const useDeleteWebhook = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => webhookService.deleteWebhook(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({
        queryKey: [...EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS, deletedId],
      });
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS,
      });
    },
  });
};

// Test webhook mutation
export const useTestWebhook = () => {
  return useMutation({
    mutationFn: ({ id, timeout }: { id: string; timeout?: number }) =>
      webhookService.testWebhook(id, timeout),
  });
};

// Retry webhook delivery mutation
export const useRetryWebhookDelivery = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (deliveryId: string) => webhookService.retryWebhookDelivery(deliveryId),
    onSuccess: () => {
      // Invalidate webhook deliveries
      queryClient.invalidateQueries({
        queryKey: EXTERNAL_AGENT_QUERY_KEYS.WEBHOOKS,
      });
    },
  });
};

// Custom hook for webhook management
export const useWebhookManager = (webhookId?: string) => {
  const createMutation = useCreateWebhook();
  const updateMutation = useUpdateWebhook();
  const deleteMutation = useDeleteWebhook();
  const testMutation = useTestWebhook();
  const retryMutation = useRetryWebhookDelivery();

  const createWebhook = (data: Omit<WebhookConfig, 'id' | 'createdAt' | 'updatedAt'>) => {
    return createMutation.mutate(data);
  };

  const updateWebhook = (data: Partial<WebhookConfig>) => {
    if (!webhookId) throw new Error('Webhook ID is required for update');
    return updateMutation.mutate({ id: webhookId, data });
  };

  const deleteWebhook = () => {
    if (!webhookId) throw new Error('Webhook ID is required for delete');
    return deleteMutation.mutate(webhookId);
  };

  const testWebhook = (timeout?: number) => {
    if (!webhookId) throw new Error('Webhook ID is required for test');
    return testMutation.mutate({ id: webhookId, timeout });
  };

  const retryDelivery = (deliveryId: string) => {
    return retryMutation.mutate(deliveryId);
  };

  return {
    createWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook,
    retryDelivery,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isTesting: testMutation.isPending,
    isRetrying: retryMutation.isPending,
    error: createMutation.error || updateMutation.error || deleteMutation.error || testMutation.error || retryMutation.error,
    testResult: testMutation.data,
  };
};
