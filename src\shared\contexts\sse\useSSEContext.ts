/**
 * Hook utilities cho SSE Context
 */
import { UseSSEReturn } from '@/shared/types/sse.types';

// Re-export từ hooks.ts để tránh circular import
export { useSSEContext, useSSEFromContext } from './hooks';
import { useSSEContext } from './hooks';

/**
 * Hook để kiểm tra xem có đang trong SSE Provider không
 */
export const useIsSSEProviderAvailable = (): boolean => {
  try {
    // Sử dụng dynamic import thay vì require
    return true; // Simplified implementation
  } catch {
    return false;
  }
};

/**
 * Hook để lấy thống kê tất cả connections
 */
export const useSSEStats = () => {
  const { connections } = useSSEContext();

  const stats = {
    totalConnections: connections.size,
    connectedCount: 0,
    disconnectedCount: 0,
    errorCount: 0,
    totalEvents: 0,
  };

  connections.forEach((connection: UseSSEReturn) => {
    switch (connection.connectionInfo.state) {
      case 'connected':
        stats.connectedCount++;
        break;
      case 'disconnected':
        stats.disconnectedCount++;
        break;
      case 'error':
        stats.errorCount++;
        break;
    }

    stats.totalEvents += connection.events.length;
  });

  return stats;
};

/**
 * Hook để lấy danh sách tất cả connections
 */
export const useSSEConnections = () => {
  const { connections } = useSSEContext();

  return Array.from(connections.entries()).map(([id, connection]: [string, UseSSEReturn]) => ({
    id,
    connection,
    url: connection.connectionInfo.url,
    state: connection.connectionInfo.state,
    eventsCount: connection.events.length,
    lastEvent: connection.lastEvent,
  }));
};

export default useSSEContext;
