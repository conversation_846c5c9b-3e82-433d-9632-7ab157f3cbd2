import { useState, useRef, useEffect, ReactNode, useLayoutEffect } from 'react';
import ScrollArea from './ScrollArea';

interface DropdownItem {
  id: string;
  label?: ReactNode;
  onClick?: () => void;
  icon?: ReactNode;
  disabled?: boolean;
  divider?: boolean;
  subItems?: DropdownItem[];
}

interface DropdownProps {
  trigger?: ReactNode;
  items: DropdownItem[];
  placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  width?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

const Dropdown = ({
  trigger,
  items,
  placement = 'bottom-left',
  width = 'w-48',
  isOpen: controlledIsOpen,
  onClose,
}: DropdownProps) => {
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownContentRef = useRef<HTMLDivElement>(null);

  // Determine if component is controlled or uncontrolled
  const isControlled = controlledIsOpen !== undefined;
  const isOpen = isControlled ? controlledIsOpen : internalIsOpen;

  // Ref cho việc tính toán vị trí
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>('bottom');

  // Tính toán vị trí tốt nhất cho dropdown - sử dụng useLayoutEffect để cập nhật trước khi render
  useLayoutEffect(() => {
    if (isOpen && dropdownRef.current) {
      const rect = dropdownRef.current.getBoundingClientRect();
      const viewportHeight = window.innerHeight;
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceAbove = rect.top;
      const dropdownHeight = 300; // Ước tính chiều cao của dropdown

      // Nếu không đủ không gian phía dưới nhưng có đủ không gian phía trên
      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setDropdownPosition('top');
      } else {
        setDropdownPosition('bottom');
      }
    }
  }, [isOpen]);

  // Tính toán vị trí ngang dựa trên placement
  const getHorizontalPosition = () => {
    switch (placement) {
      case 'bottom-right':
      case 'top-right':
        return dropdownRef.current
          ? dropdownRef.current.getBoundingClientRect().right -
              parseInt(width.replace('w-', '')) * 4
          : 0;
      case 'bottom-left':
      case 'top-left':
      default:
        return dropdownRef.current ? dropdownRef.current.getBoundingClientRect().left : 0;
    }
  };

  // Track which items have their subitems expanded
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  // Close dropdown when clicking outside
  useEffect(() => {
    // Chỉ thêm event listener khi dropdown đang mở
    if (!isOpen) return;

    const handleClickOutside = (event: MouseEvent) => {
      // Ngăn sự kiện lan truyền
      event.stopPropagation();

      // Kiểm tra xem click có phải là bên ngoài dropdown trigger và dropdown content không
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        (!dropdownContentRef.current || !dropdownContentRef.current.contains(event.target as Node))
      ) {
        if (isControlled && onClose) {
          onClose();
        } else {
          setInternalIsOpen(false);
        }
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      // Đóng dropdown khi nhấn Escape
      if (event.key === 'Escape') {
        if (isControlled && onClose) {
          onClose();
        } else {
          setInternalIsOpen(false);
        }
      }
    };

    // Sử dụng capture phase để bắt sự kiện trước khi nó đến target
    document.addEventListener('mousedown', handleClickOutside, true);
    document.addEventListener('keydown', handleKeyDown, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
      document.removeEventListener('keydown', handleKeyDown, true);
    };
  }, [isControlled, onClose, isOpen]);

  // Handle item click
  const handleItemClick = (item: DropdownItem, e?: React.MouseEvent) => {
    // Ngăn sự kiện lan truyền để tránh đóng dropdown trước khi xử lý onClick
    if (e) {
      e.stopPropagation();
    }

    if (!item.disabled && item.onClick) {
      // Thực hiện hành động onClick
      item.onClick();

      // Close dropdown and collapse all expanded items
      if (isControlled && onClose) {
        onClose();
      } else {
        setInternalIsOpen(false);
      }
      setExpandedItems({});
    }
  };

  // Toggle subitem visibility
  const toggleSubItems = (itemId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId],
    }));
  };

  // Render dropdown item
  const renderDropdownItem = (item: DropdownItem) => {
    if (item.divider) {
      return <hr className="my-1 border-gray-200 dark:border-gray-700" />;
    }

    const hasSubItems = item.subItems && item.subItems.length > 0;
    const isExpanded = expandedItems[item.id] || false;

    return (
      <>
        <button
          onClick={e => {
            // Ngăn sự kiện lan truyền
            e.stopPropagation();
            e.preventDefault();

            if (hasSubItems) {
              toggleSubItems(item.id, e);
            } else {
              handleItemClick(item, e);
            }
          }}
          onMouseDown={e => {
            // Ngăn sự kiện mousedown lan truyền
            e.stopPropagation();
          }}
          disabled={item.disabled}
          className={`w-full text-left px-4 py-2 text-sm flex items-center justify-between ${
            item.disabled
              ? 'opacity-50 cursor-not-allowed'
              : 'hover:bg-gray-100 dark:hover:bg-dark-lighter'
          }`}
        >
          <div className="flex items-center">
            {item.icon && <span className="mr-2">{item.icon}</span>}
            {item.label}
          </div>
          {hasSubItems && (
            <svg
              className={`w-4 h-4 transition-transform ${isExpanded ? 'transform rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          )}
        </button>

        {hasSubItems && isExpanded && (
          <div className="pl-4 border-l border-gray-200 dark:border-gray-700 ml-4 my-1">
            {item.subItems!.map(subItem => (
              <div key={subItem.id}>
                <button
                  onClick={e => {
                    // Ngăn sự kiện lan truyền
                    e.stopPropagation();
                    e.preventDefault();
                    handleItemClick(subItem, e);
                  }}
                  onMouseDown={e => {
                    // Ngăn sự kiện mousedown lan truyền
                    e.stopPropagation();
                  }}
                  disabled={subItem.disabled}
                  className={`w-full text-left px-4 py-2 text-sm flex items-center ${
                    subItem.disabled
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-100 dark:hover:bg-dark-lighter'
                  }`}
                >
                  {subItem.icon && <span className="mr-2">{subItem.icon}</span>}
                  {subItem.label}
                </button>
              </div>
            ))}
          </div>
        )}
      </>
    );
  };

  // Xử lý click vào trigger
  const handleTriggerClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (isControlled) {
      if (onClose && isOpen) {
        onClose();
      }
    } else {
      setInternalIsOpen(!internalIsOpen);
    }
  };

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      {/* Trigger */}
      {trigger && (
        <div
          onClick={handleTriggerClick}
          onMouseDown={e => {
            // Ngăn sự kiện mousedown lan truyền
            e.stopPropagation();
          }}
          className="cursor-pointer"
        >
          {trigger}
        </div>
      )}

      {/* Overlay để bắt sự kiện click bên ngoài dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-[99990]"
          onClick={e => {
            e.stopPropagation();
            e.preventDefault();
            if (isControlled && onClose) {
              onClose();
            } else {
              setInternalIsOpen(false);
            }
          }}
        />
      )}

      {/* Dropdown Content */}
      {isOpen && (
        <div
          ref={dropdownContentRef}
          className={`fixed ${width} bg-white dark:bg-dark-light rounded shadow-lg z-[999999] animate-fade-in`}
          style={{
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
            pointerEvents: 'auto', // Đảm bảo sự kiện click được xử lý
            position: 'fixed',
            left: getHorizontalPosition(),
            top: dropdownRef.current
              ? dropdownPosition === 'top'
                ? dropdownRef.current.getBoundingClientRect().top - 200 // Ước tính chiều cao dropdown
                : dropdownRef.current.getBoundingClientRect().bottom + 5
              : 0,
            // Đảm bảo dropdown hiển thị trên tất cả các phần tử khác
            zIndex: 999999,
          }}
          onClick={e => {
            // Ngăn sự kiện lan truyền
            e.stopPropagation();
            e.preventDefault();
          }}
          onMouseDown={e => {
            // Ngăn sự kiện mousedown lan truyền
            e.stopPropagation();
          }}
        >
          <ScrollArea
            height="auto"
            maxHeight="80vh"
            className="py-1"
            autoHide={true}
            invisible={false}
            direction="vertical"
          >
            <ul>
              {items.map(item => (
                <li key={item.id}>{renderDropdownItem(item)}</li>
              ))}
            </ul>
          </ScrollArea>
        </div>
      )}
    </div>
  );
};

export default Dropdown;
