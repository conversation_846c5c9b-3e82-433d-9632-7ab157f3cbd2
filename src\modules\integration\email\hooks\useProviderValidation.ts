/**
 * Provider Validation Hooks
 */

import { useState, useCallback, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  ApiKeyValidationService, 
  DomainValidationService,
  ConnectionHealthService,
  ProviderErrorService,
  ValidationResult,
  ProviderStatus
} from '../services/validation';

export interface UseApiKeyValidationOptions {
  providerId: string;
  apiKey: string;
  additionalData?: Record<string, unknown>;
  enabled?: boolean;
  debounceMs?: number;
}

export interface UseApiKeyValidationReturn {
  isValidating: boolean;
  isValid: boolean | null;
  validationResult: ValidationResult | null;
  error: Error | null;
  validate: () => Promise<void>;
}

/**
 * Hook for real-time API key validation
 */
export const useApiKeyValidation = (options: UseApiKeyValidationOptions): UseApiKeyValidationReturn => {
  const { providerId, apiKey, additionalData, enabled = true, debounceMs = 1000 } = options;
  
  const [debouncedApiKey, setDebouncedApiKey] = useState(apiKey);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);

  // Debounce API key input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedApiKey(apiKey);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [apiKey, debounceMs]);

  // Validation mutation
  const validationMutation = useMutation({
    mutationFn: async () => {
      if (!debouncedApiKey || debouncedApiKey.length < 3) {
        throw new Error('API Key too short');
      }

      return ApiKeyValidationService.validateApiKey({
        providerId,
        apiKey: debouncedApiKey,
        additionalData
      });
    },
    onSuccess: (result) => {
      setValidationResult(result);
    },
    onError: (error) => {
      setValidationResult({
        isValid: false,
        message: error instanceof Error ? error.message : 'Validation failed'
      });
    }
  });

  // Auto-validate when debounced API key changes
  useEffect(() => {
    if (enabled && debouncedApiKey && debouncedApiKey.length >= 3) {
      validationMutation.mutate();
    }
  }, [debouncedApiKey, enabled, providerId, additionalData, validationMutation]);

  const validate = useCallback(async () => {
    await validationMutation.mutateAsync();
  }, [validationMutation]);

  return {
    isValidating: validationMutation.isPending,
    isValid: validationResult?.isValid ?? null,
    validationResult,
    error: validationMutation.error,
    validate
  };
};

/**
 * Hook for domain verification status
 */
export const useDomainVerification = (providerId: string, domain: string) => {
  return useQuery({
    queryKey: ['domain-verification', providerId, domain],
    queryFn: () => DomainValidationService.checkDomainVerification({ providerId, domain }),
    enabled: Boolean(providerId && domain),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2
  });
};

/**
 * Hook for connection health monitoring
 */
export const useConnectionHealth = (providerId: string, configurationId: string) => {
  return useQuery({
    queryKey: ['connection-health', providerId, configurationId],
    queryFn: () => ConnectionHealthService.checkConnectionHealth({ providerId, configurationId }),
    enabled: Boolean(providerId && configurationId),
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: 1
  });
};

/**
 * Hook for multiple provider health status
 */
export const useMultipleProviderHealth = (configurationIds: string[]) => {
  return useQuery({
    queryKey: ['multiple-provider-health', configurationIds],
    queryFn: () => ConnectionHealthService.getMultipleProviderStatus(configurationIds),
    enabled: configurationIds.length > 0,
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
    staleTime: 5 * 60 * 1000 // 5 minutes
  });
};

/**
 * Hook for provider-specific error handling
 */
export const useProviderErrorHandler = () => {
  const getErrorMessage = useCallback((providerId: string, errorCode: string) => {
    return ProviderErrorService.getErrorMessage(providerId, errorCode);
  }, []);

  const getTroubleshootingSuggestions = useCallback((providerId: string, errorCode: string) => {
    return ProviderErrorService.getTroubleshootingSuggestions(providerId, errorCode);
  }, []);

  return {
    getErrorMessage,
    getTroubleshootingSuggestions
  };
};

/**
 * Hook for comprehensive provider validation
 */
export const useProviderValidation = (
  providerId: string,
  credentials: Record<string, string>
) => {
  const [validationStatus, setValidationStatus] = useState<{
    apiKey?: ValidationResult | null;
    domain?: ValidationResult | null;
    connection?: ProviderStatus | null;
  }>({});

  // API Key validation
  const apiKeyValidation = useApiKeyValidation({
    providerId,
    apiKey: credentials.apiKey || credentials.password || '',
    additionalData: credentials,
    enabled: Boolean(credentials.apiKey || credentials.password)
  });

  // Domain validation (for providers that require it)
  const domainValidation = useDomainVerification(
    providerId,
    credentials.domain || ''
  );

  // Update validation status
  useEffect(() => {
    setValidationStatus(prev => ({
      ...prev,
      apiKey: apiKeyValidation.validationResult
    }));
  }, [apiKeyValidation.validationResult]);

  useEffect(() => {
    if (domainValidation.data) {
      setValidationStatus(prev => ({
        ...prev,
        domain: domainValidation.data
      }));
    }
  }, [domainValidation.data]);

  const isValid = () => {
    const { apiKey, domain } = validationStatus;
    
    // Check API key validation
    if (apiKey && !apiKey.isValid) return false;
    
    // Check domain validation (if required)
    if (providerId === 'mailgun' && domain && !domain.isValid) return false;
    
    return true;
  };

  const isValidating = () => {
    return apiKeyValidation.isValidating || domainValidation.isLoading;
  };

  const getValidationErrors = () => {
    const errors: string[] = [];
    
    if (validationStatus.apiKey && !validationStatus.apiKey.isValid) {
      errors.push(validationStatus.apiKey.message);
    }
    
    if (validationStatus.domain && !validationStatus.domain.isValid) {
      errors.push(validationStatus.domain.message);
    }
    
    return errors;
  };

  return {
    isValid: isValid(),
    isValidating: isValidating(),
    validationStatus,
    errors: getValidationErrors(),
    apiKeyValidation,
    domainValidation
  };
};
