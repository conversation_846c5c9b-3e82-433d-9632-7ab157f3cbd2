import { ReactNode } from 'react';
import { FieldValues, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';

/**
 * Trạng thái của một bước trong form wizard
 */
export enum StepStatus {
  /**
   * Bước chưa được truy cập
   */
  PENDING = 'pending',

  /**
   * Bước hiện tại
   */
  ACTIVE = 'active',

  /**
   * Bước đã hoàn thành và hợp lệ
   */
  COMPLETED = 'completed',

  /**
   * Bước có lỗi
   */
  ERROR = 'error',
}

/**
 * Thông tin về một bước trong form wizard
 */
export interface FormStep<TFormValues extends FieldValues = FieldValues> {
  /**
   * ID của bước
   */
  id: string;

  /**
   * Tiêu đề của bước
   */
  title: string;

  /**
   * Mô tả của bước
   */
  description?: string;

  /**
   * Schema validation cho bước
   */
  validationSchema?: z.ZodType<unknown>;

  /**
   * Nội dung của bước
   */
  content: ReactNode | ((formMethods: UseFormReturn<TFormValues>) => ReactNode);

  /**
   * Điều kiện để hiển thị bước
   */
  condition?: (data: Partial<TFormValues>) => boolean;

  /**
   * Callback khi bước được kích hoạt
   */
  onActivate?: (data: Partial<TFormValues>) => void;

  /**
   * Callback khi bước được hoàn thành
   */
  onComplete?: (data: Partial<TFormValues>) => void;

  /**
   * Callback khi bước có lỗi
   */
  onError?: (errors: Record<string, unknown>) => void;
}

/**
 * Props cho FormWizard component
 */
export interface FormWizardProps<TFormValues extends FieldValues = FieldValues> {
  /**
   * Mảng các bước
   */
  steps: FormStep<TFormValues>[];

  /**
   * Giá trị ban đầu cho form
   */
  initialValues?: Partial<TFormValues>;

  /**
   * Callback khi submit form
   */
  onSubmit: (data: TFormValues) => void | Promise<void>;

  /**
   * Callback khi chuyển bước
   */
  onStepChange?: (
    currentStep: FormStep<TFormValues>,
    nextStep: FormStep<TFormValues>,
    data: Partial<TFormValues>
  ) => void | Promise<void>;

  /**
   * Có validate khi chuyển bước không
   */
  validateOnStepChange?: boolean;

  /**
   * Có cho phép quay lại bước trước không
   */
  allowBackNavigation?: boolean;

  /**
   * Có cho phép nhảy đến bước bất kỳ không
   */
  allowJumpNavigation?: boolean;

  /**
   * Có lưu trữ dữ liệu giữa các bước không
   */
  persistData?: boolean;

  /**
   * Có hiển thị indicator không
   */
  showStepIndicator?: boolean;

  /**
   * Có hiển thị navigation không
   */
  showNavigation?: boolean;

  /**
   * Hướng của indicator
   */
  indicatorOrientation?: 'horizontal' | 'vertical';

  /**
   * Label cho nút next
   */
  nextLabel?: string;

  /**
   * Label cho nút previous
   */
  previousLabel?: string;

  /**
   * Label cho nút submit
   */
  submitLabel?: string;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Class cho indicator
   */
  indicatorClassName?: string;

  /**
   * Class cho navigation
   */
  navigationClassName?: string;

  /**
   * Class cho content
   */
  contentClassName?: string;

  /**
   * Children
   */
  children?: ReactNode | ((context: FormWizardContextType<TFormValues>) => ReactNode);
}

/**
 * Context cho FormWizard
 */
export interface FormWizardContextType<TFormValues extends FieldValues = FieldValues> {
  /**
   * Mảng các bước
   */
  steps: FormStep<TFormValues>[];

  /**
   * Bước hiện tại
   */
  currentStep: FormStep<TFormValues>;

  /**
   * Index của bước hiện tại
   */
  currentStepIndex: number;

  /**
   * Trạng thái của các bước
   */
  stepStatus: Record<string, StepStatus>;

  /**
   * Chuyển đến bước tiếp theo
   */
  goToNextStep: () => void;

  /**
   * Chuyển đến bước trước
   */
  goToPreviousStep: () => void;

  /**
   * Chuyển đến bước cụ thể
   */
  goToStep: (stepId: string) => void;

  /**
   * Kiểm tra xem có phải bước đầu tiên không
   */
  isFirstStep: boolean;

  /**
   * Kiểm tra xem có phải bước cuối cùng không
   */
  isLastStep: boolean;

  /**
   * Form methods
   */
  formMethods: UseFormReturn<TFormValues>;

  /**
   * Submit form
   */
  submitForm: () => void;
}
