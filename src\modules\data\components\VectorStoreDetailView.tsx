import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Table,
  ConfirmDeleteModal,
  IconCard,
} from '@/shared/components/common';

import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import ActiveFilters, { FilterTag } from '@/modules/components/filters/ActiveFilters';

import { NotificationUtil } from '@/shared/utils/notification';

import {
  useVectorStoreDetail,
  useRemoveFileFromVectorStore,
  useRemoveFilesFromVectorStore,
} from '@/modules/data/knowledge-files/hooks/useVectorStoreQuery';
import { useKnowledgeFiles } from '@/modules/data/knowledge-files/hooks/useKnowledgeFileQuery';
import {
  FileResponseDto,
  QueryFileDto,
} from '@/modules/data/knowledge-files/types/knowledge-files.types';

interface VectorStoreDetailViewProps {
  vectorStoreId: string;
  onClose: () => void;
  onAssignFiles: (vectorStoreId: string) => void;
}

/**
 * Component hiển thị chi tiết Vector Store và danh sách file đã gán
 */
const VectorStoreDetailView: React.FC<VectorStoreDetailViewProps> = ({
  vectorStoreId,
  onClose,
  onAssignFiles,
}) => {
  const { t } = useTranslation();
  const [selectedFileIds, setSelectedFileIds] = useState<string[]>([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<FileResponseDto | null>(null);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // Tạo query params cho API
  const queryParams = useMemo<QueryFileDto>(() => {
    return {
      page: currentPage,
      limit: itemsPerPage,
      search: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection,
      vectorStoreId: vectorStoreId, // Thêm vectorStoreId để lọc các file đã được gán
    };
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, vectorStoreId]);

  // Lấy thông tin chi tiết Vector Store
  const {
    data: vectorStoreDetail,
    isLoading: isLoadingVectorStore,
    refetch: refetchVectorStore,
  } = useVectorStoreDetail(vectorStoreId);

  // Lấy danh sách file tri thức
  const {
    data: knowledgeFilesData,
    isLoading: isLoadingFiles,
    refetch: refetchFiles,
  } = useKnowledgeFiles(queryParams);

  // Hook để xóa file khỏi Vector Store
  const { removeFile } = useRemoveFileFromVectorStore(vectorStoreId);
  const { removeFiles } = useRemoveFilesFromVectorStore(vectorStoreId);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setFileToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!fileToDelete) return;

    try {
      await removeFile(fileToDelete.id);
      refetchVectorStore();
      refetchFiles();

      NotificationUtil.success({
        message: t('data:vectorStore.removeFileSuccess', 'Xóa file khỏi Vector Store thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error removing file from Vector Store:', error);
      NotificationUtil.error({
        message: t('data:vectorStore.removeFileError', 'Lỗi khi xóa file khỏi Vector Store'),
        duration: 3000,
      });
    } finally {
      setShowDeleteConfirm(false);
      setFileToDelete(null);
    }
  }, [fileToDelete, removeFile, refetchVectorStore, refetchFiles, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedFileIds.length === 0) return;

    try {
      await removeFiles(selectedFileIds);
      refetchVectorStore();
      refetchFiles();
      setSelectedFileIds([]);

      NotificationUtil.success({
        message: t('data:vectorStore.removeFilesSuccess', 'Xóa các file đã chọn thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error removing multiple files from Vector Store:', error);
      NotificationUtil.error({
        message: t('data:vectorStore.removeFilesError', 'Lỗi khi xóa các file đã chọn'),
        duration: 3000,
      });
    } finally {
      setShowBulkDeleteConfirm(false);
    }
  }, [selectedFileIds, removeFiles, refetchVectorStore, refetchFiles, t]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);



  // Tạo các hàm xử lý riêng để đảm bảo filter được tắt đúng cách
  const handleClearSearch = useCallback(() => {
    setSearchTerm('');
    // Cập nhật lại query params và gọi lại API
    setCurrentPage(1);
  }, []);

  const handleClearSort = useCallback(() => {
    setSortBy('createdAt');
    setSortDirection(SortDirection.DESC);
    // Cập nhật lại query params và gọi lại API
    setCurrentPage(1);
  }, []);

  const handleClearAll = useCallback(() => {
    handleClearSearch();
    handleClearSort();
  }, [handleClearSearch, handleClearSort]);

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Lọc danh sách file đã được gán vào Vector Store
  const assignedFileIds = useMemo<string[]>(() => {
    // Lấy danh sách ID của các file đã được gán vào Vector Store từ API
    // Trong trường hợp này, chúng ta đã lọc trực tiếp từ API bằng tham số vectorStoreId
    // Nên tất cả các file trong knowledgeFilesData đều đã được gán vào Vector Store
    return knowledgeFilesData?.items.map(file => file.id) || [];
  }, [knowledgeFilesData]);

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => [
    {
      key: 'name',
      title: t('data:knowledgeFiles.table.name', 'Tên file'),
      dataIndex: 'name',
      width: '30%',
    },
    {
      key: 'extension',
      title: t('data:knowledgeFiles.table.extension', 'Định dạng'),
      dataIndex: 'extension',
      width: '15%',
    },
    {
      key: 'storage',
      title: t('data:knowledgeFiles.table.size', 'Kích thước'),
      dataIndex: 'storage',
      width: '20%',
      render: (value: unknown) => {
        // Chuyển đổi byte sang KB, MB, GB
        const size = Number(value);
        if (isNaN(size)) return 'N/A';

        if (size < 1024) {
          return `${size} B`;
        } else if (size < 1024 * 1024) {
          return `${(size / 1024).toFixed(2)} KB`;
        } else if (size < 1024 * 1024 * 1024) {
          return `${(size / (1024 * 1024)).toFixed(2)} MB`;
        } else {
          return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
        }
      },
    },
    {
      key: 'createdAt',
      title: t('data:common.createdAt', 'Ngày tạo'),
      dataIndex: 'createdAt',
      width: '20%',
      render: (value: unknown) => {
        const timestamp = Number(value);
        return isNaN(timestamp) ? 'N/A' : new Date(timestamp).toLocaleString();
      },
    }
  ], [t]);

  // Tổng hợp trạng thái loading
  const isLoading = isLoadingVectorStore || isLoadingFiles;

  // Tạo danh sách các bộ lọc đang áp dụng
  const activeFilters = useMemo<FilterTag[]>(() => {
    const filters: FilterTag[] = [];

    // Thêm bộ lọc Vector Store
    if (vectorStoreDetail) {
      filters.push({
        id: 'vectorStore',
        label: t('data:vectorStore.filter.vectorStore', 'Vector Store'),
        value: vectorStoreDetail.storeName,
        onRemove: () => {
          // Không cho phép xóa bộ lọc Vector Store trong trường hợp này
        },
      });
    }

    // Thêm bộ lọc sắp xếp
    if (sortBy && sortDirection) {
      const sortLabel = sortBy === 'createdAt' ? t('common:createdAt', 'Ngày tạo') : sortBy;
      const directionLabel = sortDirection === SortDirection.ASC
        ? t('common:ascending', 'Tăng dần')
        : t('common:descending', 'Giảm dần');

      filters.push({
        id: 'sortBy',
        label: t('common:sortBy', 'Sắp xếp theo'),
        value: `${sortLabel} (${directionLabel})`,
        onRemove: handleClearSort,
      });
    }

    return filters;
  }, [vectorStoreDetail, sortBy, sortDirection, handleClearSort, t]);

  return (
    <Card className="p-6">
      <div className="space-y-4">
        <Typography variant="h6" className="mb-4">
          {t('data:vectorStore.detail', 'Chi tiết Vector Store')}
        </Typography>

        {isLoading ? (
          <div className="py-4 text-center">
            <Typography>{t('common:loading', 'Đang tải...')}</Typography>
          </div>
        ) : vectorStoreDetail ? (
          <div>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <Typography variant="subtitle2">{t('data:vectorStore.table.name', 'Tên')}</Typography>
                <Typography>{vectorStoreDetail.storeName}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2">{t('data:vectorStore.table.files', 'Số file')}</Typography>
                <Typography>{vectorStoreDetail.files}</Typography>
              </div>
              <div>
                <Typography variant="subtitle2">{t('data:vectorStore.table.size', 'Dung lượng')}</Typography>
                <Typography>
                  {(() => {
                    const size = Number(vectorStoreDetail.size);
                    if (size < 1024) return `${size} B`;
                    if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
                    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
                  })()}
                </Typography>
              </div>
              <div>
                <Typography variant="subtitle2">{t('data:vectorStore.table.agents', 'Số agents')}</Typography>
                <Typography>{vectorStoreDetail.agents}</Typography>
              </div>
            </div>

            <div className="mb-4">
              <MenuIconBar
                onSearch={handleSearch}
                items={[]}
                additionalIcons={[
                  {
                    icon: 'plus',
                    tooltip: t('data:vectorStore.assignFiles', 'Gán file'),
                    variant: 'primary',
                    onClick: () => onAssignFiles(vectorStoreId),
                  },
                  {
                    icon: 'trash',
                    tooltip: t('data:vectorStore.removeFiles', 'Xóa file đã chọn'),
                    variant: 'primary',
                    onClick: () => {
                      if (selectedFileIds.length > 0) {
                        setShowBulkDeleteConfirm(true);
                      } else {
                        NotificationUtil.info({
                          message: t('data:vectorStore.selectFilesToRemove', 'Vui lòng chọn ít nhất một file để xóa'),
                          duration: 3000,
                        });
                      }
                    },
                    condition: selectedFileIds.length > 0,
                  },
                ]}
                onColumnVisibilityChange={handleColumnVisibilityChange}
                columns={visibleColumns}
                showDateFilter={false}
                showColumnFilter={true}
              />

              {/* Hiển thị các bộ lọc đang áp dụng */}
              <ActiveFilters
                searchTerm={searchTerm}
                onClearSearch={handleClearSearch}
                customTags={activeFilters}
                onClearAll={handleClearAll}
              />
            </div>

            <div>
              <Table
                columns={columns}
                data={knowledgeFilesData?.items.map(file => ({
                  ...file,
                  isAssigned: assignedFileIds.includes(file.id)
                })) || []}
                rowKey="id"
                loading={isLoading}
                rowSelection={{
                  selectedRowKeys: selectedFileIds,
                  onChange: (keys: React.Key[]) => setSelectedFileIds(keys as string[]),
                }}
                pagination={{
                  current: currentPage,
                  pageSize: itemsPerPage,
                  total: knowledgeFilesData?.meta.totalItems || 0,
                  onChange: handlePageChange,
                  showSizeChanger: true,
                  pageSizeOptions: [10, 20, 50, 100],
                  showFirstLastButtons: true,
                  showPageInfo: true,
                }}
              />
            </div>
          </div>
        ) : (
          <div className="py-4 text-center">
            <Typography>{t('data:vectorStore.notFound', 'Không tìm thấy Vector Store')}</Typography>
          </div>
        )}

        <div className="flex justify-end">
          <IconCard
            icon="x"
            size="md"
            variant="secondary"
            title={t('common:close', 'Đóng')}
            onClick={onClose}
          />
        </div>
      </div>

      {/* Modal xác nhận xóa file */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('data:vectorStore.removeFileTitle', 'Xóa file khỏi Vector Store')}
        message={t('data:vectorStore.removeFileMessage', 'Bạn có chắc chắn muốn xóa file này khỏi Vector Store?')}
        itemName={fileToDelete?.name || ''}
      />

      {/* Modal xác nhận xóa nhiều file */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('data:vectorStore.removeFilesTitle', 'Xóa các file đã chọn')}
        message={t(
          'data:vectorStore.removeFilesMessage',
          'Bạn có chắc chắn muốn xóa {{count}} file đã chọn khỏi Vector Store?',
          { count: selectedFileIds.length }
        )}
      />
    </Card>
  );
};

export default VectorStoreDetailView;
