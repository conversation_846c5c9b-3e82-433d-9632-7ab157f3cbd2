import React, { ReactNode } from 'react';
import { Breakpoint, MEDIA_QUERIES } from '@/shared/constants/breakpoints';
import { useMediaQuery } from '@/shared/hooks/common';

export interface HiddenProps {
  /**
   * Nội dung sẽ được ẩn/hiện
   */
  children: ReactNode;

  /**
   * Ẩn trên các breakpoint này
   */
  hideOn?: Breakpoint[];

  /**
   * Hiện trên các breakpoint này
   */
  showOn?: Breakpoint[];

  /**
   * Ẩn trên mobile
   */
  hideOnMobile?: boolean;

  /**
   * Ẩn trên tablet
   */
  hideOnTablet?: boolean;

  /**
   * Ẩn trên desktop
   */
  hideOnDesktop?: boolean;

  /**
   * Ẩn trên portrait
   */
  hideOnPortrait?: boolean;

  /**
   * Ẩn trên landscape
   */
  hideOnLandscape?: boolean;

  /**
   * Sử dụng CSS để ẩn (display: none) thay vì không render
   */
  useCSS?: boolean;
}

/**
 * Hidden component ẩn/hiện nội dung dựa trên breakpoint
 *
 * @example
 * // Ẩn trên mobile
 * <Hidden hideOnMobile>
 *   <p>This content is hidden on mobile</p>
 * </Hidden>
 *
 * @example
 * // Chỉ hiện trên desktop
 * <Hidden showOn={['lg', 'xl', '2xl']}>
 *   <p>This content is only visible on desktop</p>
 * </Hidden>
 */
const Hidden: React.FC<HiddenProps> = ({
  children,
  hideOn = [],
  showOn = [],
  hideOnMobile = false,
  hideOnTablet = false,
  hideOnDesktop = false,
  hideOnPortrait = false,
  hideOnLandscape = false,
  useCSS = false,
}) => {
  // Convert hideOn/showOn to media queries
  const hideQueries: string[] = hideOn.map(breakpoint => MEDIA_QUERIES[breakpoint]);

  // Add device specific queries
  if (hideOnMobile) hideQueries.push('(max-width: 767px)');
  if (hideOnTablet) hideQueries.push('(min-width: 768px) and (max-width: 1023px)');
  if (hideOnDesktop) hideQueries.push('(min-width: 1024px)');
  if (hideOnPortrait) hideQueries.push('(orientation: portrait)');
  if (hideOnLandscape) hideQueries.push('(orientation: landscape)');

  // Tạo các hooks riêng biệt cho từng query có thể có
  // Tạo một số lượng cố định các hooks và sử dụng chúng khi cần
  const mediaQuery1 = useMediaQuery(hideQueries[0] || '');
  const mediaQuery2 = useMediaQuery(hideQueries[1] || '');
  const mediaQuery3 = useMediaQuery(hideQueries[2] || '');
  const mediaQuery4 = useMediaQuery(hideQueries[3] || '');
  const mediaQuery5 = useMediaQuery(hideQueries[4] || '');

  // Tạo mảng kết quả từ các hooks đã gọi
  const hideQueryResults = [
    hideQueries[0] ? mediaQuery1 : false,
    hideQueries[1] ? mediaQuery2 : false,
    hideQueries[2] ? mediaQuery3 : false,
    hideQueries[3] ? mediaQuery4 : false,
    hideQueries[4] ? mediaQuery5 : false,
  ];
  const shouldHide = hideQueryResults.some(result => result);

  // If showOn is provided, check if any show query matches
  const showQueries: string[] = showOn.map(breakpoint => MEDIA_QUERIES[breakpoint]);

  // Tương tự cho showQueries
  const showMediaQuery1 = useMediaQuery(showQueries[0] || '');
  const showMediaQuery2 = useMediaQuery(showQueries[1] || '');
  const showMediaQuery3 = useMediaQuery(showQueries[2] || '');

  // Tạo mảng kết quả từ các hooks đã gọi
  const showQueryResults = [
    showQueries[0] ? showMediaQuery1 : false,
    showQueries[1] ? showMediaQuery2 : false,
    showQueries[2] ? showMediaQuery3 : false,
  ];
  const shouldShow = showQueries.length === 0 || showQueryResults.some(result => result);

  // Determine if content should be visible
  const isVisible = !shouldHide && shouldShow;

  if (useCSS) {
    return <div className={isVisible ? '' : 'hidden'}>{children}</div>;
  }

  // If not visible, don't render anything
  return isVisible ? <>{children}</> : null;
};

export default Hidden;
