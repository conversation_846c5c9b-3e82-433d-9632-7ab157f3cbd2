import { ExternalAgentQueryDto, ProtocolType, AuthenticationType, ExternalAgentStatus } from '../types';

export const DEFAULT_QUERY_PARAMS: ExternalAgentQueryDto = {
  page: 1,
  limit: 10,
  sortBy: 'updatedAt',
  sortOrder: 'desc',
};

export const PROTOCOL_LABELS: Record<ProtocolType, string> = {
  [ProtocolType.MCP]: 'Model Context Protocol',
  [ProtocolType.GOOGLE_AGENT]: 'Google Agent Communication',
  [ProtocolType.REST_API]: 'REST API',
  [ProtocolType.WEBSOCKET]: 'WebSocket',
  [ProtocolType.GRPC]: 'gRPC',
  [ProtocolType.CUSTOM]: 'Custom Protocol',
};

export const AUTH_TYPE_LABELS: Record<AuthenticationType, string> = {
  [AuthenticationType.NONE]: 'No Authentication',
  [AuthenticationType.API_KEY]: 'API Key',
  [AuthenticationType.BEARER_TOKEN]: 'Bearer Token',
  [AuthenticationType.OAUTH2]: 'OAuth 2.0',
  [AuthenticationType.BASIC_AUTH]: 'Basic Authentication',
  [AuthenticationType.CUSTOM]: 'Custom Authentication',
};

export const STATUS_LABELS: Record<ExternalAgentStatus, string> = {
  [ExternalAgentStatus.ACTIVE]: 'Active',
  [ExternalAgentStatus.INACTIVE]: 'Inactive',
  [ExternalAgentStatus.CONNECTING]: 'Connecting',
  [ExternalAgentStatus.ERROR]: 'Error',
  [ExternalAgentStatus.MAINTENANCE]: 'Maintenance',
};

export const STATUS_COLORS: Record<ExternalAgentStatus, string> = {
  [ExternalAgentStatus.ACTIVE]: 'success',
  [ExternalAgentStatus.INACTIVE]: 'secondary',
  [ExternalAgentStatus.CONNECTING]: 'warning',
  [ExternalAgentStatus.ERROR]: 'destructive',
  [ExternalAgentStatus.MAINTENANCE]: 'outline',
};

export const DEFAULT_TIMEOUT = 30000; // 30 seconds
export const DEFAULT_RETRY_ATTEMPTS = 3;
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;
