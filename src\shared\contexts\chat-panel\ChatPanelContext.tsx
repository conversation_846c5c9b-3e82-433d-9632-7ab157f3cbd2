import React, { createContext, useState, ReactNode } from 'react';

export interface ChatPanelContextType {
  isChatPanelOpen: boolean;
  setIsChatPanelOpen: (isOpen: boolean) => void;
}

const ChatPanelContext = createContext<ChatPanelContextType | undefined>(undefined);

interface ChatPanelProviderProps {
  children: ReactNode;
}

export const ChatPanelProvider: React.FC<ChatPanelProviderProps> = ({ children }) => {
  const [isChatPanelOpen, setIsChatPanelOpen] = useState(false);

  return (
    <ChatPanelContext.Provider value={{ isChatPanelOpen, setIsChatPanelOpen }}>
      {children}
    </ChatPanelContext.Provider>
  );
};

export default ChatPanelContext;
