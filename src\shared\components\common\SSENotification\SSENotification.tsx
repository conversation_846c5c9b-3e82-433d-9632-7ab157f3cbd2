/**
 * Component hiển thị notifications từ SSE events
 */
import React, { useEffect, useState, useCallback } from 'react';
import Alert from '@/shared/components/common/Alert';
import Button from '@/shared/components/common/Button';
import Icon from '@/shared/components/common/Icon';
import { SSEEvent } from '@/shared/types/sse.types';
import { cn } from '@/shared/utils';

/**
 * Loại notification
 */
export type NotificationType = 'info' | 'success' | 'warning' | 'error';

/**
 * SSE Notification item
 */
export interface SSENotificationItem {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  event: SSEEvent;
  timestamp: Date;
  autoHide?: boolean;
  hideAfter?: number; // ms
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary';
  }>;
}

/**
 * Props cho SSENotification component
 */
export interface SSENotificationProps {
  /**
   * Danh sách notifications
   */
  notifications: SSENotificationItem[];

  /**
   * Callback khi dismiss notification
   */
  onDismiss: (id: string) => void;

  /**
   * Callback khi clear all notifications
   */
  onClearAll?: () => void;

  /**
   * Vị trí hiển thị
   */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';

  /**
   * Số lượng notifications tối đa hiển thị
   */
  maxVisible?: number;

  /**
   * Có hiển thị timestamp không
   */
  showTimestamp?: boolean;

  /**
   * Có hiển thị nút clear all không
   */
  showClearAll?: boolean;

  /**
   * Custom className
   */
  className?: string;

  /**
   * Animation duration (ms)
   */
  animationDuration?: number;
}

/**
 * Component cho một notification item
 */
const NotificationItem: React.FC<{
  notification: SSENotificationItem;
  onDismiss: (id: string) => void;
  showTimestamp: boolean;
  animationDuration: number;
}> = ({ notification, onDismiss, showTimestamp, animationDuration }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);

  const handleDismiss = useCallback(() => {
    setIsRemoving(true);
    setTimeout(() => {
      onDismiss(notification.id);
    }, animationDuration);
  }, [notification.id, onDismiss, animationDuration]);

  // Auto hide effect
  useEffect(() => {
    if (notification.autoHide && notification.hideAfter) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, notification.hideAfter);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [notification.autoHide, notification.hideAfter, handleDismiss]);

  // Show animation
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('vi-VN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  return (
    <div
      className={cn(
        'transform transition-all duration-300 ease-in-out',
        isVisible && !isRemoving
          ? 'translate-x-0 opacity-100 scale-100'
          : 'translate-x-full opacity-0 scale-95',
        'mb-3'
      )}
      style={{ transitionDuration: `${animationDuration}ms` }}
    >
      <Alert
        type={notification.type}
        message={
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-sm mb-1">
                  {notification.title}
                </h4>
                {notification.message && (
                  <p className="text-sm opacity-90">
                    {notification.message}
                  </p>
                )}
                {showTimestamp && (
                  <p className="text-xs opacity-70 mt-1">
                    {formatTime(notification.timestamp)}
                  </p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="absolute top-2 right-2 p-1 h-6 w-6"
              >
                <Icon name="x" size="xs" />
              </Button>
            </div>

            {notification.actions && notification.actions.length > 0 && (
              <div className="flex gap-2 mt-3">
                {notification.actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant || 'secondary'}
                    size="sm"
                    onClick={action.onClick}
                    className="text-xs"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
        }
        className="relative pr-12 shadow-lg"
      />
    </div>
  );
};

/**
 * SSENotification component
 */
export const SSENotification: React.FC<SSENotificationProps> = ({
  notifications,
  onDismiss,
  onClearAll,
  position = 'top-right',
  maxVisible = 5,
  showTimestamp = true,
  showClearAll = true,
  className,
  animationDuration = 300,
}) => {
  // Lấy notifications hiển thị (giới hạn số lượng)
  const visibleNotifications = notifications.slice(0, maxVisible);

  // Position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-center':
        return 'top-4 left-1/2 transform -translate-x-1/2';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        'fixed z-50 w-80 max-w-sm',
        getPositionClasses(),
        className
      )}
    >
      {/* Clear all button */}
      {showClearAll && notifications.length > 1 && onClearAll && (
        <div className="mb-3">
          <Button
            variant="secondary"
            size="sm"
            onClick={onClearAll}
            className="w-full text-xs"
          >
            <Icon name="x" size="xs" className="mr-1" />
            Xóa tất cả ({notifications.length})
          </Button>
        </div>
      )}

      {/* Notifications */}
      <div className="space-y-0">
        {visibleNotifications.map((notification) => (
          <NotificationItem
            key={notification.id}
            notification={notification}
            onDismiss={onDismiss}
            showTimestamp={showTimestamp}
            animationDuration={animationDuration}
          />
        ))}
      </div>

      {/* More notifications indicator */}
      {notifications.length > maxVisible && (
        <div className="text-center">
          <div className="text-xs text-gray-500 bg-white rounded-md px-2 py-1 shadow-sm border">
            +{notifications.length - maxVisible} thông báo khác
          </div>
        </div>
      )}
    </div>
  );
};

export default SSENotification;
