import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Form,
  FormItem,
  Input,
  IconCard,
  Card,
  Typography,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useUpdateInventoryQuantity } from '../../hooks/useInventoryQuery';
import { ProductInventoryResponseDto, UpdateInventoryQuantityDto } from '../../types/inventory.types';

interface UpdateInventoryQuantityFormProps {
  inventoryData: ProductInventoryResponseDto;
  onSubmit: () => void;
  onCancel: () => void;
}

interface UpdateInventoryFormValues {
  availableQuantity: number;
  reservedQuantity: number;
  defectiveQuantity: number;
  sku?: string;
  barcode?: string;
}

/**
 * Form cập nhật số lượng tồn kho
 */
const UpdateInventoryQuantityForm: React.FC<UpdateInventoryQuantityFormProps> = ({
  inventoryData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const formRef = useRef<FormRef<UpdateInventoryFormValues> | null>(null);
  
  const { mutateAsync: updateInventoryQuantity, isPending: isUpdating } = useUpdateInventoryQuantity();

  // Schema validation
  const updateInventorySchema = z.object({
    availableQuantity: z.coerce.number().min(0, t('business:inventory.validation.availableQuantityMin', 'Số lượng có sẵn phải >= 0')),
    reservedQuantity: z.coerce.number().min(0, t('business:inventory.validation.reservedQuantityMin', 'Số lượng đặt trước phải >= 0')),
    defectiveQuantity: z.coerce.number().min(0, t('business:inventory.validation.defectiveQuantityMin', 'Số lượng hỏng phải >= 0')),
    sku: z.string().optional(),
    barcode: z.string().optional(),
  });

  // Giá trị mặc định
  const defaultValues: UpdateInventoryFormValues = {
    availableQuantity: inventoryData.availableQuantity || 0,
    reservedQuantity: inventoryData.reservedQuantity || 0,
    defectiveQuantity: inventoryData.defectiveQuantity || 0,
    sku: inventoryData.sku || '',
    barcode: inventoryData.barcode || '',
  };

  // Xử lý submit
  const handleSubmit = async (values: UpdateInventoryFormValues) => {
    try {
      // Prepare data object with proper typing
      const updateData: Partial<UpdateInventoryQuantityDto> & {
        availableQuantity: number;
        reservedQuantity: number;
        defectiveQuantity: number;
      } = {
        availableQuantity: values.availableQuantity,
        reservedQuantity: values.reservedQuantity,
        defectiveQuantity: values.defectiveQuantity,
      };

      // Only add optional fields if they have values
      if (inventoryData.warehouseId) {
        updateData.warehouseId = inventoryData.warehouseId;
      }
      if (values.sku && values.sku.trim()) {
        updateData.sku = values.sku.trim();
      }
      if (values.barcode && values.barcode.trim()) {
        updateData.barcode = values.barcode.trim();
      }

      await updateInventoryQuantity({
        id: inventoryData.inventoryId,
        data: updateData as UpdateInventoryQuantityDto,
      });
      onSubmit();
    } catch (error) {
      console.error('Error updating inventory quantity:', error);
    }
  };

  // Wrapper function for Form component
  const handleFormSubmit = async (data: Record<string, unknown>) => {
    const typedData = data as unknown as UpdateInventoryFormValues;
    await handleSubmit(typedData);
  };

  return (
    <Card title={t('business:inventory.updateQuantityTitle', 'Cập nhật số lượng tồn kho')}>
      <div className="p-4">
        {/* Product Info */}
        <div className="mb-6">
          <Typography variant="h6" className="mb-2">
            {t('business:inventory.productInfo', 'Thông tin sản phẩm')}
          </Typography>
          <div className="flex items-center space-x-3">
            {inventoryData.productImages && inventoryData.productImages.length > 0 && (
              <img
                src={inventoryData.productImages[0]?.url || '/placeholder-image.png'}
                alt={inventoryData.productName}
                className="w-12 h-12 rounded object-cover"
              />
            )}
            <div>
              <Typography variant="body1" className="font-medium">
                {inventoryData.productName}
              </Typography>
              {inventoryData.sku && (
                <Typography variant="caption" className="text-gray-500">
                  SKU: {inventoryData.sku}
                </Typography>
              )}
            </div>
          </div>
        </div>

        <Form
          ref={formRef as React.RefObject<FormRef<Record<string, unknown>>>}
          schema={updateInventorySchema}
          onSubmit={handleFormSubmit}
          defaultValues={defaultValues}
          className="space-y-4"
        >
          {/* Quantity Fields */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FormItem
              name="availableQuantity"
              label={t('business:inventory.availableQuantity', 'Số lượng có sẵn')}
              required
            >
              <Input
                type="number"
                min="0"
                fullWidth
                placeholder="0"
              />
            </FormItem>

            <FormItem
              name="reservedQuantity"
              label={t('business:inventory.reservedQuantity', 'Số lượng đặt trước')}
              required
            >
              <Input
                type="number"
                min="0"
                fullWidth
                placeholder="0"
              />
            </FormItem>

            <FormItem
              name="defectiveQuantity"
              label={t('business:inventory.defectiveQuantity', 'Số lượng hỏng')}
              required
            >
              <Input
                type="number"
                min="0"
                fullWidth
                placeholder="0"
              />
            </FormItem>
          </div>

          {/* SKU and Barcode */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem
              name="sku"
              label={t('business:inventory.sku', 'Mã SKU')}
            >
              <Input
                fullWidth
                placeholder={t('business:inventory.skuPlaceholder', 'Nhập mã SKU')}
              />
            </FormItem>

            <FormItem
              name="barcode"
              label={t('business:inventory.barcode', 'Mã vạch')}
            >
              <Input
                fullWidth
                placeholder={t('business:inventory.barcodePlaceholder', 'Nhập mã vạch')}
              />
            </FormItem>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('common:cancel')}
              onClick={onCancel}
              disabled={isUpdating}
            />
            <IconCard
              icon="save"
              variant="primary"
              size="md"
              title={t('common:save')}
              onClick={() => {
                formRef.current?.submit();
              }}
              disabled={isUpdating}
              isLoading={isUpdating}
            />
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default UpdateInventoryQuantityForm;
