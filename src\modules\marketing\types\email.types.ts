/**
 * Types cho Email Marketing
 */

/**
 * Trạng thái template email
 */
export enum EmailTemplateStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED'
}

/**
 * Loại template email
 */
export enum EmailTemplateType {
  NEWSLETTER = 'NEWSLETTER',
  PROMOTIONAL = 'PROMOTIONAL',
  TRANSACTIONAL = 'TRANSACTIONAL',
  WELCOME = 'WELCOME',
  ABANDONED_CART = 'ABANDONED_CART',
  FOLLOW_UP = 'FOLLOW_UP'
}

/**
 * Trạng thái chiến dịch email
 */
export enum EmailCampaignStatus {
  DRAFT = 'DRAFT',
  SCHEDULED = 'SCHEDULED',
  SENDING = 'SENDING',
  SENT = 'SENT',
  PAUSED = 'PAUSED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED'
}

/**
 * Trạng thái email
 */
export enum EmailStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  OPENED = 'OPENED',
  CLICKED = 'CLICKED',
  BOUNCED = 'BOUNCED',
  COMPLAINED = 'COMPLAINED',
  UNSUBSCRIBED = 'UNSUBSCRIBED'
}

/**
 * DTO template email
 */
export interface EmailTemplateDto {
  id: string;
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  type: EmailTemplateType;
  status: EmailTemplateStatus;
  previewText?: string;
  thumbnailUrl?: string;
  tags: string[];
  variables: EmailVariable[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Biến trong template email
 */
export interface EmailVariable {
  name: string;
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
  defaultValue?: string;
  required: boolean;
  description?: string;
}

/**
 * DTO chiến dịch email
 */
export interface EmailCampaignDto {
  id: string;
  name?: string; // For backward compatibility
  title?: string; // API returns title
  description?: string; // API returns description
  subject: string;
  templateId: string;
  template?: EmailTemplateDto;
  status: EmailCampaignStatus;
  audienceIds?: string[];
  segmentIds?: string[];
  totalRecipients: number;
  sentCount: number;
  sentRate?: number; // API returns sentRate (percentage)
  deliveredCount?: number;
  openedCount?: number;
  clickedCount: number;
  clickRate?: number; // API returns clickRate (percentage)
  bouncedCount?: number;
  unsubscribedCount?: number;
  scheduledAt?: Date;
  sentAt?: Date;
  completedAt?: Date;
  createdAt: Date | string; // API returns string timestamp
  updatedAt: Date | string; // API returns string timestamp
}

/**
 * DTO email đã gửi
 */
export interface EmailDto {
  id: string;
  campaignId: string;
  recipientEmail: string;
  recipientName?: string;
  subject: string;
  htmlContent: string;
  status: EmailStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  openedAt?: Date;
  clickedAt?: Date;
  bouncedAt?: Date;
  errorMessage?: string;
  trackingId: string;
}

/**
 * DTO automation email
 */
export interface EmailAutomationDto {
  id: string;
  name: string;
  description?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  trigger: EmailTrigger;
  actions: EmailAction[];
  totalTriggered: number;
  totalCompleted: number;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Trigger cho automation
 */
export interface EmailTrigger {
  type: 'AUDIENCE_JOINED' | 'TAG_ADDED' | 'DATE_BASED' | 'BEHAVIOR' | 'API_CALL';
  conditions: Record<string, unknown>;
  delay?: number; // minutes
}

/**
 * Action trong automation
 */
export interface EmailAction {
  id: string;
  type: 'SEND_EMAIL' | 'ADD_TAG' | 'REMOVE_TAG' | 'WAIT' | 'CONDITION';
  templateId?: string;
  delay?: number; // minutes
  conditions?: Record<string, unknown>;
  nextActionId?: string;
}

/**
 * DTO analytics email
 */
export interface EmailAnalyticsDto {
  totalCampaigns: number;
  totalEmailsSent: number;
  totalEmailsDelivered: number;
  totalEmailsOpened: number;
  totalEmailsClicked: number;
  averageOpenRate: number;
  averageClickRate: number;
  averageBounceRate: number;
  recentCampaigns: EmailCampaignDto[];
  topPerformingTemplates: Array<{
    template: EmailTemplateDto;
    openRate: number;
    clickRate: number;
    totalSent: number;
  }>;
}

/**
 * Query parameters cho templates
 */
export interface EmailTemplateQueryDto {
  page?: number;
  limit?: number;
  search?: string   | undefined;
  type?: EmailTemplateType;
  status?: EmailTemplateStatus;
  tags?: string[];
  sortBy?: string | undefined ;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}

/**
 * Query parameters cho campaigns
 */
export interface EmailCampaignQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  status?: EmailCampaignStatus;
  templateId?: string;
  startDate?: Date;
  endDate?: Date;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * DTO tạo template email
 */
export interface CreateEmailTemplateDto {
  name: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  type: EmailTemplateType;
  previewText?: string;
  tags?: string[];
  variables?: EmailVariable[];
}

/**
 * DTO cập nhật template email
 */
export interface UpdateEmailTemplateDto {
  name?: string;
  subject?: string;
  htmlContent?: string;
  textContent?: string;
  type?: EmailTemplateType;
  status?: EmailTemplateStatus;
  previewText?: string;
  tags?: string[];
  variables?: EmailVariable[];
}

/**
 * DTO tạo chiến dịch email
 */
export interface CreateEmailCampaignDto {
  name: string;
  subject?: string; // Optional vì sẽ lấy từ template
  templateId: string;
  emailServerId?: string | undefined;
  audienceIds?: string[] | undefined;
  segmentIds?: string[] | undefined;
  scheduledAt?: Date | undefined;
  description?: string | undefined;
  /**
   * Cấu hình giá trị cho các biến template
   * @example { "companyName": "RedAI", "discountPercent": "50", "validUntil": "31/12/2024" }
   */
  templateVariables?: Record<string, string> | undefined;
}

/**
 * DTO gửi test email
 */
export interface SendTestEmailDto {
  templateId: string;
  recipientEmails: string[];
  variables?: Record<string, string>;
}

/**
 * DTO cho thống kê tổng quan email campaign
 */
export interface EmailCampaignOverviewDto {
  /**
   * Tổng số chiến dịch
   */
  totalCampaigns: number;

  /**
   * Số chiến dịch đang gửi
   */
  sendingCampaigns: number;

  /**
   * Số chiến dịch đã gửi
   */
  sentCampaigns: number;

  /**
   * Số chiến dịch đã lên lịch
   */
  scheduledCampaigns: number;

  /**
   * Thời gian cập nhật thống kê (Unix timestamp)
   */
  updatedAt: number;
}

/**
 * DTO response cho API overview email campaign
 */
export interface EmailCampaignOverviewResponseDto {
  overview: EmailCampaignOverviewDto;
}


/**
 * DTO tạo automation email
 */
export interface CreateEmailAutomationDto {
  name: string;
  description?: string;
  trigger: EmailTrigger;
  actions: EmailAction[];
}

/**
 * Query parameters cho analytics
 */
export interface EmailAnalyticsQueryDto {
  startDate?: Date;
  endDate?: Date;
  campaignIds?: string[];
  templateIds?: string[];
  period?: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR';
}

/**
 * Response cho email analytics
 */
export interface EmailMetricsResponse {
  period: 'TODAY' | 'WEEK' | 'MONTH' | 'YEAR';
  metrics: {
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
    bounced: number;
    unsubscribed: number;
    openRate: number;
    clickRate: number;
    bounceRate: number;
    unsubscribeRate: number;
  };
  chartData: Array<{
    date: string;
    sent: number;
    delivered: number;
    opened: number;
    clicked: number;
  }>;
}
