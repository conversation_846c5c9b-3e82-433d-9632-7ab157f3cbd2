# R-Point Table Improvements - Profile Page

## Tóm tắt thay đổi

### 1. 🔧 **Bỏ chữ "đ" trong hiển thị số tiền**
- **Trước:** `100,000đ`
- **Sau:** `100,000`
- **Files:** `RPointHistoryCard.tsx`, `RPointHistoryTable.tsx`

### 2. 🎯 **Icon R-Point lên tiêu đề cột**
- **Trước:** Icon nằm trong từng cell
- **Sau:** Icon nằm trong tiêu đề cột "Số R-Point"
- **Benefit:** G<PERSON><PERSON> gàng hơn, không lặp lại icon trong mỗi dòng

### 3. 📐 **Căn chỉnh text**
- **Căn trái:** Tất cả các trường và tiêu đề (ID, Ngày tạo, Số R-Point, Trạng thái, <PERSON><PERSON><PERSON> đơn)
- **<PERSON><PERSON><PERSON> phải:** Chỉ trường "Thanh toán/Amount" (tiêu đề và nội dung)
- **<PERSON><PERSON> do:** Số tiền thường căn phải để dễ so sánh

### 4. 🌍 **Rà soát đa ngôn ngữ**

#### Files đã cập nhật:
- `src/modules/profile/locales/vi.json` ✅
- `src/modules/profile/locales/en.json` ✅ 
- `src/modules/profile/locales/zh.json` ✅

#### Keys translation đã thêm/cập nhật:

**purchaseHistory section:**
- `rpointTitle` - "Lịch sử nạp R-Point" / "R-Point Deposit History" / "R-Point充值历史"
- `createdAt` - "Ngày tạo" / "Created Date" / "创建日期"
- `amount` - "Thanh toán" / "Payment" / "支付金额"
- `points` - "Số R-Point" / "R-Point Amount" / "R-Point数量"
- `status` - "Trạng thái" / "Status" / "状态"
- `statusCompleted` - "Đã thanh toán" / "Completed" / "已完成"
- `statusPending` - "Chờ thanh toán" / "Pending" / "待处理"
- `invoice` - "Hóa đơn" / "Invoice" / "发票"
- `download` - "Tải xuống" / "Download" / "下载"
- `processing` - "Chờ xử lý" / "Processing" / "处理中"

## Files đã sửa

### 1. `src/modules/profile/components/RPointHistoryCard.tsx`
```tsx
// Tiêu đề cột với icon
title: (
  <div className="flex items-center">
    <span>{t('profile:rpoint.amount')}</span>
    <Icon name="rpoint" size="sm" className="ml-1 text-red-600" />
  </div>
),

// Nội dung cell - bỏ icon, căn trái
render: (value: unknown) => {
  return (
    <div className="text-left">
      <span>{formatCurrency(value as number)}</span>
    </div>
  );
},

// Cột Amount - căn phải
title: <div className="text-right">{t('common:amount')}</div>,
render: (value: unknown) => {
  return <div className="text-right">{formatCurrency(value as number)}</div>;
},
```

### 2. `src/modules/profile/components/PurchaseHistory/RPointHistoryTable.tsx`
```tsx
// Tiêu đề cột với icon
title: (
  <div className="flex items-center">
    <span>{t('purchaseHistory.points', 'Số point')}</span>
    <img src={rpointImage} alt="R-Point" className="ml-1 w-4 h-4" />
  </div>
),

// Nội dung cell - bỏ icon, căn trái
render: (value: unknown) => (
  <div className="text-left">
    <span>{(value as number).toLocaleString()}</span>
  </div>
),

// Cột Amount - căn phải, bỏ chữ "đ"
title: <div className="text-right">{t('purchaseHistory.amount', 'Thanh toán')}</div>,
render: (value: unknown) => (
  <div className="text-right">{(value as number).toLocaleString()}</div>
),
```

## Kết quả

### ✅ **Trước khi sửa:**
```
| ID | Ngày tạo | Số point | Thanh toán | Trạng thái |
|----|----------|----------|------------|------------|
| 1  | 01/01/24 | 1,000 🔴 | 100,000đ   | Completed  |
```

### ✨ **Sau khi sửa:**
```
| ID | Ngày tạo | Số R-Point 🔴 |    Thanh toán | Trạng thái |
|----|----------|---------------|---------------|------------|
| 1  | 01/01/24 | 1,000         |       100,000 | Completed  |
```

## Test đa ngôn ngữ

1. Mở `/profile`
2. Chuyển đổi ngôn ngữ (VI/EN/ZH)
3. Kiểm tra section "Lịch sử nạp R-Point"
4. Verify tất cả labels đều được dịch đúng

## Status: ✅ HOÀN THÀNH

Tất cả yêu cầu đã được thực hiện thành công với đầy đủ hỗ trợ đa ngôn ngữ.
