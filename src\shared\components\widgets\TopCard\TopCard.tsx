import React, { useState, useMemo } from 'react';
import { Card, Typography, Icon, Pagination } from '@/shared/components/common';
import { TopCardProps } from './TopCard.types';

/**
 * Component TopCard hiển thị danh sách các card với tiêu đề và giá trị
 *
 * @example
 * // Sử dụng cơ bản
 * <TopCard
 *   items={[
 *     { title: 'Tổng người dùng', value: '1,234', icon: 'users' },
 *     { title: '<PERSON>anh thu', value: '45,678,000đ', icon: 'chart', change: '+12%', isPositive: true }
 *   ]}
 * />
 *
 * @example
 * // Sử dụng với phân trang
 * <TopCard
 *   items={statsItems}
 *   pagination={true}
 *   itemsPerPage={4}
 *   paginationVariant="simple"
 * />
 */
const TopCard: React.FC<TopCardProps> = ({
  items,
  title,
  className = '',
  itemsPerPage = 4,
  pagination = false,
  paginationVariant = 'default',
  paginationSize = 'md',
}) => {
  // State cho trang hiện tại
  const [currentPage, setCurrentPage] = useState(1);

  // Tính toán các items hiển thị trên trang hiện tại
  const displayedItems = useMemo(() => {
    if (!pagination) return items;

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return items.slice(startIndex, endIndex);
  }, [items, currentPage, itemsPerPage, pagination]);

  // Tính tổng số trang
  const totalPages = useMemo(() => {
    return Math.ceil(items.length / itemsPerPage);
  }, [items.length, itemsPerPage]);

  // Xử lý khi thay đổi trang
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className={className}>
      {title && (
        <Typography variant="h5" className="mb-4">
          {title}
        </Typography>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {displayedItems.map((item, index) => (
          <Card key={index} className="p-4">
            <div className="flex items-center justify-between mb-2">
              <Typography variant="subtitle1" color="muted">
                {item.title}
              </Typography>
              {item.icon && (
                <Icon
                  name={item.icon}
                  size="md"
                  className={item.iconColor ? '' : 'text-primary'}
                  {...(item.iconColor && { color: item.iconColor })}
                />
              )}
            </div>
            <Typography variant="h3" className="mb-1 text-center">
              {item.value}
            </Typography>
            {item.change && (
              <div
                className={`flex items-center ${item.isPositive !== false ? 'text-success' : 'text-destructive'}`}
              >
                <Icon
                  name={item.isPositive !== false ? 'trending-up' : 'trending-down'}
                  size="sm"
                />
                <Typography variant="body2" className="ml-1">
                  {item.change}
                </Typography>
              </div>
            )}
            {item.description && (
              <Typography variant="body2" color="muted" className="mt-2">
                {item.description}
              </Typography>
            )}
          </Card>
        ))}
      </div>

      {/* Hiển thị phân trang nếu được yêu cầu và có nhiều hơn 1 trang */}
      {pagination && totalPages > 1 && (
        <div className="mt-6">
          <Pagination
            variant={paginationVariant}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={items.length}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            size={paginationSize}
          />
        </div>
      )}
    </div>
  );
};

export default TopCard;
