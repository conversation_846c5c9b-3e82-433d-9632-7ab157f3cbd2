import { useMemo } from 'react';

export interface UsePaginationProps {
  /**
   * Tổng số mục
   */
  totalItems: number;

  /**
   * Số lượng mục trên mỗi trang
   */
  itemsPerPage: number;

  /**
   * Trang hiện tại (bắt đầu từ 1)
   */
  currentPage: number;

  /**
   * Số lượng tối đa các nút trang hiển thị
   * @default 5
   */
  maxPageButtons?: number;

  /**
   * <PERSON>hi đè tổng số trang (nếu được cung cấp)
   */
  overrideTotalPages?: number | undefined;
}

export interface UsePaginationReturn {
  /**
   * Tổng số trang
   */
  totalPages: number;

  /**
   * Mảng các số trang để hiển thị (có thể bao gồm chuỗi "..." cho dấu chấm lửng)
   */
  pageNumbers: (number | string)[];

  /**
   * <PERSON><PERSON> thể quay lại trang trước không
   */
  canGoPrevious: boolean;

  /**
   * <PERSON><PERSON> thể đi đến trang tiếp theo không
   */
  canGoNext: boolean;

  /**
   * Đi đến trang cụ thể
   */
  goToPage: (page: number) => number;

  /**
   * Đi đến trang trước
   */
  goToPreviousPage: () => number;

  /**
   * Đi đến trang tiếp theo
   */
  goToNextPage: () => number;

  /**
   * Đi đến trang đầu tiên
   */
  goToFirstPage: () => number;

  /**
   * Đi đến trang cuối cùng
   */
  goToLastPage: () => number;
}

/**
 * Hook tùy chỉnh để xử lý logic phân trang
 */
export function usePagination({
  totalItems,
  itemsPerPage,
  currentPage,
  maxPageButtons = 5,
  overrideTotalPages,
}: UsePaginationProps): UsePaginationReturn {
  // Tính toán tổng số trang
  const totalPages = useMemo(() => {
    // Sử dụng giá trị ghi đè nếu được cung cấp
    if (overrideTotalPages !== undefined) {
      return Math.max(1, overrideTotalPages);
    }
    return Math.max(1, Math.ceil(totalItems / itemsPerPage));
  }, [totalItems, itemsPerPage, overrideTotalPages]);

  // Kiểm tra trang hiện tại có hợp lệ không
  const validCurrentPage = useMemo(() => {
    return Math.min(Math.max(1, currentPage), totalPages);
  }, [currentPage, totalPages]);

  // Tạo mảng các số trang để hiển thị
  const pageNumbers = useMemo(() => {
    // Nếu tổng số trang ít hơn hoặc bằng số nút tối đa, hiển thị tất cả
    if (totalPages <= maxPageButtons) {
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }

    // Số nút ở mỗi bên của trang hiện tại
    const sideButtons = Math.floor((maxPageButtons - 3) / 2);
    const leftSide = Math.max(1, validCurrentPage - sideButtons);
    const rightSide = Math.min(totalPages, validCurrentPage + sideButtons);

    const result: (number | string)[] = [];

    // Luôn hiển thị trang đầu tiên
    if (leftSide > 1) {
      result.push(1);
      // Thêm dấu chấm lửng nếu cần
      if (leftSide > 2) {
        result.push('...');
      }
    }

    // Thêm các trang ở giữa
    for (let i = leftSide; i <= rightSide; i++) {
      result.push(i);
    }

    // Luôn hiển thị trang cuối cùng
    if (rightSide < totalPages) {
      // Thêm dấu chấm lửng nếu cần
      if (rightSide < totalPages - 1) {
        result.push('...');
      }
      result.push(totalPages);
    }

    return result;
  }, [totalPages, validCurrentPage, maxPageButtons]);

  // Kiểm tra có thể đi đến trang trước/tiếp theo không
  const canGoPrevious = validCurrentPage > 1;
  const canGoNext = validCurrentPage < totalPages;

  // Các hàm điều hướng
  const goToPage = (page: number) => {
    return Math.min(Math.max(1, page), totalPages);
  };

  const goToPreviousPage = () => {
    return goToPage(validCurrentPage - 1);
  };

  const goToNextPage = () => {
    return goToPage(validCurrentPage + 1);
  };

  const goToFirstPage = () => {
    return 1;
  };

  const goToLastPage = () => {
    return totalPages;
  };

  return {
    totalPages,
    pageNumbers,
    canGoPrevious,
    canGoNext,
    goToPage,
    goToPreviousPage,
    goToNextPage,
    goToFirstPage,
    goToLastPage,
  };
}

export default usePagination;
