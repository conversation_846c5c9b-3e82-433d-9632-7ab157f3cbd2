/**
 * Hook đơn giản để upload file lên URL tạm thời
 */
import { useCallback } from 'react';
import { usePresignedUpload, type UploadToPresignedUrlParams } from './usePresignedUpload';

/**
 * Tham số cho hàm uploadFile
 */
export interface UploadFileParams {
  /**
   * URL tạm thời để upload file
   */
  url: string;

  /**
   * File cần upload
   */
  file: File;

  /**
   * Callback khi upload thành công
   */
  onSuccess?: (result: unknown) => void;

  /**
   * Callback khi upload thất bại
   */
  onError?: (error: Error) => void;

  /**
   * Callback khi tiến trình upload thay đổi
   */
  onProgress?: (progress: number) => void;
}

/**
 * Hook đơn giản để upload file lên URL tạm thời
 * @returns Hàm để upload file
 */
export function useFileUploadToUrl() {
  const { uploadToPresignedUrl } = usePresignedUpload();

  /**
   * Upload file lên URL tạm thời
   * @param params Tham số cho việc upload
   * @returns ID của task trong TaskQueuePanel
   */
  const uploadFile = useCallback(
    (params: UploadFileParams): string => {
      const { url, file, onSuccess, onError, onProgress } = params;

      const uploadParams: UploadToPresignedUrlParams = {
        presignedUrl: url,
        file,
      };

      if (onSuccess) uploadParams.onSuccess = onSuccess;
      if (onError) uploadParams.onError = onError;
      if (onProgress) uploadParams.onProgress = onProgress;

      return uploadToPresignedUrl(uploadParams);
    },
    [uploadToPresignedUrl]
  );

  return {
    uploadFile,
  };
}

export default useFileUploadToUrl;
