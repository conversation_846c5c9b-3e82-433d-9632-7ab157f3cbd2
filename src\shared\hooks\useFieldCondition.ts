import { useEffect } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

/**
 * Loại điều kiện cho field
 */
export enum ConditionType {
  /**
   * Điều kiện bằng (===)
   */
  EQUALS = 'equals',

  /**
   * Điều kiện không bằng (!==)
   */
  NOT_EQUALS = 'notEquals',

  /**
   * Điều kiện lớn hơn (>)
   */
  GREATER_THAN = 'greaterThan',

  /**
   * Điều kiện nhỏ hơn (<)
   */
  LESS_THAN = 'lessThan',

  /**
   * Điều kiện có giá trị (!!value)
   */
  HAS_VALUE = 'hasValue',

  /**
   * Điều kiện không có giá trị (!value)
   */
  NO_VALUE = 'noValue',

  /**
   * Điều kiện là true (value === true)
   */
  IS_TRUE = 'isTrue',

  /**
   * Điều kiện là false (value === false)
   */
  IS_FALSE = 'isFalse',

  /**
   * Điều kiện có trong mảng (array.includes(value))
   */
  IN = 'in',

  /**
   * Điều kiện không có trong mảng (!array.includes(value))
   */
  NOT_IN = 'notIn',

  /**
   * Điều kiện tùy chỉnh (custom function)
   */
  CUSTOM = 'custom',
}

/**
 * Điều kiện đơn
 */
export interface SingleCondition {
  /**
   * Tên field để kiểm tra điều kiện
   */
  field: string;

  /**
   * Loại điều kiện
   */
  type: ConditionType;

  /**
   * Giá trị để so sánh
   */
  value?: unknown;

  /**
   * Hàm tùy chỉnh để kiểm tra điều kiện (chỉ dùng khi type = CUSTOM)
   */
  customCheck?: (fieldValue: unknown) => boolean;
}

/**
 * Điều kiện logic AND
 */
export interface AndCondition {
  /**
   * Danh sách các điều kiện con (AND)
   */
  and: (SingleCondition | AndCondition | OrCondition)[];
}

/**
 * Điều kiện logic OR
 */
export interface OrCondition {
  /**
   * Danh sách các điều kiện con (OR)
   */
  or: (SingleCondition | AndCondition | OrCondition)[];
}

/**
 * Kiểu điều kiện tổng hợp
 */
export type Condition = SingleCondition | AndCondition | OrCondition;

/**
 * Tham số cho hook useFieldCondition
 */
export interface UseFieldConditionOptions {
  /**
   * Điều kiện để hiển thị field
   */
  condition: Condition;

  /**
   * Có xóa giá trị khi field bị ẩn hay không
   * @default true
   */
  clearWhenHidden?: boolean;

  /**
   * Tên field sẽ bị ảnh hưởng bởi điều kiện
   */
  targetField?: string;
}

/**
 * Kiểm tra một điều kiện đơn
 */
const checkSingleCondition = (condition: SingleCondition, fieldValue: unknown): boolean => {
  const { type, value, customCheck } = condition;

  switch (type) {
    case ConditionType.EQUALS:
      return fieldValue === value;
    case ConditionType.NOT_EQUALS:
      return fieldValue !== value;
    case ConditionType.GREATER_THAN:
      // Type assertion cho trường hợp so sánh số
      return typeof fieldValue === 'number' && typeof value === 'number' && fieldValue > value;
    case ConditionType.LESS_THAN:
      // Type assertion cho trường hợp so sánh số
      return typeof fieldValue === 'number' && typeof value === 'number' && fieldValue < value;
    case ConditionType.HAS_VALUE:
      return !!fieldValue;
    case ConditionType.NO_VALUE:
      return !fieldValue;
    case ConditionType.IS_TRUE:
      return fieldValue === true;
    case ConditionType.IS_FALSE:
      return fieldValue === false;
    case ConditionType.IN:
      return Array.isArray(value) && value.includes(fieldValue as string | number);
    case ConditionType.NOT_IN:
      return Array.isArray(value) && !value.includes(fieldValue as string | number);
    case ConditionType.CUSTOM:
      return customCheck ? customCheck(fieldValue) : false;
    default:
      return false;
  }
};

/**
 * Kiểm tra một điều kiện phức tạp
 */
const checkCondition = (condition: Condition, getValues: (name: string) => unknown): boolean => {
  // Điều kiện AND
  if ('and' in condition) {
    return condition.and.every(subCondition => checkCondition(subCondition, getValues));
  }

  // Điều kiện OR
  if ('or' in condition) {
    return condition.or.some(subCondition => checkCondition(subCondition, getValues));
  }

  // Điều kiện đơn
  const fieldValue = getValues(condition.field);
  return checkSingleCondition(condition, fieldValue);
};

/**
 * Hook để quản lý điều kiện hiển thị cho field
 *
 * @example
 * // Hiển thị field khi field khác có giá trị cụ thể
 * const isVisible = useFieldCondition({
 *   condition: {
 *     field: 'userType',
 *     type: ConditionType.EQUALS,
 *     value: 'business'
 *   }
 * });
 *
 * // Điều kiện phức tạp với AND
 * const isVisible = useFieldCondition({
 *   condition: {
 *     and: [
 *       { field: 'userType', type: ConditionType.EQUALS, value: 'business' },
 *       { field: 'hasPlan', type: ConditionType.IS_TRUE }
 *     ]
 *   }
 * });
 *
 * // Điều kiện phức tạp với OR
 * const isVisible = useFieldCondition({
 *   condition: {
 *     or: [
 *       { field: 'userType', type: ConditionType.EQUALS, value: 'business' },
 *       { field: 'userType', type: ConditionType.EQUALS, value: 'enterprise' }
 *     ]
 *   }
 * });
 */
export function useFieldCondition({
  condition,
  clearWhenHidden = true,
  targetField,
}: UseFieldConditionOptions): boolean {
  const { getValues, setValue, unregister } = useFormContext();

  // Lấy danh sách các field cần theo dõi
  const getFieldsToWatch = (cond: Condition): string[] => {
    if ('and' in cond) {
      return cond.and.flatMap(subCondition => getFieldsToWatch(subCondition));
    }
    if ('or' in cond) {
      return cond.or.flatMap(subCondition => getFieldsToWatch(subCondition));
    }
    return [cond.field];
  };

  const fieldsToWatch = getFieldsToWatch(condition);

  // Theo dõi các field liên quan
  useWatch({
    name: fieldsToWatch,
  });

  // Kiểm tra điều kiện
  const isVisible = checkCondition(condition, getValues);

  // Xử lý khi field bị ẩn
  useEffect(() => {
    if (!isVisible && clearWhenHidden && targetField) {
      setValue(targetField, undefined);
      unregister(targetField);
    }
  }, [isVisible, clearWhenHidden, targetField, setValue, unregister]);

  return isVisible;
}

export default useFieldCondition;
