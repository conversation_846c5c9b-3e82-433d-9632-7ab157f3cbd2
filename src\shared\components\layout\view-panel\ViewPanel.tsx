import { ReactNode, memo } from 'react';
import ViewHeader from './ViewHeader';
import { ScrollArea } from '@/shared/components/common';

interface ViewPanelProps {
  title: string;
  children: ReactNode;
  actions?: ReactNode;
}

const ViewPanel = memo<ViewPanelProps>(({ title, children, actions }) => {
  return (
    <div className="flex flex-col h-full w-full bg-white dark:bg-dark">
      <ViewHeader title={title} actions={actions} />
      <ScrollArea
        className="flex-1 p-4 w-full"
        height="100%"
        autoHide={true}
        direction="vertical"
        invisible={false}
      >
        <div className="view-panel-content w-full max-w-full overflow-hidden">{children}</div>
      </ScrollArea>
    </div>
  );
});

ViewPanel.displayName = 'ViewPanel';

export default ViewPanel;
