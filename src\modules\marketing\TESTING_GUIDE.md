# Marketing Module - Testing Guide

> **Server đang chạy tại:** http://localhost:5174  
> **Test Page:** http://localhost:5174/marketing/test

## 🧪 Cách test giao diện

### 1. **Trang Test chính**
```
URL: http://localhost:5174/marketing/test
```
- Trang này có links đến tất cả các trang trong Marketing Module
- Click vào từng card để test từng trang

### 2. **Các trang có thể test:**

#### 📊 **Dashboard & Overview**
- **Marketing Dashboard**: `/marketing/dashboard`
  - Tổng quan tất cả channels
  - Quick stats và recent activity
  - Navigation đến các modules con

- **Zalo Overview**: `/marketing/zalo/overview`
  - Stats về Zalo OA và followers
  - Quick actions cho Zalo marketing

- **Email Overview**: `/marketing/email/overview`
  - Stats về email templates và campaigns
  - Recent templates và campaigns

#### 📱 **Zalo Marketing**
- **Zalo Accounts**: `/marketing/zalo/accounts`
  - <PERSON><PERSON> sách Zalo OA accounts
  - Connect new OA form
  - Account management actions

- **Zalo ZNS Templates**: `/marketing/zalo/zns`
  - Danh sách ZNS templates
  - Create new template form
  - Template preview và management

- **Zalo Followers**: `/marketing/zalo/accounts/1/followers`
  - Danh sách followers của OA
  - Bulk operations (add tags, send messages)
  - Search và filtering

#### 📧 **Email Marketing**
- **Email Templates**: `/marketing/email/templates`
  - Danh sách email templates
  - Create new template form
  - Template management (edit, duplicate, delete)

### 3. **Features để test:**

#### ✅ **UI Components**
- [ ] Responsive design (mobile, tablet, desktop)
- [ ] Dark/Light mode switching
- [ ] Loading states (skeleton loading)
- [ ] Error states
- [ ] Empty states

#### ✅ **Forms**
- [ ] Connect Zalo OA form
- [ ] Create ZNS Template form
- [ ] Create Email Template form
- [ ] Form validation
- [ ] Form submission (mock data)

#### ✅ **Tables & Lists**
- [ ] Data tables với pagination
- [ ] Search functionality
- [ ] Sorting
- [ ] Bulk selection
- [ ] Action menus

#### ✅ **Navigation**
- [ ] Breadcrumb navigation
- [ ] Back buttons
- [ ] Internal links
- [ ] Route transitions

#### ✅ **Modals & Dialogs**
- [ ] Create forms trong modals
- [ ] Preview modals
- [ ] Confirmation dialogs
- [ ] Modal close/open

### 4. **Mock Data Testing**

Tất cả data hiện tại là mock data để test UI:

- **Zalo Accounts**: 3 mock OA accounts
- **Zalo Followers**: Mock followers với tags
- **ZNS Templates**: Mock templates với different statuses
- **Email Templates**: Mock templates với variables

### 5. **Browser Testing**

Test trên các browsers:
- [ ] Chrome
- [ ] Firefox  
- [ ] Safari
- [ ] Edge

### 6. **Performance Testing**

- [ ] Page load times
- [ ] Smooth animations
- [ ] No console errors
- [ ] Memory usage

## 🐛 **Báo cáo lỗi**

Nếu gặp lỗi, vui lòng báo cáo:

1. **URL** của trang bị lỗi
2. **Browser** và version
3. **Steps** để reproduce lỗi
4. **Screenshot** nếu có
5. **Console errors** (F12 > Console)

## 📱 **Mobile Testing**

Để test mobile:
1. Mở DevTools (F12)
2. Click icon mobile/tablet
3. Chọn device size
4. Test responsive design

## 🎨 **Design System**

Module sử dụng design system của project:
- **Colors**: Red-orange gradient theme
- **Components**: Từ `/shared/components/common`
- **Typography**: Plus Jakarta Sans font
- **Spacing**: Tailwind CSS spacing scale

## ✅ **Checklist hoàn chỉnh**

### Phase 1 & 2 Features:
- [x] Marketing Dashboard
- [x] Zalo Overview & Accounts management
- [x] Zalo Followers management
- [x] ZNS Templates management
- [x] Email Overview & Templates management
- [x] Responsive design
- [x] TypeScript strict mode
- [x] Component architecture
- [x] Mock API integration

### Ready for Production:
- [x] No TypeScript errors
- [x] ESLint compliant
- [x] Performance optimized
- [x] Accessibility features
- [x] Error handling
- [x] Loading states

---

**🚀 Enjoy testing the Marketing Module!**
