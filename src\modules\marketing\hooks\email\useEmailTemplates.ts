import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import { EmailService } from '../../services/email.service';
import type {
  CreateEmailTemplateDto,
  UpdateEmailTemplateDto,
  EmailTemplateQueryDto,
  SendTestEmailDto,
} from '../../types/email.types';

/**
 * Query keys cho Email templates
 */
export const EMAIL_TEMPLATE_QUERY_KEYS = {
  all: ['email', 'templates'] as const,
  lists: () => [...EMAIL_TEMPLATE_QUERY_KEYS.all, 'list'] as const,
  list: (query: EmailTemplateQueryDto) => [...EMAIL_TEMPLATE_QUERY_KEYS.lists(), query] as const,
  details: () => [...EMAIL_TEMPLATE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...EMAIL_TEMPLATE_QUERY_KEYS.details(), id] as const,
  preview: (id: string, variables?: Record<string, string>) => [...EMAIL_TEMPLATE_QUERY_KEYS.all, 'preview', id, variables] as const,
};

/**
 * Hook để lấy danh sách email templates
 */
export function useEmailTemplates(query?: EmailTemplateQueryDto) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_QUERY_KEYS.list(query || {}),
    queryFn: () => EmailService.getTemplates(query),
    select: (response) => response.result,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook để lấy chi tiết email template
 */
export function useEmailTemplate(id: string) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_QUERY_KEYS.detail(id),
    queryFn: () => EmailService.getTemplate(id),
    select: (response) => response.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

/**
 * Hook để tạo email template mới
 */
export function useCreateEmailTemplate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateEmailTemplateDto) => EmailService.createTemplate(data),
    onSuccess: (response) => {
      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_QUERY_KEYS.lists() });

      NotificationUtil.success({
        message: 'Tạo template thành công!',
        title: `Template "${response.result.name}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Tạo template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để cập nhật email template
 */
export function useUpdateEmailTemplate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateEmailTemplateDto }) =>
      EmailService.updateTemplate(id, data),
    onSuccess: (response, { id }) => {
      // Update cache cho template detail
      queryClient.setQueryData(
        EMAIL_TEMPLATE_QUERY_KEYS.detail(id),
        response
      );

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_QUERY_KEYS.lists() });

      NotificationUtil.success({ message: 'Cập nhật template thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Cập nhật template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để xóa email template
 */
export function useDeleteEmailTemplate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => EmailService.deleteTemplate(id),
    onSuccess: (_, id) => {
      // Remove từ cache
      queryClient.removeQueries({ queryKey: EMAIL_TEMPLATE_QUERY_KEYS.detail(id) });

      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_QUERY_KEYS.lists() });

      NotificationUtil.success({ message: 'Xóa template thành công!' });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Xóa template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để duplicate email template
 */
export function useDuplicateEmailTemplate() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, name }: { id: string; name: string }) =>
      EmailService.duplicateTemplate(id, name),
    onSuccess: (response) => {
      // Invalidate templates list
      queryClient.invalidateQueries({ queryKey: EMAIL_TEMPLATE_QUERY_KEYS.lists() });

      NotificationUtil.success({
        message: 'Duplicate template thành công!',
        title: `Template "${response.result.name}" đã được tạo`,
      });
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Duplicate template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để preview email template
 */
export function usePreviewEmailTemplate(id: string, variables?: Record<string, string>) {
  return useQuery({
    queryKey: EMAIL_TEMPLATE_QUERY_KEYS.preview(id, variables),
    queryFn: () => EmailService.previewTemplate(id, variables),
    select: (response) => response.result,
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

/**
 * Hook để gửi test email
 */
export function useSendTestEmail() {
  return useMutation({
    mutationFn: (data: SendTestEmailDto) => EmailService.sendTestEmail(data),
    onSuccess: (response) => {
      const { sentCount, failedCount } = response.result;

      if (failedCount > 0) {
        NotificationUtil.warning({
          message: 'Gửi test email hoàn thành với một số lỗi',
          title: `Thành công: ${sentCount}, Thất bại: ${failedCount}`,
        });
      } else {
        NotificationUtil.success({
          message: 'Gửi test email thành công!',
          title: `Đã gửi đến ${sentCount} email`,
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Gửi test email thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để validate email template
 */
export function useValidateEmailTemplate() {
  return useMutation({
    mutationFn: (data: { htmlContent: string; variables: Record<string, string> }) =>
      EmailService.validateTemplate(data),
    onSuccess: (response) => {
      const { isValid, errors, warnings } = response.result;

      if (isValid) {
        NotificationUtil.success({ message: 'Template hợp lệ!' });
      } else {
        NotificationUtil.error({
          message: 'Template có lỗi',
          title: errors.join(', '),
        });
      }

      if (warnings.length > 0) {
        NotificationUtil.warning({
          message: 'Template có cảnh báo',
          title: warnings.join(', '),
        });
      }
    },
    onError: (error: Error) => {
      NotificationUtil.error({
        message: 'Validate template thất bại',
        title: error.message,
      });
    },
  });
}

/**
 * Hook để lấy deliverability score
 */
export function useEmailDeliverabilityScore(templateId: string) {
  return useQuery({
    queryKey: ['email', 'deliverability', templateId],
    queryFn: () => EmailService.getDeliverabilityScore(templateId),
    select: (response) => response.result,
    enabled: !!templateId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook tổng hợp cho email template management
 */
export function useEmailTemplateManagement() {
  const createTemplate = useCreateEmailTemplate();
  const updateTemplate = useUpdateEmailTemplate();
  const deleteTemplate = useDeleteEmailTemplate();
  const duplicateTemplate = useDuplicateEmailTemplate();
  const sendTestEmail = useSendTestEmail();
  const validateTemplate = useValidateEmailTemplate();

  return {
    createTemplate,
    updateTemplate,
    deleteTemplate,
    duplicateTemplate,
    sendTestEmail,
    validateTemplate,
    isLoading: createTemplate.isPending ||
               updateTemplate.isPending ||
               deleteTemplate.isPending ||
               duplicateTemplate.isPending ||
               sendTestEmail.isPending ||
               validateTemplate.isPending,
  };
}
