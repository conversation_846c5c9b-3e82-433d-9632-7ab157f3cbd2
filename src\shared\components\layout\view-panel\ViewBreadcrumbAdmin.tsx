import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Breadcrumb, Icon } from '@/shared/components/common';
import type { BreadcrumbItem } from '@/shared/components/common/Breadcrumb/Breadcrumb';

interface ViewBreadcrumbProps {
  title: string;
  className?: string;
}

const ViewBreadcrumbAdmin: React.FC<ViewBreadcrumbProps> = ({ title, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation([
    'common',
    'admin',
    'affiliate',
    'employee',
    'rpointAdmin',
    'user',
    'data',
    'blog',
    'marketplace',
  ]);

  // Tạo breadcrumb items dựa trên đường dẫn hiện tại
  const generateBreadcrumbItems = (): BreadcrumbItem[] => {
    // Luôn có item Home (hiển thị icon và text "Trang chủ")
    const items: BreadcrumbItem[] = [
      {
        label: t('common:home', 'Trang chủ'),
        path: '/admin',
        icon: <Icon name="home" size="sm" />,
        onClick: () => navigate('/admin'),
      },
    ];

    // Nếu không phải trang admin chính, thêm breadcrumb
    if (location.pathname !== '/admin') {
      // Xử lý các admin routes
      switch (location.pathname) {
        // Admin R-Point module
        case '/admin/rpoint/coupons/create':
          items.push({
            label: t('admin:rpoint.coupons.title', 'Mã Khuyến Mãi'),
            path: '/admin/rpoint/coupons',
            onClick: () => navigate('/admin/rpoint/coupons'),
          });
          items.push({
            label: t('admin:rpoint.coupons.addNew', 'Thêm Mã Khuyến Mãi'),
          });
          break;
        // Xử lý trang sửa mã khuyến mãi
        case location.pathname.startsWith('/admin/rpoint/coupons/edit/') ? location.pathname : '':
          items.push({
            label: t('admin:rpoint.coupons.title', 'Mã Khuyến Mãi'),
            path: '/admin/rpoint/coupons',
            onClick: () => navigate('/admin/rpoint/coupons'),
          });
          items.push({
            label: t('admin:rpoint.coupons.edit', 'Sửa Mã Khuyến Mãi'),
          });
          break;

        // Admin Affiliate module
        case '/admin/affiliate':
          items.push({
            label: t('affiliate:title', 'Quản lý Affiliate'),
          });
          break;
        case '/admin/affiliate/publishers':
          items.push({
            label: t('affiliate:title', 'Quản lý Affiliate'),
            path: '/admin/affiliate',
            onClick: () => navigate('/admin/affiliate'),
          });
          items.push({
            label: t('affiliate:publisher.title', 'Quản lý Publisher'),
          });
          break;
        case '/admin/affiliate/ranks':
          items.push({
            label: t('affiliate:title', 'Quản lý Affiliate'),
            path: '/admin/affiliate',
            onClick: () => navigate('/admin/affiliate'),
          });
          items.push({
            label: t('affiliate:rank.title', 'Cấp bậc Affiliate'),
          });
          break;
        case '/admin/affiliate/orders':
          items.push({
            label: t('affiliate:title', 'Quản lý Affiliate'),
            path: '/admin/affiliate',
            onClick: () => navigate('/admin/affiliate'),
          });
          items.push({
            label: t('affiliate:order.title', 'Đơn hàng Affiliate'),
          });
          break;
        case '/admin/affiliate/point-conversions':
          items.push({
            label: t('affiliate:title', 'Quản lý Affiliate'),
            path: '/admin/affiliate',
            onClick: () => navigate('/admin/affiliate'),
          });
          items.push({
            label: t('affiliate:pointConversion.title', 'Chuyển đổi điểm'),
          });
          break;

        // Admin Employee module
        case '/admin/employees':
          items.push({
            label: t('employee:title', 'Quản lý Nhân viên'),
          });
          break;
        case '/admin/employees/list':
          items.push({
            label: t('employee:title', 'Quản lý Nhân viên'),
            path: '/admin/employees',
            onClick: () => navigate('/admin/employees'),
          });
          items.push({
            label: t('employee:list.title', 'Danh sách Nhân viên'),
          });
          break;
        case '/admin/employees/roles':
          items.push({
            label: t('employee:title', 'Quản lý Nhân viên'),
            path: '/admin/employees',
            onClick: () => navigate('/admin/employees'),
          });
          items.push({
            label: t('employee:role.title', 'Quản lý Vai trò'),
          });
          break;
        case '/admin/employees/permissions':
          items.push({
            label: t('employee:title', 'Quản lý Nhân viên'),
            path: '/admin/employees',
            onClick: () => navigate('/admin/employees'),
          });
          items.push({
            label: t('employee:permission.title', 'Quản lý Quyền'),
          });
          break;

        // Admin Data module
        case '/admin/data':
          items.push({
            label: t('admin:data.title', 'Quản lý Dữ liệu'),
          });
          break;
        case '/admin/data/media':
          items.push({
            label: t('admin:data.title', 'Quản lý Dữ liệu'),
            path: '/admin/data',
            onClick: () => navigate('/admin/data'),
          });
          items.push({
            label: t('admin:data.media.title', 'Quản lý Media'),
          });
          break;
        case '/admin/data/url':
          items.push({
            label: t('admin:data.title', 'Quản lý Dữ liệu'),
            path: '/admin/data',
            onClick: () => navigate('/admin/data'),
          });
          items.push({
            label: t('admin:data.url.title', 'Quản lý URL'),
          });
          break;
        case '/admin/data/knowledge-files':
          items.push({
            label: t('admin:data.title', 'Quản lý Dữ liệu'),
            path: '/admin/data',
            onClick: () => navigate('/admin/data'),
          });
          items.push({
            label: t('admin:data.knowledgeFiles.title', 'Quản lý File Tri Thức'),
          });
          break;
        case '/admin/data/vector-store':
          items.push({
            label: t('admin:data.title', 'Quản lý Dữ liệu'),
            path: '/admin/data',
            onClick: () => navigate('/admin/data'),
          });
          items.push({
            label: t('admin:data.vectorStore.title', 'Quản lý Vector Store'),
          });
          break;

        // Admin User module
        case '/admin/users-page':
          items.push({
            label: t('user:title', 'Quản lý Người dùng'),
          });
          break;

        // Admin Blog module
        case '/admin/blog':
          items.push({
            label: t('admin:blog.title', 'Quản lý Blog'),
          });
          break;
        case '/admin/blog/list':
          items.push({
            label: t('admin:blog.title', 'Quản lý Blog'),
            path: '/admin/blog',
            onClick: () => navigate('/admin/blog'),
          });
          items.push({
            label: t('admin:blog.list', 'Danh sách Blog'),
          });
          break;
        case location.pathname.startsWith('/admin/blog/detail/') ? location.pathname : '':
          items.push({
            label: t('admin:blog.title', 'Quản lý Blog'),
            path: '/admin/blog',
            onClick: () => navigate('/admin/blog'),
          });
          items.push({
            label: t('admin:blog.detail', 'Chi tiết Blog'),
          });
          break;

        // Admin Marketplace module
        case '/admin/marketplace':
          items.push({
            label: t('admin:marketplace.title', 'Quản lý Marketplace'),
          });
          break;
        case '/admin/marketplace/products':
          items.push({
            label: t('admin:marketplace.title', 'Quản lý Marketplace'),
            path: '/admin/marketplace',
            onClick: () => navigate('/admin/marketplace'),
          });
          items.push({
            label: t('admin:marketplace.products', 'Quản lý Sản phẩm'),
          });
          break;
        case '/admin/marketplace/orders':
          items.push({
            label: t('admin:marketplace.title', 'Quản lý Marketplace'),
            path: '/admin/marketplace',
            onClick: () => navigate('/admin/marketplace'),
          });
          items.push({
            label: t('admin:marketplace.orders', 'Quản lý Đơn hàng'),
          });
          break;
        case '/admin/marketplace/cart':
          items.push({
            label: t('admin:marketplace.title', 'Quản lý Marketplace'),
            path: '/admin/marketplace',
            onClick: () => navigate('/admin/marketplace'),
          });
          items.push({
            label: t('admin:marketplace.cart.cart', 'Quản lý Giỏ hàng'),
          });
          break;

        // Admin R-Point module
        case '/admin/r-point':
          items.push({
            label: t('rpointAdmin:title', 'Quản lý R-Point'),
          });
          break;
        case '/admin/r-point/points':
          items.push({
            label: t('rpointAdmin:title', 'Quản lý R-Point'),
            path: '/admin/r-point',
            onClick: () => navigate('/admin/r-point'),
          });
          items.push({
            label: t('rpointAdmin:points.title', 'Gói R-Point'),
          });
          break;
        case '/admin/r-point/transactions':
          items.push({
            label: t('rpointAdmin:title', 'Quản lý R-Point'),
            path: '/admin/r-point',
            onClick: () => navigate('/admin/r-point'),
          });
          items.push({
            label: t('rpointAdmin:transactions.title', 'Giao dịch R-Point'),
          });
          break;
        case '/admin/r-point/coupons':
          items.push({
            label: t('rpointAdmin:title', 'Quản lý R-Point'),
            path: '/admin/r-point',
            onClick: () => navigate('/admin/r-point'),
          });
          items.push({
            label: t('rpointAdmin:coupons.title', 'Quản lý mã khuyến mãi'),
          });
          break;
        case location.pathname.startsWith('/admin/r-point/points/') ? location.pathname : '':
          items.push({
            label: t('rpointAdmin:title', 'Quản lý R-Point'),
            path: '/admin/r-point',
            onClick: () => navigate('/admin/r-point'),
          });
          items.push({
            label: t('rpointAdmin:points.title', 'Gói R-Point'),
            path: '/admin/r-point/points',
            onClick: () => navigate('/admin/r-point/points'),
          });
          items.push({
            label: t('rpointAdmin:points.detail', 'Chi tiết Gói'),
          });
          break;

        // Admin Marketing module
        case '/admin/marketing':
          items.push({
            label: t('admin:marketing.title', 'Quản lý Marketing'),
          });
          break;
        case '/admin/marketing/custom-fields':
          items.push({
            label: t('admin:marketing.title', 'Quản lý Marketing'),
            path: '/admin/marketing',
            onClick: () => navigate('/admin/marketing'),
          });
          items.push({
            label: t('admin:marketing.customFields', 'Trường tùy chỉnh'),
          });
          break;

        // Admin Tools module
        case '/admin/tools':
          items.push({
            label: t('admin:tool.title', 'Quản lý Tools'),
          });
          break;
        case '/admin/tools/list':
          items.push({
            label: t('admin:tool.title', 'Quản lý Tools'),
            path: '/admin/tools',
            onClick: () => navigate('/admin/tools'),
          });
          items.push({
            label: t('admin:tool.list.title', 'Danh sách Tools'),
          });
          break;
        case '/admin/tools/groups':
          items.push({
            label: t('admin:tool.title', 'Quản lý Tools'),
            path: '/admin/tools',
            onClick: () => navigate('/admin/tools'),
          });
          items.push({
            label: t('admin:tool.groups.title', 'Nhóm Tools'),
          });
          break;
        case '/admin/tools/trash':
          items.push({
            label: t('admin:tool.title', 'Quản lý Tools'),
            path: '/admin/tools',
            onClick: () => navigate('/admin/tools'),
          });
          items.push({
            label: t('admin:tool.trash.title', 'Thùng rác - Tools đã xóa'),
          });
          break;

        // Admin Business module
        case '/admin/business':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
          });
          break;
        case '/admin/business/product':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.product', 'Quản lý Sản phẩm'),
          });
          break;
        case '/admin/business/conversion':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.conversion', 'Quản lý Chuyển đổi'),
          });
          break;
        case '/admin/business/user-customer':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.businessPage.modules.userCustomer.title', 'Quản lý khách hàng của người dùng'),
          });
          break;
        case '/admin/business/order':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.order', 'Quản lý Đơn hàng'),
          });
          break;
        case '/admin/business/warehouse':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.warehouse', 'Quản lý Kho'),
          });
          break;
        case '/admin/business/virtual-warehouse':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.virtualWarehouse', 'Quản lý kho ảo'),
          });
          break;
        case '/admin/business/custom-field':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.customField', 'Trường tùy chỉnh'),
          });
          break;
        case '/admin/business/warehouse-custom-field':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.warehouseCustomField', 'Trường tùy chỉnh kho'),
          });
          break;
        case '/admin/business/file':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.file', 'Quản lý File'),
          });
          break;
        case '/admin/business/folder':
          items.push({
            label: t('admin:business.routes.business', 'Quản lý Business'),
            path: '/admin/business',
            onClick: () => navigate('/admin/business'),
          });
          items.push({
            label: t('admin:business.routes.folder', 'Quản lý Thư mục'),
          });
          break;

        default: {
          // Nếu không có xử lý đặc biệt, sử dụng title được truyền vào
          // Kiểm tra xem title có phải là key translation không
          const isTranslationKey = title.includes(':') && !title.includes(' ');
          items.push({
            label: isTranslationKey ? t(title) : title,
          });
        }
      }
    }

    return items;
  };

  return (
    <div className="flex items-center overflow-hidden">
      <Breadcrumb items={generateBreadcrumbItems()} className={`text-sm ${className} truncate`} />
      {/* Hiệu ứng ánh sáng nhẹ */}
      <div className="absolute w-8 h-8 bg-primary opacity-10 rounded-full blur-xl -z-10 animate-pulse-slow"></div>
    </div>
  );
};

export default ViewBreadcrumbAdmin;
