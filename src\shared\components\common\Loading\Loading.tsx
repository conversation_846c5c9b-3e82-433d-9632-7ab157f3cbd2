import React from 'react';

interface LoadingProps {
  /**
   * <PERSON><PERSON><PERSON> thướ<PERSON> của loading spinner
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị trạng thái loading
 */
const Loading: React.FC<LoadingProps> = ({ size = 'md', className = '' }) => {
  // Xác định kích thước spinner
  const spinnerSize = {
    sm: 'h-8 w-8 border-2',
    md: 'h-12 w-12 border-t-2 border-b-2',
    lg: 'h-16 w-16 border-3',
  }[size];

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <div className={`animate-spin rounded-full ${spinnerSize} border-primary`}></div>
    </div>
  );
};

export default Loading;
