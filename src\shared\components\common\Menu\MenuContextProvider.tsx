import React, { useState, useMemo, useCallback } from 'react';
import { MenuContext, MenuMode, MenuTheme } from './MenuContext';

export interface MenuProviderProps {
  children: React.ReactNode;
  mode?: MenuMode;
  theme?: MenuTheme;
  collapsed?: boolean;
  selectedKey?: string | undefined;
  expandedKeys?: string[];
  onSelect?: ((key: string) => void) | undefined;
}

export const MenuProvider: React.FC<MenuProviderProps> = ({
  children,
  mode = 'horizontal',
  theme = 'default',
  collapsed = false,
  selectedKey,
  expandedKeys = [],
  onSelect,
}) => {
  const [selectedKeyState, setSelectedKeyState] = useState<string | undefined>(selectedKey);
  const [expandedKeysState, setExpandedKeysState] = useState<string[]>(expandedKeys);

  // Xử lý khi chọn một menu item
  const handleSelectKey = useCallback(
    (key: string) => {
      setSelectedKeyState(key);
      if (onSelect) {
        onSelect(key);
      }
    },
    [onSelect]
  );

  // Mở rộng/thu gọn một submenu
  const toggleExpand = useCallback((key: string) => {
    setExpandedKeysState(prevKeys => {
      if (prevKeys.includes(key)) {
        return prevKeys.filter(k => k !== key);
      } else {
        return [...prevKeys, key];
      }
    });
  }, []);

  // Kiểm tra xem một key có đang được mở rộng không
  const isExpanded = useCallback(
    (key: string) => {
      return expandedKeysState.includes(key);
    },
    [expandedKeysState]
  );

  // Tạo context value
  const contextValue = useMemo(
    () => ({
      mode,
      theme,
      collapsed,
      selectedKey: selectedKeyState,
      expandedKeys: expandedKeysState,
      setSelectedKey: handleSelectKey,
      toggleExpand,
      isExpanded,
    }),
    [
      mode,
      theme,
      collapsed,
      selectedKeyState,
      expandedKeysState,
      handleSelectKey,
      isExpanded,
      toggleExpand,
    ]
  );

  return <MenuContext.Provider value={contextValue}>{children}</MenuContext.Provider>;
};
