import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  CollapsibleCard,
} from '@/shared/components/common';

import CustomFieldRenderer from '../../CustomFieldRenderer';
import SimpleCustomFieldSelector, { CustomFieldData } from '../../SimpleCustomFieldSelector';

// Interface cho trường tùy chỉnh đã chọn
interface SelectedCustomField {
  id: number; // ID của custom field từ API
  fieldId: number; // Alias cho id để tương thích với CustomFieldRenderer
  configId: string; // configId để gửi lên API
  label: string;
  component: string;
  type: string;
  required: boolean;
  configJson: Record<string, unknown>; // configJson đã có sẵn từ API
  value: Record<string, unknown>;
}

// Component để render custom field với configJson có sẵn
interface CustomFieldWithConfigProps {
  field: SelectedCustomField;
  onChange: (value: string | number | boolean) => void;
  onRemove: () => void;
}

const CustomFieldWithConfig: React.FC<CustomFieldWithConfigProps> = ({
  field,
  onChange,
  onRemove,
}) => {
  // Không cần fetch configJson từ API vì đã có trong field.configJson
  // API /v1/user/custom-fields đã trả về đầy đủ configJson

  return (
    <div className="mb-4">
      <CustomFieldRenderer
        field={{
          ...field,
          configJson: field.configJson || {},
        }}
        value={field.value['value'] as string | number | boolean}
        onChange={onChange}
        onRemove={onRemove}
      />
    </div>
  );
};

interface WarehouseCustomFieldsProps {
  warehouseCustomFields: SelectedCustomField[];
  onCustomFieldsChange: (fields: SelectedCustomField[]) => void;
}

/**
 * Component quản lý trường tùy chỉnh cho kho
 */
const WarehouseCustomFields: React.FC<WarehouseCustomFieldsProps> = ({
  warehouseCustomFields,
  onCustomFieldsChange,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xử lý thêm trường tùy chỉnh vào kho
  const handleToggleCustomFieldToWarehouse = useCallback(
    (fieldId: number, fieldData: CustomFieldData) => {
      const existingField = warehouseCustomFields.find(f => f.fieldId === fieldId);

      if (existingField) {
        // Nếu đã tồn tại, xóa khỏi danh sách
        const updatedFields = warehouseCustomFields.filter(f => f.fieldId !== fieldId);
        onCustomFieldsChange(updatedFields);
      } else {
        // Nếu chưa tồn tại, thêm vào danh sách
        const newField: SelectedCustomField = {
          id: fieldId,
          fieldId: fieldId,
          configId: fieldData.configId || '',
          label: fieldData.label,
          component: fieldData.component,
          type: fieldData.type,
          required: fieldData.required,
          configJson: fieldData.configJson || {},
          value: { value: '' },
        };

        const updatedFields = [...warehouseCustomFields, newField];
        onCustomFieldsChange(updatedFields);
      }
    },
    [warehouseCustomFields, onCustomFieldsChange]
  );

  // Xử lý cập nhật giá trị trường tùy chỉnh
  const handleUpdateCustomFieldInWarehouse = useCallback(
    (fieldId: number, value: string | number | boolean) => {
      const updatedFields = warehouseCustomFields.map(field =>
        field.fieldId === fieldId
          ? { ...field, value: { value } }
          : field
      );
      onCustomFieldsChange(updatedFields);
    },
    [warehouseCustomFields, onCustomFieldsChange]
  );

  // Xử lý xóa trường tùy chỉnh khỏi kho
  const handleRemoveCustomFieldFromWarehouse = useCallback(
    (fieldId: number) => {
      const updatedFields = warehouseCustomFields.filter(f => f.fieldId !== fieldId);
      onCustomFieldsChange(updatedFields);
    },
    [warehouseCustomFields, onCustomFieldsChange]
  );

  // Không cần handleConfigLoaded vì configJson đã có sẵn từ API

  return (
    <CollapsibleCard
      title={
        <Typography variant="h6" className="text-foreground">
          {t('business:warehouse.detail.customFields', 'Trường tùy chỉnh')}
        </Typography>
      }
      defaultOpen={false}
    >
      <div className="space-y-4">
        {/* SimpleCustomFieldSelector thay thế cho table */}
        <SimpleCustomFieldSelector
          onFieldSelect={fieldData => {
            handleToggleCustomFieldToWarehouse(
              fieldData.id,
              fieldData
            );
          }}
          selectedFieldIds={warehouseCustomFields.map(f => f.fieldId)}
          placeholder={t(
            'business:warehouse.form.customFields.searchPlaceholder',
            'Nhập từ khóa và nhấn Enter để tìm trường tùy chỉnh...'
          )}
        />

        {warehouseCustomFields.length > 0 && (
          <div>
            {warehouseCustomFields.map((field) => (
              <CustomFieldWithConfig
                key={field.id}
                field={field}
                onChange={(value) => handleUpdateCustomFieldInWarehouse(field.id, value)}
                onRemove={() => handleRemoveCustomFieldFromWarehouse(field.id)}
              />
            ))}
          </div>
        )}

        {warehouseCustomFields.length === 0 && (
          <div className="text-center py-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <Typography variant="body2" className="text-gray-500 dark:text-gray-400">
              {t('business:warehouse.form.customFields.noFields', 'Chưa có trường tùy chỉnh nào. Sử dụng ô tìm kiếm trên để thêm trường.')}
            </Typography>
          </div>
        )}
      </div>
    </CollapsibleCard>
  );
};

export default WarehouseCustomFields;
