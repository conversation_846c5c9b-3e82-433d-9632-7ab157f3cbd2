{"agent": {"management": {"title": "代理管理", "description": "综合代理系统管理"}, "rank": {"title": "管理员 - 代理排名", "description": "代理排名管理", "pageTitle": "代理等级管理", "addRank": "添加新等级", "editRank": "编辑代理等级", "searchPlaceholder": "搜索等级...", "noSearchResults": "未找到符合搜索条件的等级。", "createFirst": "创建第一个代理等级", "sortBy": "排序方式", "createSuccess": "成功", "createSuccessMessage": "代理等级已成功创建", "updateSuccess": "成功", "updateSuccessMessage": "代理等级已成功更新", "active": "活跃", "inactive": "非活跃", "editAction": "编辑", "deleteAction": "删除", "confirmDelete": "确认删除等级", "deleteMessage": "您确定要删除此等级吗？此操作无法撤销。", "deleteSuccess": "等级删除成功", "deleteError": "删除等级时发生错误", "list": {"title": "等级列表", "noRanks": "没有等级", "noRanksDescription": "系统中目前没有等级。", "loadError": "无法加载等级列表。请重试。", "loading": "正在加载等级列表...", "refreshing": "正在刷新数据..."}, "form": {"basicInfo": "基本信息", "name": "等级名称", "namePlaceholder": "输入等级名称", "description": "描述", "descriptionPlaceholder": "输入等级描述", "expRange": "经验范围", "minExp": "最低经验", "maxExp": "最高经验", "badge": "徽章", "badgeUpload": "上传徽章", "badgeHelp": "支持格式：JPG、PNG（仅限单张图片）", "currentBadge": "当前徽章", "currentBadgeNote": "上传新图片以替换", "active": "激活", "create": "创建等级", "update": "更新", "creating": "正在创建等级...", "createSuccess": "等级创建成功", "createError": "创建等级时发生错误", "updateError": "更新等级时发生错误"}, "validation": {"nameRequired": "等级名称是必需的", "descriptionRequired": "描述是必需的", "minExpInvalid": "最低经验必须 >= 0", "minExpInteger": "最低经验必须是整数", "maxExpInvalid": "最高经验必须 > 0", "maxExpInteger": "最高经验必须是整数", "expRangeInvalid": "最高经验必须大于最低经验", "expRangeOverlap": "经验范围与其他等级的经验范围重叠"}, "edit": {"notFound": "未找到等级"}, "sort": {"name": "名称", "minExp": "最低经验", "maxExp": "最高经验", "createdAt": "创建日期"}}, "system": {"title": "管理员 - 系统代理", "description": "系统代理管理", "pageTitle": "系统代理管理", "addAgent": "添加新代理", "editAgent": "编辑代理系统", "searchPlaceholder": "搜索代理...", "noSearchResults": "未找到符合搜索条件的代理。", "createFirst": "创建第一个系统代理", "viewTrash": "查看回收站", "backToMain": "返回主列表", "createSuccess": "成功", "createSuccessMessage": "代理系统已成功创建", "updateSuccess": "成功", "cancel": "取消", "updateAgent": "更新代理", "updateSuccessMessage": "代理系统已成功更新"}, "user": {"title": "管理员 - 用户代理", "description": "用户代理管理"}, "type": {"title": "管理员 - 代理类型", "description": "管理员代理类型管理端点"}, "list": {"title": "代理列表", "noAgents": "没有代理", "noAgentsDescription": "系统中目前没有代理。", "loadError": "无法加载代理列表。请重试。", "loading": "正在加载代理列表...", "refreshing": "正在刷新数据..."}, "card": {"supervisor": "监督员", "active": "活跃", "inactive": "非活跃", "activate": "激活", "deactivate": "停用", "edit": "编辑", "delete": "删除", "restore": "恢复", "confirmDelete": "确认删除代理", "deleteMessage": "您确定要删除此代理吗？此操作无法撤销。", "deleteSuccess": "代理删除成功", "deleteError": "删除代理时发生错误", "updateSuccess": "代理更新成功", "updateError": "更新代理时发生错误", "setSupervisor": "设为监督员", "removeSupervisor": "移除监督员", "setSupervisorSuccess": "成功设为监督员", "removeSupervisorSuccess": "成功移除监督员权限", "supervisorError": "更改监督员权限时发生错误", "restoreSuccess": "代理恢复成功", "restoreError": "恢复代理时发生错误"}, "trash": {"noAgents": "回收站中没有代理", "noAgentsDescription": "回收站为空。已删除的代理将出现在这里。"}, "edit": {"notFound": "未找到代理"}, "pagination": {"itemsPerPage": "每页项目数", "showingItems": "显示 {from} - {to} 共 {total} 项", "page": "页", "of": "共", "previous": "上一页", "next": "下一页"}, "form": {"basicInfo": "基本信息", "name": "代理名称", "namePlaceholder": "输入代理名称", "nameCode": "标识符代码", "nameCodePlaceholder": "输入标识符代码", "instruction": "指令", "instructionPlaceholder": "输入代理指令", "description": "描述", "descriptionPlaceholder": "输入代理描述", "avatar": "头像", "avatarUpload": "上传头像", "avatarHelp": "支持格式：JPG、PNG（仅限单张图片）", "modelConfig": "模型配置", "temperature": "温度", "topP": "Top P", "topK": "Top K", "maxTokens": "最大令牌数", "provider": "提供商类型", "resources": "资源", "model": "模型", "selectModel": "选择模型", "vectorStore": "向量存储", "selectVectorStore": "选择向量存储", "isSupervisor": "是监督员", "create": "创建代理", "creating": "正在创建代理...", "createSuccess": "代理创建成功", "createError": "创建代理时发生错误", "uploadingAvatar": "正在上传头像...", "uploadAvatarSuccess": "头像上传成功", "uploadAvatarError": "上传头像时发生错误"}, "validation": {"nameRequired": "代理名称是必需的", "nameCodeRequired": "标识符代码是必需的", "nameCodeFormat": "标识符代码只能包含小写字母、数字、下划线和连字符", "instructionRequired": "指令是必需的", "descriptionRequired": "描述是必需的", "modelRequired": "模型是必需的", "modelIdInvalid": "模型ID必须是有效的UUID"}}}