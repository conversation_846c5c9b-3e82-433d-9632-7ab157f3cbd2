import { ReactNode, CSSProperties } from 'react';

/**
 * <PERSON>ại sắp xếp
 */
export type SortOrder = 'asc' | 'desc' | null;

/**
 * Cấu trúc cột
 */
export interface TableColumn<T = unknown> {
  /**
   * Khóa duy nhất cho cột
   */
  key: string;

  /**
   * Tiêu đề cột
   */
  title: string | ReactNode;

  /**
   * Đường dẫn đến dữ liệu trong object (hỗ trợ dot notation)
   */
  dataIndex?: string | undefined;

  /**
   * Hàm render tùy chỉnh
   */
  render?: ((value: unknown, record: T, index: number) => ReactNode) | undefined;

  /**
   * C<PERSON> thể sắp xếp không
   */
  sortable?: boolean | undefined;

  /**
   * Chiều rộng cột
   */
  width?: number | string | undefined;

  /**
   * Căn chỉnh nội dung
   */
  align?: 'left' | 'center' | 'right' | undefined;

  /**
   * <PERSON><PERSON> định cột
   */
  fixed?: 'left' | 'right' | undefined;

  /**
   * Class tùy chỉnh
   */
  className?: string | undefined;
}

/**
 * Props cho TableRow
 */
export interface TableRowProps {
  /**
   * Nội dung của hàng
   */
  children?: ReactNode;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties;

  /**
   * Sự kiện onClick
   */
  onClick?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onDoubleClick
   */
  onDoubleClick?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onContextMenu
   */
  onContextMenu?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onMouseEnter
   */
  onMouseEnter?: (event: React.MouseEvent) => void;

  /**
   * Sự kiện onMouseLeave
   */
  onMouseLeave?: (event: React.MouseEvent) => void;

  /**
   * Các thuộc tính khác
   */
  [key: string]: unknown;
}

/**
 * Props cho TableCell
 */
export interface TableCellProps {
  /**
   * Nội dung của ô
   */
  children?: ReactNode | undefined;

  /**
   * Class tùy chỉnh
   */
  className?: string | undefined;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties | undefined;

  /**
   * Căn chỉnh nội dung
   */
  align?: 'left' | 'center' | 'right' | undefined;

  /**
   * Colspan
   */
  colSpan?: number | undefined;

  /**
   * Rowspan
   */
  rowSpan?: number | undefined;

  /**
   * Các thuộc tính khác
   */
  [key: string]: unknown;
}

/**
 * Props cho TablePagination
 */
export interface TablePaginationProps {
  /**
   * Tổng số mục
   */
  total: number;

  /**
   * Trang hiện tại
   */
  current: number;

  /**
   * Số mục trên mỗi trang
   */
  pageSize: number;

  /**
   * Callback khi trang thay đổi
   */
  onChange: (page: number, pageSize: number) => void;

  /**
   * Các tùy chọn số mục trên mỗi trang
   */
  pageSizeOptions?: number[] | undefined;

  /**
   * Hiển thị bộ chọn số mục trên mỗi trang
   */
  showSizeChanger?: boolean | undefined;

  /**
   * Hiển thị nút trang đầu và trang cuối
   */
  showFirstLastButtons?: boolean | undefined;

  /**
   * Hiển thị thông tin trang
   */
  showPageInfo?: boolean | undefined;
}

/**
 * Props cho Table
 */
export interface TableProps<T = unknown> {
  /**
   * Dữ liệu hiển thị
   */
  data: T[];

  /**
   * Cấu trúc cột
   */
  columns: TableColumn<T>[];

  /**
   * Trạng thái loading
   */
  loading?: boolean | undefined;

  /**
   * Loại hiển thị loading
   * @default 'overlay'
   */
  loadingType?: 'overlay' | 'inline' | 'skeleton' | undefined;

  /**
   * Text hiển thị khi loading
   */
  loadingText?: string | undefined;

  /**
   * Cho phép sắp xếp
   */
  sortable?: boolean | undefined;

  /**
   * Callback khi thay đổi sắp xếp, dùng để gọi API
   */
  onSortChange?: ((column: string | null, order: SortOrder) => void) | undefined;

  /**
   * Cho phép chọn hàng
   */
  selectable?: boolean | undefined;

  /**
   * Cho phép mở rộng hàng
   */
  expandable?: boolean | undefined;

  /**
   * Cấu hình phân trang
   */
  pagination?: boolean | TablePaginationProps | undefined;

  /**
   * Cấu hình chọn hàng
   */
  rowSelection?: {
    selectedRowKeys: React.Key[];
    onChange: (selectedRowKeys: React.Key[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => { disabled?: boolean; name?: string };
  };

  /**
   * Cấu hình mở rộng hàng
   */
  expandableConfig?: {
    expandedRowKeys?: React.Key[];
    onExpandedRowsChange?: (expandedRowKeys: React.Key[]) => void;
    expandedRowRender?: (record: T, index: number) => ReactNode;
    rowExpandable?: (record: T) => boolean;
  };

  /**
   * Kích thước bảng
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Có viền không
   */
  bordered?: boolean;

  /**
   * Có sọc không
   */
  striped?: boolean;

  /**
   * Có hover không
   */
  hoverable?: boolean;

  /**
   * Sự kiện onRow
   */
  onRow?: (record: T, index: number) => TableRowProps;

  /**
   * Sự kiện onHeaderRow
   */
  onHeaderRow?: (columns: TableColumn<T>[]) => TableRowProps;

  /**
   * Class tùy chỉnh
   */
  className?: string;

  /**
   * Style tùy chỉnh
   */
  style?: CSSProperties;

  /**
   * Khóa duy nhất cho mỗi hàng
   */
  rowKey?: string | ((record: T) => string);

  /**
   * Sort by default
   */
  defaultSort?: {
    column: string;
    order: SortOrder;
  };
}
