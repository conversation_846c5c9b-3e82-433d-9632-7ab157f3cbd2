import { TableColumn } from './types';
import TableRow from './TableRow';
import TableSortHeader from './TableSortHeader';

interface TableHeaderProps<T = unknown> {
  /**
   * Cấu trúc cột
   */
  columns: TableColumn<T>[];

  /**
   * Cho phép sắp xếp
   */
  sortable?: boolean | undefined;

  /**
   * Cột đang sắp xếp
   */
  sortColumn?: string | null | undefined;

  /**
   * Thứ tự sắp xếp
   */
  sortOrder?: 'asc' | 'desc' | null | undefined;

  /**
   * Callback khi thay đổi sắp xếp
   */
  onSort?: ((column: string, order: 'asc' | 'desc' | null) => void) | undefined;

  /**
   * Sự kiện onHeaderRow
   */
  onHeaderRow?: ((columns: TableColumn<T>[]) => Record<string, unknown>) | undefined;

  /**
   * Class tùy chỉnh
   */
  className?: string | undefined;
}

/**
 * Component header của bảng
 */
function TableHeader<T>({
  columns,
  sortable = false,
  sortColumn,
  sortOrder,
  onSort,
  onHeaderRow,
  className = '',
}: TableHeaderProps<T>) {
  // Xử lý sự kiện onHeaderRow
  const headerRowProps = onHeaderRow ? onHeaderRow(columns) : {};

  // Kết hợp tất cả các lớp
  const headerClasses = [className].join(' ');

  return (
    <thead className={headerClasses}>
      <TableRow {...headerRowProps}>
        {columns.map(column => {
          const { key, title, align = 'left', sortable: columnSortable, className = '' } = column;

          // Xác định các lớp căn chỉnh
          const alignClasses = {
            left: 'text-left',
            center: 'text-center',
            right: 'text-right',
          };

          // Kết hợp tất cả các lớp
          const headerCellClasses = [
            'px-4 py-3 font-medium text-gray-700 dark:text-gray-300',
            alignClasses[align],
            className,
          ].join(' ');

          // Nếu cột có thể sắp xếp và sortable được bật
          if (columnSortable && sortable) {
            return (
              <TableSortHeader
                key={key}
                column={key}
                title={title}
                sortOrder={sortColumn === key ? sortOrder || null : null}
                onSort={onSort}
                className={headerCellClasses}
              />
            );
          }

          // Nếu không, hiển thị header thông thường
          return (
            <th key={key} className={headerCellClasses}>
              {title}
            </th>
          );
        })}
      </TableRow>
    </thead>
  );
}

export default TableHeader;
