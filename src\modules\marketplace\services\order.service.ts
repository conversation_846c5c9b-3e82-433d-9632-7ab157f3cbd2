/**
 * Service for order API
 */

import { apiClient } from '@/shared/api';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  ApiPurchaseHistoryItem,
} from './marketplace-api.service';

/**
 * Base URL for order API
 */
const BASE_URL = '/user/marketplace/orders';

/**
 * Order service
 */
export const OrderService = {
  /**
   * Get purchase history
   */
  getPurchaseHistory: async (params?: Record<string, unknown>): Promise<PaginatedResult<ApiPurchaseHistoryItem>> => {
    try {
      console.log('🔍 Calling purchase history API with params:', params);
      console.log('🔍 API URL:', `${BASE_URL}/purchase-history`);

      const response = await apiClient.get<PaginatedResult<ApiPurchaseHistoryItem>>(
        `${BASE_URL}/purchase-history`,
        { params }
      );

      console.log('✅ Purchase history API response:', response);
      console.log('✅ Purchase history result:', response.result);
      console.log('✅ Purchase history items:', response.result?.items);

      return response.result;
    } catch (error: unknown) {
      console.error('❌ Error fetching purchase history:', error);
      console.error('❌ Error details:', {
        message: (error as Error)?.message,
        status: (error as { response?: { status?: number } })?.response?.status,
        data: (error as { response?: { data?: unknown } })?.response?.data,
        url: `${BASE_URL}/purchase-history`,
        params
      });
      throw error;
    }
  },
};
