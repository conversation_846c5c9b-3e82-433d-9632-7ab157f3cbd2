/**
 * SSE Client utility class
 */
import {
  SSEClientConfig,
  SSEConnectionState,
  SSEEvent,
  SSEConnectionInfo,
  SSESubscription,
  SSEMetrics
} from '@/shared/types/sse.types';
import { v4 as uuidv4 } from 'uuid';

/**
 * SSE Client class để quản lý kết nối Server-Sent Events
 */
export class SSEClient {
  private eventSource: EventSource | null = null;
  private config: Required<SSEClientConfig>;
  private subscriptions = new Map<string, SSESubscription>();
  private connectionInfo: SSEConnectionInfo;
  private metrics: SSEMetrics;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private isManuallyDisconnected = false;

  constructor(config: SSEClientConfig) {
    // Thiết lập cấu hình mặc định
    this.config = {
      autoReconnect: true,
      reconnectDelay: 3000,
      maxReconnectAttempts: 5,
      timeout: 30000,
      withCredentials: false,
      headers: {},
      onOpen: () => {},
      onError: () => {},
      onClose: () => {},
      onMessage: () => {},
      ...config,
    };

    // Khởi tạo connection info
    this.connectionInfo = {
      state: SSEConnectionState.DISCONNECTED,
      url: this.config.url,
      reconnectAttempts: 0,
      eventsReceived: 0,
    };

    // Khởi tạo metrics
    this.metrics = {
      totalEvents: 0,
      eventsByType: {},
      averageConnectionTime: 0,
      reconnectCount: 0,
      errorCount: 0,
      bytesReceived: 0,
    };
  }

  /**
   * Kết nối SSE
   */
  connect(): void {
    if (this.eventSource?.readyState === EventSource.OPEN) {
      console.warn('SSE already connected');
      return;
    }

    this.isManuallyDisconnected = false;
    this.updateConnectionState(SSEConnectionState.CONNECTING);

    try {
      // Tạo URL với headers nếu cần
      const url = new URL(this.config.url);

      // Thêm headers vào URL params nếu cần (một số server yêu cầu)
      if (this.config.headers) {
        Object.entries(this.config.headers).forEach(([key, value]) => {
          if (key.toLowerCase() === 'authorization') {
            url.searchParams.set('auth', value.replace('Bearer ', ''));
          }
        });
      }

      this.eventSource = new EventSource(url.toString(), {
        withCredentials: this.config.withCredentials,
      });

      this.setupEventListeners();
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * Ngắt kết nối SSE
   */
  disconnect(): void {
    this.isManuallyDisconnected = true;
    this.clearReconnectTimer();

    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    this.updateConnectionState(SSEConnectionState.DISCONNECTED);
  }

  /**
   * Subscribe vào một loại event
   */
  subscribe(eventType: string, handler: (event: SSEEvent) => void): string {
    const id = uuidv4();
    const subscription: SSESubscription = {
      id,
      eventType,
      handler,
      active: true,
      createdAt: new Date(),
    };

    this.subscriptions.set(id, subscription);

    // Nếu đã kết nối, đăng ký listener ngay
    if (this.eventSource) {
      this.eventSource.addEventListener(eventType, this.createEventHandler(subscription));
    }

    return id;
  }

  /**
   * Unsubscribe khỏi một subscription
   */
  unsubscribe(subscriptionId: string): void {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      subscription.active = false;
      this.subscriptions.delete(subscriptionId);

      // Xóa listener nếu cần
      if (this.eventSource) {
        this.eventSource.removeEventListener(
          subscription.eventType,
          this.createEventHandler(subscription)
        );
      }
    }
  }

  /**
   * Lấy thông tin kết nối
   */
  getConnectionInfo(): SSEConnectionInfo {
    return { ...this.connectionInfo };
  }

  /**
   * Lấy metrics
   */
  getMetrics(): SSEMetrics {
    return { ...this.metrics };
  }

  /**
   * Lấy danh sách subscriptions
   */
  getSubscriptions(): SSESubscription[] {
    return Array.from(this.subscriptions.values());
  }

  /**
   * Thiết lập event listeners cho EventSource
   */
  private setupEventListeners(): void {
    if (!this.eventSource) return;

    // Xử lý khi kết nối mở
    this.eventSource.onopen = (event) => {
      this.updateConnectionState(SSEConnectionState.CONNECTED);
      this.connectionInfo.connectedAt = new Date();
      this.connectionInfo.reconnectAttempts = 0;
      this.config.onOpen(event);
    };

    // Xử lý khi có lỗi
    this.eventSource.onerror = (event) => {
      this.metrics.errorCount++;
      this.handleError(new Error('SSE connection error'));
      this.config.onError(event);
    };

    // Xử lý message mặc định
    this.eventSource.onmessage = (event) => {
      this.handleMessage(event);
      this.config.onMessage(event);
    };

    // Đăng ký các custom event listeners
    this.subscriptions.forEach(subscription => {
      if (subscription.active && this.eventSource) {
        this.eventSource.addEventListener(
          subscription.eventType,
          this.createEventHandler(subscription)
        );
      }
    });
  }

  /**
   * Tạo event handler cho subscription
   */
  private createEventHandler(subscription: SSESubscription) {
    return (event: Event) => {
      if (!subscription.active) return;

      const messageEvent = event as MessageEvent;
      const sseEvent = this.parseSSEEvent(messageEvent);

      try {
        subscription.handler(sseEvent);
      } catch (error) {
        console.error('Error in SSE event handler:', error);
      }
    };
  }

  /**
   * Xử lý message từ SSE
   */
  private handleMessage(event: MessageEvent): void {
    const sseEvent = this.parseSSEEvent(event);

    // Cập nhật metrics
    this.metrics.totalEvents++;
    this.metrics.eventsByType[sseEvent.type] =
      (this.metrics.eventsByType[sseEvent.type] || 0) + 1;
    this.metrics.bytesReceived += new Blob([event.data]).size;
    this.connectionInfo.eventsReceived++;
  }

  /**
   * Parse SSE event từ MessageEvent
   */
  private parseSSEEvent(event: MessageEvent): SSEEvent {
    let data: unknown;

    try {
      data = JSON.parse(event.data);
    } catch {
      data = event.data;
    }

    const sseEvent: SSEEvent = {
      type: event.type || 'message',
      data,
      timestamp: Date.now(),
    };

    if (event.lastEventId) {
      sseEvent.id = event.lastEventId;
    }

    return sseEvent;
  }

  /**
   * Xử lý lỗi
   */
  private handleError(error: Error): void {
    this.connectionInfo.lastError = error;
    this.updateConnectionState(SSEConnectionState.ERROR);

    if (this.config.autoReconnect && !this.isManuallyDisconnected) {
      this.scheduleReconnect();
    }
  }

  /**
   * Lên lịch kết nối lại
   */
  private scheduleReconnect(): void {
    if (this.connectionInfo.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('Max reconnect attempts reached');
      return;
    }

    this.updateConnectionState(SSEConnectionState.RECONNECTING);
    this.connectionInfo.reconnectAttempts++;
    this.metrics.reconnectCount++;

    this.reconnectTimer = setTimeout(() => {
      this.connect();
    }, this.config.reconnectDelay);
  }

  /**
   * Cập nhật trạng thái kết nối
   */
  private updateConnectionState(state: SSEConnectionState): void {
    this.connectionInfo.state = state;

    if (state === SSEConnectionState.DISCONNECTED) {
      this.connectionInfo.lastDisconnectedAt = new Date();
    }
  }

  /**
   * Xóa timer reconnect
   */
  private clearReconnectTimer(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }
}
