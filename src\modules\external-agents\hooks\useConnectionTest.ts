import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { externalAgentService } from '../services';
import { EXTERNAL_AGENT_QUERY_KEYS } from '../constants';
import { ExternalAgent } from '../types';

// Test connection mutation
export const useConnectionTest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, timeout }: { id: string; timeout?: number }) =>
      externalAgentService.testConnection(id, timeout),
    onSuccess: (result, { id }) => {
      // Cache the test result
      queryClient.setQueryData(
        EXTERNAL_AGENT_QUERY_KEYS.TEST_CONNECTION(id),
        result
      );

      // Update agent status if test was successful
      if (result.success) {
        queryClient.setQueryData(
          EXTERNAL_AGENT_QUERY_KEYS.DETAIL(id),
          (oldData: ExternalAgent | undefined) => oldData ? {
            ...oldData,
            status: 'active' as const,
            lastConnectedAt: new Date().toISOString(),
          } : oldData
        );
      }
    },
  });
};

// Get cached connection test result
export const useConnectionTestResult = (id: string) => {
  return useQuery({
    queryKey: EXTERNAL_AGENT_QUERY_KEYS.TEST_CONNECTION(id),
    queryFn: () => null, // Don't fetch automatically
    enabled: false, // Only use cached data
    staleTime: Infinity, // Keep cached data fresh
  });
};

// Custom hook for connection testing with state management
export const useConnectionTester = (id: string) => {
  const testMutation = useConnectionTest();
  const cachedResult = useConnectionTestResult(id);

  const testConnection = (timeout?: number) => {
    return testMutation.mutate({ id, timeout });
  };

  const isTestingConnection = testMutation.isPending;
  const lastTestResult = cachedResult.data || testMutation.data;
  const testError = testMutation.error;

  return {
    testConnection,
    isTestingConnection,
    lastTestResult,
    testError,
    reset: testMutation.reset,
  };
};
