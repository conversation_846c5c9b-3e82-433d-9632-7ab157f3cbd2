import { z } from 'zod';
import { SchemaBuilderOptions } from './types';
import { VALIDATION_PATTERNS, VIETNAMESE_PATTERNS, BUSINESS_PATTERNS } from './patterns';

/**
 * Common validation schemas using Zod
 */
export const ValidationSchemas = {
  /**
   * Email schema
   */
  email: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .email(options?.t?.('validation.invalid_email') || 'Invalid email address')
      .min(1, options?.t?.('validation.required') || 'Email is required');
  },

  /**
   * Password schema with strength validation
   */
  password: (options?: SchemaBuilderOptions): z.ZodString => {
    const schema = z.string()
      .min(8, options?.t?.('validation.password_min_length') || 'Password must be at least 8 characters')
      .max(128, options?.t?.('validation.password_max_length') || 'Password must be at most 128 characters');

    if (options?.strict) {
      return schema.regex(
        VALIDATION_PATTERNS.strongPassword,
        options?.t?.('validation.password_strength') ||
        'Password must contain uppercase, lowercase, number and special character'
      );
    }

    return schema;
  },

  /**
   * Phone number schema
   */
  phone: (options?: SchemaBuilderOptions): z.ZodString => {
    const pattern = options?.locale === 'vi' ? VIETNAMESE_PATTERNS.phoneVN : VALIDATION_PATTERNS.phone;
    const message = options?.locale === 'vi'
      ? (options?.t?.('validation.invalid_phone_vn') || 'Invalid Vietnamese phone number')
      : (options?.t?.('validation.invalid_phone') || 'Invalid phone number');

    return z.string()
      .regex(pattern, message)
      .min(1, options?.t?.('validation.required') || 'Phone number is required');
  },

  /**
   * URL schema
   */
  url: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .url(options?.t?.('validation.invalid_url') || 'Invalid URL')
      .min(1, options?.t?.('validation.required') || 'URL is required');
  },

  /**
   * Date schema
   */
  date: (options?: SchemaBuilderOptions): z.ZodDate => {
    return z.date({
      required_error: options?.t?.('validation.required') || 'Date is required',
      invalid_type_error: options?.t?.('validation.invalid_date') || 'Invalid date',
    });
  },

  /**
   * Flexible date schema that accepts both string and Date objects
   * Useful for DatePicker components that might send either format
   */
  flexibleDate: (options?: SchemaBuilderOptions): z.ZodUnion<[z.ZodString, z.ZodDate]> => {
    return z.union([
      z.string().min(1, options?.t?.('validation.required') || 'Date is required'),
      z.date({
        required_error: options?.t?.('validation.required') || 'Date is required',
        invalid_type_error: options?.t?.('validation.invalid_date') || 'Invalid date',
      })
    ]);
  },

  /**
   * Future date schema
   */
  futureDate: (options?: SchemaBuilderOptions): z.ZodEffects<z.ZodDate, Date, Date> => {
    return z.date()
      .refine(
        (date) => date > new Date(),
        options?.t?.('validation.future_date') || 'Date must be in the future'
      );
  },

  /**
   * Past date schema
   */
  pastDate: (options?: SchemaBuilderOptions): z.ZodEffects<z.ZodDate, Date, Date> => {
    return z.date()
      .refine(
        (date) => date < new Date(),
        options?.t?.('validation.past_date') || 'Date must be in the past'
      );
  },

  /**
   * Age validation schema
   */
  age: (minAge: number, maxAge?: number, options?: SchemaBuilderOptions) => {
    let schema = z.date()
      .refine(
        (birthDate) => {
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())
            ? age - 1
            : age;
          return actualAge >= minAge;
        },
        options?.t?.('validation.min_age', { minAge }) || `Must be at least ${minAge} years old`
      );

    if (maxAge) {
      schema = schema.refine(
        (birthDate) => {
          const today = new Date();
          const age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          const actualAge = monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())
            ? age - 1
            : age;
          return actualAge <= maxAge;
        },
        options?.t?.('validation.max_age', { maxAge }) || `Must be at most ${maxAge} years old`
      ) as unknown as typeof schema; // Type assertion to handle Zod's complex type inference
    }

    return schema;
  },

  /**
   * Numeric schema
   */
  number: (min?: number, max?: number, options?: SchemaBuilderOptions): z.ZodNumber => {
    let schema = z.number({
      required_error: options?.t?.('validation.required') || 'Number is required',
      invalid_type_error: options?.t?.('validation.invalid_number') || 'Must be a valid number',
    });

    if (min !== undefined) {
      schema = schema.min(min, options?.t?.('validation.min_value', { min }) || `Must be at least ${min}`);
    }

    if (max !== undefined) {
      schema = schema.max(max, options?.t?.('validation.max_value', { max }) || `Must be at most ${max}`);
    }

    return schema;
  },

  /**
   * Positive number schema
   */
  positiveNumber: (options?: SchemaBuilderOptions): z.ZodNumber => {
    return z.number()
      .positive(options?.t?.('validation.positive_number') || 'Must be a positive number');
  },

  /**
   * Integer schema
   */
  integer: (min?: number, max?: number, options?: SchemaBuilderOptions): z.ZodNumber => {
    let schema = z.number()
      .int(options?.t?.('validation.invalid_integer') || 'Must be a valid integer');

    if (min !== undefined) {
      schema = schema.min(min, options?.t?.('validation.min_value', { min }) || `Must be at least ${min}`);
    }

    if (max !== undefined) {
      schema = schema.max(max, options?.t?.('validation.max_value', { max }) || `Must be at most ${max}`);
    }

    return schema;
  },

  /**
   * String schema with length validation
   */
  string: (minLength?: number, maxLength?: number, options?: SchemaBuilderOptions): z.ZodString => {
    let schema = z.string({
      required_error: options?.t?.('validation.required') || 'This field is required',
      invalid_type_error: options?.t?.('validation.invalid_string') || 'Must be a valid string',
    });

    if (minLength !== undefined) {
      schema = schema.min(minLength,
        options?.t?.('validation.min_length', { min: minLength }) || `Must be at least ${minLength} characters`
      );
    }

    if (maxLength !== undefined) {
      schema = schema.max(maxLength,
        options?.t?.('validation.max_length', { max: maxLength }) || `Must be at most ${maxLength} characters`
      );
    }

    return schema;
  },

  /**
   * Vietnamese name schema
   */
  vietnameseName: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .regex(
        VIETNAMESE_PATTERNS.nameVN,
        options?.t?.('validation.invalid_name_vn') || 'Invalid Vietnamese name'
      )
      .min(1, options?.t?.('validation.required') || 'Name is required');
  },

  /**
   * Vietnamese citizen ID schema
   */
  vietnameseCitizenId: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .regex(
        VIETNAMESE_PATTERNS.citizenIdVN,
        options?.t?.('validation.invalid_citizen_id_vn') || 'Invalid Vietnamese citizen ID'
      )
      .min(1, options?.t?.('validation.required') || 'Citizen ID is required');
  },

  /**
   * Currency schema
   */
  currency: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .regex(
        BUSINESS_PATTERNS.currency,
        options?.t?.('validation.invalid_currency') || 'Invalid currency format'
      )
      .min(1, options?.t?.('validation.required') || 'Amount is required');
  },

  /**
   * Credit card schema
   */
  creditCard: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .regex(
        VALIDATION_PATTERNS.creditCard,
        options?.t?.('validation.invalid_credit_card') || 'Invalid credit card number'
      )
      .min(1, options?.t?.('validation.required') || 'Credit card number is required');
  },

  /**
   * UUID schema
   */
  uuid: (options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .uuid(options?.t?.('validation.invalid_uuid') || 'Invalid UUID format');
  },

  /**
   * File schema
   */
  file: (
    maxSizeInBytes?: number,
    allowedTypes?: string[],
    options?: SchemaBuilderOptions
  ): z.ZodType<File> => {
    let schema = z.instanceof(File, {
      message: options?.t?.('validation.invalid_file') || 'Must be a valid file',
    });

    if (maxSizeInBytes) {
      schema = schema.refine(
        (file) => file.size <= maxSizeInBytes,
        options?.t?.('validation.file_too_large', {
          maxSize: Math.round(maxSizeInBytes / 1024 / 1024 * 100) / 100
        }) || `File size must be less than ${Math.round(maxSizeInBytes / 1024 / 1024 * 100) / 100}MB`
      );
    }

    if (allowedTypes && allowedTypes.length > 0) {
      schema = schema.refine(
        (file) => allowedTypes.includes(file.type),
        options?.t?.('validation.invalid_file_type', {
          allowedTypes: allowedTypes.join(', ')
        }) || `File type must be one of: ${allowedTypes.join(', ')}`
      );
    }

    return schema;
  },

  /**
   * Array schema with length validation
   */
  array: <T>(
    itemSchema: z.ZodType<T>,
    minItems?: number,
    maxItems?: number,
    options?: SchemaBuilderOptions
  ): z.ZodArray<z.ZodType<T>> => {
    let schema = z.array(itemSchema, {
      required_error: options?.t?.('validation.required') || 'This field is required',
      invalid_type_error: options?.t?.('validation.invalid_array') || 'Must be a valid array',
    });

    if (minItems !== undefined) {
      schema = schema.min(minItems,
        options?.t?.('validation.min_items', { min: minItems }) || `Must have at least ${minItems} items`
      );
    }

    if (maxItems !== undefined) {
      schema = schema.max(maxItems,
        options?.t?.('validation.max_items', { max: maxItems }) || `Must have at most ${maxItems} items`
      );
    }

    return schema;
  },

  /**
   * Confirm password schema
   */
  confirmPassword: (_passwordField: string, options?: SchemaBuilderOptions): z.ZodString => {
    return z.string()
      .min(1, options?.t?.('validation.required') || 'Please confirm your password');
  },
};

/**
 * Schema factory functions for common use cases
 */
export const SchemaFactories = {
  /**
   * User registration schema
   */
  userRegistration: (options?: SchemaBuilderOptions) => {
    return z.object({
      email: ValidationSchemas.email(options),
      password: ValidationSchemas.password({ ...options, strict: true }),
      confirmPassword: ValidationSchemas.confirmPassword('password', options),
      firstName: ValidationSchemas.string(2, 50, options),
      lastName: ValidationSchemas.string(2, 50, options),
      phone: ValidationSchemas.phone(options).optional(),
      birthDate: ValidationSchemas.age(13, 120, options).optional(),
    }).refine(
      (data) => data.password === data.confirmPassword,
      {
        message: options?.t?.('validation.password_mismatch') || 'Passwords do not match',
        path: ['confirmPassword'],
      }
    );
  },

  /**
   * User login schema
   */
  userLogin: (options?: SchemaBuilderOptions) => {
    return z.object({
      email: ValidationSchemas.email(options),
      password: ValidationSchemas.string(1, undefined, options),
      rememberMe: z.boolean().optional(),
    });
  },

  /**
   * Contact form schema
   */
  contactForm: (options?: SchemaBuilderOptions) => {
    return z.object({
      name: ValidationSchemas.string(2, 100, options),
      email: ValidationSchemas.email(options),
      phone: ValidationSchemas.phone(options).optional(),
      subject: ValidationSchemas.string(5, 200, options),
      message: ValidationSchemas.string(10, 1000, options),
    });
  },

  /**
   * Profile update schema
   */
  profileUpdate: (options?: SchemaBuilderOptions) => {
    return z.object({
      firstName: ValidationSchemas.string(2, 50, options),
      lastName: ValidationSchemas.string(2, 50, options),
      phone: ValidationSchemas.phone(options).optional(),
      birthDate: ValidationSchemas.age(13, 120, options).optional(),
      bio: ValidationSchemas.string(0, 500, options).optional(),
      website: ValidationSchemas.url(options).optional(),
      avatar: ValidationSchemas.file(5 * 1024 * 1024, ['image/jpeg', 'image/png', 'image/webp'], options).optional(),
    });
  },

  /**
   * Address schema
   */
  address: (options?: SchemaBuilderOptions) => {
    return z.object({
      street: ValidationSchemas.string(5, 200, options),
      city: ValidationSchemas.string(2, 100, options),
      state: ValidationSchemas.string(2, 100, options).optional(),
      zipCode: ValidationSchemas.string(5, 10, options),
      country: ValidationSchemas.string(2, 100, options),
    });
  },

  /**
   * Payment schema
   */
  payment: (options?: SchemaBuilderOptions) => {
    return z.object({
      cardNumber: ValidationSchemas.creditCard(options),
      expiryMonth: ValidationSchemas.integer(1, 12, options),
      expiryYear: ValidationSchemas.integer(new Date().getFullYear(), new Date().getFullYear() + 20, options),
      cvv: ValidationSchemas.string(3, 4, options),
      cardholderName: ValidationSchemas.string(2, 100, options),
      billingAddress: SchemaFactories.address(options),
    });
  },
};

/**
 * Schema composition utilities
 */
export const SchemaUtils = {
  /**
   * Make all fields optional
   */
  partial: <T extends z.ZodRawShape>(schema: z.ZodObject<T>): z.ZodObject<{ [K in keyof T]: z.ZodOptional<T[K]> }> => {
    return schema.partial();
  },

  /**
   * Pick specific fields from schema
   */
  pick: <T extends z.ZodRawShape, K extends keyof T>(
    schema: z.ZodObject<T>,
    keys: K[]
  ): z.ZodObject<Pick<T, K>> => {
    // Use type assertion to bypass Zod's complex type constraints
    const pickObj = keys.reduce((acc, key) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (acc as any)[key] = true;
      return acc;
    }, {} as Record<string, true>);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (schema as any).pick(pickObj) as z.ZodObject<Pick<T, K>>;
  },

  /**
   * Omit specific fields from schema
   */
  omit: <T extends z.ZodRawShape, K extends keyof T>(
    schema: z.ZodObject<T>,
    keys: K[]
  ): z.ZodObject<Omit<T, K>> => {
    // Use type assertion to bypass Zod's complex type constraints
    const omitObj = keys.reduce((acc, key) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (acc as any)[key] = true;
      return acc;
    }, {} as Record<string, true>);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return (schema as any).omit(omitObj) as z.ZodObject<Omit<T, K>>;
  },

  /**
   * Extend schema with additional fields
   */
  extend: <T extends z.ZodRawShape, U extends z.ZodRawShape>(
    schema: z.ZodObject<T>,
    extension: U
  ): z.ZodObject<T & U> => {
    return schema.extend(extension) as z.ZodObject<T & U>;
  },

  /**
   * Merge two schemas
   */
  merge: <T extends z.ZodRawShape, U extends z.ZodRawShape>(
    schema1: z.ZodObject<T>,
    schema2: z.ZodObject<U>
  ): z.ZodObject<T & U> => {
    return schema1.merge(schema2) as z.ZodObject<T & U>;
  },

  /**
   * Create conditional schema
   */
  conditional: <T>(
    condition: (data: unknown) => boolean,
    trueSchema: z.ZodType<T>,
    falseSchema: z.ZodType<T>
  ): z.ZodType<T> => {
    return z.any().superRefine((data, ctx) => {
      const schema = condition(data) ? trueSchema : falseSchema;
      const result = schema.safeParse(data);

      if (!result.success) {
        result.error.issues.forEach(issue => {
          ctx.addIssue(issue);
        });
      }
    }) as z.ZodType<T>;
  },
};
