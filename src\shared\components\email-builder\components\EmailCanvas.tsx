import React, { useRef } from 'react';
import { EmailElement as EmailElementType } from '../types';
import { Typography } from '@/shared/components/common';
import EmailElement from './EmailElement';

interface EmailCanvasProps {
  emailElements: EmailElementType[];
  selectedElement: EmailElementType | null;
  selectedIndex: number | null;
  viewMode: 'desktop' | 'tablet' | 'mobile';
  onSelectElement: (element: EmailElementType | null, index: number) => void;
  onDeleteElement: () => void;
  onMoveElementUp: () => void;
  onMoveElementDown: () => void;
  onDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  showPreview: boolean;
}

const EmailCanvas: React.FC<EmailCanvasProps> = ({
  emailElements,
  selectedElement,
  selectedIndex,
  viewMode,
  onSelectElement,
  onDeleteElement,
  onMoveElementUp,
  onMoveElementDown,
  onDrop,
  showPreview
}) => {
  const canvasRef = useRef<HTMLDivElement>(null);

  // Xử lý khi kéo phần tử vào canvas
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'copy';
  };

  // Lấy chiều rộng canvas dựa trên chế độ xem
  const getCanvasWidth = () => {
    switch (viewMode) {
      case 'mobile':
        return '375px';
      case 'tablet':
        return '768px';
      case 'desktop':
      default:
        return '100%';
    }
  };

  return (
    <div className="flex-1 overflow-auto bg-muted p-4">
      <div
        ref={canvasRef}
        className={`mx-auto bg-card shadow-md border border-border overflow-hidden ${showPreview ? 'pointer-events-none' : ''}`}
        style={{
          width: getCanvasWidth(),
          maxWidth: '100%',
          minHeight: '500px',
          transition: 'width 0.3s ease'
        }}
        onDragOver={handleDragOver}
        onDrop={onDrop}
        onClick={() => {
          // Bỏ chọn phần tử khi click vào khu vực trống
          if (selectedElement) {
            // Sử dụng null thay vì type assertion
            onSelectElement(null, -1);
          }
        }}
      >
        {emailElements.length === 0 ? (
          <div className="flex items-center justify-center h-full min-h-[300px] text-muted-foreground text-center p-4">
            <div>
              <Typography variant="body1" className="mb-2">
                Kéo và thả các phần tử từ panel bên trái vào đây
              </Typography>
              <Typography variant="caption">
                hoặc chọn một mẫu email để bắt đầu
              </Typography>
            </div>
          </div>
        ) : (
          emailElements.map((element, index) => (
            <EmailElement
              key={element.id}
              element={element}
              index={index}
              isSelected={selectedIndex === index}
              onSelect={onSelectElement}
              onDelete={onDeleteElement}
              onMoveUp={onMoveElementUp}
              onMoveDown={onMoveElementDown}
              totalElements={emailElements.length}
            />
          ))
        )}
      </div>
    </div>
  );
};

export default EmailCanvas;
