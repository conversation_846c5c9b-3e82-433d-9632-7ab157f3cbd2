import {
  ValidationRule,
  ImportValidationError,
  ExcelData,
  ColumnMapping,
} from '../types/customer-import.types';

/**
 * Interface cho validation context
 */
export interface ValidationContext {
  row: number;
  column: string;
  field: string;
  value: unknown;
  allRowData: Record<string, unknown>;
}

/**
 * Interface cho custom validator function
 */
export type CustomValidator = (context: ValidationContext) => string | null;

/**
 * Service xử lý validation logic
 */
export const ValidationService = {
  /**
   * Validate email format
   * @param email Email string
   * @returns True if valid
   */
  validateEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate phone number format
   * @param phone Phone string
   * @returns True if valid
   */
  validatePhone: (phone: string): boolean => {
    // Remove all non-digit characters
    const cleanPhone = phone.replace(/\D/g, '');
    // Check if it's between 10-15 digits
    return cleanPhone.length >= 10 && cleanPhone.length <= 15;
  },

  /**
   * Validate required field
   * @param value Field value
   * @returns True if not empty
   */
  validateRequired: (value: unknown): boolean => {
    if (value === null || value === undefined) return false;
    if (typeof value === 'string') return value.trim().length > 0;
    return true;
  },

  /**
   * Validate string length
   * @param value String value
   * @param min Minimum length
   * @param max Maximum length
   * @returns True if within range
   */
  validateLength: (value: string, min?: number, max?: number): boolean => {
    const length = value?.length || 0;
    if (min !== undefined && length < min) return false;
    if (max !== undefined && length > max) return false;
    return true;
  },

  /**
   * Validate regex pattern
   * @param value String value
   * @param pattern Regex pattern
   * @returns True if matches
   */
  validateRegex: (value: string, pattern: string): boolean => {
    try {
      const regex = new RegExp(pattern);
      return regex.test(value);
    } catch {
      return false;
    }
  },

  /**
   * Apply single validation rule
   * @param rule Validation rule
   * @param context Validation context
   * @returns Error message or null if valid
   */
  applyValidationRule: (
    rule: ValidationRule,
    context: ValidationContext
  ): string | null => {
    if (!rule.enabled) return null;

    const { value } = context;
    const stringValue = value?.toString() || '';

    switch (rule.type) {
      case 'required':
        return ValidationService.validateRequired(value) ? null : rule.message;

      case 'email':
        if (!stringValue) return null; // Skip if empty (use required rule for that)
        return ValidationService.validateEmail(stringValue) ? null : rule.message;

      case 'phone':
        if (!stringValue) return null; // Skip if empty
        return ValidationService.validatePhone(stringValue) ? null : rule.message;

      case 'length': {
        if (!stringValue) return null; // Skip if empty
        const lengthConfig = rule.value as { min?: number; max?: number };
        return ValidationService.validateLength(
          stringValue,
          lengthConfig?.min,
          lengthConfig?.max
        ) ? null : rule.message;
      }

      case 'regex': {
        if (!stringValue) return null; // Skip if empty
        const pattern = rule.value as string;
        return ValidationService.validateRegex(stringValue, pattern) ? null : rule.message;
      }

      case 'custom':
        // Custom validation would be handled by external validator
        return null;

      default:
        return null;
    }
  },

  /**
   * Validate single row of data
   * @param rowData Row data object
   * @param rowIndex Row index
   * @param mappings Column mappings
   * @param validationRules Validation rules
   * @returns Array of validation errors
   */
  validateRow: (
    rowData: Record<string, unknown>,
    rowIndex: number,
    mappings: ColumnMapping[],
    validationRules: ValidationRule[]
  ): ImportValidationError[] => {
    const errors: ImportValidationError[] = [];

    // Create field to column mapping
    const fieldToColumnMap = new Map<string, string>();
    mappings.forEach(mapping => {
      if (mapping.customerField) {
        fieldToColumnMap.set(mapping.customerField, mapping.excelColumn);
      }
    });

    // Validate each field
    validationRules.forEach(rule => {
      const column = fieldToColumnMap.get(rule.field);
      if (!column) return; // Field not mapped

      const value = rowData[column];
      const context: ValidationContext = {
        row: rowIndex,
        column,
        field: rule.field,
        value,
        allRowData: rowData,
      };

      const errorMessage = ValidationService.applyValidationRule(rule, context);
      if (errorMessage) {
        errors.push({
          row: rowIndex,
          column,
          field: rule.field,
          value: value?.toString() || '',
          message: errorMessage,
        });
      }
    });

    return errors;
  },

  /**
   * Validate all data
   * @param excelData Excel data
   * @param mappings Column mappings
   * @param validationRules Validation rules
   * @returns Validation errors and warnings
   */
  validateAllData: (
    excelData: ExcelData,
    mappings: ColumnMapping[],
    validationRules: ValidationRule[]
  ) => {
    const errors: ImportValidationError[] = [];
    const warnings: ImportValidationError[] = [];

    // Convert rows to objects for easier processing
    const rowObjects = excelData.rows.map(row => {
      const obj: Record<string, unknown> = {};
      excelData.headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });

    // Validate each row
    rowObjects.forEach((rowData, index) => {
      const rowErrors = ValidationService.validateRow(
        rowData,
        index + 1, // 1-based row numbering
        mappings,
        validationRules
      );
      errors.push(...rowErrors);
    });

    // Check for duplicates
    const duplicateErrors = ValidationService.findDuplicates(
      rowObjects,
      mappings
    );
    warnings.push(...duplicateErrors);

    return { errors, warnings };
  },

  /**
   * Find duplicate rows
   * @param rowObjects Row data objects
   * @param mappings Column mappings
   * @param headers Excel headers
   * @returns Duplicate warnings
   */
  findDuplicates: (
    rowObjects: Record<string, unknown>[],
    mappings: ColumnMapping[]
  ): ImportValidationError[] => {
    const warnings: ImportValidationError[] = [];
    
    // Find key fields for duplicate detection (email, phone, name)
    const keyFields = ['email', 'phone', 'name'];
    const keyColumns = keyFields
      .map(field => mappings.find(m => m.customerField === field)?.excelColumn)
      .filter(Boolean) as string[];

    if (keyColumns.length === 0) return warnings;

    // Group rows by key values
    const rowGroups = new Map<string, number[]>();
    
    rowObjects.forEach((row, index) => {
      const keyValues = keyColumns
        .map(col => row[col]?.toString()?.trim().toLowerCase() || '')
        .filter(val => val.length > 0);
      
      if (keyValues.length > 0) {
        const key = keyValues.join('|');
        if (!rowGroups.has(key)) {
          rowGroups.set(key, []);
        }
        rowGroups.get(key)!.push(index);
      }
    });

    // Find duplicates
    rowGroups.forEach((rowIndexes, key) => {
      if (rowIndexes.length > 1) {
        rowIndexes.slice(1).forEach(rowIndex => {
          warnings.push({
            row: rowIndex + 1,
            column: keyColumns[0],
            field: 'duplicate',
            value: key,
            message: `Duplicate record found (rows: ${rowIndexes.map(i => i + 1).join(', ')})`,
          });
        });
      }
    });

    return warnings;
  },

  /**
   * Create validation summary
   * @param excelData Excel data
   * @param errors Validation errors
   * @param warnings Validation warnings
   * @returns Validation summary
   */
  createValidationSummary: (
    excelData: ExcelData,
    errors: ImportValidationError[],
    warnings: ImportValidationError[]
  ) => {
    const totalRows = excelData.rows.length;
    const errorRows = new Set(errors.map(error => error.row));
    const warningRows = new Set(warnings.filter(w => w.field === 'duplicate').map(w => w.row));
    
    return {
      totalRows,
      validRows: totalRows - errorRows.size,
      invalidRows: errorRows.size,
      duplicateRows: warningRows.size,
    };
  },

  /**
   * Get validation rules by field
   * @param field Field name
   * @param allRules All validation rules
   * @returns Rules for specific field
   */
  getRulesForField: (field: string, allRules: ValidationRule[]): ValidationRule[] => {
    return allRules.filter(rule => rule.field === field && rule.enabled);
  },

  /**
   * Create custom validation rule
   * @param id Rule ID
   * @param field Field name
   * @param validator Custom validator function
   * @param message Error message
   * @returns Custom validation rule
   */
  createCustomRule: (
    id: string,
    field: string,
    validator: CustomValidator,
    message: string
  ): ValidationRule => {
    return {
      id,
      field,
      type: 'custom',
      value: validator,
      message,
      enabled: true,
    };
  },
};

export default ValidationService;
