import { useState, useCallback } from 'react';
import { NotificationType } from '@/shared/components/common/Notification/Notification.tsx';
import { NotificationItem } from '@/shared/components/layout/chat-panel/NotificationContainer';

const useNotification = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const addNotification = useCallback(
    (type: NotificationType, message: string, duration = 5000) => {
      const id = `notification-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
      const newNotification: NotificationItem = {
        id,
        type,
        message,
        duration,
      };

      setNotifications(prev => [...prev, newNotification]);
      return id;
    },
    []
  );

  const removeNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  const clearNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications,
  };
};

export default useNotification;
