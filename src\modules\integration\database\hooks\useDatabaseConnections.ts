import { useQuery } from '@tanstack/react-query';
import { DatabaseConnectionQueryParams } from '../types';
import { databaseIntegrationService, DatabaseIntegrationService } from '../services';
import { DATABASE_INTEGRATION_QUERY_KEYS } from '../constants';

/**
 * Hook for fetching database connections list
 */
export const useDatabaseConnections = (params?: DatabaseConnectionQueryParams) => {
  const queryParams = params ? { ...params } as Record<string, unknown> : {};
  return useQuery({
    queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION_LIST(queryParams),
    queryFn: () => databaseIntegrationService.getConnectionsWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

/**
 * Hook for fetching a single database connection
 */
export const useDatabaseConnection = (id: string, enabled = true) => {
  return useQuery({
    queryKey: DATABASE_INTEGRATION_QUERY_KEYS.CONNECTION(id),
    queryFn: () => DatabaseIntegrationService.getConnection(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
  });
};

/**
 * Hook for getting active database connections only
 */
export const useActiveDatabaseConnections = () => {
  return useDatabaseConnections({
    status: 'active',
    limit: 100, // Get all active connections
    sortBy: 'name',
    sortOrder: 'asc',
  });
};

/**
 * Hook for getting the default database connection
 */
export const useDefaultDatabaseConnection = () => {
  const { data: connectionsData, ...rest } = useDatabaseConnections({
    limit: 100,
  });

  const defaultConnection = connectionsData?.items.find(connection => connection.isDefault);

  return {
    ...rest,
    data: defaultConnection,
    isLoading: rest.isLoading,
    error: rest.error,
  };
};

/**
 * Hook for getting database connections by type
 */
export const useDatabaseConnectionsByType = (type: string) => {
  return useDatabaseConnections({
    type: type as DatabaseConnectionQueryParams['type'],
    limit: 100,
    sortBy: 'name',
    sortOrder: 'asc',
  });
};
