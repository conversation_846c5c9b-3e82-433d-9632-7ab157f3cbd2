import { createContext, useState, useEffect, ReactNode } from 'react';
import i18n from 'i18next';
import { availableLanguages } from '@/lib/i18n';

export type LanguageCode = 'vi' | 'en' | 'zh';

export interface LanguageContextType {
  language: LanguageCode;
  setLanguage: (code: LanguageCode) => void;
  availableLanguages: { code: string; name: string }[];
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageProviderProps {
  children: ReactNode;
}

export const LanguageProvider = ({ children }: LanguageProviderProps) => {
  const [language, setLanguageState] = useState<LanguageCode>(() => {
    // Check if language is stored in localStorage
    const savedLanguage = localStorage.getItem('language') as LanguageCode | null;
    return savedLanguage || 'vi'; // Default to Vietnamese
  });

  useEffect(() => {
    // Update localStorage when language changes
    localStorage.setItem('language', language);

    // Update i18n language
    i18n.changeLanguage(language);
  }, [language]);

  const setLanguage = (code: LanguageCode): void => {
    setLanguageState(code);
  };

  const value = {
    language,
    setLanguage,
    availableLanguages,
  };

  return <LanguageContext.Provider value={value}>{children}</LanguageContext.Provider>;
};

export default LanguageContext;
