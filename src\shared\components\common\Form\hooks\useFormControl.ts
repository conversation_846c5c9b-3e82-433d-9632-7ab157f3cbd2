import { useCallback, useMemo, useRef, useEffect } from 'react';
import {
  FormControlSize,
  FormControlColor,
  CONTROL_SIZE_CLASSES,
  LABEL_SIZE_CLASSES,
  DOT_SIZE_CLASSES,
  getColorClasses,
  getAriaAttributes,
} from '../utils/formControlUtils';

interface UseFormControlProps {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  size?: FormControlSize;
  color?: FormControlColor;
  indeterminate?: boolean;
  onBlur?: () => void;
}

/**
 * Custom hook for form control components (Checkbox, Radio)
 * Provides shared logic and styling for form controls
 */
export function useFormControl({
  checked = false,
  onChange,
  disabled = false,
  size = 'md',
  color = 'primary',
  indeterminate,
  onBlur,
}: UseFormControlProps) {
  // Handle indeterminate state for Checkbox
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (inputRef.current && indeterminate !== undefined) {
      inputRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  // Handle onChange event
  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!disabled && onChange) {
        onChange(e.target.checked);

        // Call onBlur to trigger React Hook Form validation
        if (onBlur) {
          onBlur();
        }
      }
    },
    [disabled, onChange, onBlur]
  );

  // Memoize size classes
  const sizeClasses = useMemo(() => CONTROL_SIZE_CLASSES[size], [size]);
  const labelSizeClasses = useMemo(() => LABEL_SIZE_CLASSES[size], [size]);
  const dotSizeClasses = useMemo(() => DOT_SIZE_CLASSES[size], [size]);

  // Memoize color classes
  const colorClasses = useMemo(() => getColorClasses(color, checked), [color, checked]);

  // Memoize ARIA attributes
  const ariaAttributes = useMemo(
    () => getAriaAttributes(checked, disabled, indeterminate),
    [checked, disabled, indeterminate]
  );

  // Common label classes
  const labelClasses = useMemo(
    () =>
      `inline-flex items-center ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}`,
    [disabled]
  );

  // Common text classes for label text
  const textClasses = useMemo(
    () =>
      `ml-2 ${labelSizeClasses} ${disabled ? 'text-gray-500 dark:text-gray-400' : 'text-gray-700 dark:text-gray-200'}`,
    [labelSizeClasses, disabled]
  );

  return {
    inputRef,
    handleChange,
    sizeClasses,
    labelSizeClasses,
    dotSizeClasses,
    colorClasses,
    ariaAttributes,
    labelClasses,
    textClasses,
  };
}
