import { useEffect, useRef, useState } from 'react';

/**
 * Hook to manage focus trap within a component
 * @returns Object with ref to set on container and methods to manage focus trap
 */
export const useFocusTrap = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isActive, setIsActive] = useState(false);

  // Activate focus trap
  const activate = () => setIsActive(true);

  // Deactivate focus trap
  const deactivate = () => setIsActive(false);

  useEffect(() => {
    if (!isActive || !containerRef.current) return;

    // Get all focusable elements
    const focusableElements = containerRef.current.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    // Focus first element when trap is activated
    if (firstElement) {
      firstElement.focus();
    }

    // Handle tab key to keep focus within container
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // If shift+tab and on first element, move to last element
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        // If tab and on last element, move to first element
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    // Save previous active element to restore focus later
    const previousActiveElement = document.activeElement as HTMLElement;

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      // Restore focus when trap is deactivated
      if (previousActiveElement) {
        previousActiveElement.focus();
      }
    };
  }, [isActive]);

  return { containerRef, activate, deactivate };
};

/**
 * Hook to announce messages to screen readers
 * @returns Function to announce messages
 */
export const useAnnounce = () => {
  const [announcements, setAnnouncements] = useState<string[]>([]);

  // Create or get the live region element
  useEffect(() => {
    let liveRegion = document.getElementById('a11y-live-region');

    if (!liveRegion) {
      liveRegion = document.createElement('div');
      liveRegion.id = 'a11y-live-region';
      liveRegion.className = 'sr-only';
      liveRegion.setAttribute('aria-live', 'polite');
      liveRegion.setAttribute('aria-atomic', 'true');
      document.body.appendChild(liveRegion);
    }

    // Update the live region with the latest announcement
    if (announcements.length > 0) {
      liveRegion.textContent = announcements[announcements.length - 1] || null;
    }

    // Clean up
    return () => {
      if (liveRegion && announcements.length === 0) {
        document.body.removeChild(liveRegion);
      }
    };
  }, [announcements]);

  // Function to announce a message
  const announce = (message: string) => {
    setAnnouncements(prev => [...prev, message]);

    // Clear announcement after a delay
    setTimeout(() => {
      setAnnouncements(prev => prev.filter(item => item !== message));
    }, 3000);
  };

  return announce;
};

/**
 * Hook to detect if user is navigating with keyboard
 * @returns Boolean indicating if user is using keyboard navigation
 */
export const useKeyboardNavigation = () => {
  const [isKeyboardUser, setIsKeyboardUser] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setIsKeyboardUser(true);
      }
    };

    const handleMouseDown = () => {
      setIsKeyboardUser(false);
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('mousedown', handleMouseDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  return isKeyboardUser;
};

/**
 * Hook to manage skip links for keyboard navigation
 * @returns Object with ref to set on main content and function to create skip link
 */
export const useSkipLink = () => {
  const mainContentRef = useRef<HTMLElement>(null);

  // Function to create a skip link
  const createSkipLink = (label = 'Skip to main content') => {
    // Return a function that creates the element when called
    // This avoids JSX in a .ts file
    return () => {
      const link = document.createElement('a');
      link.href = '#main-content';
      link.className =
        'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:p-4 focus:bg-white focus:text-primary';
      link.textContent = label;
      link.onclick = e => {
        e.preventDefault();
        mainContentRef.current?.focus();
      };
      return link;
    };
  };

  return { mainContentRef, createSkipLink };
};

export default {
  useFocusTrap,
  useAnnounce,
  useKeyboardNavigation,
  useSkipLink,
};
