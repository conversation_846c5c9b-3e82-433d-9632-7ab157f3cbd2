import React, { useCallback, useMemo, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMenu } from './MenuContext';
import Icon, { IconName } from '@/shared/components/common/Icon';
import { Tooltip, Typography, Badge, Hidden } from '@/shared/components/common';
import { useKeyboardNavigation } from '@/shared/hooks/common';

export interface MenuItemProps {
  /**
   * Key duy nhất của menu item
   */
  itemKey: string;

  /**
   * Label hiển thị
   */
  label: React.ReactNode;

  /**
   * Icon hiển thị (tùy chọn)
   */
  icon?: IconName | React.ReactNode;

  /**
   * Đường dẫn (tùy chọn)
   */
  path?: string;

  /**
   * Trạng thái disabled
   */
  disabled?: boolean;

  /**
   * Sự kiện khi click
   */
  onClick?: (e: React.MouseEvent) => void;

  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Tooltip hiển thị khi hover
   */
  tooltip?: string;

  /**
   * Trạng thái active (kh<PERSON>c với selected)
   */
  active?: boolean;

  /**
   * Badge hiển thị bên cạnh label (ví dụ: "New", "Hot")
   */
  badge?: React.ReactNode;

  /**
   * Màu sắc của badge
   */
  badgeColor?: 'primary' | 'success' | 'danger' | 'warning' | 'info';

  /**
   * Phím tắt hiển thị bên phải (ví dụ: "Ctrl+K")
   */
  shortcut?: string;

  /**
   * Dữ liệu bổ sung
   */
  data?: Record<string, unknown>;
}

const MenuItem: React.FC<MenuItemProps> = ({
  itemKey,
  label,
  icon,
  path,
  disabled = false,
  onClick,
  className = '',
  tooltip,
  active = false,
  badge,
  badgeColor = 'primary',
  shortcut,
}) => {
  const navigate = useNavigate();
  const { mode, theme, selectedKey, setSelectedKey, collapsed } = useMenu();
  const itemRef = useRef<HTMLDivElement>(null);
  const isKeyboardUser = useKeyboardNavigation();

  // Kiểm tra xem item có đang được chọn không
  const isSelected = selectedKey === itemKey;

  // Xử lý khi click vào menu item
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (disabled) {
        e.preventDefault();
        return;
      }

      // Cập nhật selectedKey
      setSelectedKey(itemKey);

      // Gọi onClick callback nếu có
      if (onClick) {
        onClick(e);
      }

      // Điều hướng nếu có path
      if (path) {
        navigate(path);
      }
    },
    [disabled, itemKey, navigate, onClick, path, setSelectedKey]
  );

  // Xử lý keyboard navigation
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (disabled) return;

      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleClick(e as unknown as React.MouseEvent);
      }
    },
    [disabled, handleClick]
  );

  // Focus vào item khi được chọn (chỉ khi sử dụng bàn phím)
  useEffect(() => {
    if (isSelected && itemRef.current && isKeyboardUser) {
      itemRef.current.focus();
    }
  }, [isSelected, isKeyboardUser]);

  // Xác định các class dựa trên props và state
  const itemClasses = useMemo(() => {
    const baseClasses =
      'flex items-center transition-all duration-200 cursor-pointer select-none outline-none focus:ring-2 focus:ring-primary/30';
    const modeClasses = {
      horizontal: 'px-4 py-2',
      vertical: 'px-4 py-2',
      inline: 'px-4 py-2',
    }[mode];

    const themeClasses = {
      default: `text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-dark-lighter ${
        isSelected ? 'bg-gray-100 dark:bg-dark-lighter text-primary dark:text-primary-light' : ''
      } ${active && !isSelected ? 'bg-gray-50 dark:bg-dark-lighter' : ''}`,
      dark: `text-gray-200 hover:bg-dark-lighter ${
        isSelected ? 'bg-dark-lighter text-primary-light' : ''
      } ${active && !isSelected ? 'bg-dark-lighter/50' : ''}`,
      light: `text-gray-700 hover:bg-gray-100 ${isSelected ? 'bg-gray-100 text-primary' : ''} ${
        active && !isSelected ? 'bg-gray-50' : ''
      }`,
    }[theme];

    const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed pointer-events-none' : '';
    const collapsedClasses = collapsed && mode !== 'horizontal' ? 'justify-center' : '';

    return `${baseClasses} ${modeClasses} ${themeClasses} ${disabledClasses} ${collapsedClasses} ${className}`;
  }, [mode, theme, isSelected, active, disabled, collapsed, className]);

  // Render icon
  const renderedIcon = useMemo(() => {
    if (!icon) return null;

    if (typeof icon === 'string') {
      return <Icon name={icon as IconName} size="md" className="mr-2 flex-shrink-0" />;
    }

    return <div className="mr-2 flex-shrink-0">{icon}</div>;
  }, [icon]);

  const content = (
    <div
      ref={itemRef}
      className={itemClasses}
      onClick={handleClick}
      onKeyDown={handleKeyDown}
      role="menuitem"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
      aria-selected={isSelected}
      aria-current={isSelected ? 'page' : undefined}
      data-key={itemKey}
    >
      <div className="flex items-center justify-between w-full">
        <div className="flex items-center">
          {renderedIcon}
          {(!collapsed || mode === 'horizontal') && (
            <div className="flex items-center">
              <Typography variant="body1" className={`truncate ${isSelected ? 'font-medium' : ''}`}>
                {label}
              </Typography>

              {badge && (
                <Badge variant={badgeColor} size="sm" className="ml-2">
                  {badge}
                </Badge>
              )}
            </div>
          )}
        </div>

        {shortcut && !collapsed && mode !== 'horizontal' && (
          <Hidden hideOnMobile>
            <Typography variant="caption" className="text-gray-500 ml-2">
              {shortcut}
            </Typography>
          </Hidden>
        )}
      </div>
    </div>
  );

  // Wrap với Tooltip nếu có
  if (tooltip) {
    return <Tooltip content={tooltip}>{content}</Tooltip>;
  }

  return content;
};

export default React.memo(MenuItem);
