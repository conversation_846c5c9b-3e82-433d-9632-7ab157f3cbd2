import React, { useState, useEffect, useCallback } from 'react';
import { NotificationType } from '@/shared/components/common/Notification/Notification';

export interface NotificationMessageProps {
  id: string;
  type: NotificationType;
  message: string;
  duration?: number;
  onRemove: (id: string) => void;
  className?: string;
}

/**
 * Component hiển thị thông báo trong chat, có cấu trúc giống với ChatMessage
 */
const NotificationMessage: React.FC<NotificationMessageProps> = ({
  id,
  type,
  message,
  duration = 5000,
  onRemove,
  className = '',
}) => {
  const [visible, setVisible] = useState(true);
  const [exiting, setExiting] = useState(false);

  // Xử lý đóng notification
  const handleClose = useCallback(() => {
    console.log(`[NotificationMessage] Closing notification: ${id}`);
    setExiting(true);
    setTimeout(() => {
      setVisible(false);
      console.log(`[NotificationMessage] Removing notification: ${id}`);
      onRemove(id);
    }, 300); // Thời gian animation
  }, [id, onRemove]);

  // Tự động xóa notification sau duration
  useEffect(() => {
    console.log(`[NotificationMessage] Notification mounted: ${id}, duration: ${duration}ms`);

    if (duration > 0) {
      console.log(`[NotificationMessage] Setting auto-close timer for ${duration}ms: ${id}`);
      const timer = setTimeout(() => {
        console.log(`[NotificationMessage] Auto-close timer triggered for: ${id}`);
        handleClose();
      }, duration);

      return () => {
        console.log(`[NotificationMessage] Clearing auto-close timer for: ${id}`);
        clearTimeout(timer);
      };
    }
    return undefined;
  }, [duration, handleClose, id]);

  // Không hiển thị nếu đã ẩn
  if (!visible) return null;

  // Format timestamp
  const formatTime = (): string => {
    return new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div
      className={`flex w-full transition-all duration-300 ${className} ${
        exiting ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      }`}
    >
      {/* Avatar giống với avatar của AI */}
      <div className="flex-shrink-0 mr-2 self-start mt-1">
        <div className="w-6 h-6">
          <img
            src="/assets/images/ai-agents/assistant-robot.svg"
            alt="AI Assistant"
            className="w-full h-full rounded-full"
          />
        </div>
      </div>

      {/* Nội dung thông báo */}
      <div className="flex-1 max-w-full">
        <div className="bg-gray-100 dark:bg-dark-light rounded-lg rounded-tl-none p-3 inline-block max-w-full">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-2">
              {type === 'success' && (
                <svg
                  className="w-4 h-4 text-green-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}
              {type === 'error' && (
                <svg
                  className="w-4 h-4 text-red-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}
              {type === 'warning' && (
                <svg
                  className="w-4 h-4 text-yellow-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              )}
              {type === 'info' && (
                <svg
                  className="w-4 h-4 text-blue-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              )}
            </div>
            <div className="flex-grow text-sm break-words">{message}</div>
          </div>
        </div>

        <div className="text-xs text-gray-500 mt-1 text-left">{formatTime()}</div>
      </div>
    </div>
  );
};

export default NotificationMessage;
