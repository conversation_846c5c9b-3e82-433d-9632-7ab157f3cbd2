/**
 * Hook xử lý dữ liệu từ API
 * @module useTableDataProcessor
 */
import { useMemo } from 'react';

/**
 * Interface cho dữ liệu phân trang từ API
 */
export interface PaginatedData<T> {
  items: T[];
  meta: {
    totalItems: number;
    currentPage: number;
    totalPages: number;
    itemsPerPage: number;
  };
}

/**
 * Interface cho dữ liệu bảng
 */
export interface TableData<T> {
  items: T[];
  totalItems: number;
  currentPage: number;
  totalPages: number;
}

/**
 * Hook xử lý dữ liệu từ API
 * @template T Kiểu dữ liệu của dòng trong bảng
 * @param data Dữ liệu phân trang từ API
 * @param isLoading Trạng thái đang tải
 * @returns Dữ liệu bảng đã xử lý
 */
export function useTableDataProcessor<T>(
  data: PaginatedData<T> | undefined,
  isLoading: boolean
): TableData<T> & { loading: boolean } {
  return useMemo(() => {
    if (!data) {
      return {
        items: [],
        totalItems: 0,
        currentPage: 1,
        totalPages: 0,
        loading: isLoading,
      };
    }

    return {
      items: data.items,
      totalItems: data.meta.totalItems,
      currentPage: data.meta.currentPage,
      totalPages: data.meta.totalPages,
      loading: isLoading,
    };
  }, [data, isLoading]);
}
