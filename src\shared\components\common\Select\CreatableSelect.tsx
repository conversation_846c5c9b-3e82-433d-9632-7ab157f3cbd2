import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/shared/contexts/theme';
import { Icon } from '@/shared/components/common';
import SelectOption from './SelectOption';
import { SelectOption as SelectOptionType } from './Select';

export interface CreatableSelectProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[] | number | number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | string[] | number | number[]) => void;

  /**
   * Options
   */
  options: SelectOptionType[];

  /**
   * Callback khi tạo option mới
   */
  onCreateOption?: (inputValue: string) => void;

  /**
   * Format giá trị khi tạo option mới
   */
  formatCreateLabel?: (inputValue: string) => string;

  /**
   * <PERSON>ểm tra xem có thể tạo option mới không
   */
  isValidNewOption?: (inputValue: string, options: SelectOptionType[]) => boolean;

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Các props khác
   */
  name?: string;
  id?: string;
  className?: string;
  error?: string;
  helperText?: string;

  /**
   * Kích thước
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Chiều rộng
   */
  fullWidth?: boolean;
}

/**
 * Component CreatableSelect - Select cho phép tạo option mới nếu không tìm thấy
 */
const CreatableSelect = forwardRef<HTMLInputElement, CreatableSelectProps>(
  (
    {
      value,
      onChange,
      options = [],
      onCreateOption,
      formatCreateLabel,
      isValidNewOption,
      multiple = false,
      placeholder = '',
      label,
      disabled = false,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
    },
    ref
  ) => {
    const { t } = useTranslation();
    useTheme(); // Sử dụng hook theme mới
    const [isOpen, setIsOpen] = useState(false);
    const [inputValue, setInputValue] = useState('');
    const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
      multiple
        ? Array.isArray(value)
          ? (value as (string | number)[])
          : []
        : value !== undefined
          ? [value as string | number]
          : []
    );
    const [internalOptions, setInternalOptions] = useState<SelectOptionType[]>(options);

    const selectRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    // Forward ref to input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as (string | number)[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string | number] : []);
      }
    }, [value, multiple]);

    // Update internal options when options prop changes
    useEffect(() => {
      setInternalOptions(options);
    }, [options]);

    // Close dropdown when clicking outside
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    // Focus input when dropdown opens
    useEffect(() => {
      if (isOpen && inputRef.current) {
        inputRef.current.focus();
      }
    }, [isOpen]);

    // Check if input value is a valid new option
    const checkIsValidNewOption = (input: string) => {
      if (!input) return false;

      // Check if option already exists
      const optionExists = internalOptions.some(
        option => option.label.toLowerCase() === input.toLowerCase()
      );

      if (optionExists) return false;

      // Use custom validation if provided
      if (isValidNewOption) {
        return isValidNewOption(input, internalOptions);
      }

      // Default validation: input must be at least 1 character
      return input.trim().length > 0;
    };

    // Handle input change
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);
    };

    // Handle create option
    const handleCreateOption = () => {
      if (!checkIsValidNewOption(inputValue)) return;

      const newOption: SelectOptionType = {
        value: `new-${Date.now()}`,
        label: inputValue,
      };

      // Add to internal options
      setInternalOptions(prev => [...prev, newOption]);

      // Call onCreateOption callback if provided
      if (onCreateOption) {
        onCreateOption(inputValue);
      }

      // Select the new option
      handleOptionClick(newOption.value);

      // Clear input
      setInputValue('');
    };

    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      let newSelectedValues: (string | number)[];

      if (multiple) {
        // Toggle selection for multiple select
        if (selectedValues.includes(optionValue)) {
          newSelectedValues = selectedValues.filter(val => val !== optionValue);
        } else {
          newSelectedValues = [...selectedValues, optionValue];
        }
      } else {
        // Single select
        newSelectedValues = [optionValue];
        setIsOpen(false); // Close dropdown for single select
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues as string[] | number[]);
        } else {
          // For single select, ensure we have a value to pass
          const firstValue = newSelectedValues[0];
          if (firstValue !== undefined) {
            onChange(firstValue);
          }
        }
      }

      // Clear input value
      setInputValue('');
    };

    // Filter options based on input value
    const filteredOptions = inputValue
      ? internalOptions.filter(option =>
          option.label.toLowerCase().includes(inputValue.toLowerCase())
        )
      : internalOptions;

    // Get display value
    const getDisplayValue = () => {
      if (selectedValues.length === 0) return placeholder;

      if (multiple) {
        if (selectedValues.length === 1) {
          const selectedOption = internalOptions.find(opt => opt.value === selectedValues[0]);
          return selectedOption ? selectedOption.label : '';
        } else {
          return t('common.selected', { count: selectedValues.length });
        }
      } else {
        const selectedOption = internalOptions.find(opt => opt.value === selectedValues[0]);
        return selectedOption ? selectedOption.label : '';
      }
    };

    // Format create label
    const getCreateLabel = () => {
      if (formatCreateLabel) {
        return formatCreateLabel(inputValue);
      }
      return t('common.createOption', { option: inputValue });
    };

    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border rounded-md bg-white dark:bg-dark-light
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            <svg
              className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>

        {/* Error message */}
        {error && <p className="mt-1 text-sm text-red-500">{error}</p>}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{helperText}</p>
        )}

        {/* Dropdown */}
        {isOpen && (
          <div className="absolute z-10 w-full mt-1 bg-white dark:bg-dark-light rounded-md shadow-lg max-h-60 overflow-auto animate-fade-in">
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-white dark:bg-dark-light border-b border-gray-200 dark:border-gray-700">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                placeholder={t('common.search', 'Search...')}
                className="w-full px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/30 dark:bg-dark-lighter dark:text-white"
                onClick={e => e.stopPropagation()}
                onKeyDown={e => {
                  if (e.key === 'Enter' && checkIsValidNewOption(inputValue)) {
                    e.preventDefault();
                    handleCreateOption();
                  }
                }}
              />
            </div>

            {/* Options */}
            <div role="listbox" aria-multiselectable={multiple}>
              {filteredOptions.length > 0 ? (
                filteredOptions.map(option => {
                  const isSelected = selectedValues.includes(option.value);

                  return (
                    <SelectOption
                      key={`option-${option.value}`}
                      value={option.value}
                      label={option.label}
                      icon={option.icon}
                      {...(option.disabled !== undefined && { disabled: option.disabled })}
                      selected={isSelected}
                      onClick={() => handleOptionClick(option.value)}
                      data={option.data}
                    />
                  );
                })
              ) : (
                <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400">
                  {inputValue && checkIsValidNewOption(inputValue) ? (
                    <div
                      className="flex items-center cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-lighter py-1"
                      onClick={handleCreateOption}
                    >
                      <Icon name="plus" className="mr-2" size="sm" />
                      {getCreateLabel()}
                    </div>
                  ) : (
                    t('common.noResults', 'No results found')
                  )}
                </div>
              )}

              {/* Create option button */}
              {inputValue && checkIsValidNewOption(inputValue) && filteredOptions.length > 0 && (
                <div
                  className="px-4 py-2 text-sm border-t border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-lighter"
                  onClick={handleCreateOption}
                >
                  <div className="flex items-center">
                    <Icon name="plus" className="mr-2" size="sm" />
                    {getCreateLabel()}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }
);

CreatableSelect.displayName = 'CreatableSelect';

export default CreatableSelect;
