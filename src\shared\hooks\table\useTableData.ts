/**
 * Hook quản lý dữ liệu bảng với phân trang, sắp xếp và tìm kiếm
 * @module useTableData
 */
import { useState, useMemo, useCallback, useEffect } from 'react';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { SortOrder } from '@/shared/components/common/Table/types';

/**
 * Interface cho tham số đầu vào của hook useTableData
 */
export interface UseTableDataOptions<TQueryParams> {
  /**
   * Giá trị mặc định cho trang hiện tại
   * @default 1
   */
  defaultPage?: number;

  /**
   * Giá trị mặc định cho kích thước trang
   * @default 10
   */
  defaultPageSize?: number;

  /**
   * Giá trị mặc định cho từ khóa tìm kiếm
   * @default ''
   */
  defaultSearchTerm?: string;

  /**
   * Gi<PERSON> trị mặc định cho trường sắp xếp
   * @default null
   */
  defaultSortBy?: string | null;

  /**
   * <PERSON>i<PERSON> trị mặc định cho hướng sắp xếp
   * @default null
   */
  defaultSortDirection?: SortDirection | null;

  /**
   * Hàm tạo query params từ các tham số
   * @param page Trang hiện tại
   * @param pageSize Kích thước trang
   * @param searchTerm Từ khóa tìm kiếm
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Query params cho API
   */
  createQueryParams: (
    page: number,
    pageSize: number,
    searchTerm: string,
    sortBy: string | null,
    sortDirection: SortDirection | null
  ) => TQueryParams;
}

/**
 * Hook quản lý dữ liệu bảng với phân trang, sắp xếp và tìm kiếm
 * @template TQueryParams Kiểu dữ liệu của query params
 * @param options Tùy chọn cho hook
 * @returns Các state và hàm xử lý cho bảng
 */
export function useTableData<TQueryParams>(options: UseTableDataOptions<TQueryParams>) {
  // Khởi tạo các state với giá trị mặc định
  const [currentPage, setCurrentPage] = useState(options.defaultPage || 1);
  const [pageSize, setPageSize] = useState(options.defaultPageSize || 10);
  const [searchTerm, setSearchTerm] = useState(options.defaultSearchTerm || '');
  const [sortBy, setSortBy] = useState<string | null>(options.defaultSortBy || null);
  const [sortDirection, setSortDirection] = useState<SortDirection | null>(
    options.defaultSortDirection || null
  );

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    return options.createQueryParams(currentPage, pageSize, searchTerm, sortBy, sortDirection);
  }, [currentPage, pageSize, searchTerm, sortBy, sortDirection, options]);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    // Reset về trang 1 khi tìm kiếm
    setCurrentPage(1);
  }, []);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== pageSize) {
        setPageSize(newPageSize);
      }
    },
    [pageSize]
  );

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    // Chuyển đổi từ SortOrder (Table) sang SortDirection (API)
    let direction: SortDirection | null = null;
    if (order === 'asc') {
      direction = SortDirection.ASC;
    } else if (order === 'desc') {
      direction = SortDirection.DESC;
    }

    setSortBy(column);
    setSortDirection(direction);
  }, []);

  // Reset trang về 1 khi thay đổi kích thước trang
  useEffect(() => {
    setCurrentPage(1);
  }, [pageSize]);

  return {
    // States
    currentPage,
    pageSize,
    searchTerm,
    sortBy,
    sortDirection,
    queryParams,

    // Handlers
    handleSearch,
    handlePageChange,
    handleSortChange,

    // Setters
    setCurrentPage,
    setPageSize,
    setSearchTerm,
    setSortBy,
    setSortDirection,
  };
}
